{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['ម.គស', 'គស'],\n  abbreviated: ['មុនគ.ស', 'គ.ស'],\n  wide: ['មុនគ្រិស្តសករាជ', 'នៃគ្រិស្តសករាជ']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['ត្រីមាសទី 1', 'ត្រីមាសទី 2', 'ត្រីមាសទី 3', 'ត្រីមាសទី 4']\n};\nvar monthValues = {\n  narrow: ['ម.ក', 'ក.ម', 'មិ', 'ម.ស', 'ឧ.ស', 'ម.ថ', 'ក.ដ', 'សី', 'កញ', 'តុ', 'វិ', 'ធ'],\n  abbreviated: ['មករា', 'កុម្ភៈ', 'មីនា', 'មេសា', 'ឧសភា', 'មិថុនា', 'កក្កដា', 'សីហា', 'កញ្ញា', 'តុលា', 'វិច្ឆិកា', 'ធ្នូ'],\n  wide: ['មករា', 'កុម្ភៈ', 'មីនា', 'មេសា', 'ឧសភា', 'មិថុនា', 'កក្កដា', 'សីហា', 'កញ្ញា', 'តុលា', 'វិច្ឆិកា', 'ធ្នូ']\n};\nvar dayValues = {\n  narrow: ['អា', 'ច', 'អ', 'ព', 'ព្រ', 'សុ', 'ស'],\n  short: ['អា', 'ច', 'អ', 'ព', 'ព្រ', 'សុ', 'ស'],\n  abbreviated: ['អា', 'ច', 'អ', 'ព', 'ព្រ', 'សុ', 'ស'],\n  wide: ['អាទិត្យ', 'ចន្ទ', 'អង្គារ', 'ពុធ', 'ព្រហស្បតិ៍', 'សុក្រ', 'សៅរ៍']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ព្រឹក',\n    pm: 'ល្ងាច',\n    midnight: '​ពេលកណ្ដាលអធ្រាត្រ',\n    noon: 'ពេលថ្ងៃត្រង់',\n    morning: 'ពេលព្រឹក',\n    afternoon: 'ពេលរសៀល',\n    evening: 'ពេលល្ងាច',\n    night: 'ពេលយប់'\n  },\n  abbreviated: {\n    am: 'ព្រឹក',\n    pm: 'ល្ងាច',\n    midnight: '​ពេលកណ្ដាលអធ្រាត្រ',\n    noon: 'ពេលថ្ងៃត្រង់',\n    morning: 'ពេលព្រឹក',\n    afternoon: 'ពេលរសៀល',\n    evening: 'ពេលល្ងាច',\n    night: 'ពេលយប់'\n  },\n  wide: {\n    am: 'ព្រឹក',\n    pm: 'ល្ងាច',\n    midnight: '​ពេលកណ្ដាលអធ្រាត្រ',\n    noon: 'ពេលថ្ងៃត្រង់',\n    morning: 'ពេលព្រឹក',\n    afternoon: 'ពេលរសៀល',\n    evening: 'ពេលល្ងាច',\n    night: 'ពេលយប់'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ព្រឹក',\n    pm: 'ល្ងាច',\n    midnight: '​ពេលកណ្ដាលអធ្រាត្រ',\n    noon: 'ពេលថ្ងៃត្រង់',\n    morning: 'ពេលព្រឹក',\n    afternoon: 'ពេលរសៀល',\n    evening: 'ពេលល្ងាច',\n    night: 'ពេលយប់'\n  },\n  abbreviated: {\n    am: 'ព្រឹក',\n    pm: 'ល្ងាច',\n    midnight: '​ពេលកណ្ដាលអធ្រាត្រ',\n    noon: 'ពេលថ្ងៃត្រង់',\n    morning: 'ពេលព្រឹក',\n    afternoon: 'ពេលរសៀល',\n    evening: 'ពេលល្ងាច',\n    night: 'ពេលយប់'\n  },\n  wide: {\n    am: 'ព្រឹក',\n    pm: 'ល្ងាច',\n    midnight: '​ពេលកណ្ដាលអធ្រាត្រ',\n    noon: 'ពេលថ្ងៃត្រង់',\n    morning: 'ពេលព្រឹក',\n    afternoon: 'ពេលរសៀល',\n    evening: 'ពេលល្ងាច',\n    night: 'ពេលយប់'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _) {\n  var number = Number(dirtyNumber);\n  return number.toString();\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_", "number", "Number", "toString", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/km/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['ម.គស', 'គស'],\n  abbreviated: ['មុនគ.ស', 'គ.ស'],\n  wide: ['មុនគ្រិស្តសករាជ', 'នៃគ្រិស្តសករាជ']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['ត្រីមាសទី 1', 'ត្រីមាសទី 2', 'ត្រីមាសទី 3', 'ត្រីមាសទី 4']\n};\nvar monthValues = {\n  narrow: ['ម.ក', 'ក.ម', 'មិ', 'ម.ស', 'ឧ.ស', 'ម.ថ', 'ក.ដ', 'សី', 'កញ', 'តុ', 'វិ', 'ធ'],\n  abbreviated: ['មករា', 'កុម្ភៈ', 'មីនា', 'មេសា', 'ឧសភា', 'មិថុនា', 'កក្កដា', 'សីហា', 'កញ្ញា', 'តុលា', 'វិច្ឆិកា', 'ធ្នូ'],\n  wide: ['មករា', 'កុម្ភៈ', 'មីនា', 'មេសា', 'ឧសភា', 'មិថុនា', 'កក្កដា', 'សីហា', 'កញ្ញា', 'តុលា', 'វិច្ឆិកា', 'ធ្នូ']\n};\nvar dayValues = {\n  narrow: ['អា', 'ច', 'អ', 'ព', 'ព្រ', 'សុ', 'ស'],\n  short: ['អា', 'ច', 'អ', 'ព', 'ព្រ', 'សុ', 'ស'],\n  abbreviated: ['អា', 'ច', 'អ', 'ព', 'ព្រ', 'សុ', 'ស'],\n  wide: ['អាទិត្យ', 'ចន្ទ', 'អង្គារ', 'ពុធ', 'ព្រហស្បតិ៍', 'សុក្រ', 'សៅរ៍']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ព្រឹក',\n    pm: 'ល្ងាច',\n    midnight: '​ពេលកណ្ដាលអធ្រាត្រ',\n    noon: 'ពេលថ្ងៃត្រង់',\n    morning: 'ពេលព្រឹក',\n    afternoon: 'ពេលរសៀល',\n    evening: 'ពេលល្ងាច',\n    night: 'ពេលយប់'\n  },\n  abbreviated: {\n    am: 'ព្រឹក',\n    pm: 'ល្ងាច',\n    midnight: '​ពេលកណ្ដាលអធ្រាត្រ',\n    noon: 'ពេលថ្ងៃត្រង់',\n    morning: 'ពេលព្រឹក',\n    afternoon: 'ពេលរសៀល',\n    evening: 'ពេលល្ងាច',\n    night: 'ពេលយប់'\n  },\n  wide: {\n    am: 'ព្រឹក',\n    pm: 'ល្ងាច',\n    midnight: '​ពេលកណ្ដាលអធ្រាត្រ',\n    noon: 'ពេលថ្ងៃត្រង់',\n    morning: 'ពេលព្រឹក',\n    afternoon: 'ពេលរសៀល',\n    evening: 'ពេលល្ងាច',\n    night: 'ពេលយប់'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ព្រឹក',\n    pm: 'ល្ងាច',\n    midnight: '​ពេលកណ្ដាលអធ្រាត្រ',\n    noon: 'ពេលថ្ងៃត្រង់',\n    morning: 'ពេលព្រឹក',\n    afternoon: 'ពេលរសៀល',\n    evening: 'ពេលល្ងាច',\n    night: 'ពេលយប់'\n  },\n  abbreviated: {\n    am: 'ព្រឹក',\n    pm: 'ល្ងាច',\n    midnight: '​ពេលកណ្ដាលអធ្រាត្រ',\n    noon: 'ពេលថ្ងៃត្រង់',\n    morning: 'ពេលព្រឹក',\n    afternoon: 'ពេលរសៀល',\n    evening: 'ពេលល្ងាច',\n    night: 'ពេលយប់'\n  },\n  wide: {\n    am: 'ព្រឹក',\n    pm: 'ល្ងាច',\n    midnight: '​ពេលកណ្ដាលអធ្រាត្រ',\n    noon: 'ពេលថ្ងៃត្រង់',\n    morning: 'ពេលព្រឹក',\n    afternoon: 'ពេលរសៀល',\n    evening: 'ពេលល្ងាច',\n    night: 'ពេលយប់'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _) {\n  var number = Number(dirtyNumber);\n  return number.toString();\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC;EACtBC,WAAW,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC;EAC9BC,IAAI,EAAE,CAAC,iBAAiB,EAAE,gBAAgB;AAC5C,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa;AACnE,CAAC;AACD,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;EACrFC,WAAW,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC;EACxHC,IAAI,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM;AAClH,CAAC;AACD,IAAIG,SAAS,GAAG;EACdL,MAAM,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC;EAC/CM,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC;EAC9CL,WAAW,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC;EACpDC,IAAI,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM;AAC1E,CAAC;AACD,IAAIK,eAAe,GAAG;EACpBP,MAAM,EAAE;IACNQ,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,OAAO;IACXC,QAAQ,EAAE,oBAAoB;IAC9BC,IAAI,EAAE,cAAc;IACpBC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,OAAO;IACXC,QAAQ,EAAE,oBAAoB;IAC9BC,IAAI,EAAE,cAAc;IACpBC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,OAAO;IACXC,QAAQ,EAAE,oBAAoB;IAC9BC,IAAI,EAAE,cAAc;IACpBC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BhB,MAAM,EAAE;IACNQ,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,OAAO;IACXC,QAAQ,EAAE,oBAAoB;IAC9BC,IAAI,EAAE,cAAc;IACpBC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,OAAO;IACXC,QAAQ,EAAE,oBAAoB;IAC9BC,IAAI,EAAE,cAAc;IACpBC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,OAAO;IACXC,QAAQ,EAAE,oBAAoB;IAC9BC,IAAI,EAAE,cAAc;IACpBC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,CAAC,EAAE;EACzD,IAAIC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAChC,OAAOE,MAAM,CAACE,QAAQ,CAAC,CAAC;AAC1B,CAAC;AACD,IAAIC,QAAQ,GAAG;EACbN,aAAa,EAAEA,aAAa;EAC5BO,GAAG,EAAE1B,eAAe,CAAC;IACnB2B,MAAM,EAAE1B,SAAS;IACjB2B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE7B,eAAe,CAAC;IACvB2B,MAAM,EAAEtB,aAAa;IACrBuB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAE/B,eAAe,CAAC;IACrB2B,MAAM,EAAErB,WAAW;IACnBsB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFI,GAAG,EAAEhC,eAAe,CAAC;IACnB2B,MAAM,EAAEpB,SAAS;IACjBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,SAAS,EAAEjC,eAAe,CAAC;IACzB2B,MAAM,EAAElB,eAAe;IACvBmB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEhB,yBAAyB;IAC3CiB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}