{"ast": null, "code": "export { YearCalendar } from './YearCalendar';\nexport { yearCalendarClasses, getYearCalendarUtilityClass } from './yearCalendarClasses';\nexport { pickersYearClasses } from './pickersYearClasses';", "map": {"version": 3, "names": ["YearCalendar", "yearCalendarClasses", "getYearCalendarUtilityClass", "pickersYearClasses"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/x-date-pickers/YearCalendar/index.js"], "sourcesContent": ["export { YearCalendar } from './YearCalendar';\nexport { yearCalendarClasses, getYearCalendarUtilityClass } from './yearCalendarClasses';\nexport { pickersYearClasses } from './pickersYearClasses';"], "mappings": "AAAA,SAASA,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,mBAAmB,EAAEC,2BAA2B,QAAQ,uBAAuB;AACxF,SAASC,kBAAkB,QAAQ,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}