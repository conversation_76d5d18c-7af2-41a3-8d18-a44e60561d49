{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'أقل من ثانية',\n    two: 'أقل من زوز ثواني',\n    threeToTen: 'أقل من {{count}} ثواني',\n    other: 'أقل من {{count}} ثانية'\n  },\n  xSeconds: {\n    one: 'ثانية',\n    two: 'زوز ثواني',\n    threeToTen: '{{count}} ثواني',\n    other: '{{count}} ثانية'\n  },\n  halfAMinute: 'نص دقيقة',\n  lessThanXMinutes: {\n    one: 'أقل من دقيقة',\n    two: 'أقل من دقيقتين',\n    threeToTen: 'أقل من {{count}} دقايق',\n    other: 'أقل من {{count}} دقيقة'\n  },\n  xMinutes: {\n    one: 'دقيقة',\n    two: 'دقيقتين',\n    threeToTen: '{{count}} دقايق',\n    other: '{{count}} دقيقة'\n  },\n  aboutXHours: {\n    one: 'ساعة تقريب',\n    two: 'ساعتين تقريب',\n    threeToTen: '{{count}} سوايع تقريب',\n    other: '{{count}} ساعة تقريب'\n  },\n  xHours: {\n    one: 'ساعة',\n    two: 'ساعتين',\n    threeToTen: '{{count}} سوايع',\n    other: '{{count}} ساعة'\n  },\n  xDays: {\n    one: 'نهار',\n    two: 'نهارين',\n    threeToTen: '{{count}} أيام',\n    other: '{{count}} يوم'\n  },\n  aboutXWeeks: {\n    one: 'جمعة تقريب',\n    two: 'جمعتين تقريب',\n    threeToTen: '{{count}} جماع تقريب',\n    other: '{{count}} جمعة تقريب'\n  },\n  xWeeks: {\n    one: 'جمعة',\n    two: 'جمعتين',\n    threeToTen: '{{count}} جماع',\n    other: '{{count}} جمعة'\n  },\n  aboutXMonths: {\n    one: 'شهر تقريب',\n    two: 'شهرين تقريب',\n    threeToTen: '{{count}} أشهرة تقريب',\n    other: '{{count}} شهر تقريب'\n  },\n  xMonths: {\n    one: 'شهر',\n    two: 'شهرين',\n    threeToTen: '{{count}} أشهرة',\n    other: '{{count}} شهر'\n  },\n  aboutXYears: {\n    one: 'عام تقريب',\n    two: 'عامين تقريب',\n    threeToTen: '{{count}} أعوام تقريب',\n    other: '{{count}} عام تقريب'\n  },\n  xYears: {\n    one: 'عام',\n    two: 'عامين',\n    threeToTen: '{{count}} أعوام',\n    other: '{{count}} عام'\n  },\n  overXYears: {\n    one: 'أكثر من عام',\n    two: 'أكثر من عامين',\n    threeToTen: 'أكثر من {{count}} أعوام',\n    other: 'أكثر من {{count}} عام'\n  },\n  almostXYears: {\n    one: 'عام تقريب',\n    two: 'عامين تقريب',\n    threeToTen: '{{count}} أعوام تقريب',\n    other: '{{count}} عام تقريب'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var usageGroup = formatDistanceLocale[token];\n  var result;\n  if (typeof usageGroup === 'string') {\n    result = usageGroup;\n  } else if (count === 1) {\n    result = usageGroup.one;\n  } else if (count === 2) {\n    result = usageGroup.two;\n  } else if (count <= 10) {\n    result = usageGroup.threeToTen.replace('{{count}}', String(count));\n  } else {\n    result = usageGroup.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'في ' + result;\n    } else {\n      return 'عندو ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "two", "threeToTen", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "usageGroup", "result", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/ar-TN/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'أقل من ثانية',\n    two: 'أقل من زوز ثواني',\n    threeToTen: 'أقل من {{count}} ثواني',\n    other: 'أقل من {{count}} ثانية'\n  },\n  xSeconds: {\n    one: 'ثانية',\n    two: 'زوز ثواني',\n    threeToTen: '{{count}} ثواني',\n    other: '{{count}} ثانية'\n  },\n  halfAMinute: 'نص دقيقة',\n  lessThanXMinutes: {\n    one: 'أقل من دقيقة',\n    two: 'أقل من دقيقتين',\n    threeToTen: 'أقل من {{count}} دقايق',\n    other: 'أقل من {{count}} دقيقة'\n  },\n  xMinutes: {\n    one: 'دقيقة',\n    two: 'دقيقتين',\n    threeToTen: '{{count}} دقايق',\n    other: '{{count}} دقيقة'\n  },\n  aboutXHours: {\n    one: 'ساعة تقريب',\n    two: 'ساعتين تقريب',\n    threeToTen: '{{count}} سوايع تقريب',\n    other: '{{count}} ساعة تقريب'\n  },\n  xHours: {\n    one: 'ساعة',\n    two: 'ساعتين',\n    threeToTen: '{{count}} سوايع',\n    other: '{{count}} ساعة'\n  },\n  xDays: {\n    one: 'نهار',\n    two: 'نهارين',\n    threeToTen: '{{count}} أيام',\n    other: '{{count}} يوم'\n  },\n  aboutXWeeks: {\n    one: 'جمعة تقريب',\n    two: 'جمعتين تقريب',\n    threeToTen: '{{count}} جماع تقريب',\n    other: '{{count}} جمعة تقريب'\n  },\n  xWeeks: {\n    one: 'جمعة',\n    two: 'جمعتين',\n    threeToTen: '{{count}} جماع',\n    other: '{{count}} جمعة'\n  },\n  aboutXMonths: {\n    one: 'شهر تقريب',\n    two: 'شهرين تقريب',\n    threeToTen: '{{count}} أشهرة تقريب',\n    other: '{{count}} شهر تقريب'\n  },\n  xMonths: {\n    one: 'شهر',\n    two: 'شهرين',\n    threeToTen: '{{count}} أشهرة',\n    other: '{{count}} شهر'\n  },\n  aboutXYears: {\n    one: 'عام تقريب',\n    two: 'عامين تقريب',\n    threeToTen: '{{count}} أعوام تقريب',\n    other: '{{count}} عام تقريب'\n  },\n  xYears: {\n    one: 'عام',\n    two: 'عامين',\n    threeToTen: '{{count}} أعوام',\n    other: '{{count}} عام'\n  },\n  overXYears: {\n    one: 'أكثر من عام',\n    two: 'أكثر من عامين',\n    threeToTen: 'أكثر من {{count}} أعوام',\n    other: 'أكثر من {{count}} عام'\n  },\n  almostXYears: {\n    one: 'عام تقريب',\n    two: 'عامين تقريب',\n    threeToTen: '{{count}} أعوام تقريب',\n    other: '{{count}} عام تقريب'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var usageGroup = formatDistanceLocale[token];\n  var result;\n  if (typeof usageGroup === 'string') {\n    result = usageGroup;\n  } else if (count === 1) {\n    result = usageGroup.one;\n  } else if (count === 2) {\n    result = usageGroup.two;\n  } else if (count <= 10) {\n    result = usageGroup.threeToTen.replace('{{count}}', String(count));\n  } else {\n    result = usageGroup.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'في ' + result;\n    } else {\n      return 'عندو ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,cAAc;IACnBC,GAAG,EAAE,kBAAkB;IACvBC,UAAU,EAAE,wBAAwB;IACpCC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRJ,GAAG,EAAE,OAAO;IACZC,GAAG,EAAE,WAAW;IAChBC,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,UAAU;EACvBC,gBAAgB,EAAE;IAChBN,GAAG,EAAE,cAAc;IACnBC,GAAG,EAAE,gBAAgB;IACrBC,UAAU,EAAE,wBAAwB;IACpCC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRP,GAAG,EAAE,OAAO;IACZC,GAAG,EAAE,SAAS;IACdC,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXR,GAAG,EAAE,YAAY;IACjBC,GAAG,EAAE,cAAc;IACnBC,UAAU,EAAE,uBAAuB;IACnCC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNT,GAAG,EAAE,MAAM;IACXC,GAAG,EAAE,QAAQ;IACbC,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLV,GAAG,EAAE,MAAM;IACXC,GAAG,EAAE,QAAQ;IACbC,UAAU,EAAE,gBAAgB;IAC5BC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXX,GAAG,EAAE,YAAY;IACjBC,GAAG,EAAE,cAAc;IACnBC,UAAU,EAAE,sBAAsB;IAClCC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNZ,GAAG,EAAE,MAAM;IACXC,GAAG,EAAE,QAAQ;IACbC,UAAU,EAAE,gBAAgB;IAC5BC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZb,GAAG,EAAE,WAAW;IAChBC,GAAG,EAAE,aAAa;IAClBC,UAAU,EAAE,uBAAuB;IACnCC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPd,GAAG,EAAE,KAAK;IACVC,GAAG,EAAE,OAAO;IACZC,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXf,GAAG,EAAE,WAAW;IAChBC,GAAG,EAAE,aAAa;IAClBC,UAAU,EAAE,uBAAuB;IACnCC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNhB,GAAG,EAAE,KAAK;IACVC,GAAG,EAAE,OAAO;IACZC,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVjB,GAAG,EAAE,aAAa;IAClBC,GAAG,EAAE,eAAe;IACpBC,UAAU,EAAE,yBAAyB;IACrCC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZlB,GAAG,EAAE,WAAW;IAChBC,GAAG,EAAE,aAAa;IAClBC,UAAU,EAAE,uBAAuB;IACnCC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,UAAU,GAAGzB,oBAAoB,CAACsB,KAAK,CAAC;EAC5C,IAAII,MAAM;EACV,IAAI,OAAOD,UAAU,KAAK,QAAQ,EAAE;IAClCC,MAAM,GAAGD,UAAU;EACrB,CAAC,MAAM,IAAIF,KAAK,KAAK,CAAC,EAAE;IACtBG,MAAM,GAAGD,UAAU,CAACvB,GAAG;EACzB,CAAC,MAAM,IAAIqB,KAAK,KAAK,CAAC,EAAE;IACtBG,MAAM,GAAGD,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM,IAAIoB,KAAK,IAAI,EAAE,EAAE;IACtBG,MAAM,GAAGD,UAAU,CAACrB,UAAU,CAACuB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EACpE,CAAC,MAAM;IACLG,MAAM,GAAGD,UAAU,CAACpB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAGJ,MAAM;IACvB,CAAC,MAAM;MACL,OAAO,OAAO,GAAGA,MAAM;IACzB;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}