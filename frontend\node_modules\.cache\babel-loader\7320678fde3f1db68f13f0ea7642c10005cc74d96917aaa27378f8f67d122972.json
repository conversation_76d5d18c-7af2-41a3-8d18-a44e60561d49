{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['пр.н.е.', 'н.е.'],\n  abbreviated: ['пред н. е.', 'н. е.'],\n  wide: ['пред нашата ера', 'нашата ера']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1-ви кв.', '2-ри кв.', '3-ти кв.', '4-ти кв.'],\n  wide: ['1-ви квартал', '2-ри квартал', '3-ти квартал', '4-ти квартал']\n};\nvar monthValues = {\n  abbreviated: ['јан', 'фев', 'мар', 'апр', 'мај', 'јун', 'јул', 'авг', 'септ', 'окт', 'ноем', 'дек'],\n  wide: ['јануари', 'февруари', 'март', 'април', 'мај', 'јуни', 'јули', 'август', 'септември', 'октомври', 'ноември', 'декември']\n};\nvar dayValues = {\n  narrow: ['Н', 'П', 'В', 'С', 'Ч', 'П', 'С'],\n  short: ['не', 'по', 'вт', 'ср', 'че', 'пе', 'са'],\n  abbreviated: ['нед', 'пон', 'вто', 'сре', 'чет', 'пет', 'саб'],\n  wide: ['недела', 'понеделник', 'вторник', 'среда', 'четврток', 'петок', 'сабота']\n};\nvar dayPeriodValues = {\n  wide: {\n    am: 'претпладне',\n    pm: 'попладне',\n    midnight: 'полноќ',\n    noon: 'напладне',\n    morning: 'наутро',\n    afternoon: 'попладне',\n    evening: 'навечер',\n    night: 'ноќе'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  var rem100 = number % 100;\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n        return number + '-ви';\n      case 2:\n        return number + '-ри';\n      case 7:\n      case 8:\n        return number + '-ми';\n    }\n  }\n  return number + '-ти';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "ordinalNumber", "dirtyNumber", "_options", "number", "Number", "rem100", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/mk/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['пр.н.е.', 'н.е.'],\n  abbreviated: ['пред н. е.', 'н. е.'],\n  wide: ['пред нашата ера', 'нашата ера']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1-ви кв.', '2-ри кв.', '3-ти кв.', '4-ти кв.'],\n  wide: ['1-ви квартал', '2-ри квартал', '3-ти квартал', '4-ти квартал']\n};\nvar monthValues = {\n  abbreviated: ['јан', 'фев', 'мар', 'апр', 'мај', 'јун', 'јул', 'авг', 'септ', 'окт', 'ноем', 'дек'],\n  wide: ['јануари', 'февруари', 'март', 'април', 'мај', 'јуни', 'јули', 'август', 'септември', 'октомври', 'ноември', 'декември']\n};\nvar dayValues = {\n  narrow: ['Н', 'П', 'В', 'С', 'Ч', 'П', 'С'],\n  short: ['не', 'по', 'вт', 'ср', 'че', 'пе', 'са'],\n  abbreviated: ['нед', 'пон', 'вто', 'сре', 'чет', 'пет', 'саб'],\n  wide: ['недела', 'понеделник', 'вторник', 'среда', 'четврток', 'петок', 'сабота']\n};\nvar dayPeriodValues = {\n  wide: {\n    am: 'претпладне',\n    pm: 'попладне',\n    midnight: 'полноќ',\n    noon: 'напладне',\n    morning: 'наутро',\n    afternoon: 'попладне',\n    evening: 'навечер',\n    night: 'ноќе'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  var rem100 = number % 100;\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n        return number + '-ви';\n      case 2:\n        return number + '-ри';\n      case 7:\n      case 8:\n        return number + '-ми';\n    }\n  }\n  return number + '-ти';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;EAC3BC,WAAW,EAAE,CAAC,YAAY,EAAE,OAAO,CAAC;EACpCC,IAAI,EAAE,CAAC,iBAAiB,EAAE,YAAY;AACxC,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;EAC7DC,IAAI,EAAE,CAAC,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc;AACvE,CAAC;AACD,IAAIE,WAAW,GAAG;EAChBH,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;EACnGC,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU;AAChI,CAAC;AACD,IAAIG,SAAS,GAAG;EACdL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ;AAClF,CAAC;AACD,IAAIK,eAAe,GAAG;EACpBL,IAAI,EAAE;IACJM,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,QAAQ,EAAE;EAChE,IAAIC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAChC,IAAII,MAAM,GAAGF,MAAM,GAAG,GAAG;EACzB,IAAIE,MAAM,GAAG,EAAE,IAAIA,MAAM,GAAG,EAAE,EAAE;IAC9B,QAAQA,MAAM,GAAG,EAAE;MACjB,KAAK,CAAC;QACJ,OAAOF,MAAM,GAAG,KAAK;MACvB,KAAK,CAAC;QACJ,OAAOA,MAAM,GAAG,KAAK;MACvB,KAAK,CAAC;MACN,KAAK,CAAC;QACJ,OAAOA,MAAM,GAAG,KAAK;IACzB;EACF;EACA,OAAOA,MAAM,GAAG,KAAK;AACvB,CAAC;AACD,IAAIG,QAAQ,GAAG;EACbN,aAAa,EAAEA,aAAa;EAC5BO,GAAG,EAAEzB,eAAe,CAAC;IACnB0B,MAAM,EAAEzB,SAAS;IACjB0B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE5B,eAAe,CAAC;IACvB0B,MAAM,EAAErB,aAAa;IACrBsB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAE9B,eAAe,CAAC;IACrB0B,MAAM,EAAEpB,WAAW;IACnBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFI,GAAG,EAAE/B,eAAe,CAAC;IACnB0B,MAAM,EAAEnB,SAAS;IACjBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,SAAS,EAAEhC,eAAe,CAAC;IACzB0B,MAAM,EAAEjB,eAAe;IACvBkB,YAAY,EAAE;EAChB,CAAC;AACH,CAAC;AACD,eAAeH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}