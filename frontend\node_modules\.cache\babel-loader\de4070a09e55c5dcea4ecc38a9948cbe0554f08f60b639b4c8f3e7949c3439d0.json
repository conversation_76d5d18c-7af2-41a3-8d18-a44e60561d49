{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'minder dan een seconde',\n    other: 'minder dan {{count}} seconden'\n  },\n  xSeconds: {\n    one: '1 seconde',\n    other: '{{count}} seconden'\n  },\n  halfAMinute: 'een halve minuut',\n  lessThanXMinutes: {\n    one: 'minder dan een minuut',\n    other: 'minder dan {{count}} minuten'\n  },\n  xMinutes: {\n    one: 'een minuut',\n    other: '{{count}} minuten'\n  },\n  aboutXHours: {\n    one: 'ongeveer 1 uur',\n    other: 'ongeveer {{count}} uur'\n  },\n  xHours: {\n    one: '1 uur',\n    other: '{{count}} uur'\n  },\n  xDays: {\n    one: '1 dag',\n    other: '{{count}} dagen'\n  },\n  aboutXWeeks: {\n    one: 'ongeveer 1 week',\n    other: 'ongeveer {{count}} weken'\n  },\n  xWeeks: {\n    one: '1 week',\n    other: '{{count}} weken'\n  },\n  aboutXMonths: {\n    one: 'ongeveer 1 maand',\n    other: 'ongeveer {{count}} maanden'\n  },\n  xMonths: {\n    one: '1 maand',\n    other: '{{count}} maanden'\n  },\n  aboutXYears: {\n    one: 'ongeveer 1 jaar',\n    other: 'ongeveer {{count}} jaar'\n  },\n  xYears: {\n    one: '1 jaar',\n    other: '{{count}} jaar'\n  },\n  overXYears: {\n    one: 'meer dan 1 jaar',\n    other: 'meer dan {{count}} jaar'\n  },\n  almostXYears: {\n    one: 'bijna 1 jaar',\n    other: 'bijna {{count}} jaar'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'over ' + result;\n    } else {\n      return result + ' geleden';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/nl/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'minder dan een seconde',\n    other: 'minder dan {{count}} seconden'\n  },\n  xSeconds: {\n    one: '1 seconde',\n    other: '{{count}} seconden'\n  },\n  halfAMinute: 'een halve minuut',\n  lessThanXMinutes: {\n    one: 'minder dan een minuut',\n    other: 'minder dan {{count}} minuten'\n  },\n  xMinutes: {\n    one: 'een minuut',\n    other: '{{count}} minuten'\n  },\n  aboutXHours: {\n    one: 'ongeveer 1 uur',\n    other: 'ongeveer {{count}} uur'\n  },\n  xHours: {\n    one: '1 uur',\n    other: '{{count}} uur'\n  },\n  xDays: {\n    one: '1 dag',\n    other: '{{count}} dagen'\n  },\n  aboutXWeeks: {\n    one: 'ongeveer 1 week',\n    other: 'ongeveer {{count}} weken'\n  },\n  xWeeks: {\n    one: '1 week',\n    other: '{{count}} weken'\n  },\n  aboutXMonths: {\n    one: 'ongeveer 1 maand',\n    other: 'ongeveer {{count}} maanden'\n  },\n  xMonths: {\n    one: '1 maand',\n    other: '{{count}} maanden'\n  },\n  aboutXYears: {\n    one: 'ongeveer 1 jaar',\n    other: 'ongeveer {{count}} jaar'\n  },\n  xYears: {\n    one: '1 jaar',\n    other: '{{count}} jaar'\n  },\n  overXYears: {\n    one: 'meer dan 1 jaar',\n    other: 'meer dan {{count}} jaar'\n  },\n  almostXYears: {\n    one: 'bijna 1 jaar',\n    other: 'bijna {{count}} jaar'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'over ' + result;\n    } else {\n      return result + ' geleden';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,wBAAwB;IAC7BC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,kBAAkB;EAC/BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,uBAAuB;IAC5BC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,OAAO,GAAGL,MAAM;IACzB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,UAAU;IAC5B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}