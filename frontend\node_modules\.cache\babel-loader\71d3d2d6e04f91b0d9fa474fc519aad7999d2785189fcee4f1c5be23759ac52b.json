{"ast": null, "code": "import buildFormatLongFn from \"../../../_lib/buildFormatLongFn/index.js\";\nvar dateFormats = {\n  full: 'EEEE, do MMMM, y',\n  // CLDR #1787\n  long: 'do MMMM, y',\n  // CLDR #1788\n  medium: 'd MMM, y',\n  // CLDR #1789\n  short: 'dd/MM/yyyy' // CLDR #1790\n};\nvar timeFormats = {\n  full: 'h:mm:ss a zzzz',\n  // CLDR #1791\n  long: 'h:mm:ss a z',\n  // CLDR #1792\n  medium: 'h:mm:ss a',\n  // CLDR #1793\n  short: 'h:mm a' // CLDR #1794\n};\nvar dateTimeFormats = {\n  full: \"{{date}} 'को' {{time}}\",\n  // CLDR #1795\n  long: \"{{date}} 'को' {{time}}\",\n  // CLDR #1796\n  medium: '{{date}}, {{time}}',\n  // CLDR #1797\n  short: '{{date}}, {{time}}' // CLDR #1798\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nexport default formatLong;", "map": {"version": 3, "names": ["buildFormatLongFn", "dateFormats", "full", "long", "medium", "short", "timeFormats", "dateTimeFormats", "formatLong", "date", "formats", "defaultWidth", "time", "dateTime"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/hi/_lib/formatLong/index.js"], "sourcesContent": ["import buildFormatLongFn from \"../../../_lib/buildFormatLongFn/index.js\";\nvar dateFormats = {\n  full: 'EEEE, do MMMM, y',\n  // CLDR #1787\n  long: 'do MMMM, y',\n  // CLDR #1788\n  medium: 'd MMM, y',\n  // CLDR #1789\n  short: 'dd/MM/yyyy' // CLDR #1790\n};\n\nvar timeFormats = {\n  full: 'h:mm:ss a zzzz',\n  // CLDR #1791\n  long: 'h:mm:ss a z',\n  // CLDR #1792\n  medium: 'h:mm:ss a',\n  // CLDR #1793\n  short: 'h:mm a' // CLDR #1794\n};\n\nvar dateTimeFormats = {\n  full: \"{{date}} 'को' {{time}}\",\n  // CLDR #1795\n  long: \"{{date}} 'को' {{time}}\",\n  // CLDR #1796\n  medium: '{{date}}, {{time}}',\n  // CLDR #1797\n  short: '{{date}}, {{time}}' // CLDR #1798\n};\n\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nexport default formatLong;"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,0CAA0C;AACxE,IAAIC,WAAW,GAAG;EAChBC,IAAI,EAAE,kBAAkB;EACxB;EACAC,IAAI,EAAE,YAAY;EAClB;EACAC,MAAM,EAAE,UAAU;EAClB;EACAC,KAAK,EAAE,YAAY,CAAC;AACtB,CAAC;AAED,IAAIC,WAAW,GAAG;EAChBJ,IAAI,EAAE,gBAAgB;EACtB;EACAC,IAAI,EAAE,aAAa;EACnB;EACAC,MAAM,EAAE,WAAW;EACnB;EACAC,KAAK,EAAE,QAAQ,CAAC;AAClB,CAAC;AAED,IAAIE,eAAe,GAAG;EACpBL,IAAI,EAAE,wBAAwB;EAC9B;EACAC,IAAI,EAAE,wBAAwB;EAC9B;EACAC,MAAM,EAAE,oBAAoB;EAC5B;EACAC,KAAK,EAAE,oBAAoB,CAAC;AAC9B,CAAC;AAED,IAAIG,UAAU,GAAG;EACfC,IAAI,EAAET,iBAAiB,CAAC;IACtBU,OAAO,EAAET,WAAW;IACpBU,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,IAAI,EAAEZ,iBAAiB,CAAC;IACtBU,OAAO,EAAEJ,WAAW;IACpBK,YAAY,EAAE;EAChB,CAAC,CAAC;EACFE,QAAQ,EAAEb,iBAAiB,CAAC;IAC1BU,OAAO,EAAEH,eAAe;IACxBI,YAAY,EAAE;EAChB,CAAC;AACH,CAAC;AACD,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}