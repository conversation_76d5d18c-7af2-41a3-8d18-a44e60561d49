{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    standalone: {\n      one: 'manner wéi eng Sekonn',\n      other: 'manner wéi {{count}} Sekonnen'\n    },\n    withPreposition: {\n      one: 'manner wéi enger Sekonn',\n      other: 'manner wéi {{count}} Se<PERSON>nen'\n    }\n  },\n  xSeconds: {\n    standalone: {\n      one: 'eng Sekonn',\n      other: '{{count}} Sekonnen'\n    },\n    withPreposition: {\n      one: 'enger Sekonn',\n      other: '{{count}} Sekonnen'\n    }\n  },\n  halfAMinute: {\n    standalone: 'eng hallef Minutt',\n    withPreposition: 'enger hallwer Minutt'\n  },\n  lessThanXMinutes: {\n    standalone: {\n      one: 'manner wéi eng Minutt',\n      other: 'manner wéi {{count}} Minutten'\n    },\n    withPreposition: {\n      one: 'manner wéi enger Minutt',\n      other: 'manner wéi {{count}} Minutten'\n    }\n  },\n  xMinutes: {\n    standalone: {\n      one: 'eng Minutt',\n      other: '{{count}} Minutten'\n    },\n    withPreposition: {\n      one: 'enger Minutt',\n      other: '{{count}} Minutten'\n    }\n  },\n  aboutXHours: {\n    standalone: {\n      one: 'ongeféier eng Stonn',\n      other: 'ongeféier {{count}} Stonnen'\n    },\n    withPreposition: {\n      one: 'ongeféier enger Stonn',\n      other: 'ongeféier {{count}} Stonnen'\n    }\n  },\n  xHours: {\n    standalone: {\n      one: 'eng Stonn',\n      other: '{{count}} Stonnen'\n    },\n    withPreposition: {\n      one: 'enger Stonn',\n      other: '{{count}} Stonnen'\n    }\n  },\n  xDays: {\n    standalone: {\n      one: 'een Dag',\n      other: '{{count}} Deeg'\n    },\n    withPreposition: {\n      one: 'engem Dag',\n      other: '{{count}} Deeg'\n    }\n  },\n  aboutXWeeks: {\n    standalone: {\n      one: 'ongeféier eng Woch',\n      other: 'ongeféier {{count}} Wochen'\n    },\n    withPreposition: {\n      one: 'ongeféier enger Woche',\n      other: 'ongeféier {{count}} Wochen'\n    }\n  },\n  xWeeks: {\n    standalone: {\n      one: 'eng Woch',\n      other: '{{count}} Wochen'\n    },\n    withPreposition: {\n      one: 'enger Woch',\n      other: '{{count}} Wochen'\n    }\n  },\n  aboutXMonths: {\n    standalone: {\n      one: 'ongeféier ee Mount',\n      other: 'ongeféier {{count}} Méint'\n    },\n    withPreposition: {\n      one: 'ongeféier engem Mount',\n      other: 'ongeféier {{count}} Méint'\n    }\n  },\n  xMonths: {\n    standalone: {\n      one: 'ee Mount',\n      other: '{{count}} Méint'\n    },\n    withPreposition: {\n      one: 'engem Mount',\n      other: '{{count}} Méint'\n    }\n  },\n  aboutXYears: {\n    standalone: {\n      one: 'ongeféier ee Joer',\n      other: 'ongeféier {{count}} Joer'\n    },\n    withPreposition: {\n      one: 'ongeféier engem Joer',\n      other: 'ongeféier {{count}} Joer'\n    }\n  },\n  xYears: {\n    standalone: {\n      one: 'ee Joer',\n      other: '{{count}} Joer'\n    },\n    withPreposition: {\n      one: 'engem Joer',\n      other: '{{count}} Joer'\n    }\n  },\n  overXYears: {\n    standalone: {\n      one: 'méi wéi ee Joer',\n      other: 'méi wéi {{count}} Joer'\n    },\n    withPreposition: {\n      one: 'méi wéi engem Joer',\n      other: 'méi wéi {{count}} Joer'\n    }\n  },\n  almostXYears: {\n    standalone: {\n      one: 'bal ee Joer',\n      other: 'bal {{count}} Joer'\n    },\n    withPreposition: {\n      one: 'bal engem Joer',\n      other: 'bal {{count}} Joer'\n    }\n  }\n};\nvar EXCEPTION_CONSONANTS = ['d', 'h', 'n', 't', 'z'];\nvar VOWELS = ['a,', 'e', 'i', 'o', 'u'];\nvar DIGITS_SPOKEN_N_NEEDED = [0, 1, 2, 3, 8, 9];\nvar FIRST_TWO_DIGITS_SPOKEN_NO_N_NEEDED = [40, 50, 60, 70];\n\n// Eifeler Regel\nfunction isFinalNNeeded(nextWords) {\n  var firstLetter = nextWords.charAt(0).toLowerCase();\n  if (VOWELS.indexOf(firstLetter) != -1 || EXCEPTION_CONSONANTS.indexOf(firstLetter) != -1) {\n    return true;\n  }\n\n  // Numbers would need to converted into words for checking.\n  // Therefore, I have listed the digits that require a preceeding n with a few exceptions.\n  var firstWord = nextWords.split(' ')[0];\n  var number = parseInt(firstWord);\n  if (!isNaN(number) && DIGITS_SPOKEN_N_NEEDED.indexOf(number % 10) != -1 && FIRST_TWO_DIGITS_SPOKEN_NO_N_NEEDED.indexOf(parseInt(firstWord.substring(0, 2))) == -1) {\n    return true;\n  }\n\n  // Omit other checks as they are not expected here.\n  return false;\n}\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  var usageGroup = options !== null && options !== void 0 && options.addSuffix ? tokenValue.withPreposition : tokenValue.standalone;\n  if (typeof usageGroup === 'string') {\n    result = usageGroup;\n  } else if (count === 1) {\n    result = usageGroup.one;\n  } else {\n    result = usageGroup.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'a' + (isFinalNNeeded(result) ? 'n' : '') + ' ' + result;\n    } else {\n      return 'viru' + (isFinalNNeeded(result) ? 'n' : '') + ' ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "standalone", "one", "other", "withPreposition", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "EXCEPTION_CONSONANTS", "VOWELS", "DIGITS_SPOKEN_N_NEEDED", "FIRST_TWO_DIGITS_SPOKEN_NO_N_NEEDED", "isFinalNNeeded", "nextWords", "firstLetter", "char<PERSON>t", "toLowerCase", "indexOf", "firstWord", "split", "number", "parseInt", "isNaN", "substring", "formatDistance", "token", "count", "options", "result", "tokenValue", "usageGroup", "addSuffix", "replace", "String", "comparison"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/lb/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    standalone: {\n      one: 'manner wéi eng Sekonn',\n      other: 'manner wéi {{count}} Sekonnen'\n    },\n    withPreposition: {\n      one: 'manner wéi enger Sekonn',\n      other: 'manner wéi {{count}} Se<PERSON>nen'\n    }\n  },\n  xSeconds: {\n    standalone: {\n      one: 'eng Sekonn',\n      other: '{{count}} Sekonnen'\n    },\n    withPreposition: {\n      one: 'enger Sekonn',\n      other: '{{count}} Sekonnen'\n    }\n  },\n  halfAMinute: {\n    standalone: 'eng hallef Minutt',\n    withPreposition: 'enger hallwer Minutt'\n  },\n  lessThanXMinutes: {\n    standalone: {\n      one: 'manner wéi eng Minutt',\n      other: 'manner wéi {{count}} Minutten'\n    },\n    withPreposition: {\n      one: 'manner wéi enger Minutt',\n      other: 'manner wéi {{count}} Minutten'\n    }\n  },\n  xMinutes: {\n    standalone: {\n      one: 'eng Minutt',\n      other: '{{count}} Minutten'\n    },\n    withPreposition: {\n      one: 'enger Minutt',\n      other: '{{count}} Minutten'\n    }\n  },\n  aboutXHours: {\n    standalone: {\n      one: 'ongeféier eng Stonn',\n      other: 'ongeféier {{count}} Stonnen'\n    },\n    withPreposition: {\n      one: 'ongeféier enger Stonn',\n      other: 'ongeféier {{count}} Stonnen'\n    }\n  },\n  xHours: {\n    standalone: {\n      one: 'eng Stonn',\n      other: '{{count}} Stonnen'\n    },\n    withPreposition: {\n      one: 'enger Stonn',\n      other: '{{count}} Stonnen'\n    }\n  },\n  xDays: {\n    standalone: {\n      one: 'een Dag',\n      other: '{{count}} Deeg'\n    },\n    withPreposition: {\n      one: 'engem Dag',\n      other: '{{count}} Deeg'\n    }\n  },\n  aboutXWeeks: {\n    standalone: {\n      one: 'ongeféier eng Woch',\n      other: 'ongeféier {{count}} Wochen'\n    },\n    withPreposition: {\n      one: 'ongeféier enger Woche',\n      other: 'ongeféier {{count}} Wochen'\n    }\n  },\n  xWeeks: {\n    standalone: {\n      one: 'eng Woch',\n      other: '{{count}} Wochen'\n    },\n    withPreposition: {\n      one: 'enger Woch',\n      other: '{{count}} Wochen'\n    }\n  },\n  aboutXMonths: {\n    standalone: {\n      one: 'ongeféier ee Mount',\n      other: 'ongeféier {{count}} Méint'\n    },\n    withPreposition: {\n      one: 'ongeféier engem Mount',\n      other: 'ongeféier {{count}} Méint'\n    }\n  },\n  xMonths: {\n    standalone: {\n      one: 'ee Mount',\n      other: '{{count}} Méint'\n    },\n    withPreposition: {\n      one: 'engem Mount',\n      other: '{{count}} Méint'\n    }\n  },\n  aboutXYears: {\n    standalone: {\n      one: 'ongeféier ee Joer',\n      other: 'ongeféier {{count}} Joer'\n    },\n    withPreposition: {\n      one: 'ongeféier engem Joer',\n      other: 'ongeféier {{count}} Joer'\n    }\n  },\n  xYears: {\n    standalone: {\n      one: 'ee Joer',\n      other: '{{count}} Joer'\n    },\n    withPreposition: {\n      one: 'engem Joer',\n      other: '{{count}} Joer'\n    }\n  },\n  overXYears: {\n    standalone: {\n      one: 'méi wéi ee Joer',\n      other: 'méi wéi {{count}} Joer'\n    },\n    withPreposition: {\n      one: 'méi wéi engem Joer',\n      other: 'méi wéi {{count}} Joer'\n    }\n  },\n  almostXYears: {\n    standalone: {\n      one: 'bal ee Joer',\n      other: 'bal {{count}} Joer'\n    },\n    withPreposition: {\n      one: 'bal engem Joer',\n      other: 'bal {{count}} Joer'\n    }\n  }\n};\nvar EXCEPTION_CONSONANTS = ['d', 'h', 'n', 't', 'z'];\nvar VOWELS = ['a,', 'e', 'i', 'o', 'u'];\nvar DIGITS_SPOKEN_N_NEEDED = [0, 1, 2, 3, 8, 9];\nvar FIRST_TWO_DIGITS_SPOKEN_NO_N_NEEDED = [40, 50, 60, 70];\n\n// Eifeler Regel\nfunction isFinalNNeeded(nextWords) {\n  var firstLetter = nextWords.charAt(0).toLowerCase();\n  if (VOWELS.indexOf(firstLetter) != -1 || EXCEPTION_CONSONANTS.indexOf(firstLetter) != -1) {\n    return true;\n  }\n\n  // Numbers would need to converted into words for checking.\n  // Therefore, I have listed the digits that require a preceeding n with a few exceptions.\n  var firstWord = nextWords.split(' ')[0];\n  var number = parseInt(firstWord);\n  if (!isNaN(number) && DIGITS_SPOKEN_N_NEEDED.indexOf(number % 10) != -1 && FIRST_TWO_DIGITS_SPOKEN_NO_N_NEEDED.indexOf(parseInt(firstWord.substring(0, 2))) == -1) {\n    return true;\n  }\n\n  // Omit other checks as they are not expected here.\n  return false;\n}\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  var usageGroup = options !== null && options !== void 0 && options.addSuffix ? tokenValue.withPreposition : tokenValue.standalone;\n  if (typeof usageGroup === 'string') {\n    result = usageGroup;\n  } else if (count === 1) {\n    result = usageGroup.one;\n  } else {\n    result = usageGroup.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'a' + (isFinalNNeeded(result) ? 'n' : '') + ' ' + result;\n    } else {\n      return 'viru' + (isFinalNNeeded(result) ? 'n' : '') + ' ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,UAAU,EAAE;MACVC,GAAG,EAAE,uBAAuB;MAC5BC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,yBAAyB;MAC9BC,KAAK,EAAE;IACT;EACF,CAAC;EACDE,QAAQ,EAAE;IACRJ,UAAU,EAAE;MACVC,GAAG,EAAE,YAAY;MACjBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,cAAc;MACnBC,KAAK,EAAE;IACT;EACF,CAAC;EACDG,WAAW,EAAE;IACXL,UAAU,EAAE,mBAAmB;IAC/BG,eAAe,EAAE;EACnB,CAAC;EACDG,gBAAgB,EAAE;IAChBN,UAAU,EAAE;MACVC,GAAG,EAAE,uBAAuB;MAC5BC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,yBAAyB;MAC9BC,KAAK,EAAE;IACT;EACF,CAAC;EACDK,QAAQ,EAAE;IACRP,UAAU,EAAE;MACVC,GAAG,EAAE,YAAY;MACjBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,cAAc;MACnBC,KAAK,EAAE;IACT;EACF,CAAC;EACDM,WAAW,EAAE;IACXR,UAAU,EAAE;MACVC,GAAG,EAAE,qBAAqB;MAC1BC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,uBAAuB;MAC5BC,KAAK,EAAE;IACT;EACF,CAAC;EACDO,MAAM,EAAE;IACNT,UAAU,EAAE;MACVC,GAAG,EAAE,WAAW;MAChBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE;IACT;EACF,CAAC;EACDQ,KAAK,EAAE;IACLV,UAAU,EAAE;MACVC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,WAAW;MAChBC,KAAK,EAAE;IACT;EACF,CAAC;EACDS,WAAW,EAAE;IACXX,UAAU,EAAE;MACVC,GAAG,EAAE,oBAAoB;MACzBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,uBAAuB;MAC5BC,KAAK,EAAE;IACT;EACF,CAAC;EACDU,MAAM,EAAE;IACNZ,UAAU,EAAE;MACVC,GAAG,EAAE,UAAU;MACfC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,YAAY;MACjBC,KAAK,EAAE;IACT;EACF,CAAC;EACDW,YAAY,EAAE;IACZb,UAAU,EAAE;MACVC,GAAG,EAAE,oBAAoB;MACzBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,uBAAuB;MAC5BC,KAAK,EAAE;IACT;EACF,CAAC;EACDY,OAAO,EAAE;IACPd,UAAU,EAAE;MACVC,GAAG,EAAE,UAAU;MACfC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE;IACT;EACF,CAAC;EACDa,WAAW,EAAE;IACXf,UAAU,EAAE;MACVC,GAAG,EAAE,mBAAmB;MACxBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,sBAAsB;MAC3BC,KAAK,EAAE;IACT;EACF,CAAC;EACDc,MAAM,EAAE;IACNhB,UAAU,EAAE;MACVC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,YAAY;MACjBC,KAAK,EAAE;IACT;EACF,CAAC;EACDe,UAAU,EAAE;IACVjB,UAAU,EAAE;MACVC,GAAG,EAAE,iBAAiB;MACtBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,oBAAoB;MACzBC,KAAK,EAAE;IACT;EACF,CAAC;EACDgB,YAAY,EAAE;IACZlB,UAAU,EAAE;MACVC,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,gBAAgB;MACrBC,KAAK,EAAE;IACT;EACF;AACF,CAAC;AACD,IAAIiB,oBAAoB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACpD,IAAIC,MAAM,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACvC,IAAIC,sBAAsB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC/C,IAAIC,mCAAmC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;AAE1D;AACA,SAASC,cAAcA,CAACC,SAAS,EAAE;EACjC,IAAIC,WAAW,GAAGD,SAAS,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EACnD,IAAIP,MAAM,CAACQ,OAAO,CAACH,WAAW,CAAC,IAAI,CAAC,CAAC,IAAIN,oBAAoB,CAACS,OAAO,CAACH,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE;IACxF,OAAO,IAAI;EACb;;EAEA;EACA;EACA,IAAII,SAAS,GAAGL,SAAS,CAACM,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACvC,IAAIC,MAAM,GAAGC,QAAQ,CAACH,SAAS,CAAC;EAChC,IAAI,CAACI,KAAK,CAACF,MAAM,CAAC,IAAIV,sBAAsB,CAACO,OAAO,CAACG,MAAM,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,IAAIT,mCAAmC,CAACM,OAAO,CAACI,QAAQ,CAACH,SAAS,CAACK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;IACjK,OAAO,IAAI;EACb;;EAEA;EACA,OAAO,KAAK;AACd;AACA,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAG1C,oBAAoB,CAACsC,KAAK,CAAC;EAC5C,IAAIK,UAAU,GAAGH,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACI,SAAS,GAAGF,UAAU,CAACrC,eAAe,GAAGqC,UAAU,CAACxC,UAAU;EACjI,IAAI,OAAOyC,UAAU,KAAK,QAAQ,EAAE;IAClCF,MAAM,GAAGE,UAAU;EACrB,CAAC,MAAM,IAAIJ,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGE,UAAU,CAACxC,GAAG;EACzB,CAAC,MAAM;IACLsC,MAAM,GAAGE,UAAU,CAACvC,KAAK,CAACyC,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACP,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACI,SAAS,EAAE;IAC/D,IAAIJ,OAAO,CAACO,UAAU,IAAIP,OAAO,CAACO,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,GAAG,IAAItB,cAAc,CAACgB,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,GAAGA,MAAM;IACjE,CAAC,MAAM;MACL,OAAO,MAAM,IAAIhB,cAAc,CAACgB,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,GAAGA,MAAM;IACpE;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}