{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"'上個'eeee p\",\n  yesterday: \"'昨天' p\",\n  today: \"'今天' p\",\n  tomorrow: \"'明天' p\",\n  nextWeek: \"'下個'eeee p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/zh-TW/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: \"'上個'eeee p\",\n  yesterday: \"'昨天' p\",\n  today: \"'今天' p\",\n  tomorrow: \"'明天' p\",\n  nextWeek: \"'下個'eeee p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,YAAY;EACtBC,SAAS,EAAE,QAAQ;EACnBC,KAAK,EAAE,QAAQ;EACfC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAE,YAAY;EACtBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC9E,OAAOX,oBAAoB,CAACQ,KAAK,CAAC;AACpC,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}