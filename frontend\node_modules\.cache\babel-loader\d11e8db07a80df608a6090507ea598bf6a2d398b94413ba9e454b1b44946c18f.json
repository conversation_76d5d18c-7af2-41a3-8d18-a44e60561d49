{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"'el' eeee 'passat a la' LT\",\n  yesterday: \"'ahir a la' p\",\n  today: \"'avui a la' p\",\n  tomorrow: \"'demà a la' p\",\n  nextWeek: \"eeee 'a la' p\",\n  other: 'P'\n};\nvar formatRelativeLocalePlural = {\n  lastWeek: \"'el' eeee 'passat a les' p\",\n  yesterday: \"'ahir a les' p\",\n  today: \"'avui a les' p\",\n  tomorrow: \"'demà a les' p\",\n  nextWeek: \"eeee 'a les' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, _baseDate, _options) {\n  if (date.getUTCHours() !== 1) {\n    return formatRelativeLocalePlural[token];\n  }\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelativeLocalePlural", "formatRelative", "token", "date", "_baseDate", "_options", "getUTCHours"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/ca/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: \"'el' eeee 'passat a la' LT\",\n  yesterday: \"'ahir a la' p\",\n  today: \"'avui a la' p\",\n  tomorrow: \"'demà a la' p\",\n  nextWeek: \"eeee 'a la' p\",\n  other: 'P'\n};\nvar formatRelativeLocalePlural = {\n  lastWeek: \"'el' eeee 'passat a les' p\",\n  yesterday: \"'ahir a les' p\",\n  today: \"'avui a les' p\",\n  tomorrow: \"'demà a les' p\",\n  nextWeek: \"eeee 'a les' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, _baseDate, _options) {\n  if (date.getUTCHours() !== 1) {\n    return formatRelativeLocalePlural[token];\n  }\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,4BAA4B;EACtCC,SAAS,EAAE,eAAe;EAC1BC,KAAK,EAAE,eAAe;EACtBC,QAAQ,EAAE,eAAe;EACzBC,QAAQ,EAAE,eAAe;EACzBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,0BAA0B,GAAG;EAC/BN,QAAQ,EAAE,4BAA4B;EACtCC,SAAS,EAAE,gBAAgB;EAC3BC,KAAK,EAAE,gBAAgB;EACvBC,QAAQ,EAAE,gBAAgB;EAC1BC,QAAQ,EAAE,gBAAgB;EAC1BC,KAAK,EAAE;AACT,CAAC;AACD,IAAIE,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,IAAI,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC7E,IAAIF,IAAI,CAACG,WAAW,CAAC,CAAC,KAAK,CAAC,EAAE;IAC5B,OAAON,0BAA0B,CAACE,KAAK,CAAC;EAC1C;EACA,OAAOT,oBAAoB,CAACS,KAAK,CAAC;AACpC,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}