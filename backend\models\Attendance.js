const mongoose = require('mongoose');

const AttendanceSchema = new mongoose.Schema({
  student: {
    type: mongoose.Schema.ObjectId,
    ref: 'Student',
    required: [true, 'الطالب مطلوب']
  },
  date: {
    type: Date,
    required: [true, 'تاريخ الحضور مطلوب'],
    default: Date.now
  },
  timeIn: {
    type: Date,
    required: [true, 'وقت الدخول مطلوب']
  },
  timeOut: {
    type: Date
  },
  status: {
    type: String,
    required: [true, 'حالة الحضور مطلوبة'],
    enum: {
      values: ['present', 'absent', 'late', 'excused'],
      message: 'حالة الحضور يجب أن تكون حاضر، غائب، متأخر، أو معذور'
    },
    default: 'present'
  },
  classType: {
    type: String,
    required: [true, 'نوع الحصة مطلوب'],
    enum: {
      values: ['regular', 'private', 'group', 'competition', 'seminar'],
      message: 'نوع الحصة يجب أن يكون عادي، خاص، جماعي، منافسة، أو ندوة'
    },
    default: 'regular'
  },
  beltLevel: {
    type: String,
    required: [true, 'مستوى الحزام مطلوب'],
    enum: {
      values: [
        'white', 'yellow', 'orange', 'green', 
        'blue', 'brown', 'red', 'black-1st', 
        'black-2nd', 'black-3rd', 'black-4th', 
        'black-5th', 'black-6th', 'black-7th', 
        'black-8th', 'black-9th'
      ],
      message: 'مستوى الحزام غير صحيح'
    }
  },
  instructor: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: [true, 'المدرب مطلوب']
  },
  duration: {
    type: Number, // in minutes
    min: [0, 'مدة الحصة يجب أن تكون أكبر من أو تساوي صفر']
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [500, 'الملاحظات يجب أن تكون أقل من 500 حرف']
  },
  performance: {
    rating: {
      type: Number,
      min: [1, 'التقييم يجب أن يكون بين 1 و 5'],
      max: [5, 'التقييم يجب أن يكون بين 1 و 5']
    },
    comments: {
      type: String,
      trim: true,
      maxlength: [300, 'تعليقات الأداء يجب أن تكون أقل من 300 حرف']
    }
  },
  makeup: {
    isMakeup: {
      type: Boolean,
      default: false
    },
    originalDate: {
      type: Date
    },
    reason: {
      type: String,
      trim: true,
      maxlength: [200, 'سبب الحصة التعويضية يجب أن يكون أقل من 200 حرف']
    }
  },
  createdBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Create indexes for better performance
AttendanceSchema.index({ student: 1, date: -1 });
AttendanceSchema.index({ date: -1 });
AttendanceSchema.index({ status: 1 });
AttendanceSchema.index({ instructor: 1 });
AttendanceSchema.index({ classType: 1 });
AttendanceSchema.index({ beltLevel: 1 });

// Compound index for unique attendance per student per day
AttendanceSchema.index({ 
  student: 1, 
  date: 1,
  classType: 1 
}, { 
  unique: true,
  partialFilterExpression: { status: { $ne: 'absent' } }
});

// Virtual for status in Arabic
AttendanceSchema.virtual('statusArabic').get(function() {
  const statusMap = {
    'present': 'حاضر',
    'absent': 'غائب',
    'late': 'متأخر',
    'excused': 'معذور'
  };
  return statusMap[this.status] || this.status;
});

// Virtual for class type in Arabic
AttendanceSchema.virtual('classTypeArabic').get(function() {
  const typeMap = {
    'regular': 'عادي',
    'private': 'خاص',
    'group': 'جماعي',
    'competition': 'منافسة',
    'seminar': 'ندوة'
  };
  return typeMap[this.classType] || this.classType;
});

// Virtual for belt level in Arabic
AttendanceSchema.virtual('beltLevelArabic').get(function() {
  const beltMap = {
    'white': 'أبيض',
    'yellow': 'أصفر',
    'orange': 'برتقالي',
    'green': 'أخضر',
    'blue': 'أزرق',
    'brown': 'بني',
    'red': 'أحمر',
    'black-1st': 'أسود - الدان الأول',
    'black-2nd': 'أسود - الدان الثاني',
    'black-3rd': 'أسود - الدان الثالث',
    'black-4th': 'أسود - الدان الرابع',
    'black-5th': 'أسود - الدان الخامس',
    'black-6th': 'أسود - الدان السادس',
    'black-7th': 'أسود - الدان السابع',
    'black-8th': 'أسود - الدان الثامن',
    'black-9th': 'أسود - الدان التاسع'
  };
  return beltMap[this.beltLevel] || this.beltLevel;
});

// Virtual for calculated duration
AttendanceSchema.virtual('calculatedDuration').get(function() {
  if (this.timeIn && this.timeOut) {
    return Math.round((this.timeOut - this.timeIn) / (1000 * 60)); // in minutes
  }
  return this.duration || 0;
});

// Pre-save middleware to calculate duration and validate times
AttendanceSchema.pre('save', function(next) {
  // Calculate duration if timeIn and timeOut are provided
  if (this.timeIn && this.timeOut) {
    if (this.timeOut <= this.timeIn) {
      return next(new Error('وقت الخروج يجب أن يكون بعد وقت الدخول'));
    }
    this.duration = Math.round((this.timeOut - this.timeIn) / (1000 * 60));
  }
  
  // Set date to the date part of timeIn if not provided
  if (this.timeIn && !this.date) {
    this.date = new Date(this.timeIn.getFullYear(), this.timeIn.getMonth(), this.timeIn.getDate());
  }
  
  next();
});

// Static method to get attendance by date range
AttendanceSchema.statics.getByDateRange = function(startDate, endDate, filters = {}) {
  const query = {
    date: {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    },
    ...filters
  };
  
  return this.find(query)
    .populate('student', 'fullName studentId beltLevel')
    .populate('instructor', 'fullName')
    .sort({ date: -1, timeIn: -1 });
};

// Static method to get student attendance statistics
AttendanceSchema.statics.getStudentStats = async function(studentId, startDate, endDate) {
  const stats = await this.aggregate([
    {
      $match: {
        student: mongoose.Types.ObjectId(studentId),
        date: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      }
    },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalDuration: { $sum: '$duration' }
      }
    }
  ]);
  
  const result = {
    present: 0,
    absent: 0,
    late: 0,
    excused: 0,
    totalDuration: 0
  };
  
  stats.forEach(stat => {
    result[stat._id] = stat.count;
    result.totalDuration += stat.totalDuration || 0;
  });
  
  result.total = result.present + result.absent + result.late + result.excused;
  result.attendanceRate = result.total > 0 ? ((result.present + result.late) / result.total * 100).toFixed(2) : 0;
  
  return result;
};

// Static method to get daily attendance summary
AttendanceSchema.statics.getDailySummary = function(date) {
  const startOfDay = new Date(date);
  startOfDay.setHours(0, 0, 0, 0);
  
  const endOfDay = new Date(date);
  endOfDay.setHours(23, 59, 59, 999);
  
  return this.aggregate([
    {
      $match: {
        date: {
          $gte: startOfDay,
          $lte: endOfDay
        }
      }
    },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ]);
};

// Instance method to mark as present
AttendanceSchema.methods.markPresent = async function(timeIn, updatedBy) {
  this.status = 'present';
  this.timeIn = timeIn || new Date();
  this.updatedBy = updatedBy;
  await this.save();
};

// Instance method to mark as absent
AttendanceSchema.methods.markAbsent = async function(updatedBy) {
  this.status = 'absent';
  this.updatedBy = updatedBy;
  await this.save();
};

// Instance method to check out
AttendanceSchema.methods.checkOut = async function(timeOut, updatedBy) {
  this.timeOut = timeOut || new Date();
  this.updatedBy = updatedBy;
  await this.save();
};

module.exports = mongoose.model('Attendance', AttendanceSchema);
