{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { DATE_TIME_VALIDATION_PROP_NAMES, DATE_VALIDATION_PROP_NAMES, TIME_VALIDATION_PROP_NAMES } from './validation/extractValidationProps';\nconst SHARED_FIELD_INTERNAL_PROP_NAMES = ['value', 'defaultValue', 'referenceDate', 'format', 'formatDensity', 'onChange', 'timezone', 'readOnly', 'onError', 'shouldRespectLeadingZeros', 'selectedSections', 'onSelectedSectionsChange', 'unstableFieldRef'];\nexport const splitFieldInternalAndForwardedProps = (props, valueType) => {\n  const forwardedProps = _extends({}, props);\n  const internalProps = {};\n  const extractProp = propName => {\n    if (forwardedProps.hasOwnProperty(propName)) {\n      // @ts-ignore\n      internalProps[propName] = forwardedProps[propName];\n      delete forwardedProps[propName];\n    }\n  };\n  SHARED_FIELD_INTERNAL_PROP_NAMES.forEach(extractProp);\n  if (valueType === 'date') {\n    DATE_VALIDATION_PROP_NAMES.forEach(extractProp);\n  } else if (valueType === 'time') {\n    TIME_VALIDATION_PROP_NAMES.forEach(extractProp);\n  } else if (valueType === 'date-time') {\n    DATE_VALIDATION_PROP_NAMES.forEach(extractProp);\n    TIME_VALIDATION_PROP_NAMES.forEach(extractProp);\n    DATE_TIME_VALIDATION_PROP_NAMES.forEach(extractProp);\n  }\n  return {\n    forwardedProps,\n    internalProps\n  };\n};", "map": {"version": 3, "names": ["_extends", "DATE_TIME_VALIDATION_PROP_NAMES", "DATE_VALIDATION_PROP_NAMES", "TIME_VALIDATION_PROP_NAMES", "SHARED_FIELD_INTERNAL_PROP_NAMES", "splitFieldInternalAndForwardedProps", "props", "valueType", "forwardedProps", "internalProps", "extractProp", "propName", "hasOwnProperty", "for<PERSON>ach"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/x-date-pickers/internals/utils/fields.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { DATE_TIME_VALIDATION_PROP_NAMES, DATE_VALIDATION_PROP_NAMES, TIME_VALIDATION_PROP_NAMES } from './validation/extractValidationProps';\nconst SHARED_FIELD_INTERNAL_PROP_NAMES = ['value', 'defaultValue', 'referenceDate', 'format', 'formatDensity', 'onChange', 'timezone', 'readOnly', 'onError', 'shouldRespectLeadingZeros', 'selectedSections', 'onSelectedSectionsChange', 'unstableFieldRef'];\nexport const splitFieldInternalAndForwardedProps = (props, valueType) => {\n  const forwardedProps = _extends({}, props);\n  const internalProps = {};\n  const extractProp = propName => {\n    if (forwardedProps.hasOwnProperty(propName)) {\n      // @ts-ignore\n      internalProps[propName] = forwardedProps[propName];\n      delete forwardedProps[propName];\n    }\n  };\n  SHARED_FIELD_INTERNAL_PROP_NAMES.forEach(extractProp);\n  if (valueType === 'date') {\n    DATE_VALIDATION_PROP_NAMES.forEach(extractProp);\n  } else if (valueType === 'time') {\n    TIME_VALIDATION_PROP_NAMES.forEach(extractProp);\n  } else if (valueType === 'date-time') {\n    DATE_VALIDATION_PROP_NAMES.forEach(extractProp);\n    TIME_VALIDATION_PROP_NAMES.forEach(extractProp);\n    DATE_TIME_VALIDATION_PROP_NAMES.forEach(extractProp);\n  }\n  return {\n    forwardedProps,\n    internalProps\n  };\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,+BAA+B,EAAEC,0BAA0B,EAAEC,0BAA0B,QAAQ,qCAAqC;AAC7I,MAAMC,gCAAgC,GAAG,CAAC,OAAO,EAAE,cAAc,EAAE,eAAe,EAAE,QAAQ,EAAE,eAAe,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,2BAA2B,EAAE,kBAAkB,EAAE,0BAA0B,EAAE,kBAAkB,CAAC;AAC9P,OAAO,MAAMC,mCAAmC,GAAGA,CAACC,KAAK,EAAEC,SAAS,KAAK;EACvE,MAAMC,cAAc,GAAGR,QAAQ,CAAC,CAAC,CAAC,EAAEM,KAAK,CAAC;EAC1C,MAAMG,aAAa,GAAG,CAAC,CAAC;EACxB,MAAMC,WAAW,GAAGC,QAAQ,IAAI;IAC9B,IAAIH,cAAc,CAACI,cAAc,CAACD,QAAQ,CAAC,EAAE;MAC3C;MACAF,aAAa,CAACE,QAAQ,CAAC,GAAGH,cAAc,CAACG,QAAQ,CAAC;MAClD,OAAOH,cAAc,CAACG,QAAQ,CAAC;IACjC;EACF,CAAC;EACDP,gCAAgC,CAACS,OAAO,CAACH,WAAW,CAAC;EACrD,IAAIH,SAAS,KAAK,MAAM,EAAE;IACxBL,0BAA0B,CAACW,OAAO,CAACH,WAAW,CAAC;EACjD,CAAC,MAAM,IAAIH,SAAS,KAAK,MAAM,EAAE;IAC/BJ,0BAA0B,CAACU,OAAO,CAACH,WAAW,CAAC;EACjD,CAAC,MAAM,IAAIH,SAAS,KAAK,WAAW,EAAE;IACpCL,0BAA0B,CAACW,OAAO,CAACH,WAAW,CAAC;IAC/CP,0BAA0B,CAACU,OAAO,CAACH,WAAW,CAAC;IAC/CT,+BAA+B,CAACY,OAAO,CAACH,WAAW,CAAC;EACtD;EACA,OAAO;IACLF,cAAc;IACdC;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}