{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name areIntervalsOverlapping\n * @category Interval Helpers\n * @summary Is the given time interval overlapping with another time interval?\n *\n * @description\n * Is the given time interval overlapping with another time interval? Adjacent intervals do not count as overlapping.\n *\n * @param {Interval} intervalLeft - the first interval to compare. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @param {Interval} intervalRight - the second interval to compare. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @param {Object} [options] - the object with options\n * @param {Boolean} [options.inclusive=false] - whether the comparison is inclusive or not\n * @returns {<PERSON><PERSON><PERSON>} whether the time intervals are overlapping\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // For overlapping time intervals:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 17), end: new Date(2014, 0, 21) }\n * )\n * //=> true\n *\n * @example\n * // For non-overlapping time intervals:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 21), end: new Date(2014, 0, 22) }\n * )\n * //=> false\n *\n * @example\n * // For adjacent time intervals:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 20), end: new Date(2014, 0, 30) }\n * )\n * //=> false\n *\n * @example\n * // Using the inclusive option:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 20), end: new Date(2014, 0, 24) }\n * )\n * //=> false\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 20), end: new Date(2014, 0, 24) },\n *   { inclusive: true }\n * )\n * //=> true\n */\nexport default function areIntervalsOverlapping(intervalLeft, intervalRight, options) {\n  requiredArgs(2, arguments);\n  var leftStartTime = toDate(intervalLeft === null || intervalLeft === void 0 ? void 0 : intervalLeft.start).getTime();\n  var leftEndTime = toDate(intervalLeft === null || intervalLeft === void 0 ? void 0 : intervalLeft.end).getTime();\n  var rightStartTime = toDate(intervalRight === null || intervalRight === void 0 ? void 0 : intervalRight.start).getTime();\n  var rightEndTime = toDate(intervalRight === null || intervalRight === void 0 ? void 0 : intervalRight.end).getTime();\n\n  // Throw an exception if start date is after end date or if any date is `Invalid Date`\n  if (!(leftStartTime <= leftEndTime && rightStartTime <= rightEndTime)) {\n    throw new RangeError('Invalid interval');\n  }\n  if (options !== null && options !== void 0 && options.inclusive) {\n    return leftStartTime <= rightEndTime && rightStartTime <= leftEndTime;\n  }\n  return leftStartTime < rightEndTime && rightStartTime < leftEndTime;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "areIntervalsOverlapping", "intervalLeft", "intervalRight", "options", "arguments", "leftStartTime", "start", "getTime", "leftEndTime", "end", "rightStartTime", "rightEndTime", "RangeError", "inclusive"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/areIntervalsOverlapping/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name areIntervalsOverlapping\n * @category Interval Helpers\n * @summary Is the given time interval overlapping with another time interval?\n *\n * @description\n * Is the given time interval overlapping with another time interval? Adjacent intervals do not count as overlapping.\n *\n * @param {Interval} intervalLeft - the first interval to compare. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @param {Interval} intervalRight - the second interval to compare. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @param {Object} [options] - the object with options\n * @param {Boolean} [options.inclusive=false] - whether the comparison is inclusive or not\n * @returns {<PERSON><PERSON><PERSON>} whether the time intervals are overlapping\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // For overlapping time intervals:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 17), end: new Date(2014, 0, 21) }\n * )\n * //=> true\n *\n * @example\n * // For non-overlapping time intervals:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 21), end: new Date(2014, 0, 22) }\n * )\n * //=> false\n *\n * @example\n * // For adjacent time intervals:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 20), end: new Date(2014, 0, 30) }\n * )\n * //=> false\n *\n * @example\n * // Using the inclusive option:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 20), end: new Date(2014, 0, 24) }\n * )\n * //=> false\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 20), end: new Date(2014, 0, 24) },\n *   { inclusive: true }\n * )\n * //=> true\n */\nexport default function areIntervalsOverlapping(intervalLeft, intervalRight, options) {\n  requiredArgs(2, arguments);\n  var leftStartTime = toDate(intervalLeft === null || intervalLeft === void 0 ? void 0 : intervalLeft.start).getTime();\n  var leftEndTime = toDate(intervalLeft === null || intervalLeft === void 0 ? void 0 : intervalLeft.end).getTime();\n  var rightStartTime = toDate(intervalRight === null || intervalRight === void 0 ? void 0 : intervalRight.start).getTime();\n  var rightEndTime = toDate(intervalRight === null || intervalRight === void 0 ? void 0 : intervalRight.end).getTime();\n\n  // Throw an exception if start date is after end date or if any date is `Invalid Date`\n  if (!(leftStartTime <= leftEndTime && rightStartTime <= rightEndTime)) {\n    throw new RangeError('Invalid interval');\n  }\n  if (options !== null && options !== void 0 && options.inclusive) {\n    return leftStartTime <= rightEndTime && rightStartTime <= leftEndTime;\n  }\n  return leftStartTime < rightEndTime && rightStartTime < leftEndTime;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,uBAAuBA,CAACC,YAAY,EAAEC,aAAa,EAAEC,OAAO,EAAE;EACpFJ,YAAY,CAAC,CAAC,EAAEK,SAAS,CAAC;EAC1B,IAAIC,aAAa,GAAGP,MAAM,CAACG,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACK,KAAK,CAAC,CAACC,OAAO,CAAC,CAAC;EACpH,IAAIC,WAAW,GAAGV,MAAM,CAACG,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACQ,GAAG,CAAC,CAACF,OAAO,CAAC,CAAC;EAChH,IAAIG,cAAc,GAAGZ,MAAM,CAACI,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACI,KAAK,CAAC,CAACC,OAAO,CAAC,CAAC;EACxH,IAAII,YAAY,GAAGb,MAAM,CAACI,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACO,GAAG,CAAC,CAACF,OAAO,CAAC,CAAC;;EAEpH;EACA,IAAI,EAAEF,aAAa,IAAIG,WAAW,IAAIE,cAAc,IAAIC,YAAY,CAAC,EAAE;IACrE,MAAM,IAAIC,UAAU,CAAC,kBAAkB,CAAC;EAC1C;EACA,IAAIT,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACU,SAAS,EAAE;IAC/D,OAAOR,aAAa,IAAIM,YAAY,IAAID,cAAc,IAAIF,WAAW;EACvE;EACA,OAAOH,aAAa,GAAGM,YAAY,IAAID,cAAc,GAAGF,WAAW;AACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}