{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isFuture\n * @category Common Helpers\n * @summary Is the given date in the future?\n * @pure false\n *\n * @description\n * Is the given date in the future?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is in the future\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If today is 6 October 2014, is 31 December 2014 in the future?\n * const result = isFuture(new Date(2014, 11, 31))\n * //=> true\n */\nexport default function isFuture(dirtyDate) {\n  requiredArgs(1, arguments);\n  return toDate(dirtyDate).getTime() > Date.now();\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "isFuture", "dirtyDate", "arguments", "getTime", "Date", "now"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/isFuture/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isFuture\n * @category Common Helpers\n * @summary Is the given date in the future?\n * @pure false\n *\n * @description\n * Is the given date in the future?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is in the future\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If today is 6 October 2014, is 31 December 2014 in the future?\n * const result = isFuture(new Date(2014, 11, 31))\n * //=> true\n */\nexport default function isFuture(dirtyDate) {\n  requiredArgs(1, arguments);\n  return toDate(dirtyDate).getTime() > Date.now();\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,QAAQA,CAACC,SAAS,EAAE;EAC1CF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,OAAOJ,MAAM,CAACG,SAAS,CAAC,CAACE,OAAO,CAAC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}