{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'mindre enn eitt sekund',\n    other: 'mindre enn {{count}} sekund'\n  },\n  xSeconds: {\n    one: 'eitt sekund',\n    other: '{{count}} sekund'\n  },\n  halfAMinute: 'eit halvt minutt',\n  lessThanXMinutes: {\n    one: 'mindre enn eitt minutt',\n    other: 'mindre enn {{count}} minutt'\n  },\n  xMinutes: {\n    one: 'eitt minutt',\n    other: '{{count}} minutt'\n  },\n  aboutXHours: {\n    one: 'omtrent ein time',\n    other: 'omtrent {{count}} timar'\n  },\n  xHours: {\n    one: 'ein time',\n    other: '{{count}} timar'\n  },\n  xDays: {\n    one: 'ein dag',\n    other: '{{count}} dagar'\n  },\n  aboutXWeeks: {\n    one: 'omtrent ei veke',\n    other: 'omtrent {{count}} veker'\n  },\n  xWeeks: {\n    one: 'ei veke',\n    other: '{{count}} veker'\n  },\n  aboutXMonths: {\n    one: 'omtrent ein månad',\n    other: 'omtrent {{count}} månader'\n  },\n  xMonths: {\n    one: 'ein månad',\n    other: '{{count}} månader'\n  },\n  aboutXYears: {\n    one: 'omtrent eitt år',\n    other: 'omtrent {{count}} år'\n  },\n  xYears: {\n    one: 'eitt år',\n    other: '{{count}} år'\n  },\n  overXYears: {\n    one: 'over eitt år',\n    other: 'over {{count}} år'\n  },\n  almostXYears: {\n    one: 'nesten eitt år',\n    other: 'nesten {{count}} år'\n  }\n};\nvar wordMapping = ['null', 'ein', 'to', 'tre', 'fire', 'fem', 'seks', 'sju', 'åtte', 'ni', 'ti', 'elleve', 'tolv'];\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    if (options && options.onlyNumeric) {\n      result = tokenValue.other.replace('{{count}}', String(count));\n    } else {\n      result = tokenValue.other.replace('{{count}}', count < 13 ? wordMapping[count] : String(count));\n    }\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'om ' + result;\n    } else {\n      return result + ' sidan';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "wordMapping", "formatDistance", "token", "count", "options", "result", "tokenValue", "onlyNumeric", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/nn/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'mindre enn eitt sekund',\n    other: 'mindre enn {{count}} sekund'\n  },\n  xSeconds: {\n    one: 'eitt sekund',\n    other: '{{count}} sekund'\n  },\n  halfAMinute: 'eit halvt minutt',\n  lessThanXMinutes: {\n    one: 'mindre enn eitt minutt',\n    other: 'mindre enn {{count}} minutt'\n  },\n  xMinutes: {\n    one: 'eitt minutt',\n    other: '{{count}} minutt'\n  },\n  aboutXHours: {\n    one: 'omtrent ein time',\n    other: 'omtrent {{count}} timar'\n  },\n  xHours: {\n    one: 'ein time',\n    other: '{{count}} timar'\n  },\n  xDays: {\n    one: 'ein dag',\n    other: '{{count}} dagar'\n  },\n  aboutXWeeks: {\n    one: 'omtrent ei veke',\n    other: 'omtrent {{count}} veker'\n  },\n  xWeeks: {\n    one: 'ei veke',\n    other: '{{count}} veker'\n  },\n  aboutXMonths: {\n    one: 'omtrent ein månad',\n    other: 'omtrent {{count}} månader'\n  },\n  xMonths: {\n    one: 'ein månad',\n    other: '{{count}} månader'\n  },\n  aboutXYears: {\n    one: 'omtrent eitt år',\n    other: 'omtrent {{count}} år'\n  },\n  xYears: {\n    one: 'eitt år',\n    other: '{{count}} år'\n  },\n  overXYears: {\n    one: 'over eitt år',\n    other: 'over {{count}} år'\n  },\n  almostXYears: {\n    one: 'nesten eitt år',\n    other: 'nesten {{count}} år'\n  }\n};\nvar wordMapping = ['null', 'ein', 'to', 'tre', 'fire', 'fem', 'seks', 'sju', 'åtte', 'ni', 'ti', 'elleve', 'tolv'];\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    if (options && options.onlyNumeric) {\n      result = tokenValue.other.replace('{{count}}', String(count));\n    } else {\n      result = tokenValue.other.replace('{{count}}', count < 13 ? wordMapping[count] : String(count));\n    }\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'om ' + result;\n    } else {\n      return result + ' sidan';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,wBAAwB;IAC7BC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,kBAAkB;EAC/BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,wBAAwB;IAC7BC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,WAAW,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,CAAC;AAClH,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGzB,oBAAoB,CAACqB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACvB,GAAG;EACzB,CAAC,MAAM;IACL,IAAIqB,OAAO,IAAIA,OAAO,CAACG,WAAW,EAAE;MAClCF,MAAM,GAAGC,UAAU,CAACtB,KAAK,CAACwB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACN,KAAK,CAAC,CAAC;IAC/D,CAAC,MAAM;MACLE,MAAM,GAAGC,UAAU,CAACtB,KAAK,CAACwB,OAAO,CAAC,WAAW,EAAEL,KAAK,GAAG,EAAE,GAAGH,WAAW,CAACG,KAAK,CAAC,GAAGM,MAAM,CAACN,KAAK,CAAC,CAAC;IACjG;EACF;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACM,SAAS,EAAE;IAC/D,IAAIN,OAAO,CAACO,UAAU,IAAIP,OAAO,CAACO,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAGN,MAAM;IACvB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,QAAQ;IAC1B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}