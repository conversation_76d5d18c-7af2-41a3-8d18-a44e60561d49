{"ast": null, "code": "export { PickersArrowSwitcher } from './PickersArrowSwitcher';", "map": {"version": 3, "names": ["PickersArrowSwitcher"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/x-date-pickers/internals/components/PickersArrowSwitcher/index.js"], "sourcesContent": ["export { PickersArrowSwitcher } from './PickersArrowSwitcher';"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}