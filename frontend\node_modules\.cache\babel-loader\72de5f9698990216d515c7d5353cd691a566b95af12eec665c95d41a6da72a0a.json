{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - MOE Student S4M\\\\Desktop\\\\CRM Project\\\\frontend\\\\src\\\\pages\\\\Auth\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Paper, TextField, Button, Typography, Alert, Avatar, Container, InputAdornment, IconButton, Fade, CircularProgress } from '@mui/material';\nimport { SportsMartialArts, Visibility, VisibilityOff, Person, Lock } from '@mui/icons-material';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  var _location$state, _location$state$from;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    login,\n    error,\n    loading,\n    clearError\n  } = useAuth();\n  const [formData, setFormData] = useState({\n    username: '',\n    password: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [formErrors, setFormErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [loginSuccess, setLoginSuccess] = useState(false);\n\n  // Get redirect path from location state\n  const from = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : (_location$state$from = _location$state.from) === null || _location$state$from === void 0 ? void 0 : _location$state$from.pathname) || '/dashboard';\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Clear field error when user starts typing\n    if (formErrors[name]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n\n    // Clear auth error\n    if (error) {\n      clearError();\n    }\n  };\n  const handleTogglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n  const validateForm = () => {\n    const errors = {};\n    if (!formData.username.trim()) {\n      errors.username = 'اسم المستخدم مطلوب';\n    }\n    if (!formData.password) {\n      errors.password = 'كلمة المرور مطلوبة';\n    }\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    e.stopPropagation();\n    console.log('Form submitted with data:', formData);\n    if (!validateForm()) {\n      console.log('Form validation failed');\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      console.log('Attempting login...');\n      const result = await login(formData.username, formData.password);\n      console.log('Login result:', result);\n      if (result.success) {\n        console.log('Login successful, setting success state');\n        setLoginSuccess(true);\n        // Small delay to show success state before redirect\n        setTimeout(() => {\n          console.log('Navigating to:', from);\n          navigate(from, {\n            replace: true\n          });\n        }, 1000);\n      } else {\n        console.log('Login failed:', result.error);\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Auto-fill demo credentials\n  const fillDemoCredentials = type => {\n    if (type === 'admin') {\n      setFormData({\n        username: 'admin',\n        password: '123456'\n      });\n    } else if (type === 'coach') {\n      setFormData({\n        username: 'coach',\n        password: '123456'\n      });\n    }\n    clearError();\n    setFormErrors({});\n  };\n\n  // Clear errors when component mounts\n  useEffect(() => {\n    clearError();\n  }, [clearError]);\n  return /*#__PURE__*/_jsxDEV(Container, {\n    component: \"main\",\n    maxWidth: \"sm\",\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        minHeight: '100vh',\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        py: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        elevation: 8,\n        sx: {\n          p: 4,\n          width: '100%',\n          maxWidth: 400,\n          borderRadius: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            mb: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            sx: {\n              m: 1,\n              bgcolor: 'primary.main',\n              width: 64,\n              height: 64\n            },\n            children: /*#__PURE__*/_jsxDEV(SportsMartialArts, {\n              sx: {\n                fontSize: 32\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"h1\",\n            variant: \"h4\",\n            fontWeight: \"bold\",\n            gutterBottom: true,\n            children: \"\\u0623\\u0643\\u0627\\u062F\\u064A\\u0645\\u064A\\u0629 \\u0627\\u0644\\u062A\\u0627\\u064A\\u0643\\u0648\\u0646\\u062F\\u0648\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            textAlign: \"center\",\n            children: \"\\u0646\\u0638\\u0627\\u0645 \\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0623\\u0643\\u0627\\u062F\\u064A\\u0645\\u064A\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), loginSuccess && /*#__PURE__*/_jsxDEV(Fade, {\n          in: loginSuccess,\n          children: /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"success\",\n            sx: {\n              mb: 3\n            },\n            children: \"\\u062A\\u0645 \\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644 \\u0628\\u0646\\u062C\\u0627\\u062D! \\u062C\\u0627\\u0631\\u064A \\u0627\\u0644\\u062A\\u0648\\u062C\\u064A\\u0647...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 13\n        }, this), error && !loginSuccess && /*#__PURE__*/_jsxDEV(Fade, {\n          in: !!error,\n          children: /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"error\",\n            sx: {\n              mb: 3\n            },\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            margin: \"normal\",\n            required: true,\n            fullWidth: true,\n            id: \"username\",\n            label: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\",\n            name: \"username\",\n            autoComplete: \"username\",\n            autoFocus: true,\n            value: formData.username,\n            onChange: handleChange,\n            error: !!formErrors.username,\n            helperText: formErrors.username,\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"start\",\n                children: /*#__PURE__*/_jsxDEV(Person, {\n                  color: \"action\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            margin: \"normal\",\n            required: true,\n            fullWidth: true,\n            name: \"password\",\n            label: \"\\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\",\n            type: showPassword ? 'text' : 'password',\n            id: \"password\",\n            autoComplete: \"current-password\",\n            value: formData.password,\n            onChange: handleChange,\n            error: !!formErrors.password,\n            helperText: formErrors.password,\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"start\",\n                children: /*#__PURE__*/_jsxDEV(Lock, {\n                  color: \"action\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 19\n              }, this),\n              endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"end\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  \"aria-label\": \"toggle password visibility\",\n                  onClick: handleTogglePasswordVisibility,\n                  edge: \"end\",\n                  children: showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 39\n                  }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 59\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 19\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"button\",\n            fullWidth: true,\n            variant: \"contained\",\n            disabled: loading || isSubmitting || loginSuccess,\n            onClick: handleSubmit,\n            sx: {\n              mt: 3,\n              mb: 2,\n              py: 1.5,\n              fontSize: '1.1rem',\n              fontWeight: 600,\n              position: 'relative'\n            },\n            children: [isSubmitting && /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20,\n              sx: {\n                position: 'absolute',\n                left: '50%',\n                top: '50%',\n                marginLeft: '-10px',\n                marginTop: '-10px',\n                color: 'inherit'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this), loginSuccess ? 'تم بنجاح! جاري التوجيه...' : isSubmitting ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: 'grey.50',\n            borderRadius: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            fontWeight: \"bold\",\n            gutterBottom: true,\n            children: \"\\u062D\\u0633\\u0627\\u0628\\u0627\\u062A \\u062A\\u062C\\u0631\\u064A\\u0628\\u064A\\u0629:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 1,\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              variant: \"outlined\",\n              onClick: () => fillDemoCredentials('admin'),\n              disabled: isSubmitting || loginSuccess,\n              sx: {\n                fontSize: '0.75rem'\n              },\n              children: \"\\u0627\\u0644\\u0645\\u062F\\u064A\\u0631: admin / 123456\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              variant: \"outlined\",\n              onClick: () => fillDemoCredentials('coach'),\n              disabled: isSubmitting || loginSuccess,\n              sx: {\n                fontSize: '0.75rem'\n              },\n              children: \"\\u0627\\u0644\\u0645\\u062F\\u0631\\u0628: coach / 123456\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"\\u0627\\u0646\\u0642\\u0631 \\u0639\\u0644\\u0649 \\u0623\\u064A \\u062D\\u0633\\u0627\\u0628 \\u0644\\u0645\\u0644\\u0621 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u062A\\u0644\\u0642\\u0627\\u0626\\u064A\\u0627\\u064B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 136,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"cK8u3frctXIzrWqGf3kySxrhpl0=\", false, function () {\n  return [useNavigate, useLocation, useAuth];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Paper", "TextField", "<PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Avatar", "Container", "InputAdornment", "IconButton", "Fade", "CircularProgress", "SportsMartialArts", "Visibility", "VisibilityOff", "Person", "Lock", "useAuth", "useNavigate", "useLocation", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "_location$state", "_location$state$from", "navigate", "location", "login", "error", "loading", "clearError", "formData", "setFormData", "username", "password", "showPassword", "setShowPassword", "formErrors", "setFormErrors", "isSubmitting", "setIsSubmitting", "loginSuccess", "setLoginSuccess", "from", "state", "pathname", "handleChange", "e", "name", "value", "target", "prev", "handleTogglePasswordVisibility", "validateForm", "errors", "trim", "Object", "keys", "length", "handleSubmit", "preventDefault", "stopPropagation", "console", "log", "result", "success", "setTimeout", "replace", "fillDemoCredentials", "type", "component", "max<PERSON><PERSON><PERSON>", "children", "sx", "minHeight", "display", "flexDirection", "alignItems", "justifyContent", "py", "elevation", "p", "width", "borderRadius", "mb", "m", "bgcolor", "height", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "gutterBottom", "color", "textAlign", "in", "severity", "margin", "required", "fullWidth", "id", "label", "autoComplete", "autoFocus", "onChange", "helperText", "InputProps", "startAdornment", "position", "endAdornment", "onClick", "edge", "disabled", "mt", "size", "left", "top", "marginLeft", "marginTop", "gap", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/src/pages/Auth/Login.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Paper,\n  TextField,\n  Button,\n  Typography,\n  Alert,\n  Avatar,\n  Container,\n  InputAdornment,\n  IconButton,\n  Fade,\n  CircularProgress,\n} from '@mui/material';\nimport {\n  SportsMartialArts,\n  Visibility,\n  VisibilityOff,\n  Person,\n  Lock,\n} from '@mui/icons-material';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useNavigate, useLocation } from 'react-router-dom';\n\nconst Login = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { login, error, loading, clearError } = useAuth();\n\n  const [formData, setFormData] = useState({\n    username: '',\n    password: '',\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [formErrors, setFormErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [loginSuccess, setLoginSuccess] = useState(false);\n\n  // Get redirect path from location state\n  const from = location.state?.from?.pathname || '/dashboard';\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value,\n    }));\n    \n    // Clear field error when user starts typing\n    if (formErrors[name]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: '',\n      }));\n    }\n    \n    // Clear auth error\n    if (error) {\n      clearError();\n    }\n  };\n\n  const handleTogglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n\n  const validateForm = () => {\n    const errors = {};\n    \n    if (!formData.username.trim()) {\n      errors.username = 'اسم المستخدم مطلوب';\n    }\n    \n    if (!formData.password) {\n      errors.password = 'كلمة المرور مطلوبة';\n    }\n    \n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    e.stopPropagation();\n\n    console.log('Form submitted with data:', formData);\n\n    if (!validateForm()) {\n      console.log('Form validation failed');\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    try {\n      console.log('Attempting login...');\n      const result = await login(formData.username, formData.password);\n      console.log('Login result:', result);\n\n      if (result.success) {\n        console.log('Login successful, setting success state');\n        setLoginSuccess(true);\n        // Small delay to show success state before redirect\n        setTimeout(() => {\n          console.log('Navigating to:', from);\n          navigate(from, { replace: true });\n        }, 1000);\n      } else {\n        console.log('Login failed:', result.error);\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Auto-fill demo credentials\n  const fillDemoCredentials = (type) => {\n    if (type === 'admin') {\n      setFormData({ username: 'admin', password: '123456' });\n    } else if (type === 'coach') {\n      setFormData({ username: 'coach', password: '123456' });\n    }\n    clearError();\n    setFormErrors({});\n  };\n\n  // Clear errors when component mounts\n  useEffect(() => {\n    clearError();\n  }, [clearError]);\n\n  return (\n    <Container component=\"main\" maxWidth=\"sm\">\n      <Box\n        sx={{\n          minHeight: '100vh',\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          py: 4,\n        }}\n      >\n        <Paper\n          elevation={8}\n          sx={{\n            p: 4,\n            width: '100%',\n            maxWidth: 400,\n            borderRadius: 3,\n          }}\n        >\n          {/* Logo and Title */}\n          <Box\n            sx={{\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              mb: 4,\n            }}\n          >\n            <Avatar\n              sx={{\n                m: 1,\n                bgcolor: 'primary.main',\n                width: 64,\n                height: 64,\n              }}\n            >\n              <SportsMartialArts sx={{ fontSize: 32 }} />\n            </Avatar>\n            <Typography component=\"h1\" variant=\"h4\" fontWeight=\"bold\" gutterBottom>\n              أكاديمية التايكوندو\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\" textAlign=\"center\">\n              نظام إدارة الأكاديمية\n            </Typography>\n          </Box>\n\n          {/* Success Alert */}\n          {loginSuccess && (\n            <Fade in={loginSuccess}>\n              <Alert severity=\"success\" sx={{ mb: 3 }}>\n                تم تسجيل الدخول بنجاح! جاري التوجيه...\n              </Alert>\n            </Fade>\n          )}\n\n          {/* Error Alert */}\n          {error && !loginSuccess && (\n            <Fade in={!!error}>\n              <Alert severity=\"error\" sx={{ mb: 3 }}>\n                {error}\n              </Alert>\n            </Fade>\n          )}\n\n          {/* Login Form */}\n          <Box sx={{ width: '100%' }}>\n            <TextField\n              margin=\"normal\"\n              required\n              fullWidth\n              id=\"username\"\n              label=\"اسم المستخدم\"\n              name=\"username\"\n              autoComplete=\"username\"\n              autoFocus\n              value={formData.username}\n              onChange={handleChange}\n              error={!!formErrors.username}\n              helperText={formErrors.username}\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <Person color=\"action\" />\n                  </InputAdornment>\n                ),\n              }}\n            />\n            \n            <TextField\n              margin=\"normal\"\n              required\n              fullWidth\n              name=\"password\"\n              label=\"كلمة المرور\"\n              type={showPassword ? 'text' : 'password'}\n              id=\"password\"\n              autoComplete=\"current-password\"\n              value={formData.password}\n              onChange={handleChange}\n              error={!!formErrors.password}\n              helperText={formErrors.password}\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <Lock color=\"action\" />\n                  </InputAdornment>\n                ),\n                endAdornment: (\n                  <InputAdornment position=\"end\">\n                    <IconButton\n                      aria-label=\"toggle password visibility\"\n                      onClick={handleTogglePasswordVisibility}\n                      edge=\"end\"\n                    >\n                      {showPassword ? <VisibilityOff /> : <Visibility />}\n                    </IconButton>\n                  </InputAdornment>\n                ),\n              }}\n            />\n            \n            <Button\n              type=\"button\"\n              fullWidth\n              variant=\"contained\"\n              disabled={loading || isSubmitting || loginSuccess}\n              onClick={handleSubmit}\n              sx={{\n                mt: 3,\n                mb: 2,\n                py: 1.5,\n                fontSize: '1.1rem',\n                fontWeight: 600,\n                position: 'relative',\n              }}\n            >\n              {isSubmitting && (\n                <CircularProgress\n                  size={20}\n                  sx={{\n                    position: 'absolute',\n                    left: '50%',\n                    top: '50%',\n                    marginLeft: '-10px',\n                    marginTop: '-10px',\n                    color: 'inherit',\n                  }}\n                />\n              )}\n              {loginSuccess\n                ? 'تم بنجاح! جاري التوجيه...'\n                : isSubmitting\n                ? 'جاري تسجيل الدخول...'\n                : 'تسجيل الدخول'\n              }\n            </Button>\n          </Box>\n\n          {/* Demo Accounts Info */}\n          <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 2 }}>\n            <Typography variant=\"body2\" fontWeight=\"bold\" gutterBottom>\n              حسابات تجريبية:\n            </Typography>\n            <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>\n              <Button\n                size=\"small\"\n                variant=\"outlined\"\n                onClick={() => fillDemoCredentials('admin')}\n                disabled={isSubmitting || loginSuccess}\n                sx={{ fontSize: '0.75rem' }}\n              >\n                المدير: admin / 123456\n              </Button>\n              <Button\n                size=\"small\"\n                variant=\"outlined\"\n                onClick={() => fillDemoCredentials('coach')}\n                disabled={isSubmitting || loginSuccess}\n                sx={{ fontSize: '0.75rem' }}\n              >\n                المدرب: coach / 123456\n              </Button>\n            </Box>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              انقر على أي حساب لملء البيانات تلقائياً\n            </Typography>\n          </Box>\n        </Paper>\n      </Box>\n    </Container>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,KAAK,EACLC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,cAAc,EACdC,UAAU,EACVC,IAAI,EACJC,gBAAgB,QACX,eAAe;AACtB,SACEC,iBAAiB,EACjBC,UAAU,EACVC,aAAa,EACbC,MAAM,EACNC,IAAI,QACC,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,oBAAA;EAClB,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES,KAAK;IAAEC,KAAK;IAAEC,OAAO;IAAEC;EAAW,CAAC,GAAGd,OAAO,CAAC,CAAC;EAEvD,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC;IACvCoC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACwC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC0C,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM8C,IAAI,GAAG,EAAApB,eAAA,GAAAG,QAAQ,CAACkB,KAAK,cAAArB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBoB,IAAI,cAAAnB,oBAAA,uBAApBA,oBAAA,CAAsBqB,QAAQ,KAAI,YAAY;EAE3D,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChClB,WAAW,CAACmB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIZ,UAAU,CAACW,IAAI,CAAC,EAAE;MACpBV,aAAa,CAACa,IAAI,KAAK;QACrB,GAAGA,IAAI;QACP,CAACH,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;;IAEA;IACA,IAAIpB,KAAK,EAAE;MACTE,UAAU,CAAC,CAAC;IACd;EACF,CAAC;EAED,MAAMsB,8BAA8B,GAAGA,CAAA,KAAM;IAC3ChB,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;EAED,MAAMkB,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,MAAM,GAAG,CAAC,CAAC;IAEjB,IAAI,CAACvB,QAAQ,CAACE,QAAQ,CAACsB,IAAI,CAAC,CAAC,EAAE;MAC7BD,MAAM,CAACrB,QAAQ,GAAG,oBAAoB;IACxC;IAEA,IAAI,CAACF,QAAQ,CAACG,QAAQ,EAAE;MACtBoB,MAAM,CAACpB,QAAQ,GAAG,oBAAoB;IACxC;IAEAI,aAAa,CAACgB,MAAM,CAAC;IACrB,OAAOE,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,MAAM,KAAK,CAAC;EACzC,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOZ,CAAC,IAAK;IAChCA,CAAC,CAACa,cAAc,CAAC,CAAC;IAClBb,CAAC,CAACc,eAAe,CAAC,CAAC;IAEnBC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEhC,QAAQ,CAAC;IAElD,IAAI,CAACsB,YAAY,CAAC,CAAC,EAAE;MACnBS,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC;IACF;IAEAvB,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACFsB,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MAClC,MAAMC,MAAM,GAAG,MAAMrC,KAAK,CAACI,QAAQ,CAACE,QAAQ,EAAEF,QAAQ,CAACG,QAAQ,CAAC;MAChE4B,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEC,MAAM,CAAC;MAEpC,IAAIA,MAAM,CAACC,OAAO,EAAE;QAClBH,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;QACtDrB,eAAe,CAAC,IAAI,CAAC;QACrB;QACAwB,UAAU,CAAC,MAAM;UACfJ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEpB,IAAI,CAAC;UACnClB,QAAQ,CAACkB,IAAI,EAAE;YAAEwB,OAAO,EAAE;UAAK,CAAC,CAAC;QACnC,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLL,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEC,MAAM,CAACpC,KAAK,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdkC,OAAO,CAAClC,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC,CAAC,SAAS;MACRY,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAM4B,mBAAmB,GAAIC,IAAI,IAAK;IACpC,IAAIA,IAAI,KAAK,OAAO,EAAE;MACpBrC,WAAW,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IACxD,CAAC,MAAM,IAAImC,IAAI,KAAK,OAAO,EAAE;MAC3BrC,WAAW,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IACxD;IACAJ,UAAU,CAAC,CAAC;IACZQ,aAAa,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC;;EAED;EACAxC,SAAS,CAAC,MAAM;IACdgC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAEhB,oBACEV,OAAA,CAACd,SAAS;IAACgE,SAAS,EAAC,MAAM;IAACC,QAAQ,EAAC,IAAI;IAAAC,QAAA,eACvCpD,OAAA,CAACrB,GAAG;MACF0E,EAAE,EAAE;QACFC,SAAS,EAAE,OAAO;QAClBC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,EAAE,EAAE;MACN,CAAE;MAAAP,QAAA,eAEFpD,OAAA,CAACpB,KAAK;QACJgF,SAAS,EAAE,CAAE;QACbP,EAAE,EAAE;UACFQ,CAAC,EAAE,CAAC;UACJC,KAAK,EAAE,MAAM;UACbX,QAAQ,EAAE,GAAG;UACbY,YAAY,EAAE;QAChB,CAAE;QAAAX,QAAA,gBAGFpD,OAAA,CAACrB,GAAG;UACF0E,EAAE,EAAE;YACFE,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE,QAAQ;YACvBC,UAAU,EAAE,QAAQ;YACpBO,EAAE,EAAE;UACN,CAAE;UAAAZ,QAAA,gBAEFpD,OAAA,CAACf,MAAM;YACLoE,EAAE,EAAE;cACFY,CAAC,EAAE,CAAC;cACJC,OAAO,EAAE,cAAc;cACvBJ,KAAK,EAAE,EAAE;cACTK,MAAM,EAAE;YACV,CAAE;YAAAf,QAAA,eAEFpD,OAAA,CAACT,iBAAiB;cAAC8D,EAAE,EAAE;gBAAEe,QAAQ,EAAE;cAAG;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACTxE,OAAA,CAACjB,UAAU;YAACmE,SAAS,EAAC,IAAI;YAACuB,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACC,YAAY;YAAAvB,QAAA,EAAC;UAEvE;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxE,OAAA,CAACjB,UAAU;YAAC0F,OAAO,EAAC,OAAO;YAACG,KAAK,EAAC,gBAAgB;YAACC,SAAS,EAAC,QAAQ;YAAAzB,QAAA,EAAC;UAEtE;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAGLnD,YAAY,iBACXrB,OAAA,CAACX,IAAI;UAACyF,EAAE,EAAEzD,YAAa;UAAA+B,QAAA,eACrBpD,OAAA,CAAChB,KAAK;YAAC+F,QAAQ,EAAC,SAAS;YAAC1B,EAAE,EAAE;cAAEW,EAAE,EAAE;YAAE,CAAE;YAAAZ,QAAA,EAAC;UAEzC;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACP,EAGAhE,KAAK,IAAI,CAACa,YAAY,iBACrBrB,OAAA,CAACX,IAAI;UAACyF,EAAE,EAAE,CAAC,CAACtE,KAAM;UAAA4C,QAAA,eAChBpD,OAAA,CAAChB,KAAK;YAAC+F,QAAQ,EAAC,OAAO;YAAC1B,EAAE,EAAE;cAAEW,EAAE,EAAE;YAAE,CAAE;YAAAZ,QAAA,EACnC5C;UAAK;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACP,eAGDxE,OAAA,CAACrB,GAAG;UAAC0E,EAAE,EAAE;YAAES,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,gBACzBpD,OAAA,CAACnB,SAAS;YACRmG,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRC,SAAS;YACTC,EAAE,EAAC,UAAU;YACbC,KAAK,EAAC,qEAAc;YACpBxD,IAAI,EAAC,UAAU;YACfyD,YAAY,EAAC,UAAU;YACvBC,SAAS;YACTzD,KAAK,EAAElB,QAAQ,CAACE,QAAS;YACzB0E,QAAQ,EAAE7D,YAAa;YACvBlB,KAAK,EAAE,CAAC,CAACS,UAAU,CAACJ,QAAS;YAC7B2E,UAAU,EAAEvE,UAAU,CAACJ,QAAS;YAChC4E,UAAU,EAAE;cACVC,cAAc,eACZ1F,OAAA,CAACb,cAAc;gBAACwG,QAAQ,EAAC,OAAO;gBAAAvC,QAAA,eAC9BpD,OAAA,CAACN,MAAM;kBAACkF,KAAK,EAAC;gBAAQ;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAEpB;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFxE,OAAA,CAACnB,SAAS;YACRmG,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRC,SAAS;YACTtD,IAAI,EAAC,UAAU;YACfwD,KAAK,EAAC,+DAAa;YACnBnC,IAAI,EAAElC,YAAY,GAAG,MAAM,GAAG,UAAW;YACzCoE,EAAE,EAAC,UAAU;YACbE,YAAY,EAAC,kBAAkB;YAC/BxD,KAAK,EAAElB,QAAQ,CAACG,QAAS;YACzByE,QAAQ,EAAE7D,YAAa;YACvBlB,KAAK,EAAE,CAAC,CAACS,UAAU,CAACH,QAAS;YAC7B0E,UAAU,EAAEvE,UAAU,CAACH,QAAS;YAChC2E,UAAU,EAAE;cACVC,cAAc,eACZ1F,OAAA,CAACb,cAAc;gBAACwG,QAAQ,EAAC,OAAO;gBAAAvC,QAAA,eAC9BpD,OAAA,CAACL,IAAI;kBAACiF,KAAK,EAAC;gBAAQ;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CACjB;cACDoB,YAAY,eACV5F,OAAA,CAACb,cAAc;gBAACwG,QAAQ,EAAC,KAAK;gBAAAvC,QAAA,eAC5BpD,OAAA,CAACZ,UAAU;kBACT,cAAW,4BAA4B;kBACvCyG,OAAO,EAAE7D,8BAA+B;kBACxC8D,IAAI,EAAC,KAAK;kBAAA1C,QAAA,EAETrC,YAAY,gBAAGf,OAAA,CAACP,aAAa;oBAAA4E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGxE,OAAA,CAACR,UAAU;oBAAA6E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAEpB;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFxE,OAAA,CAAClB,MAAM;YACLmE,IAAI,EAAC,QAAQ;YACbiC,SAAS;YACTT,OAAO,EAAC,WAAW;YACnBsB,QAAQ,EAAEtF,OAAO,IAAIU,YAAY,IAAIE,YAAa;YAClDwE,OAAO,EAAEtD,YAAa;YACtBc,EAAE,EAAE;cACF2C,EAAE,EAAE,CAAC;cACLhC,EAAE,EAAE,CAAC;cACLL,EAAE,EAAE,GAAG;cACPS,QAAQ,EAAE,QAAQ;cAClBM,UAAU,EAAE,GAAG;cACfiB,QAAQ,EAAE;YACZ,CAAE;YAAAvC,QAAA,GAEDjC,YAAY,iBACXnB,OAAA,CAACV,gBAAgB;cACf2G,IAAI,EAAE,EAAG;cACT5C,EAAE,EAAE;gBACFsC,QAAQ,EAAE,UAAU;gBACpBO,IAAI,EAAE,KAAK;gBACXC,GAAG,EAAE,KAAK;gBACVC,UAAU,EAAE,OAAO;gBACnBC,SAAS,EAAE,OAAO;gBAClBzB,KAAK,EAAE;cACT;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACF,EACAnD,YAAY,GACT,2BAA2B,GAC3BF,YAAY,GACZ,sBAAsB,GACtB,cAAc;UAAA;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNxE,OAAA,CAACrB,GAAG;UAAC0E,EAAE,EAAE;YAAE2C,EAAE,EAAE,CAAC;YAAEnC,CAAC,EAAE,CAAC;YAAEK,OAAO,EAAE,SAAS;YAAEH,YAAY,EAAE;UAAE,CAAE;UAAAX,QAAA,gBAC5DpD,OAAA,CAACjB,UAAU;YAAC0F,OAAO,EAAC,OAAO;YAACC,UAAU,EAAC,MAAM;YAACC,YAAY;YAAAvB,QAAA,EAAC;UAE3D;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxE,OAAA,CAACrB,GAAG;YAAC0E,EAAE,EAAE;cAAEE,OAAO,EAAE,MAAM;cAAE+C,GAAG,EAAE,CAAC;cAAEtC,EAAE,EAAE;YAAE,CAAE;YAAAZ,QAAA,gBAC1CpD,OAAA,CAAClB,MAAM;cACLmH,IAAI,EAAC,OAAO;cACZxB,OAAO,EAAC,UAAU;cAClBoB,OAAO,EAAEA,CAAA,KAAM7C,mBAAmB,CAAC,OAAO,CAAE;cAC5C+C,QAAQ,EAAE5E,YAAY,IAAIE,YAAa;cACvCgC,EAAE,EAAE;gBAAEe,QAAQ,EAAE;cAAU,CAAE;cAAAhB,QAAA,EAC7B;YAED;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxE,OAAA,CAAClB,MAAM;cACLmH,IAAI,EAAC,OAAO;cACZxB,OAAO,EAAC,UAAU;cAClBoB,OAAO,EAAEA,CAAA,KAAM7C,mBAAmB,CAAC,OAAO,CAAE;cAC5C+C,QAAQ,EAAE5E,YAAY,IAAIE,YAAa;cACvCgC,EAAE,EAAE;gBAAEe,QAAQ,EAAE;cAAU,CAAE;cAAAhB,QAAA,EAC7B;YAED;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNxE,OAAA,CAACjB,UAAU;YAAC0F,OAAO,EAAC,SAAS;YAACG,KAAK,EAAC,gBAAgB;YAAAxB,QAAA,EAAC;UAErD;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAACtE,EAAA,CA9SID,KAAK;EAAA,QACQJ,WAAW,EACXC,WAAW,EACkBF,OAAO;AAAA;AAAA2G,EAAA,GAHjDtG,KAAK;AAgTX,eAAeA,KAAK;AAAC,IAAAsG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}