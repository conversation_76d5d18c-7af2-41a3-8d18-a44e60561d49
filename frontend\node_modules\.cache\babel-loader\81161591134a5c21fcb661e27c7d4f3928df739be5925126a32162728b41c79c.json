{"ast": null, "code": "function declension(scheme, count) {\n  // scheme for count=1 exists\n  if (scheme.one !== undefined && count === 1) {\n    return scheme.one;\n  }\n  var rem10 = count % 10;\n  var rem100 = count % 100;\n\n  // 1, 21, 31, ...\n  if (rem10 === 1 && rem100 !== 11) {\n    return scheme.singularNominative.replace('{{count}}', String(count));\n\n    // 2, 3, 4, 22, 23, 24, 32 ...\n  } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n    return scheme.singularGenitive.replace('{{count}}', String(count));\n\n    // 5, 6, 7, 8, 9, 10, 11, ...\n  } else {\n    return scheme.pluralGenitive.replace('{{count}}', String(count));\n  }\n}\nfunction buildLocalizeTokenFn(scheme) {\n  return function (count, options) {\n    if (options !== null && options !== void 0 && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        if (scheme.future) {\n          return declension(scheme.future, count);\n        } else {\n          return 'через ' + declension(scheme.regular, count);\n        }\n      } else {\n        if (scheme.past) {\n          return declension(scheme.past, count);\n        } else {\n          return declension(scheme.regular, count) + ' назад';\n        }\n      }\n    } else {\n      return declension(scheme.regular, count);\n    }\n  };\n}\nvar formatDistanceLocale = {\n  lessThanXSeconds: buildLocalizeTokenFn({\n    regular: {\n      one: 'меньше секунды',\n      singularNominative: 'меньше {{count}} секунды',\n      singularGenitive: 'меньше {{count}} секунд',\n      pluralGenitive: 'меньше {{count}} секунд'\n    },\n    future: {\n      one: 'меньше, чем через секунду',\n      singularNominative: 'меньше, чем через {{count}} секунду',\n      singularGenitive: 'меньше, чем через {{count}} секунды',\n      pluralGenitive: 'меньше, чем через {{count}} секунд'\n    }\n  }),\n  xSeconds: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} секунда',\n      singularGenitive: '{{count}} секунды',\n      pluralGenitive: '{{count}} секунд'\n    },\n    past: {\n      singularNominative: '{{count}} секунду назад',\n      singularGenitive: '{{count}} секунды назад',\n      pluralGenitive: '{{count}} секунд назад'\n    },\n    future: {\n      singularNominative: 'через {{count}} секунду',\n      singularGenitive: 'через {{count}} секунды',\n      pluralGenitive: 'через {{count}} секунд'\n    }\n  }),\n  halfAMinute: function halfAMinute(_count, options) {\n    if (options !== null && options !== void 0 && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        return 'через полминуты';\n      } else {\n        return 'полминуты назад';\n      }\n    }\n    return 'полминуты';\n  },\n  lessThanXMinutes: buildLocalizeTokenFn({\n    regular: {\n      one: 'меньше минуты',\n      singularNominative: 'меньше {{count}} минуты',\n      singularGenitive: 'меньше {{count}} минут',\n      pluralGenitive: 'меньше {{count}} минут'\n    },\n    future: {\n      one: 'меньше, чем через минуту',\n      singularNominative: 'меньше, чем через {{count}} минуту',\n      singularGenitive: 'меньше, чем через {{count}} минуты',\n      pluralGenitive: 'меньше, чем через {{count}} минут'\n    }\n  }),\n  xMinutes: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} минута',\n      singularGenitive: '{{count}} минуты',\n      pluralGenitive: '{{count}} минут'\n    },\n    past: {\n      singularNominative: '{{count}} минуту назад',\n      singularGenitive: '{{count}} минуты назад',\n      pluralGenitive: '{{count}} минут назад'\n    },\n    future: {\n      singularNominative: 'через {{count}} минуту',\n      singularGenitive: 'через {{count}} минуты',\n      pluralGenitive: 'через {{count}} минут'\n    }\n  }),\n  aboutXHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'около {{count}} часа',\n      singularGenitive: 'около {{count}} часов',\n      pluralGenitive: 'около {{count}} часов'\n    },\n    future: {\n      singularNominative: 'приблизительно через {{count}} час',\n      singularGenitive: 'приблизительно через {{count}} часа',\n      pluralGenitive: 'приблизительно через {{count}} часов'\n    }\n  }),\n  xHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} час',\n      singularGenitive: '{{count}} часа',\n      pluralGenitive: '{{count}} часов'\n    }\n  }),\n  xDays: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} день',\n      singularGenitive: '{{count}} дня',\n      pluralGenitive: '{{count}} дней'\n    }\n  }),\n  aboutXWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'около {{count}} недели',\n      singularGenitive: 'около {{count}} недель',\n      pluralGenitive: 'около {{count}} недель'\n    },\n    future: {\n      singularNominative: 'приблизительно через {{count}} неделю',\n      singularGenitive: 'приблизительно через {{count}} недели',\n      pluralGenitive: 'приблизительно через {{count}} недель'\n    }\n  }),\n  xWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} неделя',\n      singularGenitive: '{{count}} недели',\n      pluralGenitive: '{{count}} недель'\n    }\n  }),\n  aboutXMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'около {{count}} месяца',\n      singularGenitive: 'около {{count}} месяцев',\n      pluralGenitive: 'около {{count}} месяцев'\n    },\n    future: {\n      singularNominative: 'приблизительно через {{count}} месяц',\n      singularGenitive: 'приблизительно через {{count}} месяца',\n      pluralGenitive: 'приблизительно через {{count}} месяцев'\n    }\n  }),\n  xMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} месяц',\n      singularGenitive: '{{count}} месяца',\n      pluralGenitive: '{{count}} месяцев'\n    }\n  }),\n  aboutXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'около {{count}} года',\n      singularGenitive: 'около {{count}} лет',\n      pluralGenitive: 'около {{count}} лет'\n    },\n    future: {\n      singularNominative: 'приблизительно через {{count}} год',\n      singularGenitive: 'приблизительно через {{count}} года',\n      pluralGenitive: 'приблизительно через {{count}} лет'\n    }\n  }),\n  xYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} год',\n      singularGenitive: '{{count}} года',\n      pluralGenitive: '{{count}} лет'\n    }\n  }),\n  overXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'больше {{count}} года',\n      singularGenitive: 'больше {{count}} лет',\n      pluralGenitive: 'больше {{count}} лет'\n    },\n    future: {\n      singularNominative: 'больше, чем через {{count}} год',\n      singularGenitive: 'больше, чем через {{count}} года',\n      pluralGenitive: 'больше, чем через {{count}} лет'\n    }\n  }),\n  almostXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'почти {{count}} год',\n      singularGenitive: 'почти {{count}} года',\n      pluralGenitive: 'почти {{count}} лет'\n    },\n    future: {\n      singularNominative: 'почти через {{count}} год',\n      singularGenitive: 'почти через {{count}} года',\n      pluralGenitive: 'почти через {{count}} лет'\n    }\n  })\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  return formatDistanceLocale[token](count, options);\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["declension", "scheme", "count", "one", "undefined", "rem10", "rem100", "singularNominative", "replace", "String", "singularGenitive", "pluralGenitive", "buildLocalizeTokenFn", "options", "addSuffix", "comparison", "future", "regular", "past", "formatDistanceLocale", "lessThanXSeconds", "xSeconds", "halfAMinute", "_count", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/ru/_lib/formatDistance/index.js"], "sourcesContent": ["function declension(scheme, count) {\n  // scheme for count=1 exists\n  if (scheme.one !== undefined && count === 1) {\n    return scheme.one;\n  }\n  var rem10 = count % 10;\n  var rem100 = count % 100;\n\n  // 1, 21, 31, ...\n  if (rem10 === 1 && rem100 !== 11) {\n    return scheme.singularNominative.replace('{{count}}', String(count));\n\n    // 2, 3, 4, 22, 23, 24, 32 ...\n  } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n    return scheme.singularGenitive.replace('{{count}}', String(count));\n\n    // 5, 6, 7, 8, 9, 10, 11, ...\n  } else {\n    return scheme.pluralGenitive.replace('{{count}}', String(count));\n  }\n}\nfunction buildLocalizeTokenFn(scheme) {\n  return function (count, options) {\n    if (options !== null && options !== void 0 && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        if (scheme.future) {\n          return declension(scheme.future, count);\n        } else {\n          return 'через ' + declension(scheme.regular, count);\n        }\n      } else {\n        if (scheme.past) {\n          return declension(scheme.past, count);\n        } else {\n          return declension(scheme.regular, count) + ' назад';\n        }\n      }\n    } else {\n      return declension(scheme.regular, count);\n    }\n  };\n}\nvar formatDistanceLocale = {\n  lessThanXSeconds: buildLocalizeTokenFn({\n    regular: {\n      one: 'меньше секунды',\n      singularNominative: 'меньше {{count}} секунды',\n      singularGenitive: 'меньше {{count}} секунд',\n      pluralGenitive: 'меньше {{count}} секунд'\n    },\n    future: {\n      one: 'меньше, чем через секунду',\n      singularNominative: 'меньше, чем через {{count}} секунду',\n      singularGenitive: 'меньше, чем через {{count}} секунды',\n      pluralGenitive: 'меньше, чем через {{count}} секунд'\n    }\n  }),\n  xSeconds: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} секунда',\n      singularGenitive: '{{count}} секунды',\n      pluralGenitive: '{{count}} секунд'\n    },\n    past: {\n      singularNominative: '{{count}} секунду назад',\n      singularGenitive: '{{count}} секунды назад',\n      pluralGenitive: '{{count}} секунд назад'\n    },\n    future: {\n      singularNominative: 'через {{count}} секунду',\n      singularGenitive: 'через {{count}} секунды',\n      pluralGenitive: 'через {{count}} секунд'\n    }\n  }),\n  halfAMinute: function halfAMinute(_count, options) {\n    if (options !== null && options !== void 0 && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        return 'через полминуты';\n      } else {\n        return 'полминуты назад';\n      }\n    }\n    return 'полминуты';\n  },\n  lessThanXMinutes: buildLocalizeTokenFn({\n    regular: {\n      one: 'меньше минуты',\n      singularNominative: 'меньше {{count}} минуты',\n      singularGenitive: 'меньше {{count}} минут',\n      pluralGenitive: 'меньше {{count}} минут'\n    },\n    future: {\n      one: 'меньше, чем через минуту',\n      singularNominative: 'меньше, чем через {{count}} минуту',\n      singularGenitive: 'меньше, чем через {{count}} минуты',\n      pluralGenitive: 'меньше, чем через {{count}} минут'\n    }\n  }),\n  xMinutes: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} минута',\n      singularGenitive: '{{count}} минуты',\n      pluralGenitive: '{{count}} минут'\n    },\n    past: {\n      singularNominative: '{{count}} минуту назад',\n      singularGenitive: '{{count}} минуты назад',\n      pluralGenitive: '{{count}} минут назад'\n    },\n    future: {\n      singularNominative: 'через {{count}} минуту',\n      singularGenitive: 'через {{count}} минуты',\n      pluralGenitive: 'через {{count}} минут'\n    }\n  }),\n  aboutXHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'около {{count}} часа',\n      singularGenitive: 'около {{count}} часов',\n      pluralGenitive: 'около {{count}} часов'\n    },\n    future: {\n      singularNominative: 'приблизительно через {{count}} час',\n      singularGenitive: 'приблизительно через {{count}} часа',\n      pluralGenitive: 'приблизительно через {{count}} часов'\n    }\n  }),\n  xHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} час',\n      singularGenitive: '{{count}} часа',\n      pluralGenitive: '{{count}} часов'\n    }\n  }),\n  xDays: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} день',\n      singularGenitive: '{{count}} дня',\n      pluralGenitive: '{{count}} дней'\n    }\n  }),\n  aboutXWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'около {{count}} недели',\n      singularGenitive: 'около {{count}} недель',\n      pluralGenitive: 'около {{count}} недель'\n    },\n    future: {\n      singularNominative: 'приблизительно через {{count}} неделю',\n      singularGenitive: 'приблизительно через {{count}} недели',\n      pluralGenitive: 'приблизительно через {{count}} недель'\n    }\n  }),\n  xWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} неделя',\n      singularGenitive: '{{count}} недели',\n      pluralGenitive: '{{count}} недель'\n    }\n  }),\n  aboutXMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'около {{count}} месяца',\n      singularGenitive: 'около {{count}} месяцев',\n      pluralGenitive: 'около {{count}} месяцев'\n    },\n    future: {\n      singularNominative: 'приблизительно через {{count}} месяц',\n      singularGenitive: 'приблизительно через {{count}} месяца',\n      pluralGenitive: 'приблизительно через {{count}} месяцев'\n    }\n  }),\n  xMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} месяц',\n      singularGenitive: '{{count}} месяца',\n      pluralGenitive: '{{count}} месяцев'\n    }\n  }),\n  aboutXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'около {{count}} года',\n      singularGenitive: 'около {{count}} лет',\n      pluralGenitive: 'около {{count}} лет'\n    },\n    future: {\n      singularNominative: 'приблизительно через {{count}} год',\n      singularGenitive: 'приблизительно через {{count}} года',\n      pluralGenitive: 'приблизительно через {{count}} лет'\n    }\n  }),\n  xYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} год',\n      singularGenitive: '{{count}} года',\n      pluralGenitive: '{{count}} лет'\n    }\n  }),\n  overXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'больше {{count}} года',\n      singularGenitive: 'больше {{count}} лет',\n      pluralGenitive: 'больше {{count}} лет'\n    },\n    future: {\n      singularNominative: 'больше, чем через {{count}} год',\n      singularGenitive: 'больше, чем через {{count}} года',\n      pluralGenitive: 'больше, чем через {{count}} лет'\n    }\n  }),\n  almostXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'почти {{count}} год',\n      singularGenitive: 'почти {{count}} года',\n      pluralGenitive: 'почти {{count}} лет'\n    },\n    future: {\n      singularNominative: 'почти через {{count}} год',\n      singularGenitive: 'почти через {{count}} года',\n      pluralGenitive: 'почти через {{count}} лет'\n    }\n  })\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  return formatDistanceLocale[token](count, options);\n};\nexport default formatDistance;"], "mappings": "AAAA,SAASA,UAAUA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACjC;EACA,IAAID,MAAM,CAACE,GAAG,KAAKC,SAAS,IAAIF,KAAK,KAAK,CAAC,EAAE;IAC3C,OAAOD,MAAM,CAACE,GAAG;EACnB;EACA,IAAIE,KAAK,GAAGH,KAAK,GAAG,EAAE;EACtB,IAAII,MAAM,GAAGJ,KAAK,GAAG,GAAG;;EAExB;EACA,IAAIG,KAAK,KAAK,CAAC,IAAIC,MAAM,KAAK,EAAE,EAAE;IAChC,OAAOL,MAAM,CAACM,kBAAkB,CAACC,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACP,KAAK,CAAC,CAAC;;IAEpE;EACF,CAAC,MAAM,IAAIG,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,CAAC,KAAKC,MAAM,GAAG,EAAE,IAAIA,MAAM,GAAG,EAAE,CAAC,EAAE;IACnE,OAAOL,MAAM,CAACS,gBAAgB,CAACF,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACP,KAAK,CAAC,CAAC;;IAElE;EACF,CAAC,MAAM;IACL,OAAOD,MAAM,CAACU,cAAc,CAACH,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACP,KAAK,CAAC,CAAC;EAClE;AACF;AACA,SAASU,oBAAoBA,CAACX,MAAM,EAAE;EACpC,OAAO,UAAUC,KAAK,EAAEW,OAAO,EAAE;IAC/B,IAAIA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACC,SAAS,EAAE;MAC/D,IAAID,OAAO,CAACE,UAAU,IAAIF,OAAO,CAACE,UAAU,GAAG,CAAC,EAAE;QAChD,IAAId,MAAM,CAACe,MAAM,EAAE;UACjB,OAAOhB,UAAU,CAACC,MAAM,CAACe,MAAM,EAAEd,KAAK,CAAC;QACzC,CAAC,MAAM;UACL,OAAO,QAAQ,GAAGF,UAAU,CAACC,MAAM,CAACgB,OAAO,EAAEf,KAAK,CAAC;QACrD;MACF,CAAC,MAAM;QACL,IAAID,MAAM,CAACiB,IAAI,EAAE;UACf,OAAOlB,UAAU,CAACC,MAAM,CAACiB,IAAI,EAAEhB,KAAK,CAAC;QACvC,CAAC,MAAM;UACL,OAAOF,UAAU,CAACC,MAAM,CAACgB,OAAO,EAAEf,KAAK,CAAC,GAAG,QAAQ;QACrD;MACF;IACF,CAAC,MAAM;MACL,OAAOF,UAAU,CAACC,MAAM,CAACgB,OAAO,EAAEf,KAAK,CAAC;IAC1C;EACF,CAAC;AACH;AACA,IAAIiB,oBAAoB,GAAG;EACzBC,gBAAgB,EAAER,oBAAoB,CAAC;IACrCK,OAAO,EAAE;MACPd,GAAG,EAAE,gBAAgB;MACrBI,kBAAkB,EAAE,0BAA0B;MAC9CG,gBAAgB,EAAE,yBAAyB;MAC3CC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNb,GAAG,EAAE,2BAA2B;MAChCI,kBAAkB,EAAE,qCAAqC;MACzDG,gBAAgB,EAAE,qCAAqC;MACvDC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EACFU,QAAQ,EAAET,oBAAoB,CAAC;IAC7BK,OAAO,EAAE;MACPV,kBAAkB,EAAE,mBAAmB;MACvCG,gBAAgB,EAAE,mBAAmB;MACrCC,cAAc,EAAE;IAClB,CAAC;IACDO,IAAI,EAAE;MACJX,kBAAkB,EAAE,yBAAyB;MAC7CG,gBAAgB,EAAE,yBAAyB;MAC3CC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,yBAAyB;MAC7CG,gBAAgB,EAAE,yBAAyB;MAC3CC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EACFW,WAAW,EAAE,SAASA,WAAWA,CAACC,MAAM,EAAEV,OAAO,EAAE;IACjD,IAAIA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACC,SAAS,EAAE;MAC/D,IAAID,OAAO,CAACE,UAAU,IAAIF,OAAO,CAACE,UAAU,GAAG,CAAC,EAAE;QAChD,OAAO,iBAAiB;MAC1B,CAAC,MAAM;QACL,OAAO,iBAAiB;MAC1B;IACF;IACA,OAAO,WAAW;EACpB,CAAC;EACDS,gBAAgB,EAAEZ,oBAAoB,CAAC;IACrCK,OAAO,EAAE;MACPd,GAAG,EAAE,eAAe;MACpBI,kBAAkB,EAAE,yBAAyB;MAC7CG,gBAAgB,EAAE,wBAAwB;MAC1CC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNb,GAAG,EAAE,0BAA0B;MAC/BI,kBAAkB,EAAE,oCAAoC;MACxDG,gBAAgB,EAAE,oCAAoC;MACtDC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EACFc,QAAQ,EAAEb,oBAAoB,CAAC;IAC7BK,OAAO,EAAE;MACPV,kBAAkB,EAAE,kBAAkB;MACtCG,gBAAgB,EAAE,kBAAkB;MACpCC,cAAc,EAAE;IAClB,CAAC;IACDO,IAAI,EAAE;MACJX,kBAAkB,EAAE,wBAAwB;MAC5CG,gBAAgB,EAAE,wBAAwB;MAC1CC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,wBAAwB;MAC5CG,gBAAgB,EAAE,wBAAwB;MAC1CC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EACFe,WAAW,EAAEd,oBAAoB,CAAC;IAChCK,OAAO,EAAE;MACPV,kBAAkB,EAAE,sBAAsB;MAC1CG,gBAAgB,EAAE,uBAAuB;MACzCC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,oCAAoC;MACxDG,gBAAgB,EAAE,qCAAqC;MACvDC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EACFgB,MAAM,EAAEf,oBAAoB,CAAC;IAC3BK,OAAO,EAAE;MACPV,kBAAkB,EAAE,eAAe;MACnCG,gBAAgB,EAAE,gBAAgB;MAClCC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EACFiB,KAAK,EAAEhB,oBAAoB,CAAC;IAC1BK,OAAO,EAAE;MACPV,kBAAkB,EAAE,gBAAgB;MACpCG,gBAAgB,EAAE,eAAe;MACjCC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EACFkB,WAAW,EAAEjB,oBAAoB,CAAC;IAChCK,OAAO,EAAE;MACPV,kBAAkB,EAAE,wBAAwB;MAC5CG,gBAAgB,EAAE,wBAAwB;MAC1CC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,uCAAuC;MAC3DG,gBAAgB,EAAE,uCAAuC;MACzDC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EACFmB,MAAM,EAAElB,oBAAoB,CAAC;IAC3BK,OAAO,EAAE;MACPV,kBAAkB,EAAE,kBAAkB;MACtCG,gBAAgB,EAAE,kBAAkB;MACpCC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EACFoB,YAAY,EAAEnB,oBAAoB,CAAC;IACjCK,OAAO,EAAE;MACPV,kBAAkB,EAAE,wBAAwB;MAC5CG,gBAAgB,EAAE,yBAAyB;MAC3CC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,sCAAsC;MAC1DG,gBAAgB,EAAE,uCAAuC;MACzDC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EACFqB,OAAO,EAAEpB,oBAAoB,CAAC;IAC5BK,OAAO,EAAE;MACPV,kBAAkB,EAAE,iBAAiB;MACrCG,gBAAgB,EAAE,kBAAkB;MACpCC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EACFsB,WAAW,EAAErB,oBAAoB,CAAC;IAChCK,OAAO,EAAE;MACPV,kBAAkB,EAAE,sBAAsB;MAC1CG,gBAAgB,EAAE,qBAAqB;MACvCC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,oCAAoC;MACxDG,gBAAgB,EAAE,qCAAqC;MACvDC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EACFuB,MAAM,EAAEtB,oBAAoB,CAAC;IAC3BK,OAAO,EAAE;MACPV,kBAAkB,EAAE,eAAe;MACnCG,gBAAgB,EAAE,gBAAgB;MAClCC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EACFwB,UAAU,EAAEvB,oBAAoB,CAAC;IAC/BK,OAAO,EAAE;MACPV,kBAAkB,EAAE,uBAAuB;MAC3CG,gBAAgB,EAAE,sBAAsB;MACxCC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,iCAAiC;MACrDG,gBAAgB,EAAE,kCAAkC;MACpDC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EACFyB,YAAY,EAAExB,oBAAoB,CAAC;IACjCK,OAAO,EAAE;MACPV,kBAAkB,EAAE,qBAAqB;MACzCG,gBAAgB,EAAE,sBAAsB;MACxCC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,2BAA2B;MAC/CG,gBAAgB,EAAE,4BAA4B;MAC9CC,cAAc,EAAE;IAClB;EACF,CAAC;AACH,CAAC;AACD,IAAI0B,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEpC,KAAK,EAAEW,OAAO,EAAE;EAClE,OAAOM,oBAAoB,CAACmB,KAAK,CAAC,CAACpC,KAAK,EAAEW,OAAO,CAAC;AACpD,CAAC;AACD,eAAewB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}