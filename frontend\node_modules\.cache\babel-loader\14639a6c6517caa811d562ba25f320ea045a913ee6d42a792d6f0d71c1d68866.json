{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: function lastWeek(date) {\n    var day = date.getUTCDay();\n    switch (day) {\n      case 0:\n        return \"'прошле недеље у' p\";\n      case 3:\n        return \"'прошле среде у' p\";\n      case 6:\n        return \"'прошле суботе у' p\";\n      default:\n        return \"'прошли' EEEE 'у' p\";\n    }\n  },\n  yesterday: \"'јуче у' p\",\n  today: \"'данас у' p\",\n  tomorrow: \"'сутра у' p\",\n  nextWeek: function nextWeek(date) {\n    var day = date.getUTCDay();\n    switch (day) {\n      case 0:\n        return \"'следеће недеље у' p\";\n      case 3:\n        return \"'следећу среду у' p\";\n      case 6:\n        return \"'следећу суботу у' p\";\n      default:\n        return \"'следећи' EEEE 'у' p\";\n    }\n  },\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, _baseDate, _options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date);\n  }\n  return format;\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "date", "day", "getUTCDay", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_baseDate", "_options", "format"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/sr/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: function lastWeek(date) {\n    var day = date.getUTCDay();\n    switch (day) {\n      case 0:\n        return \"'прошле недеље у' p\";\n      case 3:\n        return \"'прошле среде у' p\";\n      case 6:\n        return \"'прошле суботе у' p\";\n      default:\n        return \"'прошли' EEEE 'у' p\";\n    }\n  },\n  yesterday: \"'јуче у' p\",\n  today: \"'данас у' p\",\n  tomorrow: \"'сутра у' p\",\n  nextWeek: function nextWeek(date) {\n    var day = date.getUTCDay();\n    switch (day) {\n      case 0:\n        return \"'следеће недеље у' p\";\n      case 3:\n        return \"'следећу среду у' p\";\n      case 6:\n        return \"'следећу суботу у' p\";\n      default:\n        return \"'следећи' EEEE 'у' p\";\n    }\n  },\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, _baseDate, _options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date);\n  }\n  return format;\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,SAASA,QAAQA,CAACC,IAAI,EAAE;IAChC,IAAIC,GAAG,GAAGD,IAAI,CAACE,SAAS,CAAC,CAAC;IAC1B,QAAQD,GAAG;MACT,KAAK,CAAC;QACJ,OAAO,qBAAqB;MAC9B,KAAK,CAAC;QACJ,OAAO,oBAAoB;MAC7B,KAAK,CAAC;QACJ,OAAO,qBAAqB;MAC9B;QACE,OAAO,qBAAqB;IAChC;EACF,CAAC;EACDE,SAAS,EAAE,YAAY;EACvBC,KAAK,EAAE,aAAa;EACpBC,QAAQ,EAAE,aAAa;EACvBC,QAAQ,EAAE,SAASA,QAAQA,CAACN,IAAI,EAAE;IAChC,IAAIC,GAAG,GAAGD,IAAI,CAACE,SAAS,CAAC,CAAC;IAC1B,QAAQD,GAAG;MACT,KAAK,CAAC;QACJ,OAAO,sBAAsB;MAC/B,KAAK,CAAC;QACJ,OAAO,qBAAqB;MAC9B,KAAK,CAAC;QACJ,OAAO,sBAAsB;MAC/B;QACE,OAAO,sBAAsB;IACjC;EACF,CAAC;EACDM,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAET,IAAI,EAAEU,SAAS,EAAEC,QAAQ,EAAE;EAC7E,IAAIC,MAAM,GAAGd,oBAAoB,CAACW,KAAK,CAAC;EACxC,IAAI,OAAOG,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACZ,IAAI,CAAC;EACrB;EACA,OAAOY,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}