{"ast": null, "code": "import startOfISOWeekYear from \"../startOfISOWeekYear/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSameISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Are the given dates in the same ISO week-numbering year?\n *\n * @description\n * Are the given dates in the same ISO week-numbering year?\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} dateLeft - the first date to check\n * @param {Date|Number} dateRight - the second date to check\n * @returns {Boolean} the dates are in the same ISO week-numbering year\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Are 29 December 2003 and 2 January 2005 in the same ISO week-numbering year?\n * const result = isSameISOWeekYear(new Date(2003, 11, 29), new Date(2005, 0, 2))\n * //=> true\n */\nexport default function isSameISOWeekYear(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeftStartOfYear = startOfISOWeekYear(dirtyDateLeft);\n  var dateRightStartOfYear = startOfISOWeekYear(dirtyDateRight);\n  return dateLeftStartOfYear.getTime() === dateRightStartOfYear.getTime();\n}", "map": {"version": 3, "names": ["startOfISOWeekYear", "requiredArgs", "isSameISOWeekYear", "dirtyDateLeft", "dirtyDateRight", "arguments", "dateLeftStartOfYear", "dateRightStartOfYear", "getTime"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/isSameISOWeekYear/index.js"], "sourcesContent": ["import startOfISOWeekYear from \"../startOfISOWeekYear/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSameISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Are the given dates in the same ISO week-numbering year?\n *\n * @description\n * Are the given dates in the same ISO week-numbering year?\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} dateLeft - the first date to check\n * @param {Date|Number} dateRight - the second date to check\n * @returns {Boolean} the dates are in the same ISO week-numbering year\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Are 29 December 2003 and 2 January 2005 in the same ISO week-numbering year?\n * const result = isSameISOWeekYear(new Date(2003, 11, 29), new Date(2005, 0, 2))\n * //=> true\n */\nexport default function isSameISOWeekYear(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeftStartOfYear = startOfISOWeekYear(dirtyDateLeft);\n  var dateRightStartOfYear = startOfISOWeekYear(dirtyDateRight);\n  return dateLeftStartOfYear.getTime() === dateRightStartOfYear.getTime();\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,gCAAgC;AAC/D,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,iBAAiBA,CAACC,aAAa,EAAEC,cAAc,EAAE;EACvEH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,mBAAmB,GAAGN,kBAAkB,CAACG,aAAa,CAAC;EAC3D,IAAII,oBAAoB,GAAGP,kBAAkB,CAACI,cAAc,CAAC;EAC7D,OAAOE,mBAAmB,CAACE,OAAO,CAAC,CAAC,KAAKD,oBAAoB,CAACC,OAAO,CAAC,CAAC;AACzE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}