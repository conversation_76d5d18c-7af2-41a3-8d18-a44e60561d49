{"ast": null, "code": "export const DAY_SIZE = 36;\nexport const DAY_MARGIN = 2;\nexport const DIALOG_WIDTH = 320;\nexport const MAX_CALENDAR_HEIGHT = 280;\nexport const VIEW_HEIGHT = 334;\nexport const DIGITAL_CLOCK_VIEW_HEIGHT = 232;\nexport const MULTI_SECTION_CLOCK_SECTION_WIDTH = 48;", "map": {"version": 3, "names": ["DAY_SIZE", "DAY_MARGIN", "DIALOG_WIDTH", "MAX_CALENDAR_HEIGHT", "VIEW_HEIGHT", "DIGITAL_CLOCK_VIEW_HEIGHT", "MULTI_SECTION_CLOCK_SECTION_WIDTH"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/x-date-pickers/internals/constants/dimensions.js"], "sourcesContent": ["export const DAY_SIZE = 36;\nexport const DAY_MARGIN = 2;\nexport const DIALOG_WIDTH = 320;\nexport const MAX_CALENDAR_HEIGHT = 280;\nexport const VIEW_HEIGHT = 334;\nexport const DIGITAL_CLOCK_VIEW_HEIGHT = 232;\nexport const MULTI_SECTION_CLOCK_SECTION_WIDTH = 48;"], "mappings": "AAAA,OAAO,MAAMA,QAAQ,GAAG,EAAE;AAC1B,OAAO,MAAMC,UAAU,GAAG,CAAC;AAC3B,OAAO,MAAMC,YAAY,GAAG,GAAG;AAC/B,OAAO,MAAMC,mBAAmB,GAAG,GAAG;AACtC,OAAO,MAAMC,WAAW,GAAG,GAAG;AAC9B,OAAO,MAAMC,yBAAyB,GAAG,GAAG;AAC5C,OAAO,MAAMC,iCAAiC,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}