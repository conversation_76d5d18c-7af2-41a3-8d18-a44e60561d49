{"ast": null, "code": "import * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useControlled from '@mui/utils/useControlled';\nimport { useUtils } from './useUtils';\n/**\n * Hooks making sure that:\n * - The value returned by `onChange` always have the timezone of `props.value` or `props.defaultValue` if defined\n * - The value rendered is always the one from `props.timezone` if defined\n */\nexport const useValueWithTimezone = ({\n  timezone: timezoneProp,\n  value: valueProp,\n  defaultValue,\n  onChange,\n  valueManager\n}) => {\n  var _ref, _ref2;\n  const utils = useUtils();\n  const firstDefaultValue = React.useRef(defaultValue);\n  const inputValue = (_ref = valueProp != null ? valueProp : firstDefaultValue.current) != null ? _ref : valueManager.emptyValue;\n  const inputTimezone = React.useMemo(() => valueManager.getTimezone(utils, inputValue), [utils, valueManager, inputValue]);\n  const setInputTimezone = useEventCallback(newValue => {\n    if (inputTimezone == null) {\n      return newValue;\n    }\n    return valueManager.setTimezone(utils, inputTimezone, newValue);\n  });\n  const timezoneToRender = (_ref2 = timezoneProp != null ? timezoneProp : inputTimezone) != null ? _ref2 : 'default';\n  const valueWithTimezoneToRender = React.useMemo(() => valueManager.setTimezone(utils, timezoneToRender, inputValue), [valueManager, utils, timezoneToRender, inputValue]);\n  const handleValueChange = useEventCallback((newValue, ...otherParams) => {\n    const newValueWithInputTimezone = setInputTimezone(newValue);\n    onChange == null || onChange(newValueWithInputTimezone, ...otherParams);\n  });\n  return {\n    value: valueWithTimezoneToRender,\n    handleValueChange,\n    timezone: timezoneToRender\n  };\n};\n\n/**\n * Wrapper around `useControlled` and `useValueWithTimezone`\n */\nexport const useControlledValueWithTimezone = ({\n  name,\n  timezone: timezoneProp,\n  value: valueProp,\n  defaultValue,\n  onChange: onChangeProp,\n  valueManager\n}) => {\n  const [valueWithInputTimezone, setValue] = useControlled({\n    name,\n    state: 'value',\n    controlled: valueProp,\n    default: defaultValue != null ? defaultValue : valueManager.emptyValue\n  });\n  const onChange = useEventCallback((newValue, ...otherParams) => {\n    setValue(newValue);\n    onChangeProp == null || onChangeProp(newValue, ...otherParams);\n  });\n  return useValueWithTimezone({\n    timezone: timezoneProp,\n    value: valueWithInputTimezone,\n    defaultValue: undefined,\n    onChange,\n    valueManager\n  });\n};", "map": {"version": 3, "names": ["React", "useEventCallback", "useControlled", "useUtils", "useValueWithTimezone", "timezone", "timezoneProp", "value", "valueProp", "defaultValue", "onChange", "valueManager", "_ref", "_ref2", "utils", "firstDefaultValue", "useRef", "inputValue", "current", "emptyValue", "inputTimezone", "useMemo", "getTimezone", "setInputTimezone", "newValue", "setTimezone", "timezoneToRender", "valueWithTimezoneToRender", "handleValueChange", "otherParams", "newValueWithInputTimezone", "useControlledValueWithTimezone", "name", "onChangeProp", "valueWithInputTimezone", "setValue", "state", "controlled", "default", "undefined"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useValueWithTimezone.js"], "sourcesContent": ["import * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useControlled from '@mui/utils/useControlled';\nimport { useUtils } from './useUtils';\n/**\n * Hooks making sure that:\n * - The value returned by `onChange` always have the timezone of `props.value` or `props.defaultValue` if defined\n * - The value rendered is always the one from `props.timezone` if defined\n */\nexport const useValueWithTimezone = ({\n  timezone: timezoneProp,\n  value: valueProp,\n  defaultValue,\n  onChange,\n  valueManager\n}) => {\n  var _ref, _ref2;\n  const utils = useUtils();\n  const firstDefaultValue = React.useRef(defaultValue);\n  const inputValue = (_ref = valueProp != null ? valueProp : firstDefaultValue.current) != null ? _ref : valueManager.emptyValue;\n  const inputTimezone = React.useMemo(() => valueManager.getTimezone(utils, inputValue), [utils, valueManager, inputValue]);\n  const setInputTimezone = useEventCallback(newValue => {\n    if (inputTimezone == null) {\n      return newValue;\n    }\n    return valueManager.setTimezone(utils, inputTimezone, newValue);\n  });\n  const timezoneToRender = (_ref2 = timezoneProp != null ? timezoneProp : inputTimezone) != null ? _ref2 : 'default';\n  const valueWithTimezoneToRender = React.useMemo(() => valueManager.setTimezone(utils, timezoneToRender, inputValue), [valueManager, utils, timezoneToRender, inputValue]);\n  const handleValueChange = useEventCallback((newValue, ...otherParams) => {\n    const newValueWithInputTimezone = setInputTimezone(newValue);\n    onChange == null || onChange(newValueWithInputTimezone, ...otherParams);\n  });\n  return {\n    value: valueWithTimezoneToRender,\n    handleValueChange,\n    timezone: timezoneToRender\n  };\n};\n\n/**\n * Wrapper around `useControlled` and `useValueWithTimezone`\n */\nexport const useControlledValueWithTimezone = ({\n  name,\n  timezone: timezoneProp,\n  value: valueProp,\n  defaultValue,\n  onChange: onChangeProp,\n  valueManager\n}) => {\n  const [valueWithInputTimezone, setValue] = useControlled({\n    name,\n    state: 'value',\n    controlled: valueProp,\n    default: defaultValue != null ? defaultValue : valueManager.emptyValue\n  });\n  const onChange = useEventCallback((newValue, ...otherParams) => {\n    setValue(newValue);\n    onChangeProp == null || onChangeProp(newValue, ...otherParams);\n  });\n  return useValueWithTimezone({\n    timezone: timezoneProp,\n    value: valueWithInputTimezone,\n    defaultValue: undefined,\n    onChange,\n    valueManager\n  });\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,aAAa,MAAM,0BAA0B;AACpD,SAASC,QAAQ,QAAQ,YAAY;AACrC;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,oBAAoB,GAAGA,CAAC;EACnCC,QAAQ,EAAEC,YAAY;EACtBC,KAAK,EAAEC,SAAS;EAChBC,YAAY;EACZC,QAAQ;EACRC;AACF,CAAC,KAAK;EACJ,IAAIC,IAAI,EAAEC,KAAK;EACf,MAAMC,KAAK,GAAGX,QAAQ,CAAC,CAAC;EACxB,MAAMY,iBAAiB,GAAGf,KAAK,CAACgB,MAAM,CAACP,YAAY,CAAC;EACpD,MAAMQ,UAAU,GAAG,CAACL,IAAI,GAAGJ,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAGO,iBAAiB,CAACG,OAAO,KAAK,IAAI,GAAGN,IAAI,GAAGD,YAAY,CAACQ,UAAU;EAC9H,MAAMC,aAAa,GAAGpB,KAAK,CAACqB,OAAO,CAAC,MAAMV,YAAY,CAACW,WAAW,CAACR,KAAK,EAAEG,UAAU,CAAC,EAAE,CAACH,KAAK,EAAEH,YAAY,EAAEM,UAAU,CAAC,CAAC;EACzH,MAAMM,gBAAgB,GAAGtB,gBAAgB,CAACuB,QAAQ,IAAI;IACpD,IAAIJ,aAAa,IAAI,IAAI,EAAE;MACzB,OAAOI,QAAQ;IACjB;IACA,OAAOb,YAAY,CAACc,WAAW,CAACX,KAAK,EAAEM,aAAa,EAAEI,QAAQ,CAAC;EACjE,CAAC,CAAC;EACF,MAAME,gBAAgB,GAAG,CAACb,KAAK,GAAGP,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAGc,aAAa,KAAK,IAAI,GAAGP,KAAK,GAAG,SAAS;EAClH,MAAMc,yBAAyB,GAAG3B,KAAK,CAACqB,OAAO,CAAC,MAAMV,YAAY,CAACc,WAAW,CAACX,KAAK,EAAEY,gBAAgB,EAAET,UAAU,CAAC,EAAE,CAACN,YAAY,EAAEG,KAAK,EAAEY,gBAAgB,EAAET,UAAU,CAAC,CAAC;EACzK,MAAMW,iBAAiB,GAAG3B,gBAAgB,CAAC,CAACuB,QAAQ,EAAE,GAAGK,WAAW,KAAK;IACvE,MAAMC,yBAAyB,GAAGP,gBAAgB,CAACC,QAAQ,CAAC;IAC5Dd,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACoB,yBAAyB,EAAE,GAAGD,WAAW,CAAC;EACzE,CAAC,CAAC;EACF,OAAO;IACLtB,KAAK,EAAEoB,yBAAyB;IAChCC,iBAAiB;IACjBvB,QAAQ,EAAEqB;EACZ,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMK,8BAA8B,GAAGA,CAAC;EAC7CC,IAAI;EACJ3B,QAAQ,EAAEC,YAAY;EACtBC,KAAK,EAAEC,SAAS;EAChBC,YAAY;EACZC,QAAQ,EAAEuB,YAAY;EACtBtB;AACF,CAAC,KAAK;EACJ,MAAM,CAACuB,sBAAsB,EAAEC,QAAQ,CAAC,GAAGjC,aAAa,CAAC;IACvD8B,IAAI;IACJI,KAAK,EAAE,OAAO;IACdC,UAAU,EAAE7B,SAAS;IACrB8B,OAAO,EAAE7B,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAGE,YAAY,CAACQ;EAC9D,CAAC,CAAC;EACF,MAAMT,QAAQ,GAAGT,gBAAgB,CAAC,CAACuB,QAAQ,EAAE,GAAGK,WAAW,KAAK;IAC9DM,QAAQ,CAACX,QAAQ,CAAC;IAClBS,YAAY,IAAI,IAAI,IAAIA,YAAY,CAACT,QAAQ,EAAE,GAAGK,WAAW,CAAC;EAChE,CAAC,CAAC;EACF,OAAOzB,oBAAoB,CAAC;IAC1BC,QAAQ,EAAEC,YAAY;IACtBC,KAAK,EAAE2B,sBAAsB;IAC7BzB,YAAY,EAAE8B,SAAS;IACvB7B,QAAQ;IACRC;EACF,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}