{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'malpli ol sekundo',\n    other: 'malpli ol {{count}} sekundoj'\n  },\n  xSeconds: {\n    one: '1 sekundo',\n    other: '{{count}} sekundoj'\n  },\n  halfAMinute: 'duonminuto',\n  lessThanXMinutes: {\n    one: 'malpli ol minuto',\n    other: 'malpli ol {{count}} minutoj'\n  },\n  xMinutes: {\n    one: '1 minuto',\n    other: '{{count}} minutoj'\n  },\n  aboutXHours: {\n    one: 'proksimume 1 horo',\n    other: 'proksimume {{count}} horoj'\n  },\n  xHours: {\n    one: '1 horo',\n    other: '{{count}} horoj'\n  },\n  xDays: {\n    one: '1 tago',\n    other: '{{count}} tagoj'\n  },\n  aboutXMonths: {\n    one: 'proksimume 1 monato',\n    other: 'proksimume {{count}} monatoj'\n  },\n  xWeeks: {\n    one: '1 semajno',\n    other: '{{count}} semajnoj'\n  },\n  aboutXWeeks: {\n    one: 'proksimume 1 semajno',\n    other: 'proksimume {{count}} semajnoj'\n  },\n  xMonths: {\n    one: '1 monato',\n    other: '{{count}} monatoj'\n  },\n  aboutXYears: {\n    one: 'proksimume 1 jaro',\n    other: 'proksimume {{count}} jaroj'\n  },\n  xYears: {\n    one: '1 jaro',\n    other: '{{count}} jaroj'\n  },\n  overXYears: {\n    one: 'pli ol 1 jaro',\n    other: 'pli ol {{count}} jaroj'\n  },\n  almostXYears: {\n    one: 'preskaŭ 1 jaro',\n    other: 'preskaŭ {{count}} jaroj'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options !== null && options !== void 0 && options.comparison && options.comparison > 0) {\n      return 'post ' + result;\n    } else {\n      return 'antaŭ ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXMonths", "xWeeks", "aboutXWeeks", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/eo/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'malpli ol sekundo',\n    other: 'malpli ol {{count}} sekundoj'\n  },\n  xSeconds: {\n    one: '1 sekundo',\n    other: '{{count}} sekundoj'\n  },\n  halfAMinute: 'duonminuto',\n  lessThanXMinutes: {\n    one: 'malpli ol minuto',\n    other: 'malpli ol {{count}} minutoj'\n  },\n  xMinutes: {\n    one: '1 minuto',\n    other: '{{count}} minutoj'\n  },\n  aboutXHours: {\n    one: 'proksimume 1 horo',\n    other: 'proksimume {{count}} horoj'\n  },\n  xHours: {\n    one: '1 horo',\n    other: '{{count}} horoj'\n  },\n  xDays: {\n    one: '1 tago',\n    other: '{{count}} tagoj'\n  },\n  aboutXMonths: {\n    one: 'proksimume 1 monato',\n    other: 'proksimume {{count}} monatoj'\n  },\n  xWeeks: {\n    one: '1 semajno',\n    other: '{{count}} semajnoj'\n  },\n  aboutXWeeks: {\n    one: 'proksimume 1 semajno',\n    other: 'proksimume {{count}} semajnoj'\n  },\n  xMonths: {\n    one: '1 monato',\n    other: '{{count}} monatoj'\n  },\n  aboutXYears: {\n    one: 'proksimume 1 jaro',\n    other: 'proksimume {{count}} jaroj'\n  },\n  xYears: {\n    one: '1 jaro',\n    other: '{{count}} jaroj'\n  },\n  overXYears: {\n    one: 'pli ol 1 jaro',\n    other: 'pli ol {{count}} jaroj'\n  },\n  almostXYears: {\n    one: 'preskaŭ 1 jaro',\n    other: 'preskaŭ {{count}} jaroj'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options !== null && options !== void 0 && options.comparison && options.comparison > 0) {\n      return 'post ' + result;\n    } else {\n      return 'antaŭ ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,YAAY;EACzBC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDQ,YAAY,EAAE;IACZT,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDU,WAAW,EAAE;IACXX,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAC1F,OAAO,OAAO,GAAGL,MAAM;IACzB,CAAC,MAAM;MACL,OAAO,QAAQ,GAAGA,MAAM;IAC1B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}