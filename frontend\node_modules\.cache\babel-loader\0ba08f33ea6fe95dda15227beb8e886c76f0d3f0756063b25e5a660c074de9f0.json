{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"props\", \"getOpenDialogAriaText\"];\nimport * as React from 'react';\nimport { useSlotProps } from '@mui/base/utils';\nimport useForkRef from '@mui/utils/useForkRef';\nimport useId from '@mui/utils/useId';\nimport { PickersModalDialog } from '../../components/PickersModalDialog';\nimport { usePicker } from '../usePicker';\nimport { onSpaceOrEnter } from '../../utils/utils';\nimport { useUtils } from '../useUtils';\nimport { LocalizationProvider } from '../../../LocalizationProvider';\nimport { PickersLayout } from '../../../PickersLayout';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\n/**\n * Hook managing all the single-date mobile pickers:\n * - MobileDatePicker\n * - MobileDateTimePicker\n * - MobileTimePicker\n */\nexport const useMobilePicker = _ref => {\n  var _innerSlotProps$toolb, _innerSlotProps$toolb2, _slots$layout;\n  let {\n      props,\n      getOpenDialogAriaText\n    } = _ref,\n    pickerParams = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const {\n    slots,\n    slotProps: innerSlotProps,\n    className,\n    sx,\n    format,\n    formatDensity,\n    timezone,\n    name,\n    label,\n    inputRef,\n    readOnly,\n    disabled,\n    localeText\n  } = props;\n  const utils = useUtils();\n  const internalInputRef = React.useRef(null);\n  const labelId = useId();\n  const isToolbarHidden = (_innerSlotProps$toolb = innerSlotProps == null || (_innerSlotProps$toolb2 = innerSlotProps.toolbar) == null ? void 0 : _innerSlotProps$toolb2.hidden) != null ? _innerSlotProps$toolb : false;\n  const {\n    open,\n    actions,\n    layoutProps,\n    renderCurrentView,\n    fieldProps: pickerFieldProps\n  } = usePicker(_extends({}, pickerParams, {\n    props,\n    inputRef: internalInputRef,\n    autoFocusView: true,\n    additionalViewProps: {},\n    wrapperVariant: 'mobile'\n  }));\n  const Field = slots.field;\n  const fieldProps = useSlotProps({\n    elementType: Field,\n    externalSlotProps: innerSlotProps == null ? void 0 : innerSlotProps.field,\n    additionalProps: _extends({}, pickerFieldProps, isToolbarHidden && {\n      id: labelId\n    }, !(disabled || readOnly) && {\n      onClick: actions.onOpen,\n      onKeyDown: onSpaceOrEnter(actions.onOpen)\n    }, {\n      readOnly: readOnly != null ? readOnly : true,\n      disabled,\n      className,\n      sx,\n      format,\n      formatDensity,\n      timezone,\n      label,\n      name\n    }),\n    ownerState: props\n  });\n\n  // TODO: Move to `useSlotProps` when https://github.com/mui/material-ui/pull/35088 will be merged\n  fieldProps.inputProps = _extends({}, fieldProps.inputProps, {\n    'aria-label': getOpenDialogAriaText(pickerFieldProps.value, utils)\n  });\n  const slotsForField = _extends({\n    textField: slots.textField\n  }, fieldProps.slots);\n  const Layout = (_slots$layout = slots.layout) != null ? _slots$layout : PickersLayout;\n  const handleInputRef = useForkRef(internalInputRef, fieldProps.inputRef, inputRef);\n  let labelledById = labelId;\n  if (isToolbarHidden) {\n    if (label) {\n      labelledById = `${labelId}-label`;\n    } else {\n      labelledById = undefined;\n    }\n  }\n  const slotProps = _extends({}, innerSlotProps, {\n    toolbar: _extends({}, innerSlotProps == null ? void 0 : innerSlotProps.toolbar, {\n      titleId: labelId\n    }),\n    mobilePaper: _extends({\n      'aria-labelledby': labelledById\n    }, innerSlotProps == null ? void 0 : innerSlotProps.mobilePaper)\n  });\n  const renderPicker = () => /*#__PURE__*/_jsxs(LocalizationProvider, {\n    localeText: localeText,\n    children: [/*#__PURE__*/_jsx(Field, _extends({}, fieldProps, {\n      slots: slotsForField,\n      slotProps: slotProps,\n      inputRef: handleInputRef\n    })), /*#__PURE__*/_jsx(PickersModalDialog, _extends({}, actions, {\n      open: open,\n      slots: slots,\n      slotProps: slotProps,\n      children: /*#__PURE__*/_jsx(Layout, _extends({}, layoutProps, slotProps == null ? void 0 : slotProps.layout, {\n        slots: slots,\n        slotProps: slotProps,\n        children: renderCurrentView()\n      }))\n    }))]\n  });\n  return {\n    renderPicker\n  };\n};", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "useSlotProps", "useForkRef", "useId", "PickersModalDialog", "usePicker", "onSpaceOrEnter", "useUtils", "LocalizationProvider", "PickersLayout", "jsx", "_jsx", "jsxs", "_jsxs", "useMobilePicker", "_ref", "_innerSlotProps$toolb", "_innerSlotProps$toolb2", "_slots$layout", "props", "getOpenDialogAriaText", "pickerParams", "slots", "slotProps", "innerSlotProps", "className", "sx", "format", "formatDensity", "timezone", "name", "label", "inputRef", "readOnly", "disabled", "localeText", "utils", "internalInputRef", "useRef", "labelId", "isToolbarHidden", "toolbar", "hidden", "open", "actions", "layoutProps", "renderCurrentView", "fieldProps", "pickerFieldProps", "autoFocusView", "additionalViewProps", "wrapperVariant", "Field", "field", "elementType", "externalSlotProps", "additionalProps", "id", "onClick", "onOpen", "onKeyDown", "ownerState", "inputProps", "value", "slotsForField", "textField", "Layout", "layout", "handleInputRef", "labelledById", "undefined", "titleId", "mobilePaper", "renderPicker", "children"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useMobilePicker/useMobilePicker.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"props\", \"getOpenDialogAriaText\"];\nimport * as React from 'react';\nimport { useSlotProps } from '@mui/base/utils';\nimport useForkRef from '@mui/utils/useForkRef';\nimport useId from '@mui/utils/useId';\nimport { PickersModalDialog } from '../../components/PickersModalDialog';\nimport { usePicker } from '../usePicker';\nimport { onSpaceOrEnter } from '../../utils/utils';\nimport { useUtils } from '../useUtils';\nimport { LocalizationProvider } from '../../../LocalizationProvider';\nimport { PickersLayout } from '../../../PickersLayout';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\n/**\n * Hook managing all the single-date mobile pickers:\n * - MobileDatePicker\n * - MobileDateTimePicker\n * - MobileTimePicker\n */\nexport const useMobilePicker = _ref => {\n  var _innerSlotProps$toolb, _innerSlotProps$toolb2, _slots$layout;\n  let {\n      props,\n      getOpenDialogAriaText\n    } = _ref,\n    pickerParams = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const {\n    slots,\n    slotProps: innerSlotProps,\n    className,\n    sx,\n    format,\n    formatDensity,\n    timezone,\n    name,\n    label,\n    inputRef,\n    readOnly,\n    disabled,\n    localeText\n  } = props;\n  const utils = useUtils();\n  const internalInputRef = React.useRef(null);\n  const labelId = useId();\n  const isToolbarHidden = (_innerSlotProps$toolb = innerSlotProps == null || (_innerSlotProps$toolb2 = innerSlotProps.toolbar) == null ? void 0 : _innerSlotProps$toolb2.hidden) != null ? _innerSlotProps$toolb : false;\n  const {\n    open,\n    actions,\n    layoutProps,\n    renderCurrentView,\n    fieldProps: pickerFieldProps\n  } = usePicker(_extends({}, pickerParams, {\n    props,\n    inputRef: internalInputRef,\n    autoFocusView: true,\n    additionalViewProps: {},\n    wrapperVariant: 'mobile'\n  }));\n  const Field = slots.field;\n  const fieldProps = useSlotProps({\n    elementType: Field,\n    externalSlotProps: innerSlotProps == null ? void 0 : innerSlotProps.field,\n    additionalProps: _extends({}, pickerFieldProps, isToolbarHidden && {\n      id: labelId\n    }, !(disabled || readOnly) && {\n      onClick: actions.onOpen,\n      onKeyDown: onSpaceOrEnter(actions.onOpen)\n    }, {\n      readOnly: readOnly != null ? readOnly : true,\n      disabled,\n      className,\n      sx,\n      format,\n      formatDensity,\n      timezone,\n      label,\n      name\n    }),\n    ownerState: props\n  });\n\n  // TODO: Move to `useSlotProps` when https://github.com/mui/material-ui/pull/35088 will be merged\n  fieldProps.inputProps = _extends({}, fieldProps.inputProps, {\n    'aria-label': getOpenDialogAriaText(pickerFieldProps.value, utils)\n  });\n  const slotsForField = _extends({\n    textField: slots.textField\n  }, fieldProps.slots);\n  const Layout = (_slots$layout = slots.layout) != null ? _slots$layout : PickersLayout;\n  const handleInputRef = useForkRef(internalInputRef, fieldProps.inputRef, inputRef);\n  let labelledById = labelId;\n  if (isToolbarHidden) {\n    if (label) {\n      labelledById = `${labelId}-label`;\n    } else {\n      labelledById = undefined;\n    }\n  }\n  const slotProps = _extends({}, innerSlotProps, {\n    toolbar: _extends({}, innerSlotProps == null ? void 0 : innerSlotProps.toolbar, {\n      titleId: labelId\n    }),\n    mobilePaper: _extends({\n      'aria-labelledby': labelledById\n    }, innerSlotProps == null ? void 0 : innerSlotProps.mobilePaper)\n  });\n  const renderPicker = () => /*#__PURE__*/_jsxs(LocalizationProvider, {\n    localeText: localeText,\n    children: [/*#__PURE__*/_jsx(Field, _extends({}, fieldProps, {\n      slots: slotsForField,\n      slotProps: slotProps,\n      inputRef: handleInputRef\n    })), /*#__PURE__*/_jsx(PickersModalDialog, _extends({}, actions, {\n      open: open,\n      slots: slots,\n      slotProps: slotProps,\n      children: /*#__PURE__*/_jsx(Layout, _extends({}, layoutProps, slotProps == null ? void 0 : slotProps.layout, {\n        slots: slots,\n        slotProps: slotProps,\n        children: renderCurrentView()\n      }))\n    }))]\n  });\n  return {\n    renderPicker\n  };\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,uBAAuB,CAAC;AACpD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,KAAK,MAAM,kBAAkB;AACpC,SAASC,kBAAkB,QAAQ,qCAAqC;AACxE,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,oBAAoB,QAAQ,+BAA+B;AACpE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,eAAe,GAAGC,IAAI,IAAI;EACrC,IAAIC,qBAAqB,EAAEC,sBAAsB,EAAEC,aAAa;EAChE,IAAI;MACAC,KAAK;MACLC;IACF,CAAC,GAAGL,IAAI;IACRM,YAAY,GAAGvB,6BAA6B,CAACiB,IAAI,EAAEhB,SAAS,CAAC;EAC/D,MAAM;IACJuB,KAAK;IACLC,SAAS,EAAEC,cAAc;IACzBC,SAAS;IACTC,EAAE;IACFC,MAAM;IACNC,aAAa;IACbC,QAAQ;IACRC,IAAI;IACJC,KAAK;IACLC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC;EACF,CAAC,GAAGhB,KAAK;EACT,MAAMiB,KAAK,GAAG7B,QAAQ,CAAC,CAAC;EACxB,MAAM8B,gBAAgB,GAAGrC,KAAK,CAACsC,MAAM,CAAC,IAAI,CAAC;EAC3C,MAAMC,OAAO,GAAGpC,KAAK,CAAC,CAAC;EACvB,MAAMqC,eAAe,GAAG,CAACxB,qBAAqB,GAAGQ,cAAc,IAAI,IAAI,IAAI,CAACP,sBAAsB,GAAGO,cAAc,CAACiB,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGxB,sBAAsB,CAACyB,MAAM,KAAK,IAAI,GAAG1B,qBAAqB,GAAG,KAAK;EACtN,MAAM;IACJ2B,IAAI;IACJC,OAAO;IACPC,WAAW;IACXC,iBAAiB;IACjBC,UAAU,EAAEC;EACd,CAAC,GAAG3C,SAAS,CAACR,QAAQ,CAAC,CAAC,CAAC,EAAEwB,YAAY,EAAE;IACvCF,KAAK;IACLa,QAAQ,EAAEK,gBAAgB;IAC1BY,aAAa,EAAE,IAAI;IACnBC,mBAAmB,EAAE,CAAC,CAAC;IACvBC,cAAc,EAAE;EAClB,CAAC,CAAC,CAAC;EACH,MAAMC,KAAK,GAAG9B,KAAK,CAAC+B,KAAK;EACzB,MAAMN,UAAU,GAAG9C,YAAY,CAAC;IAC9BqD,WAAW,EAAEF,KAAK;IAClBG,iBAAiB,EAAE/B,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAAC6B,KAAK;IACzEG,eAAe,EAAE3D,QAAQ,CAAC,CAAC,CAAC,EAAEmD,gBAAgB,EAAER,eAAe,IAAI;MACjEiB,EAAE,EAAElB;IACN,CAAC,EAAE,EAAEL,QAAQ,IAAID,QAAQ,CAAC,IAAI;MAC5ByB,OAAO,EAAEd,OAAO,CAACe,MAAM;MACvBC,SAAS,EAAEtD,cAAc,CAACsC,OAAO,CAACe,MAAM;IAC1C,CAAC,EAAE;MACD1B,QAAQ,EAAEA,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAG,IAAI;MAC5CC,QAAQ;MACRT,SAAS;MACTC,EAAE;MACFC,MAAM;MACNC,aAAa;MACbC,QAAQ;MACRE,KAAK;MACLD;IACF,CAAC,CAAC;IACF+B,UAAU,EAAE1C;EACd,CAAC,CAAC;;EAEF;EACA4B,UAAU,CAACe,UAAU,GAAGjE,QAAQ,CAAC,CAAC,CAAC,EAAEkD,UAAU,CAACe,UAAU,EAAE;IAC1D,YAAY,EAAE1C,qBAAqB,CAAC4B,gBAAgB,CAACe,KAAK,EAAE3B,KAAK;EACnE,CAAC,CAAC;EACF,MAAM4B,aAAa,GAAGnE,QAAQ,CAAC;IAC7BoE,SAAS,EAAE3C,KAAK,CAAC2C;EACnB,CAAC,EAAElB,UAAU,CAACzB,KAAK,CAAC;EACpB,MAAM4C,MAAM,GAAG,CAAChD,aAAa,GAAGI,KAAK,CAAC6C,MAAM,KAAK,IAAI,GAAGjD,aAAa,GAAGT,aAAa;EACrF,MAAM2D,cAAc,GAAGlE,UAAU,CAACmC,gBAAgB,EAAEU,UAAU,CAACf,QAAQ,EAAEA,QAAQ,CAAC;EAClF,IAAIqC,YAAY,GAAG9B,OAAO;EAC1B,IAAIC,eAAe,EAAE;IACnB,IAAIT,KAAK,EAAE;MACTsC,YAAY,GAAG,GAAG9B,OAAO,QAAQ;IACnC,CAAC,MAAM;MACL8B,YAAY,GAAGC,SAAS;IAC1B;EACF;EACA,MAAM/C,SAAS,GAAG1B,QAAQ,CAAC,CAAC,CAAC,EAAE2B,cAAc,EAAE;IAC7CiB,OAAO,EAAE5C,QAAQ,CAAC,CAAC,CAAC,EAAE2B,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACiB,OAAO,EAAE;MAC9E8B,OAAO,EAAEhC;IACX,CAAC,CAAC;IACFiC,WAAW,EAAE3E,QAAQ,CAAC;MACpB,iBAAiB,EAAEwE;IACrB,CAAC,EAAE7C,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACgD,WAAW;EACjE,CAAC,CAAC;EACF,MAAMC,YAAY,GAAGA,CAAA,KAAM,aAAa5D,KAAK,CAACL,oBAAoB,EAAE;IAClE2B,UAAU,EAAEA,UAAU;IACtBuC,QAAQ,EAAE,CAAC,aAAa/D,IAAI,CAACyC,KAAK,EAAEvD,QAAQ,CAAC,CAAC,CAAC,EAAEkD,UAAU,EAAE;MAC3DzB,KAAK,EAAE0C,aAAa;MACpBzC,SAAS,EAAEA,SAAS;MACpBS,QAAQ,EAAEoC;IACZ,CAAC,CAAC,CAAC,EAAE,aAAazD,IAAI,CAACP,kBAAkB,EAAEP,QAAQ,CAAC,CAAC,CAAC,EAAE+C,OAAO,EAAE;MAC/DD,IAAI,EAAEA,IAAI;MACVrB,KAAK,EAAEA,KAAK;MACZC,SAAS,EAAEA,SAAS;MACpBmD,QAAQ,EAAE,aAAa/D,IAAI,CAACuD,MAAM,EAAErE,QAAQ,CAAC,CAAC,CAAC,EAAEgD,WAAW,EAAEtB,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC4C,MAAM,EAAE;QAC3G7C,KAAK,EAAEA,KAAK;QACZC,SAAS,EAAEA,SAAS;QACpBmD,QAAQ,EAAE5B,iBAAiB,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACF,OAAO;IACL2B;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}