{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isAfter\n * @category Common Helpers\n * @summary Is the first date after the second one?\n *\n * @description\n * Is the first date after the second one?\n *\n * @param {Date|Number} date - the date that should be after the other one to return true\n * @param {Date|Number} dateToCompare - the date to compare with\n * @returns {Boolean} the first date is after the second date\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Is 10 July 1989 after 11 February 1987?\n * const result = isAfter(new Date(1989, 6, 10), new Date(1987, 1, 11))\n * //=> true\n */\nexport default function isAfter(dirtyDate, dirtyDateToCompare) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var dateToCompare = toDate(dirtyDateToCompare);\n  return date.getTime() > dateToCompare.getTime();\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "isAfter", "dirtyDate", "dirtyDateToCompare", "arguments", "date", "dateToCompare", "getTime"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/isAfter/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isAfter\n * @category Common Helpers\n * @summary Is the first date after the second one?\n *\n * @description\n * Is the first date after the second one?\n *\n * @param {Date|Number} date - the date that should be after the other one to return true\n * @param {Date|Number} dateToCompare - the date to compare with\n * @returns {Boolean} the first date is after the second date\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Is 10 July 1989 after 11 February 1987?\n * const result = isAfter(new Date(1989, 6, 10), new Date(1987, 1, 11))\n * //=> true\n */\nexport default function isAfter(dirtyDate, dirtyDateToCompare) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var dateToCompare = toDate(dirtyDateToCompare);\n  return date.getTime() > dateToCompare.getTime();\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,OAAOA,CAACC,SAAS,EAAEC,kBAAkB,EAAE;EAC7DH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGN,MAAM,CAACG,SAAS,CAAC;EAC5B,IAAII,aAAa,GAAGP,MAAM,CAACI,kBAAkB,CAAC;EAC9C,OAAOE,IAAI,CAACE,OAAO,CAAC,CAAC,GAAGD,aAAa,CAACC,OAAO,CAAC,CAAC;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}