const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Subscription = require('../models/Subscription');
const Student = require('../models/Student');
const { protect, adminOnly, adminOrCoach } = require('../middleware/auth');

const router = express.Router();

// Apply protection to all routes
router.use(protect);

// @desc    Get all subscriptions
// @route   GET /api/subscriptions
// @access  Private (Admin/Coach)
router.get('/', [
  adminOrCoach,
  query('page').optional().isInt({ min: 1 }).withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('حد النتائج يجب أن يكون بين 1 و 100'),
  query('status').optional().isIn(['active', 'expired', 'expiring_soon', 'inactive']).withMessage('حالة الاشتراك غير صحيحة'),
  query('paymentStatus').optional().isIn(['paid', 'pending', 'overdue', 'cancelled']).withMessage('حالة الدفع غير صحيحة'),
  query('subscriptionType').optional().isIn(['monthly', 'quarterly', 'semi-annual', 'annual']).withMessage('نوع الاشتراك غير صحيح')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const { status, paymentStatus, subscriptionType } = req.query;

    // Build query
    let query = {};

    if (paymentStatus) {
      query.paymentStatus = paymentStatus;
    }

    if (subscriptionType) {
      query.subscriptionType = subscriptionType;
    }

    // Handle status filtering
    if (status) {
      const now = new Date();
      switch (status) {
        case 'active':
          query.isActive = true;
          query.endDate = { $gte: now };
          break;
        case 'expired':
          query.isActive = true;
          query.endDate = { $lt: now };
          break;
        case 'expiring_soon':
          const weekFromNow = new Date();
          weekFromNow.setDate(weekFromNow.getDate() + 7);
          query.isActive = true;
          query.endDate = { $gte: now, $lte: weekFromNow };
          break;
        case 'inactive':
          query.isActive = false;
          break;
      }
    }

    // Get subscriptions with pagination
    const subscriptions = await Subscription.find(query)
      .populate('student', 'fullName studentId phone beltLevel')
      .populate('createdBy', 'fullName')
      .populate('updatedBy', 'fullName')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // Get total count for pagination
    const total = await Subscription.countDocuments(query);

    res.status(200).json({
      success: true,
      count: subscriptions.length,
      total,
      pagination: {
        page,
        limit,
        pages: Math.ceil(total / limit)
      },
      data: subscriptions
    });

  } catch (error) {
    console.error('Get subscriptions error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Get single subscription
// @route   GET /api/subscriptions/:id
// @access  Private (Admin/Coach)
router.get('/:id', adminOrCoach, async (req, res) => {
  try {
    const subscription = await Subscription.findById(req.params.id)
      .populate('student', 'fullName studentId phone beltLevel')
      .populate('createdBy', 'fullName')
      .populate('updatedBy', 'fullName');

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'الاشتراك غير موجود'
      });
    }

    res.status(200).json({
      success: true,
      data: subscription
    });

  } catch (error) {
    console.error('Get subscription error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Create new subscription
// @route   POST /api/subscriptions
// @access  Private (Admin only)
router.post('/', [
  adminOnly,
  body('student')
    .isMongoId()
    .withMessage('معرف الطالب غير صحيح'),
  body('subscriptionType')
    .isIn(['monthly', 'quarterly', 'semi-annual', 'annual'])
    .withMessage('نوع الاشتراك يجب أن يكون شهري، ربع سنوي، نصف سنوي، أو سنوي'),
  body('startDate')
    .isISO8601()
    .withMessage('تاريخ بداية الاشتراك يجب أن يكون تاريخ صحيح'),
  body('endDate')
    .isISO8601()
    .withMessage('تاريخ نهاية الاشتراك يجب أن يكون تاريخ صحيح'),
  body('amount')
    .isFloat({ min: 0 })
    .withMessage('مبلغ الاشتراك يجب أن يكون رقم أكبر من أو يساوي صفر'),
  body('paymentStatus')
    .optional()
    .isIn(['paid', 'pending', 'overdue', 'cancelled'])
    .withMessage('حالة الدفع غير صحيحة'),
  body('paymentMethod')
    .optional()
    .isIn(['cash', 'card', 'bank_transfer', 'online'])
    .withMessage('طريقة الدفع غير صحيحة')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    // Check if student exists
    const student = await Student.findById(req.body.student);
    if (!student) {
      return res.status(404).json({
        success: false,
        message: 'الطالب غير موجود'
      });
    }

    // Create subscription
    const subscription = await Subscription.create({
      ...req.body,
      createdBy: req.user._id
    });

    // Populate the created subscription
    await subscription.populate('student', 'fullName studentId phone beltLevel');

    res.status(201).json({
      success: true,
      message: 'تم إنشاء الاشتراك بنجاح',
      data: subscription
    });

  } catch (error) {
    console.error('Create subscription error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Update subscription
// @route   PUT /api/subscriptions/:id
// @access  Private (Admin only)
router.put('/:id', [
  adminOnly,
  body('subscriptionType')
    .optional()
    .isIn(['monthly', 'quarterly', 'semi-annual', 'annual'])
    .withMessage('نوع الاشتراك يجب أن يكون شهري، ربع سنوي، نصف سنوي، أو سنوي'),
  body('startDate')
    .optional()
    .isISO8601()
    .withMessage('تاريخ بداية الاشتراك يجب أن يكون تاريخ صحيح'),
  body('endDate')
    .optional()
    .isISO8601()
    .withMessage('تاريخ نهاية الاشتراك يجب أن يكون تاريخ صحيح'),
  body('amount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('مبلغ الاشتراك يجب أن يكون رقم أكبر من أو يساوي صفر'),
  body('paymentStatus')
    .optional()
    .isIn(['paid', 'pending', 'overdue', 'cancelled'])
    .withMessage('حالة الدفع غير صحيحة'),
  body('paymentMethod')
    .optional()
    .isIn(['cash', 'card', 'bank_transfer', 'online'])
    .withMessage('طريقة الدفع غير صحيحة')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const subscription = await Subscription.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedBy: req.user._id },
      { new: true, runValidators: true }
    ).populate('student', 'fullName studentId phone beltLevel');

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'الاشتراك غير موجود'
      });
    }

    res.status(200).json({
      success: true,
      message: 'تم تحديث الاشتراك بنجاح',
      data: subscription
    });

  } catch (error) {
    console.error('Update subscription error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Mark subscription as paid
// @route   PUT /api/subscriptions/:id/pay
// @access  Private (Admin only)
router.put('/:id/pay', [
  adminOnly,
  body('paymentMethod')
    .isIn(['cash', 'card', 'bank_transfer', 'online'])
    .withMessage('طريقة الدفع غير صحيحة'),
  body('receiptNumber')
    .optional()
    .isLength({ max: 100 })
    .withMessage('رقم الإيصال يجب أن يكون أقل من 100 حرف')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const subscription = await Subscription.findById(req.params.id);

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'الاشتراك غير موجود'
      });
    }

    const { paymentMethod, receiptNumber } = req.body;
    await subscription.markAsPaid(paymentMethod, receiptNumber, req.user._id);

    // Populate and return updated subscription
    await subscription.populate('student', 'fullName studentId phone beltLevel');

    res.status(200).json({
      success: true,
      message: 'تم تسجيل الدفع بنجاح',
      data: subscription
    });

  } catch (error) {
    console.error('Mark as paid error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Cancel subscription
// @route   PUT /api/subscriptions/:id/cancel
// @access  Private (Admin only)
router.put('/:id/cancel', adminOnly, async (req, res) => {
  try {
    const subscription = await Subscription.findById(req.params.id);

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'الاشتراك غير موجود'
      });
    }

    await subscription.cancel(req.user._id);

    // Populate and return updated subscription
    await subscription.populate('student', 'fullName studentId phone beltLevel');

    res.status(200).json({
      success: true,
      message: 'تم إلغاء الاشتراك بنجاح',
      data: subscription
    });

  } catch (error) {
    console.error('Cancel subscription error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Get expiring subscriptions
// @route   GET /api/subscriptions/expiring/:days
// @access  Private (Admin/Coach)
router.get('/expiring/:days', adminOrCoach, async (req, res) => {
  try {
    const days = parseInt(req.params.days) || 7;

    if (days < 1 || days > 365) {
      return res.status(400).json({
        success: false,
        message: 'عدد الأيام يجب أن يكون بين 1 و 365'
      });
    }

    const subscriptions = await Subscription.getExpiringSoon(days);

    res.status(200).json({
      success: true,
      count: subscriptions.length,
      data: subscriptions
    });

  } catch (error) {
    console.error('Get expiring subscriptions error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Get expired subscriptions
// @route   GET /api/subscriptions/expired/list
// @access  Private (Admin/Coach)
router.get('/expired/list', adminOrCoach, async (req, res) => {
  try {
    const subscriptions = await Subscription.getExpired();

    res.status(200).json({
      success: true,
      count: subscriptions.length,
      data: subscriptions
    });

  } catch (error) {
    console.error('Get expired subscriptions error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Get active subscriptions
// @route   GET /api/subscriptions/active/list
// @access  Private (Admin/Coach)
router.get('/active/list', adminOrCoach, async (req, res) => {
  try {
    const subscriptions = await Subscription.getActive();

    res.status(200).json({
      success: true,
      count: subscriptions.length,
      data: subscriptions
    });

  } catch (error) {
    console.error('Get active subscriptions error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Get student subscriptions
// @route   GET /api/subscriptions/student/:studentId
// @access  Private (Admin/Coach)
router.get('/student/:studentId', adminOrCoach, async (req, res) => {
  try {
    const subscriptions = await Subscription.find({ student: req.params.studentId })
      .populate('student', 'fullName studentId phone beltLevel')
      .populate('createdBy', 'fullName')
      .populate('updatedBy', 'fullName')
      .sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      count: subscriptions.length,
      data: subscriptions
    });

  } catch (error) {
    console.error('Get student subscriptions error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

module.exports = router;
