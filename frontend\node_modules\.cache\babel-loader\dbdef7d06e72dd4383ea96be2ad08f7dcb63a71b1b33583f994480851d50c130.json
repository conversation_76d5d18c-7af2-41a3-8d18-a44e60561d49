{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"eeee'ที่แล้วเวลา' p\",\n  yesterday: \"'เมื่อวานนี้เวลา' p\",\n  today: \"'วันนี้เวลา' p\",\n  tomorrow: \"'พรุ่งนี้เวลา' p\",\n  nextWeek: \"eeee 'เวลา' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/th/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: \"eeee'ที่แล้วเวลา' p\",\n  yesterday: \"'เมื่อวานนี้เวลา' p\",\n  today: \"'วันนี้เวลา' p\",\n  tomorrow: \"'พรุ่งนี้เวลา' p\",\n  nextWeek: \"eeee 'เวลา' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,qBAAqB;EAC/BC,SAAS,EAAE,qBAAqB;EAChCC,KAAK,EAAE,gBAAgB;EACvBC,QAAQ,EAAE,kBAAkB;EAC5BC,QAAQ,EAAE,eAAe;EACzBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC9E,OAAOX,oBAAoB,CAACQ,KAAK,CAAC;AACpC,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}