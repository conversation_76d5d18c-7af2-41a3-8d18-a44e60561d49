import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box } from '@mui/material';
import { useAuth } from './contexts/AuthContext';
import Layout from './components/Layout/Layout';
import Login from './pages/Auth/Login';
import Dashboard from './pages/Dashboard/Dashboard';
import Students from './pages/Students/Students';
import StudentDetails from './pages/Students/StudentDetails';
import AddStudent from './pages/Students/AddStudent';
import EditStudent from './pages/Students/EditStudent';
import Subscriptions from './pages/Subscriptions/Subscriptions';
import SubscriptionDetails from './pages/Subscriptions/SubscriptionDetails';
import AddSubscription from './pages/Subscriptions/AddSubscription';
import EditSubscription from './pages/Subscriptions/EditSubscription';
import Attendance from './pages/Attendance/Attendance';
import AttendanceDetails from './pages/Attendance/AttendanceDetails';
import AddAttendance from './pages/Attendance/AddAttendance';
import EditAttendance from './pages/Attendance/EditAttendance';
import Reports from './pages/Reports/Reports';
import Settings from './pages/Settings/Settings';
import Profile from './pages/Profile/Profile';
import LoadingSpinner from './components/Common/LoadingSpinner';
import ProtectedRoute from './components/Auth/ProtectedRoute';
import AdminRoute from './components/Auth/AdminRoute';

function App() {
  const { user, loading } = useAuth();

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      <Routes>
        {/* Public Routes */}
        <Route 
          path="/login" 
          element={
            user ? <Navigate to="/dashboard" replace /> : <Login />
          } 
        />

        {/* Protected Routes */}
        <Route 
          path="/*" 
          element={
            <ProtectedRoute>
              <Layout>
                <Routes>
                  {/* Dashboard */}
                  <Route path="/dashboard" element={<Dashboard />} />
                  
                  {/* Students */}
                  <Route path="/students" element={<Students />} />
                  <Route path="/students/:id" element={<StudentDetails />} />
                  <Route 
                    path="/students/add" 
                    element={
                      <AdminRoute>
                        <AddStudent />
                      </AdminRoute>
                    } 
                  />
                  <Route 
                    path="/students/:id/edit" 
                    element={
                      <AdminRoute>
                        <EditStudent />
                      </AdminRoute>
                    } 
                  />
                  
                  {/* Subscriptions */}
                  <Route path="/subscriptions" element={<Subscriptions />} />
                  <Route path="/subscriptions/:id" element={<SubscriptionDetails />} />
                  <Route 
                    path="/subscriptions/add" 
                    element={
                      <AdminRoute>
                        <AddSubscription />
                      </AdminRoute>
                    } 
                  />
                  <Route 
                    path="/subscriptions/:id/edit" 
                    element={
                      <AdminRoute>
                        <EditSubscription />
                      </AdminRoute>
                    } 
                  />
                  
                  {/* Attendance */}
                  <Route path="/attendance" element={<Attendance />} />
                  <Route path="/attendance/:id" element={<AttendanceDetails />} />
                  <Route path="/attendance/add" element={<AddAttendance />} />
                  <Route path="/attendance/:id/edit" element={<EditAttendance />} />
                  
                  {/* Reports */}
                  <Route path="/reports" element={<Reports />} />
                  
                  {/* Settings */}
                  <Route 
                    path="/settings" 
                    element={
                      <AdminRoute>
                        <Settings />
                      </AdminRoute>
                    } 
                  />
                  
                  {/* Profile */}
                  <Route path="/profile" element={<Profile />} />
                  
                  {/* Default redirect */}
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />
                  <Route path="*" element={<Navigate to="/dashboard" replace />} />
                </Routes>
              </Layout>
            </ProtectedRoute>
          } 
        />
      </Routes>
    </Box>
  );
}

export default App;
