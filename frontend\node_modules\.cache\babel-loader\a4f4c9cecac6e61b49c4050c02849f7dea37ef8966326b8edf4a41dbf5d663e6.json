{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['да н.э.', 'н.э.'],\n  abbreviated: ['да н. э.', 'н. э.'],\n  wide: ['да нашай эры', 'нашай эры']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1-ы кв.', '2-і кв.', '3-і кв.', '4-ы кв.'],\n  wide: ['1-ы квартал', '2-і квартал', '3-і квартал', '4-ы квартал']\n};\nvar monthValues = {\n  narrow: ['С', 'Л', 'С', 'К', 'Т', 'Ч', 'Л', 'Ж', 'В', 'К', 'Л', 'С'],\n  abbreviated: ['студз.', 'лют.', 'сак.', 'крас.', 'трав.', 'чэрв.', 'ліп.', 'жн.', 'вер.', 'кастр.', 'ліст.', 'сьнеж.'],\n  wide: ['студзень', 'люты', 'сакавік', 'красавік', 'травень', 'чэрвень', 'ліпень', 'жнівень', 'верасень', 'кастрычнік', 'лістапад', 'сьнежань']\n};\nvar formattingMonthValues = {\n  narrow: ['С', 'Л', 'С', 'К', 'Т', 'Ч', 'Л', 'Ж', 'В', 'К', 'Л', 'С'],\n  abbreviated: ['студз.', 'лют.', 'сак.', 'крас.', 'трав.', 'чэрв.', 'ліп.', 'жн.', 'вер.', 'кастр.', 'ліст.', 'сьнеж.'],\n  wide: ['студзеня', 'лютага', 'сакавіка', 'красавіка', 'траўня', 'чэрвеня', 'ліпеня', 'жніўня', 'верасня', 'кастрычніка', 'лістапада', 'сьнежня']\n};\nvar dayValues = {\n  narrow: ['Н', 'П', 'А', 'С', 'Ч', 'П', 'С'],\n  short: ['нд', 'пн', 'аў', 'ср', 'чц', 'пт', 'сб'],\n  abbreviated: ['нядз', 'пан', 'аўт', 'сер', 'чаць', 'пят', 'суб'],\n  wide: ['нядзеля', 'панядзелак', 'аўторак', 'серада', 'чацьвер', 'пятніца', 'субота']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'поўн.',\n    noon: 'поўд.',\n    morning: 'ран.',\n    afternoon: 'дзень',\n    evening: 'веч.',\n    night: 'ноч'\n  },\n  abbreviated: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'поўн.',\n    noon: 'поўд.',\n    morning: 'ран.',\n    afternoon: 'дзень',\n    evening: 'веч.',\n    night: 'ноч'\n  },\n  wide: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'поўнач',\n    noon: 'поўдзень',\n    morning: 'раніца',\n    afternoon: 'дзень',\n    evening: 'вечар',\n    night: 'ноч'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'поўн.',\n    noon: 'поўд.',\n    morning: 'ран.',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночы'\n  },\n  abbreviated: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'поўн.',\n    noon: 'поўд.',\n    morning: 'ран.',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночы'\n  },\n  wide: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'поўнач',\n    noon: 'поўдзень',\n    morning: 'раніцы',\n    afternoon: 'дня',\n    evening: 'вечара',\n    night: 'ночы'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var unit = String(options === null || options === void 0 ? void 0 : options.unit);\n  var number = Number(dirtyNumber);\n  var suffix;\n\n  /** Though it's an incorrect ordinal form of a date we use it here for consistency with other similar locales (ru, uk)\n   *  For date-month combinations should be used `d` formatter.\n   *  Correct:   `d MMMM` (4 верасня)\n   *  Incorrect: `do MMMM` (4-га верасня)\n   *\n   *  But following the consistency leads to mistakes for literal uses of `do` formatter (ordinal day of month).\n   *  So for phrase \"5th day of month\" (`do дзень месяца`)\n   *  library will produce:            `5-га дзень месяца`\n   *  but correct spelling should be:  `5-ы дзень месяца`\n   *\n   *  So I guess there should be a stand-alone and a formatting version of \"day of month\" formatters\n   */\n  if (unit === 'date') {\n    suffix = '-га';\n  } else if (unit === 'hour' || unit === 'minute' || unit === 'second') {\n    suffix = '-я';\n  } else {\n    suffix = (number % 10 === 2 || number % 10 === 3) && number % 100 !== 12 && number % 100 !== 13 ? '-і' : '-ы';\n  }\n  return number + suffix;\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'any',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "formattingMonthValues", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "options", "unit", "String", "number", "Number", "suffix", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "formattingValues", "defaultFormattingWidth", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/be-tarask/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['да н.э.', 'н.э.'],\n  abbreviated: ['да н. э.', 'н. э.'],\n  wide: ['да нашай эры', 'нашай эры']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1-ы кв.', '2-і кв.', '3-і кв.', '4-ы кв.'],\n  wide: ['1-ы квартал', '2-і квартал', '3-і квартал', '4-ы квартал']\n};\nvar monthValues = {\n  narrow: ['С', 'Л', 'С', 'К', 'Т', 'Ч', 'Л', 'Ж', 'В', 'К', 'Л', 'С'],\n  abbreviated: ['студз.', 'лют.', 'сак.', 'крас.', 'трав.', 'чэрв.', 'ліп.', 'жн.', 'вер.', 'кастр.', 'ліст.', 'сьнеж.'],\n  wide: ['студзень', 'люты', 'сакавік', 'красавік', 'травень', 'чэрвень', 'ліпень', 'жнівень', 'верасень', 'кастрычнік', 'лістапад', 'сьнежань']\n};\nvar formattingMonthValues = {\n  narrow: ['С', 'Л', 'С', 'К', 'Т', 'Ч', 'Л', 'Ж', 'В', 'К', 'Л', 'С'],\n  abbreviated: ['студз.', 'лют.', 'сак.', 'крас.', 'трав.', 'чэрв.', 'ліп.', 'жн.', 'вер.', 'кастр.', 'ліст.', 'сьнеж.'],\n  wide: ['студзеня', 'лютага', 'сакавіка', 'красавіка', 'траўня', 'чэрвеня', 'ліпеня', 'жніўня', 'верасня', 'кастрычніка', 'лістапада', 'сьнежня']\n};\nvar dayValues = {\n  narrow: ['Н', 'П', 'А', 'С', 'Ч', 'П', 'С'],\n  short: ['нд', 'пн', 'аў', 'ср', 'чц', 'пт', 'сб'],\n  abbreviated: ['нядз', 'пан', 'аўт', 'сер', 'чаць', 'пят', 'суб'],\n  wide: ['нядзеля', 'панядзелак', 'аўторак', 'серада', 'чацьвер', 'пятніца', 'субота']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'поўн.',\n    noon: 'поўд.',\n    morning: 'ран.',\n    afternoon: 'дзень',\n    evening: 'веч.',\n    night: 'ноч'\n  },\n  abbreviated: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'поўн.',\n    noon: 'поўд.',\n    morning: 'ран.',\n    afternoon: 'дзень',\n    evening: 'веч.',\n    night: 'ноч'\n  },\n  wide: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'поўнач',\n    noon: 'поўдзень',\n    morning: 'раніца',\n    afternoon: 'дзень',\n    evening: 'вечар',\n    night: 'ноч'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'поўн.',\n    noon: 'поўд.',\n    morning: 'ран.',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночы'\n  },\n  abbreviated: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'поўн.',\n    noon: 'поўд.',\n    morning: 'ран.',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночы'\n  },\n  wide: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'поўнач',\n    noon: 'поўдзень',\n    morning: 'раніцы',\n    afternoon: 'дня',\n    evening: 'вечара',\n    night: 'ночы'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var unit = String(options === null || options === void 0 ? void 0 : options.unit);\n  var number = Number(dirtyNumber);\n  var suffix;\n\n  /** Though it's an incorrect ordinal form of a date we use it here for consistency with other similar locales (ru, uk)\n   *  For date-month combinations should be used `d` formatter.\n   *  Correct:   `d MMMM` (4 верасня)\n   *  Incorrect: `do MMMM` (4-га верасня)\n   *\n   *  But following the consistency leads to mistakes for literal uses of `do` formatter (ordinal day of month).\n   *  So for phrase \"5th day of month\" (`do дзень месяца`)\n   *  library will produce:            `5-га дзень месяца`\n   *  but correct spelling should be:  `5-ы дзень месяца`\n   *\n   *  So I guess there should be a stand-alone and a formatting version of \"day of month\" formatters\n   */\n  if (unit === 'date') {\n    suffix = '-га';\n  } else if (unit === 'hour' || unit === 'minute' || unit === 'second') {\n    suffix = '-я';\n  } else {\n    suffix = (number % 10 === 2 || number % 10 === 3) && number % 100 !== 12 && number % 100 !== 13 ? '-і' : '-ы';\n  }\n  return number + suffix;\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'any',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;EAC3BC,WAAW,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC;EAClCC,IAAI,EAAE,CAAC,cAAc,EAAE,WAAW;AACpC,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EACzDC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa;AACnE,CAAC;AACD,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;EACtHC,IAAI,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,EAAE,UAAU;AAC/I,CAAC;AACD,IAAIG,qBAAqB,GAAG;EAC1BL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;EACtHC,IAAI,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE,WAAW,EAAE,SAAS;AACjJ,CAAC;AACD,IAAII,SAAS,GAAG;EACdN,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CO,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDN,WAAW,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;EAChEC,IAAI,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ;AACrF,CAAC;AACD,IAAIM,eAAe,GAAG;EACpBR,MAAM,EAAE;IACNS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDf,WAAW,EAAE;IACXQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDd,IAAI,EAAE;IACJO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BjB,MAAM,EAAE;IACNS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDf,WAAW,EAAE;IACXQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDd,IAAI,EAAE;IACJO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,OAAO,EAAE;EAC/D,IAAIC,IAAI,GAAGC,MAAM,CAACF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACC,IAAI,CAAC;EACjF,IAAIE,MAAM,GAAGC,MAAM,CAACL,WAAW,CAAC;EAChC,IAAIM,MAAM;;EAEV;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAIJ,IAAI,KAAK,MAAM,EAAE;IACnBI,MAAM,GAAG,KAAK;EAChB,CAAC,MAAM,IAAIJ,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ,EAAE;IACpEI,MAAM,GAAG,IAAI;EACf,CAAC,MAAM;IACLA,MAAM,GAAG,CAACF,MAAM,GAAG,EAAE,KAAK,CAAC,IAAIA,MAAM,GAAG,EAAE,KAAK,CAAC,KAAKA,MAAM,GAAG,GAAG,KAAK,EAAE,IAAIA,MAAM,GAAG,GAAG,KAAK,EAAE,GAAG,IAAI,GAAG,IAAI;EAC/G;EACA,OAAOA,MAAM,GAAGE,MAAM;AACxB,CAAC;AACD,IAAIC,QAAQ,GAAG;EACbR,aAAa,EAAEA,aAAa;EAC5BS,GAAG,EAAE7B,eAAe,CAAC;IACnB8B,MAAM,EAAE7B,SAAS;IACjB8B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAEhC,eAAe,CAAC;IACvB8B,MAAM,EAAEzB,aAAa;IACrB0B,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAElC,eAAe,CAAC;IACrB8B,MAAM,EAAExB,WAAW;IACnByB,YAAY,EAAE,MAAM;IACpBI,gBAAgB,EAAE5B,qBAAqB;IACvC6B,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EACFC,GAAG,EAAErC,eAAe,CAAC;IACnB8B,MAAM,EAAEtB,SAAS;IACjBuB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFO,SAAS,EAAEtC,eAAe,CAAC;IACzB8B,MAAM,EAAEpB,eAAe;IACvBqB,YAAY,EAAE,KAAK;IACnBI,gBAAgB,EAAEhB,yBAAyB;IAC3CiB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeR,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}