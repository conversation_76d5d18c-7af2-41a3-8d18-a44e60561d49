{"ast": null, "code": "import { usePickerValue } from './usePickerValue';\nimport { usePickerViews } from './usePickerViews';\nimport { usePickerLayoutProps } from './usePickerLayoutProps';\nimport { buildWarning } from '../../utils/warning';\nconst warnRenderInputIsDefined = buildWarning(['The `renderInput` prop has been removed in version 6.0 of the Date and Time Pickers.', 'You can replace it with the `textField` component slot in most cases.', 'For more information, please have a look at the migration guide (https://mui.com/x/migration/migration-pickers-v5/#input-renderer-required-in-v5).']);\nexport const usePicker = ({\n  props,\n  valueManager,\n  valueType,\n  wrapperVariant,\n  inputRef,\n  additionalViewProps,\n  validator,\n  autoFocusView\n}) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.renderInput != null) {\n      warnRenderInputIsDefined();\n    }\n  }\n  const pickerValueResponse = usePickerValue({\n    props,\n    valueManager,\n    valueType,\n    wrapperVariant,\n    validator\n  });\n  const pickerViewsResponse = usePickerViews({\n    props,\n    inputRef,\n    additionalViewProps,\n    autoFocusView,\n    propsFromPickerValue: pickerValueResponse.viewProps\n  });\n  const pickerLayoutResponse = usePickerLayoutProps({\n    props,\n    wrapperVariant,\n    propsFromPickerValue: pickerValueResponse.layoutProps,\n    propsFromPickerViews: pickerViewsResponse.layoutProps\n  });\n  return {\n    // Picker value\n    open: pickerValueResponse.open,\n    actions: pickerValueResponse.actions,\n    fieldProps: pickerValueResponse.fieldProps,\n    // Picker views\n    renderCurrentView: pickerViewsResponse.renderCurrentView,\n    hasUIView: pickerViewsResponse.hasUIView,\n    shouldRestoreFocus: pickerViewsResponse.shouldRestoreFocus,\n    // Picker layout\n    layoutProps: pickerLayoutResponse.layoutProps\n  };\n};", "map": {"version": 3, "names": ["usePickerValue", "usePickerViews", "usePickerLayoutProps", "buildWarning", "warnRenderInputIsDefined", "usePicker", "props", "valueManager", "valueType", "wrapperVariant", "inputRef", "additionalViewProps", "validator", "autoFocusView", "process", "env", "NODE_ENV", "renderInput", "pickerValueResponse", "pickerViewsResponse", "propsFromPickerValue", "viewProps", "pickerLayoutResponse", "layoutProps", "propsFromPickerViews", "open", "actions", "fieldProps", "renderCurrentView", "hasUIView", "shouldRestoreFocus"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/x-date-pickers/internals/hooks/usePicker/usePicker.js"], "sourcesContent": ["import { usePickerValue } from './usePickerValue';\nimport { usePickerViews } from './usePickerViews';\nimport { usePickerLayoutProps } from './usePickerLayoutProps';\nimport { buildWarning } from '../../utils/warning';\nconst warnRenderInputIsDefined = buildWarning(['The `renderInput` prop has been removed in version 6.0 of the Date and Time Pickers.', 'You can replace it with the `textField` component slot in most cases.', 'For more information, please have a look at the migration guide (https://mui.com/x/migration/migration-pickers-v5/#input-renderer-required-in-v5).']);\nexport const usePicker = ({\n  props,\n  valueManager,\n  valueType,\n  wrapperVariant,\n  inputRef,\n  additionalViewProps,\n  validator,\n  autoFocusView\n}) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.renderInput != null) {\n      warnRenderInputIsDefined();\n    }\n  }\n  const pickerValueResponse = usePickerValue({\n    props,\n    valueManager,\n    valueType,\n    wrapperVariant,\n    validator\n  });\n  const pickerViewsResponse = usePickerViews({\n    props,\n    inputRef,\n    additionalViewProps,\n    autoFocusView,\n    propsFromPickerValue: pickerValueResponse.viewProps\n  });\n  const pickerLayoutResponse = usePickerLayoutProps({\n    props,\n    wrapperVariant,\n    propsFromPickerValue: pickerValueResponse.layoutProps,\n    propsFromPickerViews: pickerViewsResponse.layoutProps\n  });\n  return {\n    // Picker value\n    open: pickerValueResponse.open,\n    actions: pickerValueResponse.actions,\n    fieldProps: pickerValueResponse.fieldProps,\n    // Picker views\n    renderCurrentView: pickerViewsResponse.renderCurrentView,\n    hasUIView: pickerViewsResponse.hasUIView,\n    shouldRestoreFocus: pickerViewsResponse.shouldRestoreFocus,\n    // Picker layout\n    layoutProps: pickerLayoutResponse.layoutProps\n  };\n};"], "mappings": "AAAA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,oBAAoB,QAAQ,wBAAwB;AAC7D,SAASC,YAAY,QAAQ,qBAAqB;AAClD,MAAMC,wBAAwB,GAAGD,YAAY,CAAC,CAAC,sFAAsF,EAAE,uEAAuE,EAAE,oJAAoJ,CAAC,CAAC;AACtW,OAAO,MAAME,SAAS,GAAGA,CAAC;EACxBC,KAAK;EACLC,YAAY;EACZC,SAAS;EACTC,cAAc;EACdC,QAAQ;EACRC,mBAAmB;EACnBC,SAAS;EACTC;AACF,CAAC,KAAK;EACJ,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIV,KAAK,CAACW,WAAW,IAAI,IAAI,EAAE;MAC7Bb,wBAAwB,CAAC,CAAC;IAC5B;EACF;EACA,MAAMc,mBAAmB,GAAGlB,cAAc,CAAC;IACzCM,KAAK;IACLC,YAAY;IACZC,SAAS;IACTC,cAAc;IACdG;EACF,CAAC,CAAC;EACF,MAAMO,mBAAmB,GAAGlB,cAAc,CAAC;IACzCK,KAAK;IACLI,QAAQ;IACRC,mBAAmB;IACnBE,aAAa;IACbO,oBAAoB,EAAEF,mBAAmB,CAACG;EAC5C,CAAC,CAAC;EACF,MAAMC,oBAAoB,GAAGpB,oBAAoB,CAAC;IAChDI,KAAK;IACLG,cAAc;IACdW,oBAAoB,EAAEF,mBAAmB,CAACK,WAAW;IACrDC,oBAAoB,EAAEL,mBAAmB,CAACI;EAC5C,CAAC,CAAC;EACF,OAAO;IACL;IACAE,IAAI,EAAEP,mBAAmB,CAACO,IAAI;IAC9BC,OAAO,EAAER,mBAAmB,CAACQ,OAAO;IACpCC,UAAU,EAAET,mBAAmB,CAACS,UAAU;IAC1C;IACAC,iBAAiB,EAAET,mBAAmB,CAACS,iBAAiB;IACxDC,SAAS,EAAEV,mBAAmB,CAACU,SAAS;IACxCC,kBAAkB,EAAEX,mBAAmB,CAACW,kBAAkB;IAC1D;IACAP,WAAW,EAAED,oBAAoB,CAACC;EACpC,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}