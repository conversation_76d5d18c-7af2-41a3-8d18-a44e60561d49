{"ast": null, "code": "import { getDefaultOptions } from \"../_lib/defaultOptions/index.js\";\nimport differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport format from \"../format/index.js\";\nimport defaultLocale from \"../_lib/defaultLocale/index.js\";\nimport subMilliseconds from \"../subMilliseconds/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport getTimezoneOffsetInMilliseconds from \"../_lib/getTimezoneOffsetInMilliseconds/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name formatRelative\n * @category Common Helpers\n * @summary Represent the date in words relative to the given base date.\n *\n * @description\n * Represent the date in words relative to the given base date.\n *\n * | Distance to the base date | Result                    |\n * |---------------------------|---------------------------|\n * | Previous 6 days           | last Sunday at 04:30 AM   |\n * | Last day                  | yesterday at 04:30 AM     |\n * | Same day                  | today at 04:30 AM         |\n * | Next day                  | tomorrow at 04:30 AM      |\n * | Next 6 days               | Sunday at 04:30 AM        |\n * | Other                     | 12/31/2017                |\n *\n * @param {Date|Number} date - the date to format\n * @param {Date|Number} baseDate - the date to compare with\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @returns {String} the date in words\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `baseDate` must not be Invalid Date\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n * @throws {RangeError} `options.locale` must contain `localize` property\n * @throws {RangeError} `options.locale` must contain `formatLong` property\n * @throws {RangeError} `options.locale` must contain `formatRelative` property\n *\n * @example\n * // Represent the date of 6 days ago in words relative to the given base date. In this example, today is Wednesday\n * const result = formatRelative(addDays(new Date(), -6), new Date())\n * //=> \"last Thursday at 12:45 AM\"\n */\nexport default function formatRelative(dirtyDate, dirtyBaseDate, options) {\n  var _ref, _options$locale, _ref2, _ref3, _ref4, _options$weekStartsOn, _options$locale2, _options$locale2$opti, _defaultOptions$local, _defaultOptions$local2;\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var baseDate = toDate(dirtyBaseDate);\n  var defaultOptions = getDefaultOptions();\n  var locale = (_ref = (_options$locale = options === null || options === void 0 ? void 0 : options.locale) !== null && _options$locale !== void 0 ? _options$locale : defaultOptions.locale) !== null && _ref !== void 0 ? _ref : defaultLocale;\n  var weekStartsOn = toInteger((_ref2 = (_ref3 = (_ref4 = (_options$weekStartsOn = options === null || options === void 0 ? void 0 : options.weekStartsOn) !== null && _options$weekStartsOn !== void 0 ? _options$weekStartsOn : options === null || options === void 0 ? void 0 : (_options$locale2 = options.locale) === null || _options$locale2 === void 0 ? void 0 : (_options$locale2$opti = _options$locale2.options) === null || _options$locale2$opti === void 0 ? void 0 : _options$locale2$opti.weekStartsOn) !== null && _ref4 !== void 0 ? _ref4 : defaultOptions.weekStartsOn) !== null && _ref3 !== void 0 ? _ref3 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 ? void 0 : (_defaultOptions$local2 = _defaultOptions$local.options) === null || _defaultOptions$local2 === void 0 ? void 0 : _defaultOptions$local2.weekStartsOn) !== null && _ref2 !== void 0 ? _ref2 : 0);\n  if (!locale.localize) {\n    throw new RangeError('locale must contain localize property');\n  }\n  if (!locale.formatLong) {\n    throw new RangeError('locale must contain formatLong property');\n  }\n  if (!locale.formatRelative) {\n    throw new RangeError('locale must contain formatRelative property');\n  }\n  var diff = differenceInCalendarDays(date, baseDate);\n  if (isNaN(diff)) {\n    throw new RangeError('Invalid time value');\n  }\n  var token;\n  if (diff < -6) {\n    token = 'other';\n  } else if (diff < -1) {\n    token = 'lastWeek';\n  } else if (diff < 0) {\n    token = 'yesterday';\n  } else if (diff < 1) {\n    token = 'today';\n  } else if (diff < 2) {\n    token = 'tomorrow';\n  } else if (diff < 7) {\n    token = 'nextWeek';\n  } else {\n    token = 'other';\n  }\n  var utcDate = subMilliseconds(date, getTimezoneOffsetInMilliseconds(date));\n  var utcBaseDate = subMilliseconds(baseDate, getTimezoneOffsetInMilliseconds(baseDate));\n  var formatStr = locale.formatRelative(token, utcDate, utcBaseDate, {\n    locale: locale,\n    weekStartsOn: weekStartsOn\n  });\n  return format(date, formatStr, {\n    locale: locale,\n    weekStartsOn: weekStartsOn\n  });\n}", "map": {"version": 3, "names": ["getDefaultOptions", "differenceInCalendarDays", "format", "defaultLocale", "subMilliseconds", "toDate", "getTimezoneOffsetInMilliseconds", "requiredArgs", "toInteger", "formatRelative", "dirtyDate", "dirtyBaseDate", "options", "_ref", "_options$locale", "_ref2", "_ref3", "_ref4", "_options$weekStartsOn", "_options$locale2", "_options$locale2$opti", "_defaultOptions$local", "_defaultOptions$local2", "arguments", "date", "baseDate", "defaultOptions", "locale", "weekStartsOn", "localize", "RangeError", "formatLong", "diff", "isNaN", "token", "utcDate", "utcBaseDate", "formatStr"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/formatRelative/index.js"], "sourcesContent": ["import { getDefaultOptions } from \"../_lib/defaultOptions/index.js\";\nimport differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport format from \"../format/index.js\";\nimport defaultLocale from \"../_lib/defaultLocale/index.js\";\nimport subMilliseconds from \"../subMilliseconds/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport getTimezoneOffsetInMilliseconds from \"../_lib/getTimezoneOffsetInMilliseconds/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name formatRelative\n * @category Common Helpers\n * @summary Represent the date in words relative to the given base date.\n *\n * @description\n * Represent the date in words relative to the given base date.\n *\n * | Distance to the base date | Result                    |\n * |---------------------------|---------------------------|\n * | Previous 6 days           | last Sunday at 04:30 AM   |\n * | Last day                  | yesterday at 04:30 AM     |\n * | Same day                  | today at 04:30 AM         |\n * | Next day                  | tomorrow at 04:30 AM      |\n * | Next 6 days               | Sunday at 04:30 AM        |\n * | Other                     | 12/31/2017                |\n *\n * @param {Date|Number} date - the date to format\n * @param {Date|Number} baseDate - the date to compare with\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @returns {String} the date in words\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `baseDate` must not be Invalid Date\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n * @throws {RangeError} `options.locale` must contain `localize` property\n * @throws {RangeError} `options.locale` must contain `formatLong` property\n * @throws {RangeError} `options.locale` must contain `formatRelative` property\n *\n * @example\n * // Represent the date of 6 days ago in words relative to the given base date. In this example, today is Wednesday\n * const result = formatRelative(addDays(new Date(), -6), new Date())\n * //=> \"last Thursday at 12:45 AM\"\n */\nexport default function formatRelative(dirtyDate, dirtyBaseDate, options) {\n  var _ref, _options$locale, _ref2, _ref3, _ref4, _options$weekStartsOn, _options$locale2, _options$locale2$opti, _defaultOptions$local, _defaultOptions$local2;\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var baseDate = toDate(dirtyBaseDate);\n  var defaultOptions = getDefaultOptions();\n  var locale = (_ref = (_options$locale = options === null || options === void 0 ? void 0 : options.locale) !== null && _options$locale !== void 0 ? _options$locale : defaultOptions.locale) !== null && _ref !== void 0 ? _ref : defaultLocale;\n  var weekStartsOn = toInteger((_ref2 = (_ref3 = (_ref4 = (_options$weekStartsOn = options === null || options === void 0 ? void 0 : options.weekStartsOn) !== null && _options$weekStartsOn !== void 0 ? _options$weekStartsOn : options === null || options === void 0 ? void 0 : (_options$locale2 = options.locale) === null || _options$locale2 === void 0 ? void 0 : (_options$locale2$opti = _options$locale2.options) === null || _options$locale2$opti === void 0 ? void 0 : _options$locale2$opti.weekStartsOn) !== null && _ref4 !== void 0 ? _ref4 : defaultOptions.weekStartsOn) !== null && _ref3 !== void 0 ? _ref3 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 ? void 0 : (_defaultOptions$local2 = _defaultOptions$local.options) === null || _defaultOptions$local2 === void 0 ? void 0 : _defaultOptions$local2.weekStartsOn) !== null && _ref2 !== void 0 ? _ref2 : 0);\n  if (!locale.localize) {\n    throw new RangeError('locale must contain localize property');\n  }\n  if (!locale.formatLong) {\n    throw new RangeError('locale must contain formatLong property');\n  }\n  if (!locale.formatRelative) {\n    throw new RangeError('locale must contain formatRelative property');\n  }\n  var diff = differenceInCalendarDays(date, baseDate);\n  if (isNaN(diff)) {\n    throw new RangeError('Invalid time value');\n  }\n  var token;\n  if (diff < -6) {\n    token = 'other';\n  } else if (diff < -1) {\n    token = 'lastWeek';\n  } else if (diff < 0) {\n    token = 'yesterday';\n  } else if (diff < 1) {\n    token = 'today';\n  } else if (diff < 2) {\n    token = 'tomorrow';\n  } else if (diff < 7) {\n    token = 'nextWeek';\n  } else {\n    token = 'other';\n  }\n  var utcDate = subMilliseconds(date, getTimezoneOffsetInMilliseconds(date));\n  var utcBaseDate = subMilliseconds(baseDate, getTimezoneOffsetInMilliseconds(baseDate));\n  var formatStr = locale.formatRelative(token, utcDate, utcBaseDate, {\n    locale: locale,\n    weekStartsOn: weekStartsOn\n  });\n  return format(date, formatStr, {\n    locale: locale,\n    weekStartsOn: weekStartsOn\n  });\n}"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,iCAAiC;AACnE,OAAOC,wBAAwB,MAAM,sCAAsC;AAC3E,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,+BAA+B,MAAM,kDAAkD;AAC9F,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,SAAS,MAAM,4BAA4B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,cAAcA,CAACC,SAAS,EAAEC,aAAa,EAAEC,OAAO,EAAE;EACxE,IAAIC,IAAI,EAAEC,eAAe,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,qBAAqB,EAAEC,qBAAqB,EAAEC,sBAAsB;EAC7Jf,YAAY,CAAC,CAAC,EAAEgB,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGnB,MAAM,CAACK,SAAS,CAAC;EAC5B,IAAIe,QAAQ,GAAGpB,MAAM,CAACM,aAAa,CAAC;EACpC,IAAIe,cAAc,GAAG1B,iBAAiB,CAAC,CAAC;EACxC,IAAI2B,MAAM,GAAG,CAACd,IAAI,GAAG,CAACC,eAAe,GAAGF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACe,MAAM,MAAM,IAAI,IAAIb,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAGY,cAAc,CAACC,MAAM,MAAM,IAAI,IAAId,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAGV,aAAa;EAC9O,IAAIyB,YAAY,GAAGpB,SAAS,CAAC,CAACO,KAAK,GAAG,CAACC,KAAK,GAAG,CAACC,KAAK,GAAG,CAACC,qBAAqB,GAAGN,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACgB,YAAY,MAAM,IAAI,IAAIV,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGN,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACO,gBAAgB,GAAGP,OAAO,CAACe,MAAM,MAAM,IAAI,IAAIR,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,qBAAqB,GAAGD,gBAAgB,CAACP,OAAO,MAAM,IAAI,IAAIQ,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACQ,YAAY,MAAM,IAAI,IAAIX,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGS,cAAc,CAACE,YAAY,MAAM,IAAI,IAAIZ,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACK,qBAAqB,GAAGK,cAAc,CAACC,MAAM,MAAM,IAAI,IAAIN,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,sBAAsB,GAAGD,qBAAqB,CAACT,OAAO,MAAM,IAAI,IAAIU,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACM,YAAY,MAAM,IAAI,IAAIb,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAAC,CAAC;EAC34B,IAAI,CAACY,MAAM,CAACE,QAAQ,EAAE;IACpB,MAAM,IAAIC,UAAU,CAAC,uCAAuC,CAAC;EAC/D;EACA,IAAI,CAACH,MAAM,CAACI,UAAU,EAAE;IACtB,MAAM,IAAID,UAAU,CAAC,yCAAyC,CAAC;EACjE;EACA,IAAI,CAACH,MAAM,CAAClB,cAAc,EAAE;IAC1B,MAAM,IAAIqB,UAAU,CAAC,6CAA6C,CAAC;EACrE;EACA,IAAIE,IAAI,GAAG/B,wBAAwB,CAACuB,IAAI,EAAEC,QAAQ,CAAC;EACnD,IAAIQ,KAAK,CAACD,IAAI,CAAC,EAAE;IACf,MAAM,IAAIF,UAAU,CAAC,oBAAoB,CAAC;EAC5C;EACA,IAAII,KAAK;EACT,IAAIF,IAAI,GAAG,CAAC,CAAC,EAAE;IACbE,KAAK,GAAG,OAAO;EACjB,CAAC,MAAM,IAAIF,IAAI,GAAG,CAAC,CAAC,EAAE;IACpBE,KAAK,GAAG,UAAU;EACpB,CAAC,MAAM,IAAIF,IAAI,GAAG,CAAC,EAAE;IACnBE,KAAK,GAAG,WAAW;EACrB,CAAC,MAAM,IAAIF,IAAI,GAAG,CAAC,EAAE;IACnBE,KAAK,GAAG,OAAO;EACjB,CAAC,MAAM,IAAIF,IAAI,GAAG,CAAC,EAAE;IACnBE,KAAK,GAAG,UAAU;EACpB,CAAC,MAAM,IAAIF,IAAI,GAAG,CAAC,EAAE;IACnBE,KAAK,GAAG,UAAU;EACpB,CAAC,MAAM;IACLA,KAAK,GAAG,OAAO;EACjB;EACA,IAAIC,OAAO,GAAG/B,eAAe,CAACoB,IAAI,EAAElB,+BAA+B,CAACkB,IAAI,CAAC,CAAC;EAC1E,IAAIY,WAAW,GAAGhC,eAAe,CAACqB,QAAQ,EAAEnB,+BAA+B,CAACmB,QAAQ,CAAC,CAAC;EACtF,IAAIY,SAAS,GAAGV,MAAM,CAAClB,cAAc,CAACyB,KAAK,EAAEC,OAAO,EAAEC,WAAW,EAAE;IACjET,MAAM,EAAEA,MAAM;IACdC,YAAY,EAAEA;EAChB,CAAC,CAAC;EACF,OAAO1B,MAAM,CAACsB,IAAI,EAAEa,SAAS,EAAE;IAC7BV,MAAM,EAAEA,MAAM;IACdC,YAAY,EAAEA;EAChB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}