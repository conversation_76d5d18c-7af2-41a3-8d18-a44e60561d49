import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Alert,
  IconButton,
  InputAdornment,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Home as HomeIcon,
  ContactEmergency as ContactEmergencyIcon,
  SportsKabaddi as SportsIcon
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { ar } from 'date-fns/locale';
import LoadingSpinner from '../../components/Common/LoadingSpinner';
import { studentService } from '../../services/studentService';

const EditStudent = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [formData, setFormData] = useState({
    fullName: '',
    dateOfBirth: null,
    gender: '',
    phone: '',
    email: '',
    address: {
      street: '',
      city: '',
      postalCode: ''
    },
    emergencyContact: {
      name: '',
      relationship: '',
      phone: ''
    },
    beltLevel: 'white',
    isActive: true,
    medicalConditions: '',
    notes: ''
  });

  const [errors, setErrors] = useState({});

  const beltLevels = [
    { value: 'white', label: 'أبيض' },
    { value: 'yellow', label: 'أصفر' },
    { value: 'orange', label: 'برتقالي' },
    { value: 'green', label: 'أخضر' },
    { value: 'blue', label: 'أزرق' },
    { value: 'brown', label: 'بني' },
    { value: 'red', label: 'أحمر' },
    { value: 'black-1st', label: 'أسود - الدان الأول' },
    { value: 'black-2nd', label: 'أسود - الدان الثاني' },
    { value: 'black-3rd', label: 'أسود - الدان الثالث' }
  ];

  const relationshipOptions = [
    'والد',
    'والدة',
    'أخ',
    'أخت',
    'جد',
    'جدة',
    'عم',
    'عمة',
    'خال',
    'خالة',
    'زوج',
    'زوجة',
    'صديق',
    'أخرى'
  ];

  useEffect(() => {
    fetchStudent();
  }, [id]);

  const fetchStudent = async () => {
    try {
      setLoading(true);
      const response = await studentService.getStudent(id);
      const student = response.data;

      setFormData({
        fullName: student.fullName || '',
        dateOfBirth: student.dateOfBirth ? new Date(student.dateOfBirth) : null,
        gender: student.gender || '',
        phone: student.phone || '',
        email: student.email || '',
        address: {
          street: student.address?.street || '',
          city: student.address?.city || '',
          postalCode: student.address?.postalCode || ''
        },
        emergencyContact: {
          name: student.emergencyContact?.name || '',
          relationship: student.emergencyContact?.relationship || '',
          phone: student.emergencyContact?.phone || ''
        },
        beltLevel: student.beltLevel || 'white',
        isActive: student.isActive !== undefined ? student.isActive : true,
        medicalConditions: student.medicalConditions || '',
        notes: student.notes || ''
      });

      setError('');
    } catch (err) {
      setError('فشل في تحميل بيانات الطالب');
      console.error('Error fetching student:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Required fields validation
    if (!formData.fullName.trim()) {
      newErrors.fullName = 'الاسم الكامل مطلوب';
    }

    if (!formData.dateOfBirth) {
      newErrors.dateOfBirth = 'تاريخ الميلاد مطلوب';
    }

    if (!formData.gender) {
      newErrors.gender = 'الجنس مطلوب';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'رقم الهاتف مطلوب';
    } else if (!/^[0-9+\-\s()]+$/.test(formData.phone)) {
      newErrors.phone = 'رقم الهاتف غير صحيح';
    }

    if (formData.email && !/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح';
    }

    if (!formData.emergencyContact.name.trim()) {
      newErrors['emergencyContact.name'] = 'اسم جهة الاتصال في حالات الطوارئ مطلوب';
    }

    if (!formData.emergencyContact.relationship.trim()) {
      newErrors['emergencyContact.relationship'] = 'صلة القرابة مطلوبة';
    }

    if (!formData.emergencyContact.phone.trim()) {
      newErrors['emergencyContact.phone'] = 'رقم هاتف جهة الاتصال في حالات الطوارئ مطلوب';
    } else if (!/^[0-9+\-\s()]+$/.test(formData.emergencyContact.phone)) {
      newErrors['emergencyContact.phone'] = 'رقم الهاتف غير صحيح';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      setError('يرجى تصحيح الأخطاء في النموذج');
      return;
    }

    try {
      setSaving(true);
      setError('');
      setSuccess('');

      await studentService.updateStudent(id, formData);

      setSuccess('تم تحديث بيانات الطالب بنجاح');

      // Redirect after success
      setTimeout(() => {
        navigate(`/students/${id}`);
      }, 2000);

    } catch (err) {
      console.error('Error updating student:', err);
      if (err.response?.data?.message) {
        setError(err.response.data.message);
      } else if (err.response?.data?.errors) {
        const apiErrors = {};
        err.response.data.errors.forEach(error => {
          apiErrors[error.path] = error.msg;
        });
        setErrors(apiErrors);
        setError('يرجى تصحيح الأخطاء في النموذج');
      } else {
        setError('حدث خطأ أثناء تحديث بيانات الطالب');
      }
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return <LoadingSpinner message="جاري تحميل بيانات الطالب..." />;
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ar}>
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <IconButton
            onClick={() => navigate(`/students/${id}`)}
            sx={{ mr: 2 }}
          >
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
            تعديل بيانات الطالب
          </Typography>
        </Box>

        {/* Alerts */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 3 }}>
            {success}
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            {/* Personal Information */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      المعلومات الشخصية
                    </Typography>
                  </Box>

                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="الاسم الكامل *"
                        value={formData.fullName}
                        onChange={(e) => handleInputChange('fullName', e.target.value)}
                        error={!!errors.fullName}
                        helperText={errors.fullName}
                        sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <DatePicker
                        label="تاريخ الميلاد *"
                        value={formData.dateOfBirth}
                        onChange={(date) => handleInputChange('dateOfBirth', date)}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            fullWidth
                            error={!!errors.dateOfBirth}
                            helperText={errors.dateOfBirth}
                            sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                          />
                        )}
                        maxDate={new Date()}
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <FormControl fullWidth error={!!errors.gender}>
                        <InputLabel>الجنس *</InputLabel>
                        <Select
                          value={formData.gender}
                          label="الجنس *"
                          onChange={(e) => handleInputChange('gender', e.target.value)}
                          sx={{ borderRadius: 2 }}
                        >
                          <MenuItem value="male">ذكر</MenuItem>
                          <MenuItem value="female">أنثى</MenuItem>
                        </Select>
                        {errors.gender && (
                          <Typography variant="caption" color="error" sx={{ mt: 0.5, mx: 1.75 }}>
                            {errors.gender}
                          </Typography>
                        )}
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <FormControl fullWidth>
                        <InputLabel>مستوى الحزام</InputLabel>
                        <Select
                          value={formData.beltLevel}
                          label="مستوى الحزام"
                          onChange={(e) => handleInputChange('beltLevel', e.target.value)}
                          sx={{ borderRadius: 2 }}
                        >
                          {beltLevels.map((belt) => (
                            <MenuItem key={belt.value} value={belt.value}>
                              {belt.label}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>

                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.isActive}
                            onChange={(e) => handleInputChange('isActive', e.target.checked)}
                            color="primary"
                          />
                        }
                        label="الطالب نشط"
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Contact Information */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <PhoneIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      معلومات الاتصال
                    </Typography>
                  </Box>

                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="رقم الهاتف *"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        error={!!errors.phone}
                        helperText={errors.phone}
                        placeholder="مثال: +966501234567"
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <PhoneIcon />
                            </InputAdornment>
                          ),
                        }}
                        sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="البريد الإلكتروني"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        error={!!errors.email}
                        helperText={errors.email}
                        placeholder="مثال: <EMAIL>"
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <EmailIcon />
                            </InputAdornment>
                          ),
                        }}
                        sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Address Information */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <HomeIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      معلومات العنوان
                    </Typography>
                  </Box>

                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="الشارع"
                        value={formData.address.street}
                        onChange={(e) => handleInputChange('address.street', e.target.value)}
                        multiline
                        rows={2}
                        sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="المدينة"
                        value={formData.address.city}
                        onChange={(e) => handleInputChange('address.city', e.target.value)}
                        sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="الرمز البريدي"
                        value={formData.address.postalCode}
                        onChange={(e) => handleInputChange('address.postalCode', e.target.value)}
                        sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Emergency Contact */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <ContactEmergencyIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      جهة الاتصال في حالات الطوارئ
                    </Typography>
                  </Box>

                  <Grid container spacing={2}>
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label="الاسم *"
                        value={formData.emergencyContact.name}
                        onChange={(e) => handleInputChange('emergencyContact.name', e.target.value)}
                        error={!!errors['emergencyContact.name']}
                        helperText={errors['emergencyContact.name']}
                        sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                      />
                    </Grid>

                    <Grid item xs={12} md={4}>
                      <FormControl fullWidth error={!!errors['emergencyContact.relationship']}>
                        <InputLabel>صلة القرابة *</InputLabel>
                        <Select
                          value={formData.emergencyContact.relationship}
                          label="صلة القرابة *"
                          onChange={(e) => handleInputChange('emergencyContact.relationship', e.target.value)}
                          sx={{ borderRadius: 2 }}
                        >
                          {relationshipOptions.map((relation) => (
                            <MenuItem key={relation} value={relation}>
                              {relation}
                            </MenuItem>
                          ))}
                        </Select>
                        {errors['emergencyContact.relationship'] && (
                          <Typography variant="caption" color="error" sx={{ mt: 0.5, mx: 1.75 }}>
                            {errors['emergencyContact.relationship']}
                          </Typography>
                        )}
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label="رقم الهاتف *"
                        value={formData.emergencyContact.phone}
                        onChange={(e) => handleInputChange('emergencyContact.phone', e.target.value)}
                        error={!!errors['emergencyContact.phone']}
                        helperText={errors['emergencyContact.phone']}
                        placeholder="مثال: +966501234567"
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <PhoneIcon />
                            </InputAdornment>
                          ),
                        }}
                        sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Additional Information */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <SportsIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      معلومات إضافية
                    </Typography>
                  </Box>

                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="الحالات الطبية"
                        value={formData.medicalConditions}
                        onChange={(e) => handleInputChange('medicalConditions', e.target.value)}
                        multiline
                        rows={3}
                        placeholder="أي حالات طبية أو حساسية يجب معرفتها..."
                        sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="ملاحظات"
                        value={formData.notes}
                        onChange={(e) => handleInputChange('notes', e.target.value)}
                        multiline
                        rows={3}
                        placeholder="أي ملاحظات إضافية..."
                        sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Action Buttons */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                <Button
                  variant="outlined"
                  onClick={() => navigate(`/students/${id}`)}
                  sx={{ borderRadius: 2, minWidth: 120 }}
                >
                  إلغاء
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  startIcon={<SaveIcon />}
                  disabled={saving}
                  sx={{ borderRadius: 2, minWidth: 120 }}
                >
                  {saving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </Box>
    </LocalizationProvider>
  );
};

export default EditStudent;
