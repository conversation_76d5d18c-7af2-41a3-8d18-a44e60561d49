{"ast": null, "code": "import startOfWeek from \"../startOfWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name startOfISOWeek\n * @category ISO Week Helpers\n * @summary Return the start of an ISO week for the given date.\n *\n * @description\n * Return the start of an ISO week for the given date.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the start of an ISO week\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The start of an ISO week for 2 September 2014 11:55:00:\n * const result = startOfISOWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Mon Sep 01 2014 00:00:00\n */\nexport default function startOfISOWeek(dirtyDate) {\n  requiredArgs(1, arguments);\n  return startOfWeek(dirtyDate, {\n    weekStartsOn: 1\n  });\n}", "map": {"version": 3, "names": ["startOfWeek", "requiredArgs", "startOfISOWeek", "dirtyDate", "arguments", "weekStartsOn"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/startOfISOWeek/index.js"], "sourcesContent": ["import startOfWeek from \"../startOfWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name startOfISOWeek\n * @category ISO Week Helpers\n * @summary Return the start of an ISO week for the given date.\n *\n * @description\n * Return the start of an ISO week for the given date.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the start of an ISO week\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The start of an ISO week for 2 September 2014 11:55:00:\n * const result = startOfISOWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Mon Sep 01 2014 00:00:00\n */\nexport default function startOfISOWeek(dirtyDate) {\n  requiredArgs(1, arguments);\n  return startOfWeek(dirtyDate, {\n    weekStartsOn: 1\n  });\n}"], "mappings": "AAAA,OAAOA,WAAW,MAAM,yBAAyB;AACjD,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,cAAcA,CAACC,SAAS,EAAE;EAChDF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,OAAOJ,WAAW,CAACG,SAAS,EAAE;IAC5BE,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}