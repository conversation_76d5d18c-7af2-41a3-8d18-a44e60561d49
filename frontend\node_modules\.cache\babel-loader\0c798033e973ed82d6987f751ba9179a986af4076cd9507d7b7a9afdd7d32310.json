{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"items\", \"changeImportance\", \"isLandscape\", \"onChange\", \"isValid\"],\n  _excluded2 = [\"getValue\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport List from '@mui/material/List';\nimport ListItem from '@mui/material/ListItem';\nimport Chip from '@mui/material/Chip';\nimport { VIEW_HEIGHT } from '../internals/constants/dimensions';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * Demos:\n *\n * - [Shortcuts](https://mui.com/x/react-date-pickers/shortcuts/)\n *\n * API:\n *\n * - [PickersShortcuts API](https://mui.com/x/api/date-pickers/pickers-shortcuts/)\n */\nfunction PickersShortcuts(props) {\n  const {\n      items,\n      changeImportance,\n      onChange,\n      isValid\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  if (items == null || items.length === 0) {\n    return null;\n  }\n  const resolvedItems = items.map(_ref => {\n    let {\n        getValue\n      } = _ref,\n      item = _objectWithoutPropertiesLoose(_ref, _excluded2);\n    const newValue = getValue({\n      isValid\n    });\n    return {\n      label: item.label,\n      onClick: () => {\n        onChange(newValue, changeImportance, item);\n      },\n      disabled: !isValid(newValue)\n    };\n  });\n  return /*#__PURE__*/_jsx(List, _extends({\n    dense: true,\n    sx: [{\n      maxHeight: VIEW_HEIGHT,\n      maxWidth: 200,\n      overflow: 'auto'\n    }, ...(Array.isArray(other.sx) ? other.sx : [other.sx])]\n  }, other, {\n    children: resolvedItems.map(item => {\n      return /*#__PURE__*/_jsx(ListItem, {\n        children: /*#__PURE__*/_jsx(Chip, _extends({}, item))\n      }, item.label);\n    })\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? PickersShortcuts.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Importance of the change when picking a shortcut:\n   * - \"accept\": fires `onChange`, fires `onAccept` and closes the picker.\n   * - \"set\": fires `onChange` but do not fire `onAccept` and does not close the picker.\n   * @default \"accept\"\n   */\n  changeImportance: PropTypes.oneOf(['accept', 'set']),\n  className: PropTypes.string,\n  component: PropTypes.elementType,\n  /**\n   * If `true`, compact vertical padding designed for keyboard and mouse input is used for\n   * the list and list items.\n   * The prop is available to descendant components as the `dense` context.\n   * @default false\n   */\n  dense: PropTypes.bool,\n  /**\n   * If `true`, vertical padding is removed from the list.\n   * @default false\n   */\n  disablePadding: PropTypes.bool,\n  isLandscape: PropTypes.bool.isRequired,\n  isValid: PropTypes.func.isRequired,\n  /**\n   * Ordered array of shortcuts to display.\n   * If empty, does not display the shortcuts.\n   * @default `[]`\n   */\n  items: PropTypes.arrayOf(PropTypes.shape({\n    getValue: PropTypes.func.isRequired,\n    label: PropTypes.string.isRequired\n  })),\n  onChange: PropTypes.func.isRequired,\n  style: PropTypes.object,\n  /**\n   * The content of the subheader, normally `ListSubheader`.\n   */\n  subheader: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport { PickersShortcuts };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "_excluded2", "React", "PropTypes", "List", "ListItem", "Chip", "VIEW_HEIGHT", "jsx", "_jsx", "PickersShortcuts", "props", "items", "changeImportance", "onChange", "<PERSON><PERSON><PERSON><PERSON>", "other", "length", "resolvedItems", "map", "_ref", "getValue", "item", "newValue", "label", "onClick", "disabled", "dense", "sx", "maxHeight", "max<PERSON><PERSON><PERSON>", "overflow", "Array", "isArray", "children", "process", "env", "NODE_ENV", "propTypes", "oneOf", "className", "string", "component", "elementType", "bool", "disablePadding", "isLandscape", "isRequired", "func", "arrayOf", "shape", "style", "object", "subheader", "node", "oneOfType"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/x-date-pickers/PickersShortcuts/PickersShortcuts.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"items\", \"changeImportance\", \"isLandscape\", \"onChange\", \"isValid\"],\n  _excluded2 = [\"getValue\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport List from '@mui/material/List';\nimport ListItem from '@mui/material/ListItem';\nimport Chip from '@mui/material/Chip';\nimport { VIEW_HEIGHT } from '../internals/constants/dimensions';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * Demos:\n *\n * - [Shortcuts](https://mui.com/x/react-date-pickers/shortcuts/)\n *\n * API:\n *\n * - [PickersShortcuts API](https://mui.com/x/api/date-pickers/pickers-shortcuts/)\n */\nfunction PickersShortcuts(props) {\n  const {\n      items,\n      changeImportance,\n      onChange,\n      isValid\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  if (items == null || items.length === 0) {\n    return null;\n  }\n  const resolvedItems = items.map(_ref => {\n    let {\n        getValue\n      } = _ref,\n      item = _objectWithoutPropertiesLoose(_ref, _excluded2);\n    const newValue = getValue({\n      isValid\n    });\n    return {\n      label: item.label,\n      onClick: () => {\n        onChange(newValue, changeImportance, item);\n      },\n      disabled: !isValid(newValue)\n    };\n  });\n  return /*#__PURE__*/_jsx(List, _extends({\n    dense: true,\n    sx: [{\n      maxHeight: VIEW_HEIGHT,\n      maxWidth: 200,\n      overflow: 'auto'\n    }, ...(Array.isArray(other.sx) ? other.sx : [other.sx])]\n  }, other, {\n    children: resolvedItems.map(item => {\n      return /*#__PURE__*/_jsx(ListItem, {\n        children: /*#__PURE__*/_jsx(Chip, _extends({}, item))\n      }, item.label);\n    })\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? PickersShortcuts.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Importance of the change when picking a shortcut:\n   * - \"accept\": fires `onChange`, fires `onAccept` and closes the picker.\n   * - \"set\": fires `onChange` but do not fire `onAccept` and does not close the picker.\n   * @default \"accept\"\n   */\n  changeImportance: PropTypes.oneOf(['accept', 'set']),\n  className: PropTypes.string,\n  component: PropTypes.elementType,\n  /**\n   * If `true`, compact vertical padding designed for keyboard and mouse input is used for\n   * the list and list items.\n   * The prop is available to descendant components as the `dense` context.\n   * @default false\n   */\n  dense: PropTypes.bool,\n  /**\n   * If `true`, vertical padding is removed from the list.\n   * @default false\n   */\n  disablePadding: PropTypes.bool,\n  isLandscape: PropTypes.bool.isRequired,\n  isValid: PropTypes.func.isRequired,\n  /**\n   * Ordered array of shortcuts to display.\n   * If empty, does not display the shortcuts.\n   * @default `[]`\n   */\n  items: PropTypes.arrayOf(PropTypes.shape({\n    getValue: PropTypes.func.isRequired,\n    label: PropTypes.string.isRequired\n  })),\n  onChange: PropTypes.func.isRequired,\n  style: PropTypes.object,\n  /**\n   * The content of the subheader, normally `ListSubheader`.\n   */\n  subheader: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport { PickersShortcuts };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,kBAAkB,EAAE,aAAa,EAAE,UAAU,EAAE,SAAS,CAAC;EACnFC,UAAU,GAAG,CAAC,UAAU,CAAC;AAC3B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,IAAI,MAAM,oBAAoB;AACrC,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,MAAM;MACFC,KAAK;MACLC,gBAAgB;MAChBC,QAAQ;MACRC;IACF,CAAC,GAAGJ,KAAK;IACTK,KAAK,GAAGjB,6BAA6B,CAACY,KAAK,EAAEX,SAAS,CAAC;EACzD,IAAIY,KAAK,IAAI,IAAI,IAAIA,KAAK,CAACK,MAAM,KAAK,CAAC,EAAE;IACvC,OAAO,IAAI;EACb;EACA,MAAMC,aAAa,GAAGN,KAAK,CAACO,GAAG,CAACC,IAAI,IAAI;IACtC,IAAI;QACAC;MACF,CAAC,GAAGD,IAAI;MACRE,IAAI,GAAGvB,6BAA6B,CAACqB,IAAI,EAAEnB,UAAU,CAAC;IACxD,MAAMsB,QAAQ,GAAGF,QAAQ,CAAC;MACxBN;IACF,CAAC,CAAC;IACF,OAAO;MACLS,KAAK,EAAEF,IAAI,CAACE,KAAK;MACjBC,OAAO,EAAEA,CAAA,KAAM;QACbX,QAAQ,CAACS,QAAQ,EAAEV,gBAAgB,EAAES,IAAI,CAAC;MAC5C,CAAC;MACDI,QAAQ,EAAE,CAACX,OAAO,CAACQ,QAAQ;IAC7B,CAAC;EACH,CAAC,CAAC;EACF,OAAO,aAAad,IAAI,CAACL,IAAI,EAAEN,QAAQ,CAAC;IACtC6B,KAAK,EAAE,IAAI;IACXC,EAAE,EAAE,CAAC;MACHC,SAAS,EAAEtB,WAAW;MACtBuB,QAAQ,EAAE,GAAG;MACbC,QAAQ,EAAE;IACZ,CAAC,EAAE,IAAIC,KAAK,CAACC,OAAO,CAACjB,KAAK,CAACY,EAAE,CAAC,GAAGZ,KAAK,CAACY,EAAE,GAAG,CAACZ,KAAK,CAACY,EAAE,CAAC,CAAC;EACzD,CAAC,EAAEZ,KAAK,EAAE;IACRkB,QAAQ,EAAEhB,aAAa,CAACC,GAAG,CAACG,IAAI,IAAI;MAClC,OAAO,aAAab,IAAI,CAACJ,QAAQ,EAAE;QACjC6B,QAAQ,EAAE,aAAazB,IAAI,CAACH,IAAI,EAAER,QAAQ,CAAC,CAAC,CAAC,EAAEwB,IAAI,CAAC;MACtD,CAAC,EAAEA,IAAI,CAACE,KAAK,CAAC;IAChB,CAAC;EACH,CAAC,CAAC,CAAC;AACL;AACAW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG3B,gBAAgB,CAAC4B,SAAS,GAAG;EACnE;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;EACEzB,gBAAgB,EAAEV,SAAS,CAACoC,KAAK,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;EACpDC,SAAS,EAAErC,SAAS,CAACsC,MAAM;EAC3BC,SAAS,EAAEvC,SAAS,CAACwC,WAAW;EAChC;AACF;AACA;AACA;AACA;AACA;EACEhB,KAAK,EAAExB,SAAS,CAACyC,IAAI;EACrB;AACF;AACA;AACA;EACEC,cAAc,EAAE1C,SAAS,CAACyC,IAAI;EAC9BE,WAAW,EAAE3C,SAAS,CAACyC,IAAI,CAACG,UAAU;EACtChC,OAAO,EAAEZ,SAAS,CAAC6C,IAAI,CAACD,UAAU;EAClC;AACF;AACA;AACA;AACA;EACEnC,KAAK,EAAET,SAAS,CAAC8C,OAAO,CAAC9C,SAAS,CAAC+C,KAAK,CAAC;IACvC7B,QAAQ,EAAElB,SAAS,CAAC6C,IAAI,CAACD,UAAU;IACnCvB,KAAK,EAAErB,SAAS,CAACsC,MAAM,CAACM;EAC1B,CAAC,CAAC,CAAC;EACHjC,QAAQ,EAAEX,SAAS,CAAC6C,IAAI,CAACD,UAAU;EACnCI,KAAK,EAAEhD,SAAS,CAACiD,MAAM;EACvB;AACF;AACA;EACEC,SAAS,EAAElD,SAAS,CAACmD,IAAI;EACzB;AACF;AACA;EACE1B,EAAE,EAAEzB,SAAS,CAACoD,SAAS,CAAC,CAACpD,SAAS,CAAC8C,OAAO,CAAC9C,SAAS,CAACoD,SAAS,CAAC,CAACpD,SAAS,CAAC6C,IAAI,EAAE7C,SAAS,CAACiD,MAAM,EAAEjD,SAAS,CAACyC,IAAI,CAAC,CAAC,CAAC,EAAEzC,SAAS,CAAC6C,IAAI,EAAE7C,SAAS,CAACiD,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,SAAS1C,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}