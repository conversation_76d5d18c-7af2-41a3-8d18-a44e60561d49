{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nvar MILLISECONDS_IN_DAY = 24 * 60 * 60 * 1000;\n\n/**\n * @name getOverlappingDaysInIntervals\n * @category Interval Helpers\n * @summary Get the number of days that overlap in two time intervals\n *\n * @description\n * Get the number of days that overlap in two time intervals\n *\n * @param {Interval} intervalLeft - the first interval to compare. See [Interval]{@link docs/Interval}\n * @param {Interval} intervalRight - the second interval to compare. See [Interval]{@link docs/Interval}\n * @returns {Number} the number of days that overlap in two time intervals\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // For overlapping time intervals adds 1 for each started overlapping day:\n * getOverlappingDaysInIntervals(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 17), end: new Date(2014, 0, 21) }\n * )\n * //=> 3\n *\n * @example\n * // For non-overlapping time intervals returns 0:\n * getOverlappingDaysInIntervals(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 21), end: new Date(2014, 0, 22) }\n * )\n * //=> 0\n */\n\nexport default function getOverlappingDaysInIntervals(dirtyIntervalLeft, dirtyIntervalRight) {\n  requiredArgs(2, arguments);\n  var intervalLeft = dirtyIntervalLeft || {};\n  var intervalRight = dirtyIntervalRight || {};\n  var leftStartTime = toDate(intervalLeft.start).getTime();\n  var leftEndTime = toDate(intervalLeft.end).getTime();\n  var rightStartTime = toDate(intervalRight.start).getTime();\n  var rightEndTime = toDate(intervalRight.end).getTime();\n\n  // Throw an exception if start date is after end date or if any date is `Invalid Date`\n  if (!(leftStartTime <= leftEndTime && rightStartTime <= rightEndTime)) {\n    throw new RangeError('Invalid interval');\n  }\n  var isOverlapping = leftStartTime < rightEndTime && rightStartTime < leftEndTime;\n  if (!isOverlapping) {\n    return 0;\n  }\n  var overlapStartDate = rightStartTime < leftStartTime ? leftStartTime : rightStartTime;\n  var overlapEndDate = rightEndTime > leftEndTime ? leftEndTime : rightEndTime;\n  var differenceInMs = overlapEndDate - overlapStartDate;\n  return Math.ceil(differenceInMs / MILLISECONDS_IN_DAY);\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "MILLISECONDS_IN_DAY", "getOverlappingDaysInIntervals", "dirtyIntervalLeft", "dirtyIntervalRight", "arguments", "intervalLeft", "intervalRight", "leftStartTime", "start", "getTime", "leftEndTime", "end", "rightStartTime", "rightEndTime", "RangeError", "isOverlapping", "overlapStartDate", "overlapEndDate", "differenceInMs", "Math", "ceil"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/getOverlappingDaysInIntervals/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nvar MILLISECONDS_IN_DAY = 24 * 60 * 60 * 1000;\n\n/**\n * @name getOverlappingDaysInIntervals\n * @category Interval Helpers\n * @summary Get the number of days that overlap in two time intervals\n *\n * @description\n * Get the number of days that overlap in two time intervals\n *\n * @param {Interval} intervalLeft - the first interval to compare. See [Interval]{@link docs/Interval}\n * @param {Interval} intervalRight - the second interval to compare. See [Interval]{@link docs/Interval}\n * @returns {Number} the number of days that overlap in two time intervals\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // For overlapping time intervals adds 1 for each started overlapping day:\n * getOverlappingDaysInIntervals(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 17), end: new Date(2014, 0, 21) }\n * )\n * //=> 3\n *\n * @example\n * // For non-overlapping time intervals returns 0:\n * getOverlappingDaysInIntervals(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 21), end: new Date(2014, 0, 22) }\n * )\n * //=> 0\n */\n\nexport default function getOverlappingDaysInIntervals(dirtyIntervalLeft, dirtyIntervalRight) {\n  requiredArgs(2, arguments);\n  var intervalLeft = dirtyIntervalLeft || {};\n  var intervalRight = dirtyIntervalRight || {};\n  var leftStartTime = toDate(intervalLeft.start).getTime();\n  var leftEndTime = toDate(intervalLeft.end).getTime();\n  var rightStartTime = toDate(intervalRight.start).getTime();\n  var rightEndTime = toDate(intervalRight.end).getTime();\n\n  // Throw an exception if start date is after end date or if any date is `Invalid Date`\n  if (!(leftStartTime <= leftEndTime && rightStartTime <= rightEndTime)) {\n    throw new RangeError('Invalid interval');\n  }\n  var isOverlapping = leftStartTime < rightEndTime && rightStartTime < leftEndTime;\n  if (!isOverlapping) {\n    return 0;\n  }\n  var overlapStartDate = rightStartTime < leftStartTime ? leftStartTime : rightStartTime;\n  var overlapEndDate = rightEndTime > leftEndTime ? leftEndTime : rightEndTime;\n  var differenceInMs = overlapEndDate - overlapStartDate;\n  return Math.ceil(differenceInMs / MILLISECONDS_IN_DAY);\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD,IAAIC,mBAAmB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,6BAA6BA,CAACC,iBAAiB,EAAEC,kBAAkB,EAAE;EAC3FJ,YAAY,CAAC,CAAC,EAAEK,SAAS,CAAC;EAC1B,IAAIC,YAAY,GAAGH,iBAAiB,IAAI,CAAC,CAAC;EAC1C,IAAII,aAAa,GAAGH,kBAAkB,IAAI,CAAC,CAAC;EAC5C,IAAII,aAAa,GAAGT,MAAM,CAACO,YAAY,CAACG,KAAK,CAAC,CAACC,OAAO,CAAC,CAAC;EACxD,IAAIC,WAAW,GAAGZ,MAAM,CAACO,YAAY,CAACM,GAAG,CAAC,CAACF,OAAO,CAAC,CAAC;EACpD,IAAIG,cAAc,GAAGd,MAAM,CAACQ,aAAa,CAACE,KAAK,CAAC,CAACC,OAAO,CAAC,CAAC;EAC1D,IAAII,YAAY,GAAGf,MAAM,CAACQ,aAAa,CAACK,GAAG,CAAC,CAACF,OAAO,CAAC,CAAC;;EAEtD;EACA,IAAI,EAAEF,aAAa,IAAIG,WAAW,IAAIE,cAAc,IAAIC,YAAY,CAAC,EAAE;IACrE,MAAM,IAAIC,UAAU,CAAC,kBAAkB,CAAC;EAC1C;EACA,IAAIC,aAAa,GAAGR,aAAa,GAAGM,YAAY,IAAID,cAAc,GAAGF,WAAW;EAChF,IAAI,CAACK,aAAa,EAAE;IAClB,OAAO,CAAC;EACV;EACA,IAAIC,gBAAgB,GAAGJ,cAAc,GAAGL,aAAa,GAAGA,aAAa,GAAGK,cAAc;EACtF,IAAIK,cAAc,GAAGJ,YAAY,GAAGH,WAAW,GAAGA,WAAW,GAAGG,YAAY;EAC5E,IAAIK,cAAc,GAAGD,cAAc,GAAGD,gBAAgB;EACtD,OAAOG,IAAI,CAACC,IAAI,CAACF,cAAc,GAAGlB,mBAAmB,CAAC;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}