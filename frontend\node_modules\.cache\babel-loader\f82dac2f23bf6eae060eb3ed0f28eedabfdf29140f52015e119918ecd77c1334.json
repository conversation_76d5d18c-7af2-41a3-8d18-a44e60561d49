{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"'ўтган' eeee p 'да'\",\n  yesterday: \"'кеча' p 'да'\",\n  today: \"'бугун' p 'да'\",\n  tomorrow: \"'эртага' p 'да'\",\n  nextWeek: \"eeee p 'да'\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/uz-Cyrl/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: \"'ўтган' eeee p 'да'\",\n  yesterday: \"'кеча' p 'да'\",\n  today: \"'бугун' p 'да'\",\n  tomorrow: \"'эртага' p 'да'\",\n  nextWeek: \"eeee p 'да'\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,qBAAqB;EAC/BC,SAAS,EAAE,eAAe;EAC1BC,KAAK,EAAE,gBAAgB;EACvBC,QAAQ,EAAE,iBAAiB;EAC3BC,QAAQ,EAAE,aAAa;EACvBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC9E,OAAOX,oBAAoB,CAACQ,KAAK,CAAC;AACpC,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}