{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['f.Kr.', 'e.Kr.'],\n  abbreviated: ['f.Kr.', 'e.Kr.'],\n  wide: ['f<PERSON><PERSON>', 'e<PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['1:a kvartalet', '2:a kvartalet', '3:e kvartalet', '4:e kvartalet']\n};\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['jan.', 'feb.', 'mars', 'apr.', 'maj', 'juni', 'juli', 'aug.', 'sep.', 'okt.', 'nov.', 'dec.'],\n  wide: ['januari', 'februari', 'mars', 'april', 'maj', 'juni', 'juli', 'augusti', 'september', 'oktober', 'november', 'december']\n};\nvar dayValues = {\n  narrow: ['S', 'M', 'T', 'O', 'T', 'F', 'L'],\n  short: ['sö', 'må', 'ti', 'on', 'to', 'fr', 'lö'],\n  abbreviated: ['sön', 'mån', 'tis', 'ons', 'tors', 'fre', 'lör'],\n  wide: ['söndag', 'måndag', 'tisdag', 'onsdag', 'torsdag', 'fredag', 'lördag']\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/sv.html#1888\nvar dayPeriodValues = {\n  narrow: {\n    am: 'fm',\n    pm: 'em',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'morg.',\n    afternoon: 'efterm.',\n    evening: 'kväll',\n    night: 'natt'\n  },\n  abbreviated: {\n    am: 'f.m.',\n    pm: 'e.m.',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'morgon',\n    afternoon: 'efterm.',\n    evening: 'kväll',\n    night: 'natt'\n  },\n  wide: {\n    am: 'förmiddag',\n    pm: 'eftermiddag',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'morgon',\n    afternoon: 'eftermiddag',\n    evening: 'kväll',\n    night: 'natt'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'fm',\n    pm: 'em',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'på morg.',\n    afternoon: 'på efterm.',\n    evening: 'på kvällen',\n    night: 'på natten'\n  },\n  abbreviated: {\n    am: 'fm',\n    pm: 'em',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'på morg.',\n    afternoon: 'på efterm.',\n    evening: 'på kvällen',\n    night: 'på natten'\n  },\n  wide: {\n    am: 'fm',\n    pm: 'em',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'på morgonen',\n    afternoon: 'på eftermiddagen',\n    evening: 'på kvällen',\n    night: 'på natten'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  var rem100 = number % 100;\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n      case 2:\n        return number + ':a';\n    }\n  }\n  return number + ':e';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "number", "Number", "rem100", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/sv/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['f.Kr.', 'e.Kr.'],\n  abbreviated: ['f.Kr.', 'e.Kr.'],\n  wide: ['f<PERSON><PERSON>', 'e<PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['1:a kvartalet', '2:a kvartalet', '3:e kvartalet', '4:e kvartalet']\n};\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['jan.', 'feb.', 'mars', 'apr.', 'maj', 'juni', 'juli', 'aug.', 'sep.', 'okt.', 'nov.', 'dec.'],\n  wide: ['januari', 'februari', 'mars', 'april', 'maj', 'juni', 'juli', 'augusti', 'september', 'oktober', 'november', 'december']\n};\nvar dayValues = {\n  narrow: ['S', 'M', 'T', 'O', 'T', 'F', 'L'],\n  short: ['sö', 'må', 'ti', 'on', 'to', 'fr', 'lö'],\n  abbreviated: ['sön', 'mån', 'tis', 'ons', 'tors', 'fre', 'lör'],\n  wide: ['söndag', 'måndag', 'tisdag', 'onsdag', 'torsdag', 'fredag', 'lördag']\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/sv.html#1888\nvar dayPeriodValues = {\n  narrow: {\n    am: 'fm',\n    pm: 'em',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'morg.',\n    afternoon: 'efterm.',\n    evening: 'kväll',\n    night: 'natt'\n  },\n  abbreviated: {\n    am: 'f.m.',\n    pm: 'e.m.',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'morgon',\n    afternoon: 'efterm.',\n    evening: 'kväll',\n    night: 'natt'\n  },\n  wide: {\n    am: 'förmiddag',\n    pm: 'eftermiddag',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'morgon',\n    afternoon: 'eftermiddag',\n    evening: 'kväll',\n    night: 'natt'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'fm',\n    pm: 'em',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'på morg.',\n    afternoon: 'på efterm.',\n    evening: 'på kvällen',\n    night: 'på natten'\n  },\n  abbreviated: {\n    am: 'fm',\n    pm: 'em',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'på morg.',\n    afternoon: 'på efterm.',\n    evening: 'på kvällen',\n    night: 'på natten'\n  },\n  wide: {\n    am: 'fm',\n    pm: 'em',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'på morgonen',\n    afternoon: 'på eftermiddagen',\n    evening: 'på kvällen',\n    night: 'på natten'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  var rem100 = number % 100;\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n      case 2:\n        return number + ':a';\n    }\n  }\n  return number + ':e';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EAC1BC,WAAW,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EAC/BC,IAAI,EAAE,CAAC,cAAc,EAAE,eAAe;AACxC,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe;AAC3E,CAAC;AACD,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EAC5GC,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU;AACjI,CAAC;AACD,IAAIG,SAAS,GAAG;EACdL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;EAC/DC,IAAI,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ;AAC9E,CAAC;;AAED;AACA,IAAIK,eAAe,GAAG;EACpBP,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE,aAAa;IACjBC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BhB,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,aAAa;IACtBC,SAAS,EAAE,kBAAkB;IAC7BC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,QAAQ,EAAE;EAChE,IAAIC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAChC,IAAII,MAAM,GAAGF,MAAM,GAAG,GAAG;EACzB,IAAIE,MAAM,GAAG,EAAE,IAAIA,MAAM,GAAG,EAAE,EAAE;IAC9B,QAAQA,MAAM,GAAG,EAAE;MACjB,KAAK,CAAC;MACN,KAAK,CAAC;QACJ,OAAOF,MAAM,GAAG,IAAI;IACxB;EACF;EACA,OAAOA,MAAM,GAAG,IAAI;AACtB,CAAC;AACD,IAAIG,QAAQ,GAAG;EACbN,aAAa,EAAEA,aAAa;EAC5BO,GAAG,EAAE1B,eAAe,CAAC;IACnB2B,MAAM,EAAE1B,SAAS;IACjB2B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE7B,eAAe,CAAC;IACvB2B,MAAM,EAAEtB,aAAa;IACrBuB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAE/B,eAAe,CAAC;IACrB2B,MAAM,EAAErB,WAAW;IACnBsB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFI,GAAG,EAAEhC,eAAe,CAAC;IACnB2B,MAAM,EAAEpB,SAAS;IACjBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,SAAS,EAAEjC,eAAe,CAAC;IACzB2B,MAAM,EAAElB,eAAe;IACvBmB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEhB,yBAAyB;IAC3CiB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}