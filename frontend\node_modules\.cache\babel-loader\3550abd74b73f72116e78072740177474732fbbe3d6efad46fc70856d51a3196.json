{"ast": null, "code": "'use client';\n\nexport { default as useSlotProps } from '@mui/utils/useSlotProps';", "map": {"version": 3, "names": ["default", "useSlotProps"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/base/utils/useSlotProps.js"], "sourcesContent": ["'use client';\n\nexport { default as useSlotProps } from '@mui/utils/useSlotProps';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,IAAIC,YAAY,QAAQ,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}