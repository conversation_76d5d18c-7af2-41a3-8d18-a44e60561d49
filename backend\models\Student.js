const mongoose = require('mongoose');

const StudentSchema = new mongoose.Schema({
  studentId: {
    type: String,
    unique: true,
    required: [true, 'رقم الطالب مطلوب']
  },
  fullName: {
    type: String,
    required: [true, 'الاسم الكامل مطلوب'],
    trim: true,
    maxlength: [100, 'الاسم الكامل يجب أن يكون أقل من 100 حرف']
  },
  dateOfBirth: {
    type: Date,
    required: [true, 'تاريخ الميلاد مطلوب']
  },
  gender: {
    type: String,
    required: [true, 'الجنس مطلوب'],
    enum: {
      values: ['male', 'female'],
      message: 'الجنس يجب أن يكون ذكر أو أنثى'
    }
  },
  phone: {
    type: String,
    required: [true, 'رقم الهاتف مطلوب'],
    trim: true,
    match: [/^[0-9+\-\s()]+$/, 'يرجى إدخال رقم هاتف صحيح']
  },
  email: {
    type: String,
    trim: true,
    lowercase: true,
    match: [
      /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
      'يرجى إدخال بريد إلكتروني صحيح'
    ]
  },
  address: {
    street: {
      type: String,
      trim: true,
      maxlength: [200, 'عنوان الشارع يجب أن يكون أقل من 200 حرف']
    },
    city: {
      type: String,
      trim: true,
      maxlength: [50, 'اسم المدينة يجب أن يكون أقل من 50 حرف']
    },
    postalCode: {
      type: String,
      trim: true,
      maxlength: [20, 'الرمز البريدي يجب أن يكون أقل من 20 حرف']
    }
  },
  emergencyContact: {
    name: {
      type: String,
      required: [true, 'اسم جهة الاتصال في حالات الطوارئ مطلوب'],
      trim: true,
      maxlength: [100, 'اسم جهة الاتصال يجب أن يكون أقل من 100 حرف']
    },
    relationship: {
      type: String,
      required: [true, 'صلة القرابة مطلوبة'],
      trim: true,
      maxlength: [50, 'صلة القرابة يجب أن تكون أقل من 50 حرف']
    },
    phone: {
      type: String,
      required: [true, 'رقم هاتف جهة الاتصال في حالات الطوارئ مطلوب'],
      trim: true,
      match: [/^[0-9+\-\s()]+$/, 'يرجى إدخال رقم هاتف صحيح']
    }
  },
  beltLevel: {
    type: String,
    required: [true, 'مستوى الحزام مطلوب'],
    enum: {
      values: [
        'white', 'yellow', 'orange', 'green', 
        'blue', 'brown', 'red', 'black-1st', 
        'black-2nd', 'black-3rd', 'black-4th', 
        'black-5th', 'black-6th', 'black-7th', 
        'black-8th', 'black-9th'
      ],
      message: 'مستوى الحزام غير صحيح'
    },
    default: 'white'
  },
  joinDate: {
    type: Date,
    required: [true, 'تاريخ الانضمام مطلوب'],
    default: Date.now
  },
  isActive: {
    type: Boolean,
    default: true
  },
  medicalConditions: {
    type: String,
    trim: true,
    maxlength: [500, 'الحالات الطبية يجب أن تكون أقل من 500 حرف']
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [1000, 'الملاحظات يجب أن تكون أقل من 1000 حرف']
  },
  profileImage: {
    type: String,
    default: null
  },
  createdBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Create indexes for better performance
StudentSchema.index({ studentId: 1 });
StudentSchema.index({ fullName: 1 });
StudentSchema.index({ phone: 1 });
StudentSchema.index({ beltLevel: 1 });
StudentSchema.index({ isActive: 1 });
StudentSchema.index({ joinDate: -1 });

// Virtual for age calculation
StudentSchema.virtual('age').get(function() {
  if (!this.dateOfBirth) return null;
  const today = new Date();
  const birthDate = new Date(this.dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
});

// Virtual for gender in Arabic
StudentSchema.virtual('genderArabic').get(function() {
  return this.gender === 'male' ? 'ذكر' : 'أنثى';
});

// Virtual for belt level in Arabic
StudentSchema.virtual('beltLevelArabic').get(function() {
  const beltMap = {
    'white': 'أبيض',
    'yellow': 'أصفر',
    'orange': 'برتقالي',
    'green': 'أخضر',
    'blue': 'أزرق',
    'brown': 'بني',
    'red': 'أحمر',
    'black-1st': 'أسود - الدان الأول',
    'black-2nd': 'أسود - الدان الثاني',
    'black-3rd': 'أسود - الدان الثالث',
    'black-4th': 'أسود - الدان الرابع',
    'black-5th': 'أسود - الدان الخامس',
    'black-6th': 'أسود - الدان السادس',
    'black-7th': 'أسود - الدان السابع',
    'black-8th': 'أسود - الدان الثامن',
    'black-9th': 'أسود - الدان التاسع'
  };
  return beltMap[this.beltLevel] || this.beltLevel;
});

// Virtual for WhatsApp link
StudentSchema.virtual('whatsappLink').get(function() {
  if (!this.phone) return null;
  // Remove all non-numeric characters and format for WhatsApp
  const cleanPhone = this.phone.replace(/[^\d]/g, '');
  // Add country code if not present (assuming Saudi Arabia +966)
  const formattedPhone = cleanPhone.startsWith('966') ? cleanPhone : `966${cleanPhone}`;
  return `https://wa.me/${formattedPhone}`;
});

// Pre-save middleware to generate student ID
StudentSchema.pre('save', async function(next) {
  if (!this.studentId) {
    // Generate student ID: TKD + year + sequential number
    const year = new Date().getFullYear();
    const count = await this.constructor.countDocuments({
      studentId: new RegExp(`^TKD${year}`)
    });
    this.studentId = `TKD${year}${String(count + 1).padStart(4, '0')}`;
  }
  next();
});

// Static method to get students by belt level
StudentSchema.statics.getByBeltLevel = function(beltLevel) {
  return this.find({ beltLevel, isActive: true }).sort({ fullName: 1 });
};

// Static method to get active students
StudentSchema.statics.getActiveStudents = function() {
  return this.find({ isActive: true }).sort({ fullName: 1 });
};

// Instance method to deactivate student
StudentSchema.methods.deactivate = async function(updatedBy) {
  this.isActive = false;
  this.updatedBy = updatedBy;
  await this.save();
};

module.exports = mongoose.model('Student', StudentSchema);
