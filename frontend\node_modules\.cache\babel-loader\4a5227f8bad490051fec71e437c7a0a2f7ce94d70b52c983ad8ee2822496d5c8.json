{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'inqas minn sekonda',\n    other: 'inqas minn {{count}} sekondi'\n  },\n  xSeconds: {\n    one: 'sekonda',\n    other: '{{count}} sekondi'\n  },\n  halfAMinute: 'nofs minuta',\n  lessThanXMinutes: {\n    one: 'inqas minn minuta',\n    other: 'inqas minn {{count}} minuti'\n  },\n  xMinutes: {\n    one: 'minuta',\n    other: '{{count}} minuti'\n  },\n  aboutXHours: {\n    one: 'madwar siegħa',\n    other: 'madwar {{count}} siegħat'\n  },\n  xHours: {\n    one: 'siegħa',\n    other: '{{count}} siegħat'\n  },\n  xDays: {\n    one: 'ġurnata',\n    other: '{{count}} ġranet'\n  },\n  aboutXWeeks: {\n    one: 'madwar ġimgħa',\n    other: 'madwar {{count}} ġimgħat'\n  },\n  xWeeks: {\n    one: 'ġimgħa',\n    other: '{{count}} ġimgħat'\n  },\n  aboutXMonths: {\n    one: 'madwar xahar',\n    other: 'madwar {{count}} xhur'\n  },\n  xMonths: {\n    one: 'xahar',\n    other: '{{count}} xhur'\n  },\n  aboutXYears: {\n    one: 'madwar sena',\n    two: 'madwar sentejn',\n    other: 'madwar {{count}} snin'\n  },\n  xYears: {\n    one: 'sena',\n    two: 'sentejn',\n    other: '{{count}} snin'\n  },\n  overXYears: {\n    one: 'aktar minn sena',\n    two: 'aktar minn sentejn',\n    other: 'aktar minn {{count}} snin'\n  },\n  almostXYears: {\n    one: 'kważi sena',\n    two: 'kważi sentejn',\n    other: 'kważi {{count}} snin'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 2 && tokenValue.two) {\n    result = tokenValue.two;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"f'\" + result;\n    } else {\n      return result + ' ilu';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "two", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/mt/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'inqas minn sekonda',\n    other: 'inqas minn {{count}} sekondi'\n  },\n  xSeconds: {\n    one: 'sekonda',\n    other: '{{count}} sekondi'\n  },\n  halfAMinute: 'nofs minuta',\n  lessThanXMinutes: {\n    one: 'inqas minn minuta',\n    other: 'inqas minn {{count}} minuti'\n  },\n  xMinutes: {\n    one: 'minuta',\n    other: '{{count}} minuti'\n  },\n  aboutXHours: {\n    one: 'madwar siegħa',\n    other: 'madwar {{count}} siegħat'\n  },\n  xHours: {\n    one: 'siegħa',\n    other: '{{count}} siegħat'\n  },\n  xDays: {\n    one: 'ġurnata',\n    other: '{{count}} ġranet'\n  },\n  aboutXWeeks: {\n    one: 'madwar ġimgħa',\n    other: 'madwar {{count}} ġimgħat'\n  },\n  xWeeks: {\n    one: 'ġimgħa',\n    other: '{{count}} ġimgħat'\n  },\n  aboutXMonths: {\n    one: 'madwar xahar',\n    other: 'madwar {{count}} xhur'\n  },\n  xMonths: {\n    one: 'xahar',\n    other: '{{count}} xhur'\n  },\n  aboutXYears: {\n    one: 'madwar sena',\n    two: 'madwar sentejn',\n    other: 'madwar {{count}} snin'\n  },\n  xYears: {\n    one: 'sena',\n    two: 'sentejn',\n    other: '{{count}} snin'\n  },\n  overXYears: {\n    one: 'aktar minn sena',\n    two: 'aktar minn sentejn',\n    other: 'aktar minn {{count}} snin'\n  },\n  almostXYears: {\n    one: 'kważi sena',\n    two: 'kważi sentejn',\n    other: 'kważi {{count}} snin'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 2 && tokenValue.two) {\n    result = tokenValue.two;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"f'\" + result;\n    } else {\n      return result + ' ilu';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,aAAa;EAC1BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,aAAa;IAClBc,GAAG,EAAE,gBAAgB;IACrBb,KAAK,EAAE;EACT,CAAC;EACDc,MAAM,EAAE;IACNf,GAAG,EAAE,MAAM;IACXc,GAAG,EAAE,SAAS;IACdb,KAAK,EAAE;EACT,CAAC;EACDe,UAAU,EAAE;IACVhB,GAAG,EAAE,iBAAiB;IACtBc,GAAG,EAAE,oBAAoB;IACzBb,KAAK,EAAE;EACT,CAAC;EACDgB,YAAY,EAAE;IACZjB,GAAG,EAAE,YAAY;IACjBc,GAAG,EAAE,eAAe;IACpBb,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIiB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGzB,oBAAoB,CAACqB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACvB,GAAG;EACzB,CAAC,MAAM,IAAIoB,KAAK,KAAK,CAAC,IAAIG,UAAU,CAACT,GAAG,EAAE;IACxCQ,MAAM,GAAGC,UAAU,CAACT,GAAG;EACzB,CAAC,MAAM;IACLQ,MAAM,GAAGC,UAAU,CAACtB,KAAK,CAACuB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,IAAI,GAAGL,MAAM;IACtB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,MAAM;IACxB;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}