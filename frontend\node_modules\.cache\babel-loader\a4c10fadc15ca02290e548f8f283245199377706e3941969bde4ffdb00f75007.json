{"ast": null, "code": "import { millisecondsInMinute } from \"../constants/index.js\";\nimport differenceInMilliseconds from \"../differenceInMilliseconds/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { getRoundingMethod } from \"../_lib/roundingMethods/index.js\";\n/**\n * @name differenceInMinutes\n * @category Minute Helpers\n * @summary Get the number of minutes between the given dates.\n *\n * @description\n * Get the signed number of full (rounded towards 0) minutes between the given dates.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @param {Object} [options] - an object with options.\n * @param {String} [options.roundingMethod='trunc'] - a rounding method (`ceil`, `floor`, `round` or `trunc`)\n * @returns {Number} the number of minutes\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many minutes are between 2 July 2014 12:07:59 and 2 July 2014 12:20:00?\n * const result = differenceInMinutes(\n *   new Date(2014, 6, 2, 12, 20, 0),\n *   new Date(2014, 6, 2, 12, 7, 59)\n * )\n * //=> 12\n *\n * @example\n * // How many minutes are between 10:01:59 and 10:00:00\n * const result = differenceInMinutes(\n *   new Date(2000, 0, 1, 10, 0, 0),\n *   new Date(2000, 0, 1, 10, 1, 59)\n * )\n * //=> -1\n */\nexport default function differenceInMinutes(dateLeft, dateRight, options) {\n  requiredArgs(2, arguments);\n  var diff = differenceInMilliseconds(dateLeft, dateRight) / millisecondsInMinute;\n  return getRoundingMethod(options === null || options === void 0 ? void 0 : options.roundingMethod)(diff);\n}", "map": {"version": 3, "names": ["millisecondsInMinute", "differenceInMilliseconds", "requiredArgs", "getRoundingMethod", "differenceInMinutes", "dateLeft", "dateRight", "options", "arguments", "diff", "roundingMethod"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/differenceInMinutes/index.js"], "sourcesContent": ["import { millisecondsInMinute } from \"../constants/index.js\";\nimport differenceInMilliseconds from \"../differenceInMilliseconds/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { getRoundingMethod } from \"../_lib/roundingMethods/index.js\";\n/**\n * @name differenceInMinutes\n * @category Minute Helpers\n * @summary Get the number of minutes between the given dates.\n *\n * @description\n * Get the signed number of full (rounded towards 0) minutes between the given dates.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @param {Object} [options] - an object with options.\n * @param {String} [options.roundingMethod='trunc'] - a rounding method (`ceil`, `floor`, `round` or `trunc`)\n * @returns {Number} the number of minutes\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many minutes are between 2 July 2014 12:07:59 and 2 July 2014 12:20:00?\n * const result = differenceInMinutes(\n *   new Date(2014, 6, 2, 12, 20, 0),\n *   new Date(2014, 6, 2, 12, 7, 59)\n * )\n * //=> 12\n *\n * @example\n * // How many minutes are between 10:01:59 and 10:00:00\n * const result = differenceInMinutes(\n *   new Date(2000, 0, 1, 10, 0, 0),\n *   new Date(2000, 0, 1, 10, 1, 59)\n * )\n * //=> -1\n */\nexport default function differenceInMinutes(dateLeft, dateRight, options) {\n  requiredArgs(2, arguments);\n  var diff = differenceInMilliseconds(dateLeft, dateRight) / millisecondsInMinute;\n  return getRoundingMethod(options === null || options === void 0 ? void 0 : options.roundingMethod)(diff);\n}"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,uBAAuB;AAC5D,OAAOC,wBAAwB,MAAM,sCAAsC;AAC3E,OAAOC,YAAY,MAAM,+BAA+B;AACxD,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,mBAAmBA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAE;EACxEL,YAAY,CAAC,CAAC,EAAEM,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGR,wBAAwB,CAACI,QAAQ,EAAEC,SAAS,CAAC,GAAGN,oBAAoB;EAC/E,OAAOG,iBAAiB,CAACI,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,cAAc,CAAC,CAACD,IAAI,CAAC;AAC1G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}