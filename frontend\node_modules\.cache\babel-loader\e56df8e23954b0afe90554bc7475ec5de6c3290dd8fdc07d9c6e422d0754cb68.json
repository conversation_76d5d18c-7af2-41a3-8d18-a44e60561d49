{"ast": null, "code": "import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name addDays\n * @category Day Helpers\n * @summary Add the specified number of days to the given date.\n *\n * @description\n * Add the specified number of days to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of days to be added. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} - the new date with the days added\n * @throws {TypeError} - 2 arguments required\n *\n * @example\n * // Add 10 days to 1 September 2014:\n * const result = addDays(new Date(2014, 8, 1), 10)\n * //=> Thu Sep 11 2014 00:00:00\n */\nexport default function addDays(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var amount = toInteger(dirtyAmount);\n  if (isNaN(amount)) {\n    return new Date(NaN);\n  }\n  if (!amount) {\n    // If 0 days, no-op to avoid changing times in the hour before end of DST\n    return date;\n  }\n  date.setDate(date.getDate() + amount);\n  return date;\n}", "map": {"version": 3, "names": ["toInteger", "toDate", "requiredArgs", "addDays", "dirtyDate", "dirtyAmount", "arguments", "date", "amount", "isNaN", "Date", "NaN", "setDate", "getDate"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/addDays/index.js"], "sourcesContent": ["import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name addDays\n * @category Day Helpers\n * @summary Add the specified number of days to the given date.\n *\n * @description\n * Add the specified number of days to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of days to be added. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} - the new date with the days added\n * @throws {TypeError} - 2 arguments required\n *\n * @example\n * // Add 10 days to 1 September 2014:\n * const result = addDays(new Date(2014, 8, 1), 10)\n * //=> Thu Sep 11 2014 00:00:00\n */\nexport default function addDays(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var amount = toInteger(dirtyAmount);\n  if (isNaN(amount)) {\n    return new Date(NaN);\n  }\n  if (!amount) {\n    // If 0 days, no-op to avoid changing times in the hour before end of DST\n    return date;\n  }\n  date.setDate(date.getDate() + amount);\n  return date;\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,4BAA4B;AAClD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,OAAOA,CAACC,SAAS,EAAEC,WAAW,EAAE;EACtDH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGN,MAAM,CAACG,SAAS,CAAC;EAC5B,IAAII,MAAM,GAAGR,SAAS,CAACK,WAAW,CAAC;EACnC,IAAII,KAAK,CAACD,MAAM,CAAC,EAAE;IACjB,OAAO,IAAIE,IAAI,CAACC,GAAG,CAAC;EACtB;EACA,IAAI,CAACH,MAAM,EAAE;IACX;IACA,OAAOD,IAAI;EACb;EACAA,IAAI,CAACK,OAAO,CAACL,IAAI,CAACM,OAAO,CAAC,CAAC,GAAGL,MAAM,CAAC;EACrC,OAAOD,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}