{"ast": null, "code": "import addWeeks from \"../addWeeks/index.js\";\nimport startOfWeek from \"../startOfWeek/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachWeekOfInterval\n * @category Interval Helpers\n * @summary Return the array of weeks within the specified time interval.\n *\n * @description\n * Return the array of weeks within the specified time interval.\n *\n * @param {Interval} interval - the interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @returns {Date[]} the array with starts of weeks from the week of the interval start to the week of the interval end\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.weekStartsOn` must be 0, 1, ..., 6\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Each week within interval 6 October 2014 - 23 November 2014:\n * const result = eachWeekOfInterval({\n *   start: new Date(2014, 9, 6),\n *   end: new Date(2014, 10, 23)\n * })\n * //=> [\n * //   Sun Oct 05 2014 00:00:00,\n * //   Sun Oct 12 2014 00:00:00,\n * //   Sun Oct 19 2014 00:00:00,\n * //   Sun Oct 26 2014 00:00:00,\n * //   Sun Nov 02 2014 00:00:00,\n * //   Sun Nov 09 2014 00:00:00,\n * //   Sun Nov 16 2014 00:00:00,\n * //   Sun Nov 23 2014 00:00:00\n * // ]\n */\nexport default function eachWeekOfInterval(dirtyInterval, options) {\n  requiredArgs(1, arguments);\n  var interval = dirtyInterval || {};\n  var startDate = toDate(interval.start);\n  var endDate = toDate(interval.end);\n  var endTime = endDate.getTime();\n\n  // Throw an exception if start date is after end date or if any date is `Invalid Date`\n  if (!(startDate.getTime() <= endTime)) {\n    throw new RangeError('Invalid interval');\n  }\n  var startDateWeek = startOfWeek(startDate, options);\n  var endDateWeek = startOfWeek(endDate, options);\n\n  // Some timezones switch DST at midnight, making start of day unreliable in these timezones, 3pm is a safe bet\n  startDateWeek.setHours(15);\n  endDateWeek.setHours(15);\n  endTime = endDateWeek.getTime();\n  var weeks = [];\n  var currentWeek = startDateWeek;\n  while (currentWeek.getTime() <= endTime) {\n    currentWeek.setHours(0);\n    weeks.push(toDate(currentWeek));\n    currentWeek = addWeeks(currentWeek, 1);\n    currentWeek.setHours(15);\n  }\n  return weeks;\n}", "map": {"version": 3, "names": ["addWeeks", "startOfWeek", "toDate", "requiredArgs", "eachWeekOfInterval", "dirtyInterval", "options", "arguments", "interval", "startDate", "start", "endDate", "end", "endTime", "getTime", "RangeError", "startDateWeek", "endDateWeek", "setHours", "weeks", "currentWeek", "push"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/eachWeekOfInterval/index.js"], "sourcesContent": ["import addWeeks from \"../addWeeks/index.js\";\nimport startOfWeek from \"../startOfWeek/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachWeekOfInterval\n * @category Interval Helpers\n * @summary Return the array of weeks within the specified time interval.\n *\n * @description\n * Return the array of weeks within the specified time interval.\n *\n * @param {Interval} interval - the interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @returns {Date[]} the array with starts of weeks from the week of the interval start to the week of the interval end\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.weekStartsOn` must be 0, 1, ..., 6\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Each week within interval 6 October 2014 - 23 November 2014:\n * const result = eachWeekOfInterval({\n *   start: new Date(2014, 9, 6),\n *   end: new Date(2014, 10, 23)\n * })\n * //=> [\n * //   Sun Oct 05 2014 00:00:00,\n * //   Sun Oct 12 2014 00:00:00,\n * //   Sun Oct 19 2014 00:00:00,\n * //   Sun Oct 26 2014 00:00:00,\n * //   Sun Nov 02 2014 00:00:00,\n * //   Sun Nov 09 2014 00:00:00,\n * //   Sun Nov 16 2014 00:00:00,\n * //   Sun Nov 23 2014 00:00:00\n * // ]\n */\nexport default function eachWeekOfInterval(dirtyInterval, options) {\n  requiredArgs(1, arguments);\n  var interval = dirtyInterval || {};\n  var startDate = toDate(interval.start);\n  var endDate = toDate(interval.end);\n  var endTime = endDate.getTime();\n\n  // Throw an exception if start date is after end date or if any date is `Invalid Date`\n  if (!(startDate.getTime() <= endTime)) {\n    throw new RangeError('Invalid interval');\n  }\n  var startDateWeek = startOfWeek(startDate, options);\n  var endDateWeek = startOfWeek(endDate, options);\n\n  // Some timezones switch DST at midnight, making start of day unreliable in these timezones, 3pm is a safe bet\n  startDateWeek.setHours(15);\n  endDateWeek.setHours(15);\n  endTime = endDateWeek.getTime();\n  var weeks = [];\n  var currentWeek = startDateWeek;\n  while (currentWeek.getTime() <= endTime) {\n    currentWeek.setHours(0);\n    weeks.push(toDate(currentWeek));\n    currentWeek = addWeeks(currentWeek, 1);\n    currentWeek.setHours(15);\n  }\n  return weeks;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,kBAAkBA,CAACC,aAAa,EAAEC,OAAO,EAAE;EACjEH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,QAAQ,GAAGH,aAAa,IAAI,CAAC,CAAC;EAClC,IAAII,SAAS,GAAGP,MAAM,CAACM,QAAQ,CAACE,KAAK,CAAC;EACtC,IAAIC,OAAO,GAAGT,MAAM,CAACM,QAAQ,CAACI,GAAG,CAAC;EAClC,IAAIC,OAAO,GAAGF,OAAO,CAACG,OAAO,CAAC,CAAC;;EAE/B;EACA,IAAI,EAAEL,SAAS,CAACK,OAAO,CAAC,CAAC,IAAID,OAAO,CAAC,EAAE;IACrC,MAAM,IAAIE,UAAU,CAAC,kBAAkB,CAAC;EAC1C;EACA,IAAIC,aAAa,GAAGf,WAAW,CAACQ,SAAS,EAAEH,OAAO,CAAC;EACnD,IAAIW,WAAW,GAAGhB,WAAW,CAACU,OAAO,EAAEL,OAAO,CAAC;;EAE/C;EACAU,aAAa,CAACE,QAAQ,CAAC,EAAE,CAAC;EAC1BD,WAAW,CAACC,QAAQ,CAAC,EAAE,CAAC;EACxBL,OAAO,GAAGI,WAAW,CAACH,OAAO,CAAC,CAAC;EAC/B,IAAIK,KAAK,GAAG,EAAE;EACd,IAAIC,WAAW,GAAGJ,aAAa;EAC/B,OAAOI,WAAW,CAACN,OAAO,CAAC,CAAC,IAAID,OAAO,EAAE;IACvCO,WAAW,CAACF,QAAQ,CAAC,CAAC,CAAC;IACvBC,KAAK,CAACE,IAAI,CAACnB,MAAM,CAACkB,WAAW,CAAC,CAAC;IAC/BA,WAAW,GAAGpB,QAAQ,CAACoB,WAAW,EAAE,CAAC,CAAC;IACtCA,WAAW,CAACF,QAAQ,CAAC,EAAE,CAAC;EAC1B;EACA,OAAOC,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}