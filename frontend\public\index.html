<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#1976d2" />
    <meta name="description" content="نظام إدارة أكاديمية التايكوندو - CRM System" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Cairo Font for Arabic Support -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <title>نظام إدارة أكاديمية التايكوندو</title>
    
    <style>
      /* Loading screen styles */
      #loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        font-family: 'Cairo', sans-serif;
        color: white;
      }
      
      .loading-logo {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        animation: pulse 2s infinite;
      }
      
      .loading-text {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 10px;
      }
      
      .loading-subtitle {
        font-size: 16px;
        opacity: 0.8;
        margin-bottom: 30px;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
      }
      
      /* Hide loading screen when React loads */
      .loaded #loading-screen {
        display: none;
      }
    </style>
  </head>
  <body>
    <noscript>يجب تفعيل JavaScript لتشغيل هذا التطبيق.</noscript>
    
    <!-- Loading Screen -->
    <div id="loading-screen">
      <div class="loading-logo">
        🥋
      </div>
      <div class="loading-text">أكاديمية التايكوندو</div>
      <div class="loading-subtitle">جاري تحميل نظام الإدارة...</div>
      <div class="loading-spinner"></div>
    </div>
    
    <!-- React App Root -->
    <div id="root"></div>
    
    <script>
      // Hide loading screen when React app loads
      window.addEventListener('load', function() {
        setTimeout(function() {
          document.body.classList.add('loaded');
        }, 1000);
      });
    </script>
  </body>
</html>
