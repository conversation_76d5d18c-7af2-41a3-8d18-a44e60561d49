{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// TODO v7: This file exist only to simplify typing between\n// components/componentsProps and slots/slotProps\n// Should be deleted when components/componentsProps are removed\n\nexport const uncapitalizeObjectKeys = capitalizedObject => {\n  if (capitalizedObject === undefined) {\n    return undefined;\n  }\n  return Object.keys(capitalizedObject).reduce((acc, key) => _extends({}, acc, {\n    [`${key.slice(0, 1).toLowerCase()}${key.slice(1)}`]: capitalizedObject[key]\n  }), {});\n};", "map": {"version": 3, "names": ["_extends", "uncapitalizeObjectKeys", "capitalizedObject", "undefined", "Object", "keys", "reduce", "acc", "key", "slice", "toLowerCase"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/x-date-pickers/internals/utils/slots-migration.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// TODO v7: This file exist only to simplify typing between\n// components/componentsProps and slots/slotProps\n// Should be deleted when components/componentsProps are removed\n\nexport const uncapitalizeObjectKeys = capitalizedObject => {\n  if (capitalizedObject === undefined) {\n    return undefined;\n  }\n  return Object.keys(capitalizedObject).reduce((acc, key) => _extends({}, acc, {\n    [`${key.slice(0, 1).toLowerCase()}${key.slice(1)}`]: capitalizedObject[key]\n  }), {});\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;AACA;;AAEA,OAAO,MAAMC,sBAAsB,GAAGC,iBAAiB,IAAI;EACzD,IAAIA,iBAAiB,KAAKC,SAAS,EAAE;IACnC,OAAOA,SAAS;EAClB;EACA,OAAOC,MAAM,CAACC,IAAI,CAACH,iBAAiB,CAAC,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKR,QAAQ,CAAC,CAAC,CAAC,EAAEO,GAAG,EAAE;IAC3E,CAAC,GAAGC,GAAG,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,GAAG,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAGP,iBAAiB,CAACM,GAAG;EAC5E,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}