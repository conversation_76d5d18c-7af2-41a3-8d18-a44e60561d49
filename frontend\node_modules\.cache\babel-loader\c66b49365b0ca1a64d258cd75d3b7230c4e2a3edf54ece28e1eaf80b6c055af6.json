{"ast": null, "code": "import isSameSecond from \"../isSameSecond/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isThisSecond\n * @category Second Helpers\n * @summary Is the given date in the same second as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same second as the current date?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is in this second\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If now is 25 September 2014 18:30:15.500,\n * // is 25 September 2014 18:30:15.000 in this second?\n * const result = isThisSecond(new Date(2014, 8, 25, 18, 30, 15))\n * //=> true\n */\nexport default function isThisSecond(dirtyDate) {\n  requiredArgs(1, arguments);\n  return isSameSecond(Date.now(), dirtyDate);\n}", "map": {"version": 3, "names": ["isSameSecond", "requiredArgs", "isThisSecond", "dirtyDate", "arguments", "Date", "now"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/isThisSecond/index.js"], "sourcesContent": ["import isSameSecond from \"../isSameSecond/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isThisSecond\n * @category Second Helpers\n * @summary Is the given date in the same second as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same second as the current date?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is in this second\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If now is 25 September 2014 18:30:15.500,\n * // is 25 September 2014 18:30:15.000 in this second?\n * const result = isThisSecond(new Date(2014, 8, 25, 18, 30, 15))\n * //=> true\n */\nexport default function isThisSecond(dirtyDate) {\n  requiredArgs(1, arguments);\n  return isSameSecond(Date.now(), dirtyDate);\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,0BAA0B;AACnD,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,YAAYA,CAACC,SAAS,EAAE;EAC9CF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,OAAOJ,YAAY,CAACK,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,SAAS,CAAC;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}