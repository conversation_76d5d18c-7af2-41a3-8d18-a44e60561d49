{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: 'តិចជាង {{count}} វិនាទី',\n  xSeconds: '{{count}} វិនាទី',\n  halfAMinute: 'កន្លះនាទី',\n  lessThanXMinutes: 'តិចជាង {{count}} នាទី',\n  xMinutes: '{{count}} នាទី',\n  aboutXHours: 'ប្រហែល {{count}} ម៉ោង',\n  xHours: '{{count}} ម៉ោង',\n  xDays: '{{count}} ថ្ងៃ',\n  aboutXWeeks: 'ប្រហែល {{count}} សប្តាហ៍',\n  xWeeks: '{{count}} សប្តាហ៍',\n  aboutXMonths: 'ប្រហែល {{count}} ខែ',\n  xMonths: '{{count}} ខែ',\n  aboutXYears: 'ប្រហែល {{count}} ឆ្នាំ',\n  xYears: '{{count}} ឆ្នាំ',\n  overXYears: 'ជាង {{count}} ឆ្នាំ',\n  almostXYears: 'ជិត {{count}} ឆ្នាំ'\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var tokenValue = formatDistanceLocale[token];\n  var result = tokenValue;\n  if (typeof count === 'number') {\n    result = result.replace('{{count}}', count.toString());\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'ក្នុងរយៈពេល ' + result;\n    } else {\n      return result + 'មុន';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "tokenValue", "result", "replace", "toString", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/km/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: 'តិចជាង {{count}} វិនាទី',\n  xSeconds: '{{count}} វិនាទី',\n  halfAMinute: 'កន្លះនាទី',\n  lessThanXMinutes: 'តិចជាង {{count}} នាទី',\n  xMinutes: '{{count}} នាទី',\n  aboutXHours: 'ប្រហែល {{count}} ម៉ោង',\n  xHours: '{{count}} ម៉ោង',\n  xDays: '{{count}} ថ្ងៃ',\n  aboutXWeeks: 'ប្រហែល {{count}} សប្តាហ៍',\n  xWeeks: '{{count}} សប្តាហ៍',\n  aboutXMonths: 'ប្រហែល {{count}} ខែ',\n  xMonths: '{{count}} ខែ',\n  aboutXYears: 'ប្រហែល {{count}} ឆ្នាំ',\n  xYears: '{{count}} ឆ្នាំ',\n  overXYears: 'ជាង {{count}} ឆ្នាំ',\n  almostXYears: 'ជិត {{count}} ឆ្នាំ'\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var tokenValue = formatDistanceLocale[token];\n  var result = tokenValue;\n  if (typeof count === 'number') {\n    result = result.replace('{{count}}', count.toString());\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'ក្នុងរយៈពេល ' + result;\n    } else {\n      return result + 'មុន';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE,yBAAyB;EAC3CC,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE,WAAW;EACxBC,gBAAgB,EAAE,uBAAuB;EACzCC,QAAQ,EAAE,gBAAgB;EAC1BC,WAAW,EAAE,uBAAuB;EACpCC,MAAM,EAAE,gBAAgB;EACxBC,KAAK,EAAE,gBAAgB;EACvBC,WAAW,EAAE,0BAA0B;EACvCC,MAAM,EAAE,mBAAmB;EAC3BC,YAAY,EAAE,qBAAqB;EACnCC,OAAO,EAAE,cAAc;EACvBC,WAAW,EAAE,wBAAwB;EACrCC,MAAM,EAAE,iBAAiB;EACzBC,UAAU,EAAE,qBAAqB;EACjCC,YAAY,EAAE;AAChB,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,UAAU,GAAGrB,oBAAoB,CAACkB,KAAK,CAAC;EAC5C,IAAII,MAAM,GAAGD,UAAU;EACvB,IAAI,OAAOF,KAAK,KAAK,QAAQ,EAAE;IAC7BG,MAAM,GAAGA,MAAM,CAACC,OAAO,CAAC,WAAW,EAAEJ,KAAK,CAACK,QAAQ,CAAC,CAAC,CAAC;EACxD;EACA,IAAIJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,cAAc,GAAGJ,MAAM;IAChC,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,KAAK;IACvB;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}