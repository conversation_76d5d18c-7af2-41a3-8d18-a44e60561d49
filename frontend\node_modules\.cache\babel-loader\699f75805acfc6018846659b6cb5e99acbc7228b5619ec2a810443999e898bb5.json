{"ast": null, "code": "import buildFormatLongFn from \"../../../_lib/buildFormatLongFn/index.js\"; // Ref: https://www.unicode.org/cldr/charts/32/summary/ta.html\n// CLDR #1846 - #1849\nvar dateFormats = {\n  full: 'EEEE, d MMMM, y',\n  long: 'd MMMM, y',\n  medium: 'd MMM, y',\n  short: 'd/M/yy'\n};\n\n// CLDR #1850 - #1853\nvar timeFormats = {\n  full: 'a h:mm:ss zzzz',\n  long: 'a h:mm:ss z',\n  medium: 'a h:mm:ss',\n  short: 'a h:mm'\n};\nvar dateTimeFormats = {\n  full: '{{date}} {{time}}',\n  long: '{{date}} {{time}}',\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nexport default formatLong;", "map": {"version": 3, "names": ["buildFormatLongFn", "dateFormats", "full", "long", "medium", "short", "timeFormats", "dateTimeFormats", "formatLong", "date", "formats", "defaultWidth", "time", "dateTime"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/ta/_lib/formatLong/index.js"], "sourcesContent": ["import buildFormatLongFn from \"../../../_lib/buildFormatLongFn/index.js\"; // Ref: https://www.unicode.org/cldr/charts/32/summary/ta.html\n// CLDR #1846 - #1849\nvar dateFormats = {\n  full: 'EEEE, d MMMM, y',\n  long: 'd MMMM, y',\n  medium: 'd MMM, y',\n  short: 'd/M/yy'\n};\n\n// CLDR #1850 - #1853\nvar timeFormats = {\n  full: 'a h:mm:ss zzzz',\n  long: 'a h:mm:ss z',\n  medium: 'a h:mm:ss',\n  short: 'a h:mm'\n};\nvar dateTimeFormats = {\n  full: '{{date}} {{time}}',\n  long: '{{date}} {{time}}',\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nexport default formatLong;"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,0CAA0C,CAAC,CAAC;AAC1E;AACA,IAAIC,WAAW,GAAG;EAChBC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,WAAW;EACjBC,MAAM,EAAE,UAAU;EAClBC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,IAAIC,WAAW,GAAG;EAChBJ,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE,aAAa;EACnBC,MAAM,EAAE,WAAW;EACnBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIE,eAAe,GAAG;EACpBL,IAAI,EAAE,mBAAmB;EACzBC,IAAI,EAAE,mBAAmB;EACzBC,MAAM,EAAE,oBAAoB;EAC5BC,KAAK,EAAE;AACT,CAAC;AACD,IAAIG,UAAU,GAAG;EACfC,IAAI,EAAET,iBAAiB,CAAC;IACtBU,OAAO,EAAET,WAAW;IACpBU,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,IAAI,EAAEZ,iBAAiB,CAAC;IACtBU,OAAO,EAAEJ,WAAW;IACpBK,YAAY,EAAE;EAChB,CAAC,CAAC;EACFE,QAAQ,EAAEb,iBAAiB,CAAC;IAC1BU,OAAO,EAAEH,eAAe;IACxBI,YAAY,EAAE;EAChB,CAAC;AACH,CAAC;AACD,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}