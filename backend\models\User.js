const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const UserSchema = new mongoose.Schema({
  username: {
    type: String,
    required: [true, 'اسم المستخدم مطلوب'],
    unique: true,
    trim: true,
    minlength: [3, 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل'],
    maxlength: [50, 'اسم المستخدم يجب أن يكون أقل من 50 حرف']
  },
  email: {
    type: String,
    trim: true,
    lowercase: true,
    match: [
      /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
      'يرجى إدخال بريد إلكتروني صحيح'
    ]
  },
  password: {
    type: String,
    required: [true, 'كلمة المرور مطلوبة'],
    minlength: [6, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'],
    select: false
  },
  fullName: {
    type: String,
    required: [true, 'الاسم الكامل مطلوب'],
    trim: true,
    maxlength: [100, 'الاسم الكامل يجب أن يكون أقل من 100 حرف']
  },
  role: {
    type: String,
    enum: {
      values: ['admin', 'coach'],
      message: 'الدور يجب أن يكون إما مدير أو مدرب'
    },
    default: 'coach'
  },
  phone: {
    type: String,
    trim: true,
    match: [/^[0-9+\-\s()]+$/, 'يرجى إدخال رقم هاتف صحيح']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastLogin: {
    type: Date
  },
  profileImage: {
    type: String,
    default: null
  },
  createdBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User'
  },
  notes: {
    type: String,
    maxlength: [500, 'الملاحظات يجب أن تكون أقل من 500 حرف']
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Create index for better performance
UserSchema.index({ username: 1 });
UserSchema.index({ email: 1 });
UserSchema.index({ role: 1 });
UserSchema.index({ isActive: 1 });

// Encrypt password using bcrypt
UserSchema.pre('save', async function(next) {
  // Only run this function if password was actually modified
  if (!this.isModified('password')) {
    next();
  }

  // Hash the password with cost of 12
  const salt = await bcrypt.genSalt(12);
  this.password = await bcrypt.hash(this.password, salt);
});

// Sign JWT and return
UserSchema.methods.getSignedJwtToken = function() {
  return jwt.sign({ id: this._id }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRE
  });
};

// Match user entered password to hashed password in database
UserSchema.methods.matchPassword = async function(enteredPassword) {
  return await bcrypt.compare(enteredPassword, this.password);
};

// Update last login
UserSchema.methods.updateLastLogin = async function() {
  this.lastLogin = new Date();
  await this.save({ validateBeforeSave: false });
};

// Virtual for role display in Arabic
UserSchema.virtual('roleArabic').get(function() {
  const roleMap = {
    'admin': 'مدير',
    'coach': 'مدرب'
  };
  return roleMap[this.role] || this.role;
});

// Static method to create default users
UserSchema.statics.createDefaultUsers = async function() {
  try {
    // Check if admin exists
    const adminExists = await this.findOne({ username: 'admin' });
    if (!adminExists) {
      await this.create({
        username: 'admin',
        password: '123456',
        fullName: 'مدير النظام',
        role: 'admin',
        email: '<EMAIL>'
      });
      console.log('✅ Default admin user created');
    }

    // Check if coach exists
    const coachExists = await this.findOne({ username: 'omar' });
    if (!coachExists) {
      await this.create({
        username: 'omar',
        password: '123456',
        fullName: 'المدرب عمر',
        role: 'coach',
        email: '<EMAIL>'
      });
      console.log('✅ Default coach user created');
    }
  } catch (error) {
    console.error('❌ Error creating default users:', error);
  }
};

module.exports = mongoose.model('User', UserSchema);
