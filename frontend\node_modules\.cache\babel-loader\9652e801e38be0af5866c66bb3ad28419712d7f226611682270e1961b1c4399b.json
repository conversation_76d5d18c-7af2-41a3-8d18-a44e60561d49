{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: {\n      standalone: 'мање од 1 секунде',\n      withPrepositionAgo: 'мање од 1 секунде',\n      withPrepositionIn: 'мање од 1 секунду'\n    },\n    dual: 'мање од {{count}} секунде',\n    other: 'мање од {{count}} секунди'\n  },\n  xSeconds: {\n    one: {\n      standalone: '1 секунда',\n      withPrepositionAgo: '1 секунде',\n      withPrepositionIn: '1 секунду'\n    },\n    dual: '{{count}} секунде',\n    other: '{{count}} секунди'\n  },\n  halfAMinute: 'пола минуте',\n  lessThanXMinutes: {\n    one: {\n      standalone: 'мање од 1 минуте',\n      withPrepositionAgo: 'мање од 1 минуте',\n      withPrepositionIn: 'мање од 1 минуту'\n    },\n    dual: 'мање од {{count}} минуте',\n    other: 'мање од {{count}} минута'\n  },\n  xMinutes: {\n    one: {\n      standalone: '1 минута',\n      withPrepositionAgo: '1 минуте',\n      withPrepositionIn: '1 минуту'\n    },\n    dual: '{{count}} минуте',\n    other: '{{count}} минута'\n  },\n  aboutXHours: {\n    one: {\n      standalone: 'око 1 сат',\n      withPrepositionAgo: 'око 1 сат',\n      withPrepositionIn: 'око 1 сат'\n    },\n    dual: 'око {{count}} сата',\n    other: 'око {{count}} сати'\n  },\n  xHours: {\n    one: {\n      standalone: '1 сат',\n      withPrepositionAgo: '1 сат',\n      withPrepositionIn: '1 сат'\n    },\n    dual: '{{count}} сата',\n    other: '{{count}} сати'\n  },\n  xDays: {\n    one: {\n      standalone: '1 дан',\n      withPrepositionAgo: '1 дан',\n      withPrepositionIn: '1 дан'\n    },\n    dual: '{{count}} дана',\n    other: '{{count}} дана'\n  },\n  aboutXWeeks: {\n    one: {\n      standalone: 'око 1 недељу',\n      withPrepositionAgo: 'око 1 недељу',\n      withPrepositionIn: 'око 1 недељу'\n    },\n    dual: 'око {{count}} недеље',\n    other: 'око {{count}} недеље'\n  },\n  xWeeks: {\n    one: {\n      standalone: '1 недељу',\n      withPrepositionAgo: '1 недељу',\n      withPrepositionIn: '1 недељу'\n    },\n    dual: '{{count}} недеље',\n    other: '{{count}} недеље'\n  },\n  aboutXMonths: {\n    one: {\n      standalone: 'око 1 месец',\n      withPrepositionAgo: 'око 1 месец',\n      withPrepositionIn: 'око 1 месец'\n    },\n    dual: 'око {{count}} месеца',\n    other: 'око {{count}} месеци'\n  },\n  xMonths: {\n    one: {\n      standalone: '1 месец',\n      withPrepositionAgo: '1 месец',\n      withPrepositionIn: '1 месец'\n    },\n    dual: '{{count}} месеца',\n    other: '{{count}} месеци'\n  },\n  aboutXYears: {\n    one: {\n      standalone: 'око 1 годину',\n      withPrepositionAgo: 'око 1 годину',\n      withPrepositionIn: 'око 1 годину'\n    },\n    dual: 'око {{count}} године',\n    other: 'око {{count}} година'\n  },\n  xYears: {\n    one: {\n      standalone: '1 година',\n      withPrepositionAgo: '1 године',\n      withPrepositionIn: '1 годину'\n    },\n    dual: '{{count}} године',\n    other: '{{count}} година'\n  },\n  overXYears: {\n    one: {\n      standalone: 'преко 1 годину',\n      withPrepositionAgo: 'преко 1 годину',\n      withPrepositionIn: 'преко 1 годину'\n    },\n    dual: 'преко {{count}} године',\n    other: 'преко {{count}} година'\n  },\n  almostXYears: {\n    one: {\n      standalone: 'готово 1 годину',\n      withPrepositionAgo: 'готово 1 годину',\n      withPrepositionIn: 'готово 1 годину'\n    },\n    dual: 'готово {{count}} године',\n    other: 'готово {{count}} година'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    if (options !== null && options !== void 0 && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        result = tokenValue.one.withPrepositionIn;\n      } else {\n        result = tokenValue.one.withPrepositionAgo;\n      }\n    } else {\n      result = tokenValue.one.standalone;\n    }\n  } else if (count % 10 > 1 && count % 10 < 5 &&\n  // if last digit is between 2 and 4\n  String(count).substr(-2, 1) !== '1' // unless the 2nd to last digit is \"1\"\n  ) {\n    result = tokenValue.dual.replace('{{count}}', String(count));\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'за ' + result;\n    } else {\n      return 'пре ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "standalone", "withPrepositionAgo", "withPrepositionIn", "dual", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "addSuffix", "comparison", "String", "substr", "replace"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/sr/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: {\n      standalone: 'мање од 1 секунде',\n      withPrepositionAgo: 'мање од 1 секунде',\n      withPrepositionIn: 'мање од 1 секунду'\n    },\n    dual: 'мање од {{count}} секунде',\n    other: 'мање од {{count}} секунди'\n  },\n  xSeconds: {\n    one: {\n      standalone: '1 секунда',\n      withPrepositionAgo: '1 секунде',\n      withPrepositionIn: '1 секунду'\n    },\n    dual: '{{count}} секунде',\n    other: '{{count}} секунди'\n  },\n  halfAMinute: 'пола минуте',\n  lessThanXMinutes: {\n    one: {\n      standalone: 'мање од 1 минуте',\n      withPrepositionAgo: 'мање од 1 минуте',\n      withPrepositionIn: 'мање од 1 минуту'\n    },\n    dual: 'мање од {{count}} минуте',\n    other: 'мање од {{count}} минута'\n  },\n  xMinutes: {\n    one: {\n      standalone: '1 минута',\n      withPrepositionAgo: '1 минуте',\n      withPrepositionIn: '1 минуту'\n    },\n    dual: '{{count}} минуте',\n    other: '{{count}} минута'\n  },\n  aboutXHours: {\n    one: {\n      standalone: 'око 1 сат',\n      withPrepositionAgo: 'око 1 сат',\n      withPrepositionIn: 'око 1 сат'\n    },\n    dual: 'око {{count}} сата',\n    other: 'око {{count}} сати'\n  },\n  xHours: {\n    one: {\n      standalone: '1 сат',\n      withPrepositionAgo: '1 сат',\n      withPrepositionIn: '1 сат'\n    },\n    dual: '{{count}} сата',\n    other: '{{count}} сати'\n  },\n  xDays: {\n    one: {\n      standalone: '1 дан',\n      withPrepositionAgo: '1 дан',\n      withPrepositionIn: '1 дан'\n    },\n    dual: '{{count}} дана',\n    other: '{{count}} дана'\n  },\n  aboutXWeeks: {\n    one: {\n      standalone: 'око 1 недељу',\n      withPrepositionAgo: 'око 1 недељу',\n      withPrepositionIn: 'око 1 недељу'\n    },\n    dual: 'око {{count}} недеље',\n    other: 'око {{count}} недеље'\n  },\n  xWeeks: {\n    one: {\n      standalone: '1 недељу',\n      withPrepositionAgo: '1 недељу',\n      withPrepositionIn: '1 недељу'\n    },\n    dual: '{{count}} недеље',\n    other: '{{count}} недеље'\n  },\n  aboutXMonths: {\n    one: {\n      standalone: 'око 1 месец',\n      withPrepositionAgo: 'око 1 месец',\n      withPrepositionIn: 'око 1 месец'\n    },\n    dual: 'око {{count}} месеца',\n    other: 'око {{count}} месеци'\n  },\n  xMonths: {\n    one: {\n      standalone: '1 месец',\n      withPrepositionAgo: '1 месец',\n      withPrepositionIn: '1 месец'\n    },\n    dual: '{{count}} месеца',\n    other: '{{count}} месеци'\n  },\n  aboutXYears: {\n    one: {\n      standalone: 'око 1 годину',\n      withPrepositionAgo: 'око 1 годину',\n      withPrepositionIn: 'око 1 годину'\n    },\n    dual: 'око {{count}} године',\n    other: 'око {{count}} година'\n  },\n  xYears: {\n    one: {\n      standalone: '1 година',\n      withPrepositionAgo: '1 године',\n      withPrepositionIn: '1 годину'\n    },\n    dual: '{{count}} године',\n    other: '{{count}} година'\n  },\n  overXYears: {\n    one: {\n      standalone: 'преко 1 годину',\n      withPrepositionAgo: 'преко 1 годину',\n      withPrepositionIn: 'преко 1 годину'\n    },\n    dual: 'преко {{count}} године',\n    other: 'преко {{count}} година'\n  },\n  almostXYears: {\n    one: {\n      standalone: 'готово 1 годину',\n      withPrepositionAgo: 'готово 1 годину',\n      withPrepositionIn: 'готово 1 годину'\n    },\n    dual: 'готово {{count}} године',\n    other: 'готово {{count}} година'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    if (options !== null && options !== void 0 && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        result = tokenValue.one.withPrepositionIn;\n      } else {\n        result = tokenValue.one.withPrepositionAgo;\n      }\n    } else {\n      result = tokenValue.one.standalone;\n    }\n  } else if (count % 10 > 1 && count % 10 < 5 &&\n  // if last digit is between 2 and 4\n  String(count).substr(-2, 1) !== '1' // unless the 2nd to last digit is \"1\"\n  ) {\n    result = tokenValue.dual.replace('{{count}}', String(count));\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'за ' + result;\n    } else {\n      return 'пре ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE;MACHC,UAAU,EAAE,mBAAmB;MAC/BC,kBAAkB,EAAE,mBAAmB;MACvCC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,2BAA2B;IACjCC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRN,GAAG,EAAE;MACHC,UAAU,EAAE,WAAW;MACvBC,kBAAkB,EAAE,WAAW;MAC/BC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,mBAAmB;IACzBC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,aAAa;EAC1BC,gBAAgB,EAAE;IAChBR,GAAG,EAAE;MACHC,UAAU,EAAE,kBAAkB;MAC9BC,kBAAkB,EAAE,kBAAkB;MACtCC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,0BAA0B;IAChCC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRT,GAAG,EAAE;MACHC,UAAU,EAAE,UAAU;MACtBC,kBAAkB,EAAE,UAAU;MAC9BC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXV,GAAG,EAAE;MACHC,UAAU,EAAE,WAAW;MACvBC,kBAAkB,EAAE,WAAW;MAC/BC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,oBAAoB;IAC1BC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNX,GAAG,EAAE;MACHC,UAAU,EAAE,OAAO;MACnBC,kBAAkB,EAAE,OAAO;MAC3BC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLZ,GAAG,EAAE;MACHC,UAAU,EAAE,OAAO;MACnBC,kBAAkB,EAAE,OAAO;MAC3BC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXb,GAAG,EAAE;MACHC,UAAU,EAAE,cAAc;MAC1BC,kBAAkB,EAAE,cAAc;MAClCC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNd,GAAG,EAAE;MACHC,UAAU,EAAE,UAAU;MACtBC,kBAAkB,EAAE,UAAU;MAC9BC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZf,GAAG,EAAE;MACHC,UAAU,EAAE,aAAa;MACzBC,kBAAkB,EAAE,aAAa;MACjCC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPhB,GAAG,EAAE;MACHC,UAAU,EAAE,SAAS;MACrBC,kBAAkB,EAAE,SAAS;MAC7BC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXjB,GAAG,EAAE;MACHC,UAAU,EAAE,cAAc;MAC1BC,kBAAkB,EAAE,cAAc;MAClCC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNlB,GAAG,EAAE;MACHC,UAAU,EAAE,UAAU;MACtBC,kBAAkB,EAAE,UAAU;MAC9BC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVnB,GAAG,EAAE;MACHC,UAAU,EAAE,gBAAgB;MAC5BC,kBAAkB,EAAE,gBAAgB;MACpCC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,wBAAwB;IAC9BC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZpB,GAAG,EAAE;MACHC,UAAU,EAAE,iBAAiB;MAC7BC,kBAAkB,EAAE,iBAAiB;MACrCC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,yBAAyB;IAC/BC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAG5B,oBAAoB,CAACwB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtB,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACG,SAAS,EAAE;MAC/D,IAAIH,OAAO,CAACI,UAAU,IAAIJ,OAAO,CAACI,UAAU,GAAG,CAAC,EAAE;QAChDH,MAAM,GAAGC,UAAU,CAAC1B,GAAG,CAACG,iBAAiB;MAC3C,CAAC,MAAM;QACLsB,MAAM,GAAGC,UAAU,CAAC1B,GAAG,CAACE,kBAAkB;MAC5C;IACF,CAAC,MAAM;MACLuB,MAAM,GAAGC,UAAU,CAAC1B,GAAG,CAACC,UAAU;IACpC;EACF,CAAC,MAAM,IAAIsB,KAAK,GAAG,EAAE,GAAG,CAAC,IAAIA,KAAK,GAAG,EAAE,GAAG,CAAC;EAC3C;EACAM,MAAM,CAACN,KAAK,CAAC,CAACO,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC;EAAA,EAClC;IACAL,MAAM,GAAGC,UAAU,CAACtB,IAAI,CAAC2B,OAAO,CAAC,WAAW,EAAEF,MAAM,CAACN,KAAK,CAAC,CAAC;EAC9D,CAAC,MAAM;IACLE,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAAC0B,OAAO,CAAC,WAAW,EAAEF,MAAM,CAACN,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACG,SAAS,EAAE;IAC/D,IAAIH,OAAO,CAACI,UAAU,IAAIJ,OAAO,CAACI,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAGH,MAAM;IACvB,CAAC,MAAM;MACL,OAAO,MAAM,GAAGA,MAAM;IACxB;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}