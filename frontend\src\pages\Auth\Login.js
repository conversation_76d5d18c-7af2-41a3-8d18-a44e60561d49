import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  Alert,
  Avatar,
  Container,
  InputAdornment,
  IconButton,
  Fade,
  CircularProgress,
} from '@mui/material';
import {
  SportsMartialArts,
  Visibility,
  VisibilityOff,
  Person,
  Lock,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate, useLocation } from 'react-router-dom';

const Login = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { login, error, loading, clearError } = useAuth();

  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loginSuccess, setLoginSuccess] = useState(false);

  // Get redirect path from location state
  const from = location.state?.from?.pathname || '/dashboard';

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
    
    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }
    
    // Clear auth error
    if (error) {
      clearError();
    }
  };

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const validateForm = () => {
    const errors = {};
    
    if (!formData.username.trim()) {
      errors.username = 'اسم المستخدم مطلوب';
    }
    
    if (!formData.password) {
      errors.password = 'كلمة المرور مطلوبة';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await login(formData.username, formData.password);

      if (result.success) {
        setLoginSuccess(true);
        // Small delay to show success state before redirect
        setTimeout(() => {
          navigate(from, { replace: true });
        }, 1000);
      }
    } catch (error) {
      console.error('Login error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Auto-fill demo credentials
  const fillDemoCredentials = (type) => {
    if (type === 'admin') {
      setFormData({ username: 'admin', password: '123456' });
    } else if (type === 'coach') {
      setFormData({ username: 'coach', password: '123456' });
    }
    clearError();
    setFormErrors({});
  };

  // Clear errors when component mounts
  useEffect(() => {
    clearError();
  }, [clearError]);

  return (
    <Container component="main" maxWidth="sm">
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          py: 4,
        }}
      >
        <Paper
          elevation={8}
          sx={{
            p: 4,
            width: '100%',
            maxWidth: 400,
            borderRadius: 3,
          }}
        >
          {/* Logo and Title */}
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              mb: 4,
            }}
          >
            <Avatar
              sx={{
                m: 1,
                bgcolor: 'primary.main',
                width: 64,
                height: 64,
              }}
            >
              <SportsMartialArts sx={{ fontSize: 32 }} />
            </Avatar>
            <Typography component="h1" variant="h4" fontWeight="bold" gutterBottom>
              أكاديمية التايكوندو
            </Typography>
            <Typography variant="body2" color="text.secondary" textAlign="center">
              نظام إدارة الأكاديمية
            </Typography>
          </Box>

          {/* Success Alert */}
          {loginSuccess && (
            <Fade in={loginSuccess}>
              <Alert severity="success" sx={{ mb: 3 }}>
                تم تسجيل الدخول بنجاح! جاري التوجيه...
              </Alert>
            </Fade>
          )}

          {/* Error Alert */}
          {error && !loginSuccess && (
            <Fade in={!!error}>
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            </Fade>
          )}

          {/* Login Form */}
          <Box component="form" onSubmit={handleSubmit} noValidate>
            <TextField
              margin="normal"
              required
              fullWidth
              id="username"
              label="اسم المستخدم"
              name="username"
              autoComplete="username"
              autoFocus
              value={formData.username}
              onChange={handleChange}
              error={!!formErrors.username}
              helperText={formErrors.username}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Person color="action" />
                  </InputAdornment>
                ),
              }}
            />
            
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="كلمة المرور"
              type={showPassword ? 'text' : 'password'}
              id="password"
              autoComplete="current-password"
              value={formData.password}
              onChange={handleChange}
              error={!!formErrors.password}
              helperText={formErrors.password}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Lock color="action" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={handleTogglePasswordVisibility}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
            
            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={loading || isSubmitting || loginSuccess}
              sx={{
                mt: 3,
                mb: 2,
                py: 1.5,
                fontSize: '1.1rem',
                fontWeight: 600,
                position: 'relative',
              }}
            >
              {isSubmitting && (
                <CircularProgress
                  size={20}
                  sx={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    marginLeft: '-10px',
                    marginTop: '-10px',
                    color: 'inherit',
                  }}
                />
              )}
              {loginSuccess
                ? 'تم بنجاح! جاري التوجيه...'
                : isSubmitting
                ? 'جاري تسجيل الدخول...'
                : 'تسجيل الدخول'
              }
            </Button>
          </Box>

          {/* Demo Accounts Info */}
          <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 2 }}>
            <Typography variant="body2" fontWeight="bold" gutterBottom>
              حسابات تجريبية:
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
              <Button
                size="small"
                variant="outlined"
                onClick={() => fillDemoCredentials('admin')}
                disabled={isSubmitting || loginSuccess}
                sx={{ fontSize: '0.75rem' }}
              >
                المدير: admin / 123456
              </Button>
              <Button
                size="small"
                variant="outlined"
                onClick={() => fillDemoCredentials('coach')}
                disabled={isSubmitting || loginSuccess}
                sx={{ fontSize: '0.75rem' }}
              >
                المدرب: coach / 123456
              </Button>
            </Box>
            <Typography variant="caption" color="text.secondary">
              انقر على أي حساب لملء البيانات تلقائياً
            </Typography>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default Login;
