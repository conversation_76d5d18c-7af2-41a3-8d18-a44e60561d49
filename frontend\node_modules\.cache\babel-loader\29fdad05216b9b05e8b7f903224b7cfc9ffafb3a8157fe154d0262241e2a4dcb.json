{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    standalone: {\n      one: 'vähem kui üks sekund',\n      other: 'vähem kui {{count}} sekundit'\n    },\n    withPreposition: {\n      one: 'vähem kui ühe sekundi',\n      other: 'vähem kui {{count}} sekundi'\n    }\n  },\n  xSeconds: {\n    standalone: {\n      one: 'üks sekund',\n      other: '{{count}} sekundit'\n    },\n    withPreposition: {\n      one: 'ühe sekundi',\n      other: '{{count}} sekundi'\n    }\n  },\n  halfAMinute: {\n    standalone: 'pool minutit',\n    withPreposition: 'poole minuti'\n  },\n  lessThanXMinutes: {\n    standalone: {\n      one: 'vähem kui üks minut',\n      other: 'vähem kui {{count}} minutit'\n    },\n    withPreposition: {\n      one: 'vähem kui ühe minuti',\n      other: 'vähem kui {{count}} minuti'\n    }\n  },\n  xMinutes: {\n    standalone: {\n      one: 'üks minut',\n      other: '{{count}} minutit'\n    },\n    withPreposition: {\n      one: 'ühe minuti',\n      other: '{{count}} minuti'\n    }\n  },\n  aboutXHours: {\n    standalone: {\n      one: 'umbes üks tund',\n      other: 'umbes {{count}} tundi'\n    },\n    withPreposition: {\n      one: 'umbes ühe tunni',\n      other: 'umbes {{count}} tunni'\n    }\n  },\n  xHours: {\n    standalone: {\n      one: 'üks tund',\n      other: '{{count}} tundi'\n    },\n    withPreposition: {\n      one: 'ühe tunni',\n      other: '{{count}} tunni'\n    }\n  },\n  xDays: {\n    standalone: {\n      one: 'üks päev',\n      other: '{{count}} päeva'\n    },\n    withPreposition: {\n      one: 'ühe päeva',\n      other: '{{count}} päeva'\n    }\n  },\n  aboutXWeeks: {\n    standalone: {\n      one: 'umbes üks nädal',\n      other: 'umbes {{count}} nädalat'\n    },\n    withPreposition: {\n      one: 'umbes ühe nädala',\n      other: 'umbes {{count}} nädala'\n    }\n  },\n  xWeeks: {\n    standalone: {\n      one: 'üks nädal',\n      other: '{{count}} nädalat'\n    },\n    withPreposition: {\n      one: 'ühe nädala',\n      other: '{{count}} nädala'\n    }\n  },\n  aboutXMonths: {\n    standalone: {\n      one: 'umbes üks kuu',\n      other: 'umbes {{count}} kuud'\n    },\n    withPreposition: {\n      one: 'umbes ühe kuu',\n      other: 'umbes {{count}} kuu'\n    }\n  },\n  xMonths: {\n    standalone: {\n      one: 'üks kuu',\n      other: '{{count}} kuud'\n    },\n    withPreposition: {\n      one: 'ühe kuu',\n      other: '{{count}} kuu'\n    }\n  },\n  aboutXYears: {\n    standalone: {\n      one: 'umbes üks aasta',\n      other: 'umbes {{count}} aastat'\n    },\n    withPreposition: {\n      one: 'umbes ühe aasta',\n      other: 'umbes {{count}} aasta'\n    }\n  },\n  xYears: {\n    standalone: {\n      one: 'üks aasta',\n      other: '{{count}} aastat'\n    },\n    withPreposition: {\n      one: 'ühe aasta',\n      other: '{{count}} aasta'\n    }\n  },\n  overXYears: {\n    standalone: {\n      one: 'rohkem kui üks aasta',\n      other: 'rohkem kui {{count}} aastat'\n    },\n    withPreposition: {\n      one: 'rohkem kui ühe aasta',\n      other: 'rohkem kui {{count}} aasta'\n    }\n  },\n  almostXYears: {\n    standalone: {\n      one: 'peaaegu üks aasta',\n      other: 'peaaegu {{count}} aastat'\n    },\n    withPreposition: {\n      one: 'peaaegu ühe aasta',\n      other: 'peaaegu {{count}} aasta'\n    }\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var usageGroup = options !== null && options !== void 0 && options.addSuffix ? formatDistanceLocale[token].withPreposition : formatDistanceLocale[token].standalone;\n  var result;\n  if (typeof usageGroup === 'string') {\n    result = usageGroup;\n  } else if (count === 1) {\n    result = usageGroup.one;\n  } else {\n    result = usageGroup.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + ' pärast';\n    } else {\n      return result + ' eest';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "standalone", "one", "other", "withPreposition", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "usageGroup", "addSuffix", "result", "replace", "String", "comparison"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/et/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    standalone: {\n      one: 'vähem kui üks sekund',\n      other: 'vähem kui {{count}} sekundit'\n    },\n    withPreposition: {\n      one: 'vähem kui ühe sekundi',\n      other: 'vähem kui {{count}} sekundi'\n    }\n  },\n  xSeconds: {\n    standalone: {\n      one: 'üks sekund',\n      other: '{{count}} sekundit'\n    },\n    withPreposition: {\n      one: 'ühe sekundi',\n      other: '{{count}} sekundi'\n    }\n  },\n  halfAMinute: {\n    standalone: 'pool minutit',\n    withPreposition: 'poole minuti'\n  },\n  lessThanXMinutes: {\n    standalone: {\n      one: 'vähem kui üks minut',\n      other: 'vähem kui {{count}} minutit'\n    },\n    withPreposition: {\n      one: 'vähem kui ühe minuti',\n      other: 'vähem kui {{count}} minuti'\n    }\n  },\n  xMinutes: {\n    standalone: {\n      one: 'üks minut',\n      other: '{{count}} minutit'\n    },\n    withPreposition: {\n      one: 'ühe minuti',\n      other: '{{count}} minuti'\n    }\n  },\n  aboutXHours: {\n    standalone: {\n      one: 'umbes üks tund',\n      other: 'umbes {{count}} tundi'\n    },\n    withPreposition: {\n      one: 'umbes ühe tunni',\n      other: 'umbes {{count}} tunni'\n    }\n  },\n  xHours: {\n    standalone: {\n      one: 'üks tund',\n      other: '{{count}} tundi'\n    },\n    withPreposition: {\n      one: 'ühe tunni',\n      other: '{{count}} tunni'\n    }\n  },\n  xDays: {\n    standalone: {\n      one: 'üks päev',\n      other: '{{count}} päeva'\n    },\n    withPreposition: {\n      one: 'ühe päeva',\n      other: '{{count}} päeva'\n    }\n  },\n  aboutXWeeks: {\n    standalone: {\n      one: 'umbes üks nädal',\n      other: 'umbes {{count}} nädalat'\n    },\n    withPreposition: {\n      one: 'umbes ühe nädala',\n      other: 'umbes {{count}} nädala'\n    }\n  },\n  xWeeks: {\n    standalone: {\n      one: 'üks nädal',\n      other: '{{count}} nädalat'\n    },\n    withPreposition: {\n      one: 'ühe nädala',\n      other: '{{count}} nädala'\n    }\n  },\n  aboutXMonths: {\n    standalone: {\n      one: 'umbes üks kuu',\n      other: 'umbes {{count}} kuud'\n    },\n    withPreposition: {\n      one: 'umbes ühe kuu',\n      other: 'umbes {{count}} kuu'\n    }\n  },\n  xMonths: {\n    standalone: {\n      one: 'üks kuu',\n      other: '{{count}} kuud'\n    },\n    withPreposition: {\n      one: 'ühe kuu',\n      other: '{{count}} kuu'\n    }\n  },\n  aboutXYears: {\n    standalone: {\n      one: 'umbes üks aasta',\n      other: 'umbes {{count}} aastat'\n    },\n    withPreposition: {\n      one: 'umbes ühe aasta',\n      other: 'umbes {{count}} aasta'\n    }\n  },\n  xYears: {\n    standalone: {\n      one: 'üks aasta',\n      other: '{{count}} aastat'\n    },\n    withPreposition: {\n      one: 'ühe aasta',\n      other: '{{count}} aasta'\n    }\n  },\n  overXYears: {\n    standalone: {\n      one: 'rohkem kui üks aasta',\n      other: 'rohkem kui {{count}} aastat'\n    },\n    withPreposition: {\n      one: 'rohkem kui ühe aasta',\n      other: 'rohkem kui {{count}} aasta'\n    }\n  },\n  almostXYears: {\n    standalone: {\n      one: 'peaaegu üks aasta',\n      other: 'peaaegu {{count}} aastat'\n    },\n    withPreposition: {\n      one: 'peaaegu ühe aasta',\n      other: 'peaaegu {{count}} aasta'\n    }\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var usageGroup = options !== null && options !== void 0 && options.addSuffix ? formatDistanceLocale[token].withPreposition : formatDistanceLocale[token].standalone;\n  var result;\n  if (typeof usageGroup === 'string') {\n    result = usageGroup;\n  } else if (count === 1) {\n    result = usageGroup.one;\n  } else {\n    result = usageGroup.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + ' pärast';\n    } else {\n      return result + ' eest';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,UAAU,EAAE;MACVC,GAAG,EAAE,sBAAsB;MAC3BC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,uBAAuB;MAC5BC,KAAK,EAAE;IACT;EACF,CAAC;EACDE,QAAQ,EAAE;IACRJ,UAAU,EAAE;MACVC,GAAG,EAAE,YAAY;MACjBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE;IACT;EACF,CAAC;EACDG,WAAW,EAAE;IACXL,UAAU,EAAE,cAAc;IAC1BG,eAAe,EAAE;EACnB,CAAC;EACDG,gBAAgB,EAAE;IAChBN,UAAU,EAAE;MACVC,GAAG,EAAE,qBAAqB;MAC1BC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,sBAAsB;MAC3BC,KAAK,EAAE;IACT;EACF,CAAC;EACDK,QAAQ,EAAE;IACRP,UAAU,EAAE;MACVC,GAAG,EAAE,WAAW;MAChBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,YAAY;MACjBC,KAAK,EAAE;IACT;EACF,CAAC;EACDM,WAAW,EAAE;IACXR,UAAU,EAAE;MACVC,GAAG,EAAE,gBAAgB;MACrBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,iBAAiB;MACtBC,KAAK,EAAE;IACT;EACF,CAAC;EACDO,MAAM,EAAE;IACNT,UAAU,EAAE;MACVC,GAAG,EAAE,UAAU;MACfC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,WAAW;MAChBC,KAAK,EAAE;IACT;EACF,CAAC;EACDQ,KAAK,EAAE;IACLV,UAAU,EAAE;MACVC,GAAG,EAAE,UAAU;MACfC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,WAAW;MAChBC,KAAK,EAAE;IACT;EACF,CAAC;EACDS,WAAW,EAAE;IACXX,UAAU,EAAE;MACVC,GAAG,EAAE,iBAAiB;MACtBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,kBAAkB;MACvBC,KAAK,EAAE;IACT;EACF,CAAC;EACDU,MAAM,EAAE;IACNZ,UAAU,EAAE;MACVC,GAAG,EAAE,WAAW;MAChBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,YAAY;MACjBC,KAAK,EAAE;IACT;EACF,CAAC;EACDW,YAAY,EAAE;IACZb,UAAU,EAAE;MACVC,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE;IACT;EACF,CAAC;EACDY,OAAO,EAAE;IACPd,UAAU,EAAE;MACVC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE;IACT;EACF,CAAC;EACDa,WAAW,EAAE;IACXf,UAAU,EAAE;MACVC,GAAG,EAAE,iBAAiB;MACtBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,iBAAiB;MACtBC,KAAK,EAAE;IACT;EACF,CAAC;EACDc,MAAM,EAAE;IACNhB,UAAU,EAAE;MACVC,GAAG,EAAE,WAAW;MAChBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,WAAW;MAChBC,KAAK,EAAE;IACT;EACF,CAAC;EACDe,UAAU,EAAE;IACVjB,UAAU,EAAE;MACVC,GAAG,EAAE,sBAAsB;MAC3BC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,sBAAsB;MAC3BC,KAAK,EAAE;IACT;EACF,CAAC;EACDgB,YAAY,EAAE;IACZlB,UAAU,EAAE;MACVC,GAAG,EAAE,mBAAmB;MACxBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,mBAAmB;MACxBC,KAAK,EAAE;IACT;EACF;AACF,CAAC;AACD,IAAIiB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,UAAU,GAAGD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACE,SAAS,GAAG1B,oBAAoB,CAACsB,KAAK,CAAC,CAACjB,eAAe,GAAGL,oBAAoB,CAACsB,KAAK,CAAC,CAACpB,UAAU;EACnK,IAAIyB,MAAM;EACV,IAAI,OAAOF,UAAU,KAAK,QAAQ,EAAE;IAClCE,MAAM,GAAGF,UAAU;EACrB,CAAC,MAAM,IAAIF,KAAK,KAAK,CAAC,EAAE;IACtBI,MAAM,GAAGF,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLwB,MAAM,GAAGF,UAAU,CAACrB,KAAK,CAACwB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACN,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACE,SAAS,EAAE;IAC/D,IAAIF,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOH,MAAM,GAAG,SAAS;IAC3B,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,OAAO;IACzB;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeN,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}