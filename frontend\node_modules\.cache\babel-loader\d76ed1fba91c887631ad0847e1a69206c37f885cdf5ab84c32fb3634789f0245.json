{"ast": null, "code": "// Same as fr\nimport formatDistance from \"../fr/_lib/formatDistance/index.js\";\nimport formatRelative from \"../fr/_lib/formatRelative/index.js\";\nimport localize from \"../fr/_lib/localize/index.js\";\nimport match from \"../fr/_lib/match/index.js\";\n// Unique for fr-CA\nimport formatLong from \"./_lib/formatLong/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary French locale (Canada).\n * @language French\n * @iso-639-2 fra\n * <AUTHOR> [@izeau]{@link https://github.com/izeau}\n * <AUTHOR> [@fbonzon]{@link https://github.com/fbonzon}\n * <AUTHOR> [@gpetrioli]{@link https://github.com/gpetrioli}\n */\nvar locale = {\n  code: 'fr-CA',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  // Unique for fr-CA\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1\n  }\n};\nexport default locale;", "map": {"version": 3, "names": ["formatDistance", "formatRelative", "localize", "match", "formatLong", "locale", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/fr-CA/index.js"], "sourcesContent": ["// Same as fr\nimport formatDistance from \"../fr/_lib/formatDistance/index.js\";\nimport formatRelative from \"../fr/_lib/formatRelative/index.js\";\nimport localize from \"../fr/_lib/localize/index.js\";\nimport match from \"../fr/_lib/match/index.js\";\n// Unique for fr-CA\nimport formatLong from \"./_lib/formatLong/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary French locale (Canada).\n * @language French\n * @iso-639-2 fra\n * <AUTHOR> [@izeau]{@link https://github.com/izeau}\n * <AUTHOR> [@fbonzon]{@link https://github.com/fbonzon}\n * <AUTHOR> [@gpetrioli]{@link https://github.com/gpetrioli}\n */\nvar locale = {\n  code: 'fr-CA',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  // Unique for fr-CA\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1\n  }\n};\nexport default locale;"], "mappings": "AAAA;AACA,OAAOA,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,QAAQ,MAAM,8BAA8B;AACnD,OAAOC,KAAK,MAAM,2BAA2B;AAC7C;AACA,OAAOC,UAAU,MAAM,4BAA4B;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,MAAM,GAAG;EACXC,IAAI,EAAE,OAAO;EACbN,cAAc,EAAEA,cAAc;EAC9BI,UAAU,EAAEA,UAAU;EACtBH,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZ;EACAI,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;AACD,eAAeJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}