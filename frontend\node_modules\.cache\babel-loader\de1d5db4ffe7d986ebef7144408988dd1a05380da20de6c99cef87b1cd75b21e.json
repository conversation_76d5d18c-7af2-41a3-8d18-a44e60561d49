{"ast": null, "code": "import { applyDefaultDate } from '../date-utils';\nexport const validateDate = ({\n  props,\n  value,\n  adapter\n}) => {\n  if (value === null) {\n    return null;\n  }\n  const {\n    shouldDisableDate,\n    shouldDisableMonth,\n    shouldDisableYear,\n    disablePast,\n    disableFuture,\n    timezone\n  } = props;\n  const now = adapter.utils.dateWithTimezone(undefined, timezone);\n  const minDate = applyDefaultDate(adapter.utils, props.minDate, adapter.defaultDates.minDate);\n  const maxDate = applyDefaultDate(adapter.utils, props.maxDate, adapter.defaultDates.maxDate);\n  switch (true) {\n    case !adapter.utils.isValid(value):\n      return 'invalidDate';\n    case Boolean(shouldDisableDate && shouldDisableDate(value)):\n      return 'shouldDisableDate';\n    case Boolean(shouldDisableMonth && shouldDisableMonth(value)):\n      return 'shouldDisableMonth';\n    case Boolean(shouldDisableYear && shouldDisableYear(value)):\n      return 'shouldDisableYear';\n    case Boolean(disableFuture && adapter.utils.isAfterDay(value, now)):\n      return 'disableFuture';\n    case Boolean(disablePast && adapter.utils.isBeforeDay(value, now)):\n      return 'disablePast';\n    case Boolean(minDate && adapter.utils.isBeforeDay(value, minDate)):\n      return 'minDate';\n    case Boolean(maxDate && adapter.utils.isAfterDay(value, maxDate)):\n      return 'maxDate';\n    default:\n      return null;\n  }\n};", "map": {"version": 3, "names": ["applyDefaultDate", "validateDate", "props", "value", "adapter", "shouldDisableDate", "shouldDisableMonth", "shouldDisableYear", "disablePast", "disableFuture", "timezone", "now", "utils", "dateWithTimezone", "undefined", "minDate", "defaultDates", "maxDate", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "isAfterDay", "isBeforeDay"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/x-date-pickers/internals/utils/validation/validateDate.js"], "sourcesContent": ["import { applyDefaultDate } from '../date-utils';\nexport const validateDate = ({\n  props,\n  value,\n  adapter\n}) => {\n  if (value === null) {\n    return null;\n  }\n  const {\n    shouldDisableDate,\n    shouldDisableMonth,\n    shouldDisableYear,\n    disablePast,\n    disableFuture,\n    timezone\n  } = props;\n  const now = adapter.utils.dateWithTimezone(undefined, timezone);\n  const minDate = applyDefaultDate(adapter.utils, props.minDate, adapter.defaultDates.minDate);\n  const maxDate = applyDefaultDate(adapter.utils, props.maxDate, adapter.defaultDates.maxDate);\n  switch (true) {\n    case !adapter.utils.isValid(value):\n      return 'invalidDate';\n    case Boolean(shouldDisableDate && shouldDisableDate(value)):\n      return 'shouldDisableDate';\n    case Boolean(shouldDisableMonth && shouldDisableMonth(value)):\n      return 'shouldDisableMonth';\n    case Boolean(shouldDisableYear && shouldDisableYear(value)):\n      return 'shouldDisableYear';\n    case Boolean(disableFuture && adapter.utils.isAfterDay(value, now)):\n      return 'disableFuture';\n    case Boolean(disablePast && adapter.utils.isBeforeDay(value, now)):\n      return 'disablePast';\n    case Boolean(minDate && adapter.utils.isBeforeDay(value, minDate)):\n      return 'minDate';\n    case Boolean(maxDate && adapter.utils.isAfterDay(value, maxDate)):\n      return 'maxDate';\n    default:\n      return null;\n  }\n};"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,eAAe;AAChD,OAAO,MAAMC,YAAY,GAAGA,CAAC;EAC3BC,KAAK;EACLC,KAAK;EACLC;AACF,CAAC,KAAK;EACJ,IAAID,KAAK,KAAK,IAAI,EAAE;IAClB,OAAO,IAAI;EACb;EACA,MAAM;IACJE,iBAAiB;IACjBC,kBAAkB;IAClBC,iBAAiB;IACjBC,WAAW;IACXC,aAAa;IACbC;EACF,CAAC,GAAGR,KAAK;EACT,MAAMS,GAAG,GAAGP,OAAO,CAACQ,KAAK,CAACC,gBAAgB,CAACC,SAAS,EAAEJ,QAAQ,CAAC;EAC/D,MAAMK,OAAO,GAAGf,gBAAgB,CAACI,OAAO,CAACQ,KAAK,EAAEV,KAAK,CAACa,OAAO,EAAEX,OAAO,CAACY,YAAY,CAACD,OAAO,CAAC;EAC5F,MAAME,OAAO,GAAGjB,gBAAgB,CAACI,OAAO,CAACQ,KAAK,EAAEV,KAAK,CAACe,OAAO,EAAEb,OAAO,CAACY,YAAY,CAACC,OAAO,CAAC;EAC5F,QAAQ,IAAI;IACV,KAAK,CAACb,OAAO,CAACQ,KAAK,CAACM,OAAO,CAACf,KAAK,CAAC;MAChC,OAAO,aAAa;IACtB,KAAKgB,OAAO,CAACd,iBAAiB,IAAIA,iBAAiB,CAACF,KAAK,CAAC,CAAC;MACzD,OAAO,mBAAmB;IAC5B,KAAKgB,OAAO,CAACb,kBAAkB,IAAIA,kBAAkB,CAACH,KAAK,CAAC,CAAC;MAC3D,OAAO,oBAAoB;IAC7B,KAAKgB,OAAO,CAACZ,iBAAiB,IAAIA,iBAAiB,CAACJ,KAAK,CAAC,CAAC;MACzD,OAAO,mBAAmB;IAC5B,KAAKgB,OAAO,CAACV,aAAa,IAAIL,OAAO,CAACQ,KAAK,CAACQ,UAAU,CAACjB,KAAK,EAAEQ,GAAG,CAAC,CAAC;MACjE,OAAO,eAAe;IACxB,KAAKQ,OAAO,CAACX,WAAW,IAAIJ,OAAO,CAACQ,KAAK,CAACS,WAAW,CAAClB,KAAK,EAAEQ,GAAG,CAAC,CAAC;MAChE,OAAO,aAAa;IACtB,KAAKQ,OAAO,CAACJ,OAAO,IAAIX,OAAO,CAACQ,KAAK,CAACS,WAAW,CAAClB,KAAK,EAAEY,OAAO,CAAC,CAAC;MAChE,OAAO,SAAS;IAClB,KAAKI,OAAO,CAACF,OAAO,IAAIb,OAAO,CAACQ,KAAK,CAACQ,UAAU,CAACjB,KAAK,EAAEc,OAAO,CAAC,CAAC;MAC/D,OAAO,SAAS;IAClB;MACE,OAAO,IAAI;EACf;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}