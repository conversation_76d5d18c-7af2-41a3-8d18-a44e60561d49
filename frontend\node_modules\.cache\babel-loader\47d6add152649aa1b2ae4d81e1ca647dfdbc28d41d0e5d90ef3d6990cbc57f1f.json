{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'mindre enn ett sekund',\n    other: 'mindre enn {{count}} sekunder'\n  },\n  xSeconds: {\n    one: 'ett sekund',\n    other: '{{count}} sekunder'\n  },\n  halfAMinute: 'et halvt minutt',\n  lessThanXMinutes: {\n    one: 'mindre enn ett minutt',\n    other: 'mindre enn {{count}} minutter'\n  },\n  xMinutes: {\n    one: 'ett minutt',\n    other: '{{count}} minutter'\n  },\n  aboutXHours: {\n    one: 'omtrent en time',\n    other: 'omtrent {{count}} timer'\n  },\n  xHours: {\n    one: 'en time',\n    other: '{{count}} timer'\n  },\n  xDays: {\n    one: 'en dag',\n    other: '{{count}} dager'\n  },\n  aboutXWeeks: {\n    one: 'omtrent en uke',\n    other: 'omtrent {{count}} uker'\n  },\n  xWeeks: {\n    one: 'en uke',\n    other: '{{count}} uker'\n  },\n  aboutXMonths: {\n    one: 'omtrent en måned',\n    other: 'omtrent {{count}} måneder'\n  },\n  xMonths: {\n    one: 'en måned',\n    other: '{{count}} måneder'\n  },\n  aboutXYears: {\n    one: 'omtrent ett år',\n    other: 'omtrent {{count}} år'\n  },\n  xYears: {\n    one: 'ett år',\n    other: '{{count}} år'\n  },\n  overXYears: {\n    one: 'over ett år',\n    other: 'over {{count}} år'\n  },\n  almostXYears: {\n    one: 'nesten ett år',\n    other: 'nesten {{count}} år'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'om ' + result;\n    } else {\n      return result + ' siden';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/nb/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'mindre enn ett sekund',\n    other: 'mindre enn {{count}} sekunder'\n  },\n  xSeconds: {\n    one: 'ett sekund',\n    other: '{{count}} sekunder'\n  },\n  halfAMinute: 'et halvt minutt',\n  lessThanXMinutes: {\n    one: 'mindre enn ett minutt',\n    other: 'mindre enn {{count}} minutter'\n  },\n  xMinutes: {\n    one: 'ett minutt',\n    other: '{{count}} minutter'\n  },\n  aboutXHours: {\n    one: 'omtrent en time',\n    other: 'omtrent {{count}} timer'\n  },\n  xHours: {\n    one: 'en time',\n    other: '{{count}} timer'\n  },\n  xDays: {\n    one: 'en dag',\n    other: '{{count}} dager'\n  },\n  aboutXWeeks: {\n    one: 'omtrent en uke',\n    other: 'omtrent {{count}} uker'\n  },\n  xWeeks: {\n    one: 'en uke',\n    other: '{{count}} uker'\n  },\n  aboutXMonths: {\n    one: 'omtrent en måned',\n    other: 'omtrent {{count}} måneder'\n  },\n  xMonths: {\n    one: 'en måned',\n    other: '{{count}} måneder'\n  },\n  aboutXYears: {\n    one: 'omtrent ett år',\n    other: 'omtrent {{count}} år'\n  },\n  xYears: {\n    one: 'ett år',\n    other: '{{count}} år'\n  },\n  overXYears: {\n    one: 'over ett år',\n    other: 'over {{count}} år'\n  },\n  almostXYears: {\n    one: 'nesten ett år',\n    other: 'nesten {{count}} år'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'om ' + result;\n    } else {\n      return result + ' siden';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,uBAAuB;IAC5BC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,iBAAiB;EAC9BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,uBAAuB;IAC5BC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAGL,MAAM;IACvB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,QAAQ;IAC1B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}