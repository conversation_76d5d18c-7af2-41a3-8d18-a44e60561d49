import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Avatar,
} from '@mui/material';
import { ExitToApp, Warning } from '@mui/icons-material';

const LogoutDialog = ({ open, onClose, onConfirm, user }) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          p: 1,
        },
      }}
    >
      <DialogTitle sx={{ textAlign: 'center', pb: 1 }}>
        <Avatar
          sx={{
            bgcolor: 'warning.main',
            width: 56,
            height: 56,
            mx: 'auto',
            mb: 2,
          }}
        >
          <Warning />
        </Avatar>
        <Typography variant="h6" fontWeight="bold">
          تأكيد تسجيل الخروج
        </Typography>
      </DialogTitle>

      <DialogContent sx={{ textAlign: 'center', py: 2 }}>
        <Typography variant="body1" color="text.secondary" gutterBottom>
          هل أنت متأكد من رغبتك في تسجيل الخروج؟
        </Typography>
        
        {user && (
          <Box
            sx={{
              mt: 2,
              p: 2,
              bgcolor: 'grey.50',
              borderRadius: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 2,
            }}
          >
            <Avatar
              sx={{
                width: 32,
                height: 32,
                bgcolor: 'primary.main',
                fontSize: '0.9rem',
              }}
            >
              {user.name?.charAt(0) || user.username?.charAt(0) || 'U'}
            </Avatar>
            <Box sx={{ textAlign: 'right' }}>
              <Typography variant="body2" fontWeight="bold">
                {user.name || user.username}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {user.role === 'admin' ? 'مدير' : 'مدرب'}
              </Typography>
            </Box>
          </Box>
        )}
        
        <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
          سيتم إعادة توجيهك إلى صفحة تسجيل الدخول
        </Typography>
      </DialogContent>

      <DialogActions sx={{ justifyContent: 'center', gap: 2, pb: 2 }}>
        <Button
          onClick={onClose}
          variant="outlined"
          sx={{ minWidth: 100 }}
        >
          إلغاء
        </Button>
        <Button
          onClick={onConfirm}
          variant="contained"
          color="error"
          startIcon={<ExitToApp />}
          sx={{ minWidth: 100 }}
        >
          تسجيل الخروج
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default LogoutDialog;
