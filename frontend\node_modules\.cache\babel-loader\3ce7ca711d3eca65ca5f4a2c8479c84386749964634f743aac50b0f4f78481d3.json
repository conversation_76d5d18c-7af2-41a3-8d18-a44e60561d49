{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['f.K.', 'n.K.'],\n  abbreviated: ['f.Kr.', 'n.Kr.'],\n  wide: ['foar <PERSON>', 'ne<PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['K1', 'K2', 'K3', 'K4'],\n  wide: ['1e fearnsjier', '2e fearnsjier', '3e fearnsjier', '4e fearnsjier']\n};\nvar monthValues = {\n  narrow: ['j', 'f', 'm', 'a', 'm', 'j', 'j', 'a', 's', 'o', 'n', 'd'],\n  abbreviated: ['jan.', 'feb.', 'mrt.', 'apr.', 'mai.', 'jun.', 'jul.', 'aug.', 'sep.', 'okt.', 'nov.', 'des.'],\n  wide: ['janne<PERSON>s', 'febrew<PERSON>', 'maart', 'april', 'maaie', 'juny', 'july', 'augustus', 'septimber', 'oktober', 'novimber', 'desimber']\n};\nvar dayValues = {\n  narrow: ['s', 'm', 't', 'w', 't', 'f', 's'],\n  short: ['si', 'mo', 'ti', 'wo', 'to', 'fr', 'so'],\n  abbreviated: ['snein', 'moa', 'tii', 'woa', 'ton', 'fre', 'sneon'],\n  wide: ['snein', 'moandei', 'tiisdei', 'woansdei', 'tongersdei', 'freed', 'sneon']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'middernacht',\n    noon: 'middei',\n    morning: 'moarns',\n    afternoon: 'middeis',\n    evening: 'jûns',\n    night: 'nachts'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'middernacht',\n    noon: 'middei',\n    morning: 'moarns',\n    afternoon: 'middeis',\n    evening: 'jûns',\n    night: 'nachts'\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'middernacht',\n    noon: 'middei',\n    morning: 'moarns',\n    afternoon: 'middeis',\n    evening: 'jûns',\n    night: 'nachts'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + 'e';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "ordinalNumber", "dirtyNumber", "_options", "number", "Number", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/fy/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['f.K.', 'n.K.'],\n  abbreviated: ['f.Kr.', 'n.Kr.'],\n  wide: ['foar <PERSON>', 'ne<PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['K1', 'K2', 'K3', 'K4'],\n  wide: ['1e fearnsjier', '2e fearnsjier', '3e fearnsjier', '4e fearnsjier']\n};\nvar monthValues = {\n  narrow: ['j', 'f', 'm', 'a', 'm', 'j', 'j', 'a', 's', 'o', 'n', 'd'],\n  abbreviated: ['jan.', 'feb.', 'mrt.', 'apr.', 'mai.', 'jun.', 'jul.', 'aug.', 'sep.', 'okt.', 'nov.', 'des.'],\n  wide: ['janne<PERSON>s', 'febrew<PERSON>', 'maart', 'april', 'maaie', 'juny', 'july', 'augustus', 'septimber', 'oktober', 'novimber', 'desimber']\n};\nvar dayValues = {\n  narrow: ['s', 'm', 't', 'w', 't', 'f', 's'],\n  short: ['si', 'mo', 'ti', 'wo', 'to', 'fr', 'so'],\n  abbreviated: ['snein', 'moa', 'tii', 'woa', 'ton', 'fre', 'sneon'],\n  wide: ['snein', 'moandei', 'tiisdei', 'woansdei', 'tongersdei', 'freed', 'sneon']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'middernacht',\n    noon: 'middei',\n    morning: 'moarns',\n    afternoon: 'middeis',\n    evening: 'jûns',\n    night: 'nachts'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'middernacht',\n    noon: 'middei',\n    morning: 'moarns',\n    afternoon: 'middeis',\n    evening: 'jûns',\n    night: 'nachts'\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'middernacht',\n    noon: 'middei',\n    morning: 'moarns',\n    afternoon: 'middeis',\n    evening: 'jûns',\n    night: 'nachts'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + 'e';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACxBC,WAAW,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EAC/BC,IAAI,EAAE,CAAC,cAAc,EAAE,aAAa;AACtC,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe;AAC3E,CAAC;AACD,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EAC7GC,IAAI,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU;AAC1I,CAAC;AACD,IAAIG,SAAS,GAAG;EACdL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDL,WAAW,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC;EAClEC,IAAI,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,OAAO,EAAE,OAAO;AAClF,CAAC;AACD,IAAIK,eAAe,GAAG;EACpBP,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,QAAQ,EAAE;EAChE,IAAIC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAChC,OAAOE,MAAM,GAAG,GAAG;AACrB,CAAC;AACD,IAAIE,QAAQ,GAAG;EACbL,aAAa,EAAEA,aAAa;EAC5BM,GAAG,EAAExB,eAAe,CAAC;IACnByB,MAAM,EAAExB,SAAS;IACjByB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE3B,eAAe,CAAC;IACvByB,MAAM,EAAEpB,aAAa;IACrBqB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAE7B,eAAe,CAAC;IACrByB,MAAM,EAAEnB,WAAW;IACnBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFI,GAAG,EAAE9B,eAAe,CAAC;IACnByB,MAAM,EAAElB,SAAS;IACjBmB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,SAAS,EAAE/B,eAAe,CAAC;IACzByB,MAAM,EAAEhB,eAAe;IACvBiB,YAAY,EAAE;EAChB,CAAC;AACH,CAAC;AACD,eAAeH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}