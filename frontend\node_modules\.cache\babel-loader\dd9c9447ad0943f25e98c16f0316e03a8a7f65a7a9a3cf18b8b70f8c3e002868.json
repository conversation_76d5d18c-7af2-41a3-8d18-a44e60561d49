{"ast": null, "code": "import isSameUTCWeek from \"../../../../_lib/isSameUTCWeek/index.js\";\nvar adjectivesLastWeek = {\n  masculine: 'ostatni',\n  feminine: 'ostatnia'\n};\nvar adjectivesThisWeek = {\n  masculine: 'ten',\n  feminine: 'ta'\n};\nvar adjectivesNextWeek = {\n  masculine: 'następny',\n  feminine: 'następna'\n};\nvar dayGrammaticalGender = {\n  0: 'feminine',\n  1: 'masculine',\n  2: 'masculine',\n  3: 'feminine',\n  4: 'masculine',\n  5: 'masculine',\n  6: 'feminine'\n};\nfunction dayAndTimeWithAdjective(token, date, baseDate, options) {\n  var adjectives;\n  if (isSameUTCWeek(date, baseDate, options)) {\n    adjectives = adjectivesThisWeek;\n  } else if (token === 'lastWeek') {\n    adjectives = adjectivesLastWeek;\n  } else if (token === 'nextWeek') {\n    adjectives = adjectivesNextWeek;\n  } else {\n    throw new Error(\"Cannot determine adjectives for token \".concat(token));\n  }\n  var day = date.getUTCDay();\n  var grammaticalGender = dayGrammaticalGender[day];\n  var adjective = adjectives[grammaticalGender];\n  return \"'\".concat(adjective, \"' eeee 'o' p\");\n}\nvar formatRelativeLocale = {\n  lastWeek: dayAndTimeWithAdjective,\n  yesterday: \"'wczoraj o' p\",\n  today: \"'dzisiaj o' p\",\n  tomorrow: \"'jutro o' p\",\n  nextWeek: dayAndTimeWithAdjective,\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(token, date, baseDate, options);\n  }\n  return format;\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["isSameUTCWeek", "adjectivesLastWeek", "masculine", "feminine", "adjectivesThisWeek", "adjectivesNextWeek", "dayGram<PERSON><PERSON><PERSON>", "dayAndTimeWithAdjective", "token", "date", "baseDate", "options", "adjectives", "Error", "concat", "day", "getUTCDay", "grammaticalGender", "adjective", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "format"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/pl/_lib/formatRelative/index.js"], "sourcesContent": ["import isSameUTCWeek from \"../../../../_lib/isSameUTCWeek/index.js\";\nvar adjectivesLastWeek = {\n  masculine: 'ostatni',\n  feminine: 'ostatnia'\n};\nvar adjectivesThisWeek = {\n  masculine: 'ten',\n  feminine: 'ta'\n};\nvar adjectivesNextWeek = {\n  masculine: 'następny',\n  feminine: 'następna'\n};\nvar dayGrammaticalGender = {\n  0: 'feminine',\n  1: 'masculine',\n  2: 'masculine',\n  3: 'feminine',\n  4: 'masculine',\n  5: 'masculine',\n  6: 'feminine'\n};\nfunction dayAndTimeWithAdjective(token, date, baseDate, options) {\n  var adjectives;\n  if (isSameUTCWeek(date, baseDate, options)) {\n    adjectives = adjectivesThisWeek;\n  } else if (token === 'lastWeek') {\n    adjectives = adjectivesLastWeek;\n  } else if (token === 'nextWeek') {\n    adjectives = adjectivesNextWeek;\n  } else {\n    throw new Error(\"Cannot determine adjectives for token \".concat(token));\n  }\n  var day = date.getUTCDay();\n  var grammaticalGender = dayGrammaticalGender[day];\n  var adjective = adjectives[grammaticalGender];\n  return \"'\".concat(adjective, \"' eeee 'o' p\");\n}\nvar formatRelativeLocale = {\n  lastWeek: dayAndTimeWithAdjective,\n  yesterday: \"'wczoraj o' p\",\n  today: \"'dzisiaj o' p\",\n  tomorrow: \"'jutro o' p\",\n  nextWeek: dayAndTimeWithAdjective,\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(token, date, baseDate, options);\n  }\n  return format;\n};\nexport default formatRelative;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,yCAAyC;AACnE,IAAIC,kBAAkB,GAAG;EACvBC,SAAS,EAAE,SAAS;EACpBC,QAAQ,EAAE;AACZ,CAAC;AACD,IAAIC,kBAAkB,GAAG;EACvBF,SAAS,EAAE,KAAK;EAChBC,QAAQ,EAAE;AACZ,CAAC;AACD,IAAIE,kBAAkB,GAAG;EACvBH,SAAS,EAAE,UAAU;EACrBC,QAAQ,EAAE;AACZ,CAAC;AACD,IAAIG,oBAAoB,GAAG;EACzB,CAAC,EAAE,UAAU;EACb,CAAC,EAAE,WAAW;EACd,CAAC,EAAE,WAAW;EACd,CAAC,EAAE,UAAU;EACb,CAAC,EAAE,WAAW;EACd,CAAC,EAAE,WAAW;EACd,CAAC,EAAE;AACL,CAAC;AACD,SAASC,uBAAuBA,CAACC,KAAK,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC/D,IAAIC,UAAU;EACd,IAAIZ,aAAa,CAACS,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAE;IAC1CC,UAAU,GAAGR,kBAAkB;EACjC,CAAC,MAAM,IAAII,KAAK,KAAK,UAAU,EAAE;IAC/BI,UAAU,GAAGX,kBAAkB;EACjC,CAAC,MAAM,IAAIO,KAAK,KAAK,UAAU,EAAE;IAC/BI,UAAU,GAAGP,kBAAkB;EACjC,CAAC,MAAM;IACL,MAAM,IAAIQ,KAAK,CAAC,wCAAwC,CAACC,MAAM,CAACN,KAAK,CAAC,CAAC;EACzE;EACA,IAAIO,GAAG,GAAGN,IAAI,CAACO,SAAS,CAAC,CAAC;EAC1B,IAAIC,iBAAiB,GAAGX,oBAAoB,CAACS,GAAG,CAAC;EACjD,IAAIG,SAAS,GAAGN,UAAU,CAACK,iBAAiB,CAAC;EAC7C,OAAO,GAAG,CAACH,MAAM,CAACI,SAAS,EAAE,cAAc,CAAC;AAC9C;AACA,IAAIC,oBAAoB,GAAG;EACzBC,QAAQ,EAAEb,uBAAuB;EACjCc,SAAS,EAAE,eAAe;EAC1BC,KAAK,EAAE,eAAe;EACtBC,QAAQ,EAAE,aAAa;EACvBC,QAAQ,EAAEjB,uBAAuB;EACjCkB,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAAClB,KAAK,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC3E,IAAIgB,MAAM,GAAGR,oBAAoB,CAACX,KAAK,CAAC;EACxC,IAAI,OAAOmB,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACnB,KAAK,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EAC/C;EACA,OAAOgB,MAAM;AACf,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}