{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'minna en 1 sekúnda',\n    other: 'minna en {{count}} sekúndur'\n  },\n  xSeconds: {\n    one: '1 sekúnda',\n    other: '{{count}} sekúndur'\n  },\n  halfAMinute: 'hálf m<PERSON>',\n  lessThanXMinutes: {\n    one: 'minna en 1 mínúta',\n    other: 'minna en {{count}} mínútur'\n  },\n  xMinutes: {\n    one: '1 mínúta',\n    other: '{{count}} mínútur'\n  },\n  aboutXHours: {\n    one: 'u.þ.b. 1 klukkustund',\n    other: 'u.þ.b. {{count}} klukkustundir'\n  },\n  xHours: {\n    one: '1 klukkustund',\n    other: '{{count}} klukkustundir'\n  },\n  xDays: {\n    one: '1 dagur',\n    other: '{{count}} dagar'\n  },\n  aboutXWeeks: {\n    one: 'um viku',\n    other: 'um {{count}} vikur'\n  },\n  xWeeks: {\n    one: '1 viku',\n    other: '{{count}} vikur'\n  },\n  aboutXMonths: {\n    one: 'u.þ.b. 1 mánuður',\n    other: 'u.þ.b. {{count}} mánuðir'\n  },\n  xMonths: {\n    one: '1 mánuður',\n    other: '{{count}} mánuðir'\n  },\n  aboutXYears: {\n    one: 'u.þ.b. 1 ár',\n    other: 'u.þ.b. {{count}} ár'\n  },\n  xYears: {\n    one: '1 ár',\n    other: '{{count}} ár'\n  },\n  overXYears: {\n    one: 'meira en 1 ár',\n    other: 'meira en {{count}} ár'\n  },\n  almostXYears: {\n    one: 'næstum 1 ár',\n    other: 'næstum {{count}} ár'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', count.toString());\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'í ' + result;\n    } else {\n      return result + ' síðan';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "toString", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/is/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'minna en 1 sekúnda',\n    other: 'minna en {{count}} sekúndur'\n  },\n  xSeconds: {\n    one: '1 sekúnda',\n    other: '{{count}} sekúndur'\n  },\n  halfAMinute: 'hálf m<PERSON>',\n  lessThanXMinutes: {\n    one: 'minna en 1 mínúta',\n    other: 'minna en {{count}} mínútur'\n  },\n  xMinutes: {\n    one: '1 mínúta',\n    other: '{{count}} mínútur'\n  },\n  aboutXHours: {\n    one: 'u.þ.b. 1 klukkustund',\n    other: 'u.þ.b. {{count}} klukkustundir'\n  },\n  xHours: {\n    one: '1 klukkustund',\n    other: '{{count}} klukkustundir'\n  },\n  xDays: {\n    one: '1 dagur',\n    other: '{{count}} dagar'\n  },\n  aboutXWeeks: {\n    one: 'um viku',\n    other: 'um {{count}} vikur'\n  },\n  xWeeks: {\n    one: '1 viku',\n    other: '{{count}} vikur'\n  },\n  aboutXMonths: {\n    one: 'u.þ.b. 1 mánuður',\n    other: 'u.þ.b. {{count}} mánuðir'\n  },\n  xMonths: {\n    one: '1 mánuður',\n    other: '{{count}} mánuðir'\n  },\n  aboutXYears: {\n    one: 'u.þ.b. 1 ár',\n    other: 'u.þ.b. {{count}} ár'\n  },\n  xYears: {\n    one: '1 ár',\n    other: '{{count}} ár'\n  },\n  overXYears: {\n    one: 'meira en 1 ár',\n    other: 'meira en {{count}} ár'\n  },\n  almostXYears: {\n    one: 'næstum 1 ár',\n    other: 'næstum {{count}} ár'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', count.toString());\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'í ' + result;\n    } else {\n      return result + ' síðan';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,aAAa;EAC1BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEJ,KAAK,CAACK,QAAQ,CAAC,CAAC,CAAC;EAClE;EACA,IAAIJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,IAAI,GAAGL,MAAM;IACtB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,QAAQ;IAC1B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}