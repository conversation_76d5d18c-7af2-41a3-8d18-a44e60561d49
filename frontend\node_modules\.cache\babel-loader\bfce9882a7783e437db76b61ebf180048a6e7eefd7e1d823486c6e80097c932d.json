{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'nas lugha na diog',\n    other: 'nas lugha na {{count}} diogan'\n  },\n  xSeconds: {\n    one: '1 diog',\n    two: '2 dhiog',\n    twenty: '20 diog',\n    other: '{{count}} diogan'\n  },\n  halfAMinute: 'leth mhionaid',\n  lessThanXMinutes: {\n    one: 'nas lugha na mionaid',\n    other: 'nas lugha na {{count}} mionaidean'\n  },\n  xMinutes: {\n    one: '1 mionaid',\n    two: '2 mhionaid',\n    twenty: '20 mionaid',\n    other: '{{count}} mionaidean'\n  },\n  aboutXHours: {\n    one: 'mu uair de thìde',\n    other: 'mu {{count}} uairean de thìde'\n  },\n  xHours: {\n    one: '1 uair de thìde',\n    two: '2 uair de thìde',\n    twenty: '20 uair de thìde',\n    other: '{{count}} uairean de thìde'\n  },\n  xDays: {\n    one: '1 là',\n    other: '{{count}} là'\n  },\n  aboutXWeeks: {\n    one: 'mu 1 seachdain',\n    other: 'mu {{count}} seachdainean'\n  },\n  xWeeks: {\n    one: '1 seachdain',\n    other: '{{count}} seachdainean'\n  },\n  aboutXMonths: {\n    one: 'mu mhìos',\n    other: 'mu {{count}} mìosan'\n  },\n  xMonths: {\n    one: '1 mìos',\n    other: '{{count}} mìosan'\n  },\n  aboutXYears: {\n    one: 'mu bhliadhna',\n    other: 'mu {{count}} bliadhnaichean'\n  },\n  xYears: {\n    one: '1 bhliadhna',\n    other: '{{count}} bliadhna'\n  },\n  overXYears: {\n    one: 'còrr is bliadhna',\n    other: 'còrr is {{count}} bliadhnaichean'\n  },\n  almostXYears: {\n    one: 'cha mhòr bliadhna',\n    other: 'cha mhòr {{count}} bliadhnaichean'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 2 && !!tokenValue.two) {\n    result = tokenValue.two;\n  } else if (count === 20 && !!tokenValue.twenty) {\n    result = tokenValue.twenty;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'ann an ' + result;\n    } else {\n      return 'o chionn ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "two", "twenty", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/gd/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'nas lugha na diog',\n    other: 'nas lugha na {{count}} diogan'\n  },\n  xSeconds: {\n    one: '1 diog',\n    two: '2 dhiog',\n    twenty: '20 diog',\n    other: '{{count}} diogan'\n  },\n  halfAMinute: 'leth mhionaid',\n  lessThanXMinutes: {\n    one: 'nas lugha na mionaid',\n    other: 'nas lugha na {{count}} mionaidean'\n  },\n  xMinutes: {\n    one: '1 mionaid',\n    two: '2 mhionaid',\n    twenty: '20 mionaid',\n    other: '{{count}} mionaidean'\n  },\n  aboutXHours: {\n    one: 'mu uair de thìde',\n    other: 'mu {{count}} uairean de thìde'\n  },\n  xHours: {\n    one: '1 uair de thìde',\n    two: '2 uair de thìde',\n    twenty: '20 uair de thìde',\n    other: '{{count}} uairean de thìde'\n  },\n  xDays: {\n    one: '1 là',\n    other: '{{count}} là'\n  },\n  aboutXWeeks: {\n    one: 'mu 1 seachdain',\n    other: 'mu {{count}} seachdainean'\n  },\n  xWeeks: {\n    one: '1 seachdain',\n    other: '{{count}} seachdainean'\n  },\n  aboutXMonths: {\n    one: 'mu mhìos',\n    other: 'mu {{count}} mìosan'\n  },\n  xMonths: {\n    one: '1 mìos',\n    other: '{{count}} mìosan'\n  },\n  aboutXYears: {\n    one: 'mu bhliadhna',\n    other: 'mu {{count}} bliadhnaichean'\n  },\n  xYears: {\n    one: '1 bhliadhna',\n    other: '{{count}} bliadhna'\n  },\n  overXYears: {\n    one: 'còrr is bliadhna',\n    other: 'còrr is {{count}} bliadhnaichean'\n  },\n  almostXYears: {\n    one: 'cha mhòr bliadhna',\n    other: 'cha mhòr {{count}} bliadhnaichean'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 2 && !!tokenValue.two) {\n    result = tokenValue.two;\n  } else if (count === 20 && !!tokenValue.twenty) {\n    result = tokenValue.twenty;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'ann an ' + result;\n    } else {\n      return 'o chionn ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,QAAQ;IACbG,GAAG,EAAE,SAAS;IACdC,MAAM,EAAE,SAAS;IACjBH,KAAK,EAAE;EACT,CAAC;EACDI,WAAW,EAAE,eAAe;EAC5BC,gBAAgB,EAAE;IAChBN,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE;EACT,CAAC;EACDM,QAAQ,EAAE;IACRP,GAAG,EAAE,WAAW;IAChBG,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAE,YAAY;IACpBH,KAAK,EAAE;EACT,CAAC;EACDO,WAAW,EAAE;IACXR,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDQ,MAAM,EAAE;IACNT,GAAG,EAAE,iBAAiB;IACtBG,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,kBAAkB;IAC1BH,KAAK,EAAE;EACT,CAAC;EACDS,KAAK,EAAE;IACLV,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EACDU,WAAW,EAAE;IACXX,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EACDW,MAAM,EAAE;IACNZ,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDY,YAAY,EAAE;IACZb,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDa,OAAO,EAAE;IACPd,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDc,WAAW,EAAE;IACXf,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EACDe,MAAM,EAAE;IACNhB,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDgB,UAAU,EAAE;IACVjB,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDiB,YAAY,EAAE;IACZlB,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIkB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAG1B,oBAAoB,CAACsB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACxB,GAAG;EACzB,CAAC,MAAM,IAAIqB,KAAK,KAAK,CAAC,IAAI,CAAC,CAACG,UAAU,CAACrB,GAAG,EAAE;IAC1CoB,MAAM,GAAGC,UAAU,CAACrB,GAAG;EACzB,CAAC,MAAM,IAAIkB,KAAK,KAAK,EAAE,IAAI,CAAC,CAACG,UAAU,CAACpB,MAAM,EAAE;IAC9CmB,MAAM,GAAGC,UAAU,CAACpB,MAAM;EAC5B,CAAC,MAAM;IACLmB,MAAM,GAAGC,UAAU,CAACvB,KAAK,CAACwB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,SAAS,GAAGL,MAAM;IAC3B,CAAC,MAAM;MACL,OAAO,WAAW,GAAGA,MAAM;IAC7B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}