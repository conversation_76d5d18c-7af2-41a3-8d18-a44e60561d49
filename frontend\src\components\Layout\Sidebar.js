import React from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Typography,
  Divider,
  Avatar,
} from '@mui/material';
import {
  Dashboard,
  People,
  Payment,
  EventAvailable,
  Assessment,
  Settings,
  SportsMartialArts,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const menuItems = [
  {
    text: 'لوحة التحكم',
    icon: <Dashboard />,
    path: '/dashboard',
    roles: ['admin', 'coach'],
  },
  {
    text: 'الطلاب',
    icon: <People />,
    path: '/students',
    roles: ['admin', 'coach'],
  },
  {
    text: 'الاشتراكات',
    icon: <Payment />,
    path: '/subscriptions',
    roles: ['admin', 'coach'],
  },
  {
    text: 'الحضور',
    icon: <EventAvailable />,
    path: '/attendance',
    roles: ['admin', 'coach'],
  },
  {
    text: 'التقارير',
    icon: <Assessment />,
    path: '/reports',
    roles: ['admin', 'coach'],
  },
  {
    text: 'الإعدادات',
    icon: <Settings />,
    path: '/settings',
    roles: ['admin'],
  },
];

const Sidebar = ({ onItemClick }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();

  const handleItemClick = (path) => {
    navigate(path);
    if (onItemClick) {
      onItemClick();
    }
  };

  const filteredMenuItems = menuItems.filter(item => 
    item.roles.includes(user?.role)
  );

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo/Brand */}
      <Toolbar
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          px: 2,
          py: 3,
          backgroundColor: 'primary.main',
          color: 'primary.contrastText',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar
            sx={{
              bgcolor: 'primary.light',
              width: 40,
              height: 40,
            }}
          >
            <SportsMartialArts />
          </Avatar>
          <Box>
            <Typography variant="h6" fontWeight="bold">
              أكاديمية التايكوندو
            </Typography>
            <Typography variant="caption" sx={{ opacity: 0.8 }}>
              نظام الإدارة
            </Typography>
          </Box>
        </Box>
      </Toolbar>

      <Divider />

      {/* Navigation Menu */}
      <List sx={{ flexGrow: 1, px: 1, py: 2 }}>
        {filteredMenuItems.map((item) => {
          const isActive = location.pathname === item.path || 
                          (item.path !== '/dashboard' && location.pathname.startsWith(item.path));
          
          return (
            <ListItem key={item.text} disablePadding sx={{ mb: 0.5 }}>
              <ListItemButton
                onClick={() => handleItemClick(item.path)}
                sx={{
                  borderRadius: 2,
                  mx: 1,
                  backgroundColor: isActive ? 'primary.main' : 'transparent',
                  color: isActive ? 'primary.contrastText' : 'text.primary',
                  '&:hover': {
                    backgroundColor: isActive ? 'primary.dark' : 'action.hover',
                  },
                  transition: 'all 0.2s ease-in-out',
                }}
              >
                <ListItemIcon
                  sx={{
                    color: isActive ? 'primary.contrastText' : 'text.secondary',
                    minWidth: 40,
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText
                  primary={item.text}
                  primaryTypographyProps={{
                    fontWeight: isActive ? 600 : 400,
                  }}
                />
              </ListItemButton>
            </ListItem>
          );
        })}
      </List>

      <Divider />

      {/* User Info */}
      <Box sx={{ p: 2 }}>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            p: 2,
            backgroundColor: 'grey.50',
            borderRadius: 2,
          }}
        >
          <Avatar
            sx={{ width: 36, height: 36 }}
            alt={user?.fullName}
            src={user?.avatar}
          >
            {user?.fullName?.charAt(0)}
          </Avatar>
          <Box sx={{ flexGrow: 1, minWidth: 0 }}>
            <Typography
              variant="body2"
              fontWeight="bold"
              noWrap
              sx={{ lineHeight: 1.2 }}
            >
              {user?.fullName}
            </Typography>
            <Typography
              variant="caption"
              color="text.secondary"
              noWrap
              sx={{ lineHeight: 1.2 }}
            >
              {user?.roleArabic}
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default Sidebar;
