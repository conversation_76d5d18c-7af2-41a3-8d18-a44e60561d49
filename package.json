{"name": "taekwondo-crm-system", "version": "1.0.0", "description": "Complete Arabic CRM System for Taekwondo Academy Management", "main": "index.js", "scripts": {"install-all": "npm install && cd backend && npm install && cd ../frontend && npm install", "dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd backend && npm run dev", "client": "cd frontend && npm start", "build": "cd frontend && npm run build", "start": "cd backend && npm start", "test": "cd backend && npm test && cd ../frontend && npm test", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f"}, "keywords": ["crm", "taekwondo", "academy", "management", "arabic", "react", "nodejs", "mongodb"], "author": "Taekwondo Academy", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}