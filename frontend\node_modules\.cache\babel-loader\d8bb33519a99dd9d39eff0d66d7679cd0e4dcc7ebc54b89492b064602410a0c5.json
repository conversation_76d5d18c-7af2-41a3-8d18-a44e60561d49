{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"props\", \"ref\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/material/styles';\nimport { usePicker } from '../usePicker';\nimport { LocalizationProvider } from '../../../LocalizationProvider';\nimport { PickersLayout } from '../../../PickersLayout';\nimport { DIALOG_WIDTH } from '../../constants/dimensions';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst PickerStaticLayout = styled(PickersLayout)(({\n  theme\n}) => ({\n  overflow: 'hidden',\n  minWidth: DIALOG_WIDTH,\n  backgroundColor: (theme.vars || theme).palette.background.paper\n}));\n\n/**\n * Hook managing all the single-date static pickers:\n * - StaticDatePicker\n * - StaticDateTimePicker\n * - StaticTimePicker\n */\nexport const useStaticPicker = _ref => {\n  var _slots$layout;\n  let {\n      props,\n      ref\n    } = _ref,\n    pickerParams = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const {\n    localeText,\n    slots,\n    slotProps,\n    className,\n    sx,\n    displayStaticWrapperAs,\n    autoFocus\n  } = props;\n  const {\n    layoutProps,\n    renderCurrentView\n  } = usePicker(_extends({}, pickerParams, {\n    props,\n    autoFocusView: autoFocus != null ? autoFocus : false,\n    additionalViewProps: {},\n    wrapperVariant: displayStaticWrapperAs\n  }));\n  const Layout = (_slots$layout = slots == null ? void 0 : slots.layout) != null ? _slots$layout : PickerStaticLayout;\n  const renderPicker = () => {\n    var _slotProps$layout, _slotProps$layout2, _slotProps$layout3;\n    return /*#__PURE__*/_jsx(LocalizationProvider, {\n      localeText: localeText,\n      children: /*#__PURE__*/_jsx(Layout, _extends({}, layoutProps, slotProps == null ? void 0 : slotProps.layout, {\n        slots: slots,\n        slotProps: slotProps,\n        sx: [...(Array.isArray(sx) ? sx : [sx]), ...(Array.isArray(slotProps == null || (_slotProps$layout = slotProps.layout) == null ? void 0 : _slotProps$layout.sx) ? slotProps.layout.sx : [slotProps == null || (_slotProps$layout2 = slotProps.layout) == null ? void 0 : _slotProps$layout2.sx])],\n        className: clsx(className, slotProps == null || (_slotProps$layout3 = slotProps.layout) == null ? void 0 : _slotProps$layout3.className),\n        ref: ref,\n        children: renderCurrentView()\n      }))\n    });\n  };\n  return {\n    renderPicker\n  };\n};", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "clsx", "styled", "usePicker", "LocalizationProvider", "PickersLayout", "DIALOG_WIDTH", "jsx", "_jsx", "PickerStaticLayout", "theme", "overflow", "min<PERSON><PERSON><PERSON>", "backgroundColor", "vars", "palette", "background", "paper", "useStaticPicker", "_ref", "_slots$layout", "props", "ref", "pickerParams", "localeText", "slots", "slotProps", "className", "sx", "displayStaticWrapperAs", "autoFocus", "layoutProps", "renderCurrentView", "autoFocusView", "additionalViewProps", "wrapperVariant", "Layout", "layout", "renderPicker", "_slotProps$layout", "_slotProps$layout2", "_slotProps$layout3", "children", "Array", "isArray"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useStaticPicker/useStaticPicker.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"props\", \"ref\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/material/styles';\nimport { usePicker } from '../usePicker';\nimport { LocalizationProvider } from '../../../LocalizationProvider';\nimport { PickersLayout } from '../../../PickersLayout';\nimport { DIALOG_WIDTH } from '../../constants/dimensions';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst PickerStaticLayout = styled(PickersLayout)(({\n  theme\n}) => ({\n  overflow: 'hidden',\n  minWidth: DIALOG_WIDTH,\n  backgroundColor: (theme.vars || theme).palette.background.paper\n}));\n\n/**\n * Hook managing all the single-date static pickers:\n * - StaticDatePicker\n * - StaticDateTimePicker\n * - StaticTimePicker\n */\nexport const useStaticPicker = _ref => {\n  var _slots$layout;\n  let {\n      props,\n      ref\n    } = _ref,\n    pickerParams = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const {\n    localeText,\n    slots,\n    slotProps,\n    className,\n    sx,\n    displayStaticWrapperAs,\n    autoFocus\n  } = props;\n  const {\n    layoutProps,\n    renderCurrentView\n  } = usePicker(_extends({}, pickerParams, {\n    props,\n    autoFocusView: autoFocus != null ? autoFocus : false,\n    additionalViewProps: {},\n    wrapperVariant: displayStaticWrapperAs\n  }));\n  const Layout = (_slots$layout = slots == null ? void 0 : slots.layout) != null ? _slots$layout : PickerStaticLayout;\n  const renderPicker = () => {\n    var _slotProps$layout, _slotProps$layout2, _slotProps$layout3;\n    return /*#__PURE__*/_jsx(LocalizationProvider, {\n      localeText: localeText,\n      children: /*#__PURE__*/_jsx(Layout, _extends({}, layoutProps, slotProps == null ? void 0 : slotProps.layout, {\n        slots: slots,\n        slotProps: slotProps,\n        sx: [...(Array.isArray(sx) ? sx : [sx]), ...(Array.isArray(slotProps == null || (_slotProps$layout = slotProps.layout) == null ? void 0 : _slotProps$layout.sx) ? slotProps.layout.sx : [slotProps == null || (_slotProps$layout2 = slotProps.layout) == null ? void 0 : _slotProps$layout2.sx])],\n        className: clsx(className, slotProps == null || (_slotProps$layout3 = slotProps.layout) == null ? void 0 : _slotProps$layout3.className),\n        ref: ref,\n        children: renderCurrentView()\n      }))\n    });\n  };\n  return {\n    renderPicker\n  };\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,oBAAoB,QAAQ,+BAA+B;AACpE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,kBAAkB,GAAGP,MAAM,CAACG,aAAa,CAAC,CAAC,CAAC;EAChDK;AACF,CAAC,MAAM;EACLC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAEN,YAAY;EACtBO,eAAe,EAAE,CAACH,KAAK,CAACI,IAAI,IAAIJ,KAAK,EAAEK,OAAO,CAACC,UAAU,CAACC;AAC5D,CAAC,CAAC,CAAC;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,eAAe,GAAGC,IAAI,IAAI;EACrC,IAAIC,aAAa;EACjB,IAAI;MACAC,KAAK;MACLC;IACF,CAAC,GAAGH,IAAI;IACRI,YAAY,GAAGzB,6BAA6B,CAACqB,IAAI,EAAEpB,SAAS,CAAC;EAC/D,MAAM;IACJyB,UAAU;IACVC,KAAK;IACLC,SAAS;IACTC,SAAS;IACTC,EAAE;IACFC,sBAAsB;IACtBC;EACF,CAAC,GAAGT,KAAK;EACT,MAAM;IACJU,WAAW;IACXC;EACF,CAAC,GAAG7B,SAAS,CAACN,QAAQ,CAAC,CAAC,CAAC,EAAE0B,YAAY,EAAE;IACvCF,KAAK;IACLY,aAAa,EAAEH,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAG,KAAK;IACpDI,mBAAmB,EAAE,CAAC,CAAC;IACvBC,cAAc,EAAEN;EAClB,CAAC,CAAC,CAAC;EACH,MAAMO,MAAM,GAAG,CAAChB,aAAa,GAAGK,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACY,MAAM,KAAK,IAAI,GAAGjB,aAAa,GAAGX,kBAAkB;EACnH,MAAM6B,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,iBAAiB,EAAEC,kBAAkB,EAAEC,kBAAkB;IAC7D,OAAO,aAAajC,IAAI,CAACJ,oBAAoB,EAAE;MAC7CoB,UAAU,EAAEA,UAAU;MACtBkB,QAAQ,EAAE,aAAalC,IAAI,CAAC4B,MAAM,EAAEvC,QAAQ,CAAC,CAAC,CAAC,EAAEkC,WAAW,EAAEL,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACW,MAAM,EAAE;QAC3GZ,KAAK,EAAEA,KAAK;QACZC,SAAS,EAAEA,SAAS;QACpBE,EAAE,EAAE,CAAC,IAAIe,KAAK,CAACC,OAAO,CAAChB,EAAE,CAAC,GAAGA,EAAE,GAAG,CAACA,EAAE,CAAC,CAAC,EAAE,IAAIe,KAAK,CAACC,OAAO,CAAClB,SAAS,IAAI,IAAI,IAAI,CAACa,iBAAiB,GAAGb,SAAS,CAACW,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,iBAAiB,CAACX,EAAE,CAAC,GAAGF,SAAS,CAACW,MAAM,CAACT,EAAE,GAAG,CAACF,SAAS,IAAI,IAAI,IAAI,CAACc,kBAAkB,GAAGd,SAAS,CAACW,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGG,kBAAkB,CAACZ,EAAE,CAAC,CAAC,CAAC;QACjSD,SAAS,EAAE1B,IAAI,CAAC0B,SAAS,EAAED,SAAS,IAAI,IAAI,IAAI,CAACe,kBAAkB,GAAGf,SAAS,CAACW,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGI,kBAAkB,CAACd,SAAS,CAAC;QACxIL,GAAG,EAAEA,GAAG;QACRoB,QAAQ,EAAEV,iBAAiB,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD,OAAO;IACLM;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}