{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'помалку од секунда',\n    other: 'помалку од {{count}} секунди'\n  },\n  xSeconds: {\n    one: '1 секунда',\n    other: '{{count}} секунди'\n  },\n  halfAMinute: 'половина минута',\n  lessThanXMinutes: {\n    one: 'помалку од минута',\n    other: 'помалку од {{count}} минути'\n  },\n  xMinutes: {\n    one: '1 минута',\n    other: '{{count}} минути'\n  },\n  aboutXHours: {\n    one: 'околу 1 час',\n    other: 'околу {{count}} часа'\n  },\n  xHours: {\n    one: '1 час',\n    other: '{{count}} часа'\n  },\n  xDays: {\n    one: '1 ден',\n    other: '{{count}} дена'\n  },\n  aboutXWeeks: {\n    one: 'околу 1 недела',\n    other: 'околу {{count}} месеци'\n  },\n  xWeeks: {\n    one: '1 недела',\n    other: '{{count}} недели'\n  },\n  aboutXMonths: {\n    one: 'околу 1 месец',\n    other: 'околу {{count}} недели'\n  },\n  xMonths: {\n    one: '1 месец',\n    other: '{{count}} месеци'\n  },\n  aboutXYears: {\n    one: 'околу 1 година',\n    other: 'околу {{count}} години'\n  },\n  xYears: {\n    one: '1 година',\n    other: '{{count}} години'\n  },\n  overXYears: {\n    one: 'повеќе од 1 година',\n    other: 'повеќе од {{count}} години'\n  },\n  almostXYears: {\n    one: 'безмалку 1 година',\n    other: 'безмалку {{count}} години'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'за ' + result;\n    } else {\n      return 'пред ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/mk/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'помалку од секунда',\n    other: 'помалку од {{count}} секунди'\n  },\n  xSeconds: {\n    one: '1 секунда',\n    other: '{{count}} секунди'\n  },\n  halfAMinute: 'половина минута',\n  lessThanXMinutes: {\n    one: 'помалку од минута',\n    other: 'помалку од {{count}} минути'\n  },\n  xMinutes: {\n    one: '1 минута',\n    other: '{{count}} минути'\n  },\n  aboutXHours: {\n    one: 'околу 1 час',\n    other: 'околу {{count}} часа'\n  },\n  xHours: {\n    one: '1 час',\n    other: '{{count}} часа'\n  },\n  xDays: {\n    one: '1 ден',\n    other: '{{count}} дена'\n  },\n  aboutXWeeks: {\n    one: 'околу 1 недела',\n    other: 'околу {{count}} месеци'\n  },\n  xWeeks: {\n    one: '1 недела',\n    other: '{{count}} недели'\n  },\n  aboutXMonths: {\n    one: 'околу 1 месец',\n    other: 'околу {{count}} недели'\n  },\n  xMonths: {\n    one: '1 месец',\n    other: '{{count}} месеци'\n  },\n  aboutXYears: {\n    one: 'околу 1 година',\n    other: 'околу {{count}} години'\n  },\n  xYears: {\n    one: '1 година',\n    other: '{{count}} години'\n  },\n  overXYears: {\n    one: 'повеќе од 1 година',\n    other: 'повеќе од {{count}} години'\n  },\n  almostXYears: {\n    one: 'безмалку 1 година',\n    other: 'безмалку {{count}} години'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'за ' + result;\n    } else {\n      return 'пред ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,iBAAiB;EAC9BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAGL,MAAM;IACvB,CAAC,MAAM;MACL,OAAO,OAAO,GAAGA,MAAM;IACzB;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}