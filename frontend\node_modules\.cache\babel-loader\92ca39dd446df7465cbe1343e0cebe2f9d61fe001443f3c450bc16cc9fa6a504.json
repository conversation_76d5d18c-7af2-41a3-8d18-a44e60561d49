{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"eeee 'إلي فات مع' p\",\n  yesterday: \"'البارح مع' p\",\n  today: \"'اليوم مع' p\",\n  tomorrow: \"'غدوة مع' p\",\n  nextWeek: \"eeee 'الجمعة الجاية مع' p 'نهار'\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/ar-TN/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: \"eeee 'إلي فات مع' p\",\n  yesterday: \"'البارح مع' p\",\n  today: \"'اليوم مع' p\",\n  tomorrow: \"'غدوة مع' p\",\n  nextWeek: \"eeee 'الجمعة الجاية مع' p 'نهار'\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,qBAAqB;EAC/BC,SAAS,EAAE,eAAe;EAC1BC,KAAK,EAAE,cAAc;EACrBC,QAAQ,EAAE,aAAa;EACvBC,QAAQ,EAAE,kCAAkC;EAC5CC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EAClD,OAAOR,oBAAoB,CAACQ,KAAK,CAAC;AACpC,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}