{"ast": null, "code": "import * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { unstable_useControlled as useControlled } from '@mui/utils';\nlet warnedOnceNotValidView = false;\nexport function useViews({\n  onChange,\n  onViewChange,\n  openTo,\n  view: inView,\n  views,\n  autoFocus,\n  focusedView: inFocusedView,\n  onFocusedViewChange\n}) {\n  var _views, _views2;\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnceNotValidView) {\n      if (inView != null && !views.includes(inView)) {\n        console.warn(`MUI: \\`view=\"${inView}\"\\` is not a valid prop.`, `It must be an element of \\`views=[\"${views.join('\", \"')}\"]\\`.`);\n        warnedOnceNotValidView = true;\n      }\n      if (inView == null && openTo != null && !views.includes(openTo)) {\n        console.warn(`MUI: \\`openTo=\"${openTo}\"\\` is not a valid prop.`, `It must be an element of \\`views=[\"${views.join('\", \"')}\"]\\`.`);\n        warnedOnceNotValidView = true;\n      }\n    }\n  }\n  const previousOpenTo = React.useRef(openTo);\n  const previousViews = React.useRef(views);\n  const defaultView = React.useRef(views.includes(openTo) ? openTo : views[0]);\n  const [view, setView] = useControlled({\n    name: 'useViews',\n    state: 'view',\n    controlled: inView,\n    default: defaultView.current\n  });\n  const defaultFocusedView = React.useRef(autoFocus ? view : null);\n  const [focusedView, setFocusedView] = useControlled({\n    name: 'useViews',\n    state: 'focusedView',\n    controlled: inFocusedView,\n    default: defaultFocusedView.current\n  });\n  React.useEffect(() => {\n    // Update the current view when `openTo` or `views` props change\n    if (previousOpenTo.current && previousOpenTo.current !== openTo || previousViews.current && previousViews.current.some(previousView => !views.includes(previousView))) {\n      setView(views.includes(openTo) ? openTo : views[0]);\n      previousViews.current = views;\n      previousOpenTo.current = openTo;\n    }\n  }, [openTo, setView, view, views]);\n  const viewIndex = views.indexOf(view);\n  const previousView = (_views = views[viewIndex - 1]) != null ? _views : null;\n  const nextView = (_views2 = views[viewIndex + 1]) != null ? _views2 : null;\n  const handleFocusedViewChange = useEventCallback((viewToFocus, hasFocus) => {\n    if (hasFocus) {\n      // Focus event\n      setFocusedView(viewToFocus);\n    } else {\n      // Blur event\n      setFocusedView(prevFocusedView => viewToFocus === prevFocusedView ? null : prevFocusedView // If false the blur is due to view switching\n      );\n    }\n    onFocusedViewChange == null || onFocusedViewChange(viewToFocus, hasFocus);\n  });\n  const handleChangeView = useEventCallback(newView => {\n    // always keep the focused view in sync\n    handleFocusedViewChange(newView, true);\n    if (newView === view) {\n      return;\n    }\n    setView(newView);\n    if (onViewChange) {\n      onViewChange(newView);\n    }\n  });\n  const goToNextView = useEventCallback(() => {\n    if (nextView) {\n      handleChangeView(nextView);\n    }\n  });\n  const setValueAndGoToNextView = useEventCallback((value, currentViewSelectionState, selectedView) => {\n    const isSelectionFinishedOnCurrentView = currentViewSelectionState === 'finish';\n    const hasMoreViews = selectedView ?\n    // handles case like `DateTimePicker`, where a view might return a `finish` selection state\n    // but we it's not the final view given all `views` -> overall selection state should be `partial`.\n    views.indexOf(selectedView) < views.length - 1 : Boolean(nextView);\n    const globalSelectionState = isSelectionFinishedOnCurrentView && hasMoreViews ? 'partial' : currentViewSelectionState;\n    onChange(value, globalSelectionState, selectedView);\n    // Detects if the selected view is not the active one.\n    // Can happen if multiple views are displayed, like in `DesktopDateTimePicker` or `MultiSectionDigitalClock`.\n    if (selectedView && selectedView !== view) {\n      const nextViewAfterSelected = views[views.indexOf(selectedView) + 1];\n      if (nextViewAfterSelected) {\n        // move to next view after the selected one\n        handleChangeView(nextViewAfterSelected);\n      }\n    } else if (isSelectionFinishedOnCurrentView) {\n      goToNextView();\n    }\n  });\n  return {\n    view,\n    setView: handleChangeView,\n    focusedView,\n    setFocusedView: handleFocusedViewChange,\n    nextView,\n    previousView,\n    // Always return up to date default view instead of the initial one (i.e. defaultView.current)\n    defaultView: views.includes(openTo) ? openTo : views[0],\n    goToNextView,\n    setValueAndGoToNextView\n  };\n}", "map": {"version": 3, "names": ["React", "useEventCallback", "unstable_useControlled", "useControlled", "warnedOnceNotValidView", "useViews", "onChange", "onViewChange", "openTo", "view", "inView", "views", "autoFocus", "focused<PERSON>iew", "inFocusedView", "onFocusedViewChange", "_views", "_views2", "process", "env", "NODE_ENV", "includes", "console", "warn", "join", "previousOpenTo", "useRef", "previousViews", "defaultView", "<PERSON><PERSON><PERSON><PERSON>", "name", "state", "controlled", "default", "current", "defaultFocusedView", "setFocusedView", "useEffect", "some", "previousView", "viewIndex", "indexOf", "next<PERSON>iew", "handleFocusedViewChange", "viewToFocus", "hasFocus", "prevFocusedView", "handleChangeView", "newView", "goToNextView", "setValueAndGoToNextView", "value", "currentViewSelectionState", "<PERSON><PERSON><PERSON><PERSON>", "isSelectionFinishedOnCurrentView", "hasMoreViews", "length", "Boolean", "globalSelectionState", "nextViewAfterSelected"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useViews.js"], "sourcesContent": ["import * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { unstable_useControlled as useControlled } from '@mui/utils';\nlet warnedOnceNotValidView = false;\nexport function useViews({\n  onChange,\n  onViewChange,\n  openTo,\n  view: inView,\n  views,\n  autoFocus,\n  focusedView: inFocusedView,\n  onFocusedViewChange\n}) {\n  var _views, _views2;\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnceNotValidView) {\n      if (inView != null && !views.includes(inView)) {\n        console.warn(`MUI: \\`view=\"${inView}\"\\` is not a valid prop.`, `It must be an element of \\`views=[\"${views.join('\", \"')}\"]\\`.`);\n        warnedOnceNotValidView = true;\n      }\n      if (inView == null && openTo != null && !views.includes(openTo)) {\n        console.warn(`MUI: \\`openTo=\"${openTo}\"\\` is not a valid prop.`, `It must be an element of \\`views=[\"${views.join('\", \"')}\"]\\`.`);\n        warnedOnceNotValidView = true;\n      }\n    }\n  }\n  const previousOpenTo = React.useRef(openTo);\n  const previousViews = React.useRef(views);\n  const defaultView = React.useRef(views.includes(openTo) ? openTo : views[0]);\n  const [view, setView] = useControlled({\n    name: 'useViews',\n    state: 'view',\n    controlled: inView,\n    default: defaultView.current\n  });\n  const defaultFocusedView = React.useRef(autoFocus ? view : null);\n  const [focusedView, setFocusedView] = useControlled({\n    name: 'useViews',\n    state: 'focusedView',\n    controlled: inFocusedView,\n    default: defaultFocusedView.current\n  });\n  React.useEffect(() => {\n    // Update the current view when `openTo` or `views` props change\n    if (previousOpenTo.current && previousOpenTo.current !== openTo || previousViews.current && previousViews.current.some(previousView => !views.includes(previousView))) {\n      setView(views.includes(openTo) ? openTo : views[0]);\n      previousViews.current = views;\n      previousOpenTo.current = openTo;\n    }\n  }, [openTo, setView, view, views]);\n  const viewIndex = views.indexOf(view);\n  const previousView = (_views = views[viewIndex - 1]) != null ? _views : null;\n  const nextView = (_views2 = views[viewIndex + 1]) != null ? _views2 : null;\n  const handleFocusedViewChange = useEventCallback((viewToFocus, hasFocus) => {\n    if (hasFocus) {\n      // Focus event\n      setFocusedView(viewToFocus);\n    } else {\n      // Blur event\n      setFocusedView(prevFocusedView => viewToFocus === prevFocusedView ? null : prevFocusedView // If false the blur is due to view switching\n      );\n    }\n    onFocusedViewChange == null || onFocusedViewChange(viewToFocus, hasFocus);\n  });\n  const handleChangeView = useEventCallback(newView => {\n    // always keep the focused view in sync\n    handleFocusedViewChange(newView, true);\n    if (newView === view) {\n      return;\n    }\n    setView(newView);\n    if (onViewChange) {\n      onViewChange(newView);\n    }\n  });\n  const goToNextView = useEventCallback(() => {\n    if (nextView) {\n      handleChangeView(nextView);\n    }\n  });\n  const setValueAndGoToNextView = useEventCallback((value, currentViewSelectionState, selectedView) => {\n    const isSelectionFinishedOnCurrentView = currentViewSelectionState === 'finish';\n    const hasMoreViews = selectedView ?\n    // handles case like `DateTimePicker`, where a view might return a `finish` selection state\n    // but we it's not the final view given all `views` -> overall selection state should be `partial`.\n    views.indexOf(selectedView) < views.length - 1 : Boolean(nextView);\n    const globalSelectionState = isSelectionFinishedOnCurrentView && hasMoreViews ? 'partial' : currentViewSelectionState;\n    onChange(value, globalSelectionState, selectedView);\n    // Detects if the selected view is not the active one.\n    // Can happen if multiple views are displayed, like in `DesktopDateTimePicker` or `MultiSectionDigitalClock`.\n    if (selectedView && selectedView !== view) {\n      const nextViewAfterSelected = views[views.indexOf(selectedView) + 1];\n      if (nextViewAfterSelected) {\n        // move to next view after the selected one\n        handleChangeView(nextViewAfterSelected);\n      }\n    } else if (isSelectionFinishedOnCurrentView) {\n      goToNextView();\n    }\n  });\n  return {\n    view,\n    setView: handleChangeView,\n    focusedView,\n    setFocusedView: handleFocusedViewChange,\n    nextView,\n    previousView,\n    // Always return up to date default view instead of the initial one (i.e. defaultView.current)\n    defaultView: views.includes(openTo) ? openTo : views[0],\n    goToNextView,\n    setValueAndGoToNextView\n  };\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,SAASC,sBAAsB,IAAIC,aAAa,QAAQ,YAAY;AACpE,IAAIC,sBAAsB,GAAG,KAAK;AAClC,OAAO,SAASC,QAAQA,CAAC;EACvBC,QAAQ;EACRC,YAAY;EACZC,MAAM;EACNC,IAAI,EAAEC,MAAM;EACZC,KAAK;EACLC,SAAS;EACTC,WAAW,EAAEC,aAAa;EAC1BC;AACF,CAAC,EAAE;EACD,IAAIC,MAAM,EAAEC,OAAO;EACnB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI,CAAChB,sBAAsB,EAAE;MAC3B,IAAIM,MAAM,IAAI,IAAI,IAAI,CAACC,KAAK,CAACU,QAAQ,CAACX,MAAM,CAAC,EAAE;QAC7CY,OAAO,CAACC,IAAI,CAAC,gBAAgBb,MAAM,0BAA0B,EAAE,sCAAsCC,KAAK,CAACa,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QAC/HpB,sBAAsB,GAAG,IAAI;MAC/B;MACA,IAAIM,MAAM,IAAI,IAAI,IAAIF,MAAM,IAAI,IAAI,IAAI,CAACG,KAAK,CAACU,QAAQ,CAACb,MAAM,CAAC,EAAE;QAC/Dc,OAAO,CAACC,IAAI,CAAC,kBAAkBf,MAAM,0BAA0B,EAAE,sCAAsCG,KAAK,CAACa,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACjIpB,sBAAsB,GAAG,IAAI;MAC/B;IACF;EACF;EACA,MAAMqB,cAAc,GAAGzB,KAAK,CAAC0B,MAAM,CAAClB,MAAM,CAAC;EAC3C,MAAMmB,aAAa,GAAG3B,KAAK,CAAC0B,MAAM,CAACf,KAAK,CAAC;EACzC,MAAMiB,WAAW,GAAG5B,KAAK,CAAC0B,MAAM,CAACf,KAAK,CAACU,QAAQ,CAACb,MAAM,CAAC,GAAGA,MAAM,GAAGG,KAAK,CAAC,CAAC,CAAC,CAAC;EAC5E,MAAM,CAACF,IAAI,EAAEoB,OAAO,CAAC,GAAG1B,aAAa,CAAC;IACpC2B,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,MAAM;IACbC,UAAU,EAAEtB,MAAM;IAClBuB,OAAO,EAAEL,WAAW,CAACM;EACvB,CAAC,CAAC;EACF,MAAMC,kBAAkB,GAAGnC,KAAK,CAAC0B,MAAM,CAACd,SAAS,GAAGH,IAAI,GAAG,IAAI,CAAC;EAChE,MAAM,CAACI,WAAW,EAAEuB,cAAc,CAAC,GAAGjC,aAAa,CAAC;IAClD2B,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,aAAa;IACpBC,UAAU,EAAElB,aAAa;IACzBmB,OAAO,EAAEE,kBAAkB,CAACD;EAC9B,CAAC,CAAC;EACFlC,KAAK,CAACqC,SAAS,CAAC,MAAM;IACpB;IACA,IAAIZ,cAAc,CAACS,OAAO,IAAIT,cAAc,CAACS,OAAO,KAAK1B,MAAM,IAAImB,aAAa,CAACO,OAAO,IAAIP,aAAa,CAACO,OAAO,CAACI,IAAI,CAACC,YAAY,IAAI,CAAC5B,KAAK,CAACU,QAAQ,CAACkB,YAAY,CAAC,CAAC,EAAE;MACrKV,OAAO,CAAClB,KAAK,CAACU,QAAQ,CAACb,MAAM,CAAC,GAAGA,MAAM,GAAGG,KAAK,CAAC,CAAC,CAAC,CAAC;MACnDgB,aAAa,CAACO,OAAO,GAAGvB,KAAK;MAC7Bc,cAAc,CAACS,OAAO,GAAG1B,MAAM;IACjC;EACF,CAAC,EAAE,CAACA,MAAM,EAAEqB,OAAO,EAAEpB,IAAI,EAAEE,KAAK,CAAC,CAAC;EAClC,MAAM6B,SAAS,GAAG7B,KAAK,CAAC8B,OAAO,CAAChC,IAAI,CAAC;EACrC,MAAM8B,YAAY,GAAG,CAACvB,MAAM,GAAGL,KAAK,CAAC6B,SAAS,GAAG,CAAC,CAAC,KAAK,IAAI,GAAGxB,MAAM,GAAG,IAAI;EAC5E,MAAM0B,QAAQ,GAAG,CAACzB,OAAO,GAAGN,KAAK,CAAC6B,SAAS,GAAG,CAAC,CAAC,KAAK,IAAI,GAAGvB,OAAO,GAAG,IAAI;EAC1E,MAAM0B,uBAAuB,GAAG1C,gBAAgB,CAAC,CAAC2C,WAAW,EAAEC,QAAQ,KAAK;IAC1E,IAAIA,QAAQ,EAAE;MACZ;MACAT,cAAc,CAACQ,WAAW,CAAC;IAC7B,CAAC,MAAM;MACL;MACAR,cAAc,CAACU,eAAe,IAAIF,WAAW,KAAKE,eAAe,GAAG,IAAI,GAAGA,eAAe,CAAC;MAC3F,CAAC;IACH;IACA/B,mBAAmB,IAAI,IAAI,IAAIA,mBAAmB,CAAC6B,WAAW,EAAEC,QAAQ,CAAC;EAC3E,CAAC,CAAC;EACF,MAAME,gBAAgB,GAAG9C,gBAAgB,CAAC+C,OAAO,IAAI;IACnD;IACAL,uBAAuB,CAACK,OAAO,EAAE,IAAI,CAAC;IACtC,IAAIA,OAAO,KAAKvC,IAAI,EAAE;MACpB;IACF;IACAoB,OAAO,CAACmB,OAAO,CAAC;IAChB,IAAIzC,YAAY,EAAE;MAChBA,YAAY,CAACyC,OAAO,CAAC;IACvB;EACF,CAAC,CAAC;EACF,MAAMC,YAAY,GAAGhD,gBAAgB,CAAC,MAAM;IAC1C,IAAIyC,QAAQ,EAAE;MACZK,gBAAgB,CAACL,QAAQ,CAAC;IAC5B;EACF,CAAC,CAAC;EACF,MAAMQ,uBAAuB,GAAGjD,gBAAgB,CAAC,CAACkD,KAAK,EAAEC,yBAAyB,EAAEC,YAAY,KAAK;IACnG,MAAMC,gCAAgC,GAAGF,yBAAyB,KAAK,QAAQ;IAC/E,MAAMG,YAAY,GAAGF,YAAY;IACjC;IACA;IACA1C,KAAK,CAAC8B,OAAO,CAACY,YAAY,CAAC,GAAG1C,KAAK,CAAC6C,MAAM,GAAG,CAAC,GAAGC,OAAO,CAACf,QAAQ,CAAC;IAClE,MAAMgB,oBAAoB,GAAGJ,gCAAgC,IAAIC,YAAY,GAAG,SAAS,GAAGH,yBAAyB;IACrH9C,QAAQ,CAAC6C,KAAK,EAAEO,oBAAoB,EAAEL,YAAY,CAAC;IACnD;IACA;IACA,IAAIA,YAAY,IAAIA,YAAY,KAAK5C,IAAI,EAAE;MACzC,MAAMkD,qBAAqB,GAAGhD,KAAK,CAACA,KAAK,CAAC8B,OAAO,CAACY,YAAY,CAAC,GAAG,CAAC,CAAC;MACpE,IAAIM,qBAAqB,EAAE;QACzB;QACAZ,gBAAgB,CAACY,qBAAqB,CAAC;MACzC;IACF,CAAC,MAAM,IAAIL,gCAAgC,EAAE;MAC3CL,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,CAAC;EACF,OAAO;IACLxC,IAAI;IACJoB,OAAO,EAAEkB,gBAAgB;IACzBlC,WAAW;IACXuB,cAAc,EAAEO,uBAAuB;IACvCD,QAAQ;IACRH,YAAY;IACZ;IACAX,WAAW,EAAEjB,KAAK,CAACU,QAAQ,CAACb,MAAM,CAAC,GAAGA,MAAM,GAAGG,KAAK,CAAC,CAAC,CAAC;IACvDsC,YAAY;IACZC;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}