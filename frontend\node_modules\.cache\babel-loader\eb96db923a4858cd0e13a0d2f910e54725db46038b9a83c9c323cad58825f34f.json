{"ast": null, "code": "export { useDesktopPicker } from './useDesktopPicker';", "map": {"version": 3, "names": ["useDesktopPicker"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useDesktopPicker/index.js"], "sourcesContent": ["export { useDesktopPicker } from './useDesktopPicker';"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}