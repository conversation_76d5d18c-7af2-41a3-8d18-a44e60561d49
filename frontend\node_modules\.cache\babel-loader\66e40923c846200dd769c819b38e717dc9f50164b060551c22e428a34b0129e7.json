{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"eeee 'الماضي عند الساعة' p\",\n  yesterday: \"'الأمس عند الساعة' p\",\n  today: \"'اليوم عند الساعة' p\",\n  tomorrow: \"'غدا عند الساعة' p\",\n  nextWeek: \"eeee 'القادم عند الساعة' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/ar/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: \"eeee 'الماضي عند الساعة' p\",\n  yesterday: \"'الأمس عند الساعة' p\",\n  today: \"'اليوم عند الساعة' p\",\n  tomorrow: \"'غدا عند الساعة' p\",\n  nextWeek: \"eeee 'القادم عند الساعة' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,4BAA4B;EACtCC,SAAS,EAAE,sBAAsB;EACjCC,KAAK,EAAE,sBAAsB;EAC7BC,QAAQ,EAAE,oBAAoB;EAC9BC,QAAQ,EAAE,4BAA4B;EACtCC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EAClD,OAAOR,oBAAoB,CAACQ,KAAK,CAAC;AACpC,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}