{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _createForOfIteratorHelper from \"@babel/runtime/helpers/esm/createForOfIteratorHelper\";\nimport defaultLocale from \"../_lib/defaultLocale/index.js\";\nimport subMilliseconds from \"../subMilliseconds/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport assign from \"../_lib/assign/index.js\";\nimport longFormatters from \"../_lib/format/longFormatters/index.js\";\nimport getTimezoneOffsetInMilliseconds from \"../_lib/getTimezoneOffsetInMilliseconds/index.js\";\nimport { isProtectedDayOfYearToken, isProtectedWeekYearToken, throwProtectedError } from \"../_lib/protectedTokens/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { DateToSystemTimezoneSetter } from \"./_lib/Setter.js\";\nimport { parsers } from \"./_lib/parsers/index.js\";\nimport { getDefaultOptions } from \"../_lib/defaultOptions/index.js\"; // This RegExp consists of three parts separated by `|`:\n// - [yYQqMLwIdDecihHKkms]o matches any available ordinal number token\n//   (one of the certain letters followed by `o`)\n// - (\\w)\\1* matches any sequences of the same letter\n// - '' matches two quote characters in a row\n// - '(''|[^'])+('|$) matches anything surrounded by two quote characters ('),\n//   except a single quote symbol, which ends the sequence.\n//   Two quote characters do not end the sequence.\n//   If there is no matching single quote\n//   then the sequence will continue until the end of the string.\n// - . matches any single character unmatched by previous parts of the RegExps\nvar formattingTokensRegExp = /[yYQqMLwIdDecihHKkms]o|(\\w)\\1*|''|'(''|[^'])+('|$)|./g;\n\n// This RegExp catches symbols escaped by quotes, and also\n// sequences of symbols P, p, and the combinations like `PPPPPPPppppp`\nvar longFormattingTokensRegExp = /P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;\nvar escapedStringRegExp = /^'([^]*?)'?$/;\nvar doubleQuoteRegExp = /''/g;\nvar notWhitespaceRegExp = /\\S/;\nvar unescapedLatinCharacterRegExp = /[a-zA-Z]/;\n\n/**\n * @name parse\n * @category Common Helpers\n * @summary Parse the date.\n *\n * @description\n * Return the date parsed from string using the given format string.\n *\n * > ⚠️ Please note that the `format` tokens differ from Moment.js and other libraries.\n * > See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * The characters in the format string wrapped between two single quotes characters (') are escaped.\n * Two single quotes in a row, whether inside or outside a quoted sequence, represent a 'real' single quote.\n *\n * Format of the format string is based on Unicode Technical Standard #35:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * with a few additions (see note 5 below the table).\n *\n * Not all tokens are compatible. Combinations that don't make sense or could lead to bugs are prohibited\n * and will throw `RangeError`. For example usage of 24-hour format token with AM/PM token will throw an exception:\n *\n * ```javascript\n * parse('23 AM', 'HH a', new Date())\n * //=> RangeError: The format string mustn't contain `HH` and `a` at the same time\n * ```\n *\n * See the compatibility table: https://docs.google.com/spreadsheets/d/e/2PACX-1vQOPU3xUhplll6dyoMmVUXHKl_8CRDs6_ueLmex3SoqwhuolkuN3O05l4rqx5h1dKX8eb46Ul-CCSrq/pubhtml?gid=0&single=true\n *\n * Accepted format string patterns:\n * | Unit                            |Prior| Pattern | Result examples                   | Notes |\n * |---------------------------------|-----|---------|-----------------------------------|-------|\n * | Era                             | 140 | G..GGG  | AD, BC                            |       |\n * |                                 |     | GGGG    | Anno Domini, Before Christ        | 2     |\n * |                                 |     | GGGGG   | A, B                              |       |\n * | Calendar year                   | 130 | y       | 44, 1, 1900, 2017, 9999           | 4     |\n * |                                 |     | yo      | 44th, 1st, 1900th, 9999999th      | 4,5   |\n * |                                 |     | yy      | 44, 01, 00, 17                    | 4     |\n * |                                 |     | yyy     | 044, 001, 123, 999                | 4     |\n * |                                 |     | yyyy    | 0044, 0001, 1900, 2017            | 4     |\n * |                                 |     | yyyyy   | ...                               | 2,4   |\n * | Local week-numbering year       | 130 | Y       | 44, 1, 1900, 2017, 9000           | 4     |\n * |                                 |     | Yo      | 44th, 1st, 1900th, 9999999th      | 4,5   |\n * |                                 |     | YY      | 44, 01, 00, 17                    | 4,6   |\n * |                                 |     | YYY     | 044, 001, 123, 999                | 4     |\n * |                                 |     | YYYY    | 0044, 0001, 1900, 2017            | 4,6   |\n * |                                 |     | YYYYY   | ...                               | 2,4   |\n * | ISO week-numbering year         | 130 | R       | -43, 1, 1900, 2017, 9999, -9999   | 4,5   |\n * |                                 |     | RR      | -43, 01, 00, 17                   | 4,5   |\n * |                                 |     | RRR     | -043, 001, 123, 999, -999         | 4,5   |\n * |                                 |     | RRRR    | -0043, 0001, 2017, 9999, -9999    | 4,5   |\n * |                                 |     | RRRRR   | ...                               | 2,4,5 |\n * | Extended year                   | 130 | u       | -43, 1, 1900, 2017, 9999, -999    | 4     |\n * |                                 |     | uu      | -43, 01, 99, -99                  | 4     |\n * |                                 |     | uuu     | -043, 001, 123, 999, -999         | 4     |\n * |                                 |     | uuuu    | -0043, 0001, 2017, 9999, -9999    | 4     |\n * |                                 |     | uuuuu   | ...                               | 2,4   |\n * | Quarter (formatting)            | 120 | Q       | 1, 2, 3, 4                        |       |\n * |                                 |     | Qo      | 1st, 2nd, 3rd, 4th                | 5     |\n * |                                 |     | QQ      | 01, 02, 03, 04                    |       |\n * |                                 |     | QQQ     | Q1, Q2, Q3, Q4                    |       |\n * |                                 |     | QQQQ    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 |     | QQQQQ   | 1, 2, 3, 4                        | 4     |\n * | Quarter (stand-alone)           | 120 | q       | 1, 2, 3, 4                        |       |\n * |                                 |     | qo      | 1st, 2nd, 3rd, 4th                | 5     |\n * |                                 |     | qq      | 01, 02, 03, 04                    |       |\n * |                                 |     | qqq     | Q1, Q2, Q3, Q4                    |       |\n * |                                 |     | qqqq    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 |     | qqqqq   | 1, 2, 3, 4                        | 3     |\n * | Month (formatting)              | 110 | M       | 1, 2, ..., 12                     |       |\n * |                                 |     | Mo      | 1st, 2nd, ..., 12th               | 5     |\n * |                                 |     | MM      | 01, 02, ..., 12                   |       |\n * |                                 |     | MMM     | Jan, Feb, ..., Dec                |       |\n * |                                 |     | MMMM    | January, February, ..., December  | 2     |\n * |                                 |     | MMMMM   | J, F, ..., D                      |       |\n * | Month (stand-alone)             | 110 | L       | 1, 2, ..., 12                     |       |\n * |                                 |     | Lo      | 1st, 2nd, ..., 12th               | 5     |\n * |                                 |     | LL      | 01, 02, ..., 12                   |       |\n * |                                 |     | LLL     | Jan, Feb, ..., Dec                |       |\n * |                                 |     | LLLL    | January, February, ..., December  | 2     |\n * |                                 |     | LLLLL   | J, F, ..., D                      |       |\n * | Local week of year              | 100 | w       | 1, 2, ..., 53                     |       |\n * |                                 |     | wo      | 1st, 2nd, ..., 53th               | 5     |\n * |                                 |     | ww      | 01, 02, ..., 53                   |       |\n * | ISO week of year                | 100 | I       | 1, 2, ..., 53                     | 5     |\n * |                                 |     | Io      | 1st, 2nd, ..., 53th               | 5     |\n * |                                 |     | II      | 01, 02, ..., 53                   | 5     |\n * | Day of month                    |  90 | d       | 1, 2, ..., 31                     |       |\n * |                                 |     | do      | 1st, 2nd, ..., 31st               | 5     |\n * |                                 |     | dd      | 01, 02, ..., 31                   |       |\n * | Day of year                     |  90 | D       | 1, 2, ..., 365, 366               | 7     |\n * |                                 |     | Do      | 1st, 2nd, ..., 365th, 366th       | 5     |\n * |                                 |     | DD      | 01, 02, ..., 365, 366             | 7     |\n * |                                 |     | DDD     | 001, 002, ..., 365, 366           |       |\n * |                                 |     | DDDD    | ...                               | 2     |\n * | Day of week (formatting)        |  90 | E..EEE  | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 |     | EEEE    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 |     | EEEEE   | M, T, W, T, F, S, S               |       |\n * |                                 |     | EEEEEE  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | ISO day of week (formatting)    |  90 | i       | 1, 2, 3, ..., 7                   | 5     |\n * |                                 |     | io      | 1st, 2nd, ..., 7th                | 5     |\n * |                                 |     | ii      | 01, 02, ..., 07                   | 5     |\n * |                                 |     | iii     | Mon, Tue, Wed, ..., Sun           | 5     |\n * |                                 |     | iiii    | Monday, Tuesday, ..., Sunday      | 2,5   |\n * |                                 |     | iiiii   | M, T, W, T, F, S, S               | 5     |\n * |                                 |     | iiiiii  | Mo, Tu, We, Th, Fr, Sa, Su        | 5     |\n * | Local day of week (formatting)  |  90 | e       | 2, 3, 4, ..., 1                   |       |\n * |                                 |     | eo      | 2nd, 3rd, ..., 1st                | 5     |\n * |                                 |     | ee      | 02, 03, ..., 01                   |       |\n * |                                 |     | eee     | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 |     | eeee    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 |     | eeeee   | M, T, W, T, F, S, S               |       |\n * |                                 |     | eeeeee  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | Local day of week (stand-alone) |  90 | c       | 2, 3, 4, ..., 1                   |       |\n * |                                 |     | co      | 2nd, 3rd, ..., 1st                | 5     |\n * |                                 |     | cc      | 02, 03, ..., 01                   |       |\n * |                                 |     | ccc     | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 |     | cccc    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 |     | ccccc   | M, T, W, T, F, S, S               |       |\n * |                                 |     | cccccc  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | AM, PM                          |  80 | a..aaa  | AM, PM                            |       |\n * |                                 |     | aaaa    | a.m., p.m.                        | 2     |\n * |                                 |     | aaaaa   | a, p                              |       |\n * | AM, PM, noon, midnight          |  80 | b..bbb  | AM, PM, noon, midnight            |       |\n * |                                 |     | bbbb    | a.m., p.m., noon, midnight        | 2     |\n * |                                 |     | bbbbb   | a, p, n, mi                       |       |\n * | Flexible day period             |  80 | B..BBB  | at night, in the morning, ...     |       |\n * |                                 |     | BBBB    | at night, in the morning, ...     | 2     |\n * |                                 |     | BBBBB   | at night, in the morning, ...     |       |\n * | Hour [1-12]                     |  70 | h       | 1, 2, ..., 11, 12                 |       |\n * |                                 |     | ho      | 1st, 2nd, ..., 11th, 12th         | 5     |\n * |                                 |     | hh      | 01, 02, ..., 11, 12               |       |\n * | Hour [0-23]                     |  70 | H       | 0, 1, 2, ..., 23                  |       |\n * |                                 |     | Ho      | 0th, 1st, 2nd, ..., 23rd          | 5     |\n * |                                 |     | HH      | 00, 01, 02, ..., 23               |       |\n * | Hour [0-11]                     |  70 | K       | 1, 2, ..., 11, 0                  |       |\n * |                                 |     | Ko      | 1st, 2nd, ..., 11th, 0th          | 5     |\n * |                                 |     | KK      | 01, 02, ..., 11, 00               |       |\n * | Hour [1-24]                     |  70 | k       | 24, 1, 2, ..., 23                 |       |\n * |                                 |     | ko      | 24th, 1st, 2nd, ..., 23rd         | 5     |\n * |                                 |     | kk      | 24, 01, 02, ..., 23               |       |\n * | Minute                          |  60 | m       | 0, 1, ..., 59                     |       |\n * |                                 |     | mo      | 0th, 1st, ..., 59th               | 5     |\n * |                                 |     | mm      | 00, 01, ..., 59                   |       |\n * | Second                          |  50 | s       | 0, 1, ..., 59                     |       |\n * |                                 |     | so      | 0th, 1st, ..., 59th               | 5     |\n * |                                 |     | ss      | 00, 01, ..., 59                   |       |\n * | Seconds timestamp               |  40 | t       | 512969520                         |       |\n * |                                 |     | tt      | ...                               | 2     |\n * | Fraction of second              |  30 | S       | 0, 1, ..., 9                      |       |\n * |                                 |     | SS      | 00, 01, ..., 99                   |       |\n * |                                 |     | SSS     | 000, 001, ..., 999                |       |\n * |                                 |     | SSSS    | ...                               | 2     |\n * | Milliseconds timestamp          |  20 | T       | 512969520900                      |       |\n * |                                 |     | TT      | ...                               | 2     |\n * | Timezone (ISO-8601 w/ Z)        |  10 | X       | -08, +0530, Z                     |       |\n * |                                 |     | XX      | -0800, +0530, Z                   |       |\n * |                                 |     | XXX     | -08:00, +05:30, Z                 |       |\n * |                                 |     | XXXX    | -0800, +0530, Z, +123456          | 2     |\n * |                                 |     | XXXXX   | -08:00, +05:30, Z, +12:34:56      |       |\n * | Timezone (ISO-8601 w/o Z)       |  10 | x       | -08, +0530, +00                   |       |\n * |                                 |     | xx      | -0800, +0530, +0000               |       |\n * |                                 |     | xxx     | -08:00, +05:30, +00:00            | 2     |\n * |                                 |     | xxxx    | -0800, +0530, +0000, +123456      |       |\n * |                                 |     | xxxxx   | -08:00, +05:30, +00:00, +12:34:56 |       |\n * | Long localized date             |  NA | P       | 05/29/1453                        | 5,8   |\n * |                                 |     | PP      | May 29, 1453                      |       |\n * |                                 |     | PPP     | May 29th, 1453                    |       |\n * |                                 |     | PPPP    | Sunday, May 29th, 1453            | 2,5,8 |\n * | Long localized time             |  NA | p       | 12:00 AM                          | 5,8   |\n * |                                 |     | pp      | 12:00:00 AM                       |       |\n * | Combination of date and time    |  NA | Pp      | 05/29/1453, 12:00 AM              |       |\n * |                                 |     | PPpp    | May 29, 1453, 12:00:00 AM         |       |\n * |                                 |     | PPPpp   | May 29th, 1453 at ...             |       |\n * |                                 |     | PPPPpp  | Sunday, May 29th, 1453 at ...     | 2,5,8 |\n * Notes:\n * 1. \"Formatting\" units (e.g. formatting quarter) in the default en-US locale\n *    are the same as \"stand-alone\" units, but are different in some languages.\n *    \"Formatting\" units are declined according to the rules of the language\n *    in the context of a date. \"Stand-alone\" units are always nominative singular.\n *    In `format` function, they will produce different result:\n *\n *    `format(new Date(2017, 10, 6), 'do LLLL', {locale: cs}) //=> '6. listopad'`\n *\n *    `format(new Date(2017, 10, 6), 'do MMMM', {locale: cs}) //=> '6. listopadu'`\n *\n *    `parse` will try to match both formatting and stand-alone units interchangably.\n *\n * 2. Any sequence of the identical letters is a pattern, unless it is escaped by\n *    the single quote characters (see below).\n *    If the sequence is longer than listed in table:\n *    - for numerical units (`yyyyyyyy`) `parse` will try to match a number\n *      as wide as the sequence\n *    - for text units (`MMMMMMMM`) `parse` will try to match the widest variation of the unit.\n *      These variations are marked with \"2\" in the last column of the table.\n *\n * 3. `QQQQQ` and `qqqqq` could be not strictly numerical in some locales.\n *    These tokens represent the shortest form of the quarter.\n *\n * 4. The main difference between `y` and `u` patterns are B.C. years:\n *\n *    | Year | `y` | `u` |\n *    |------|-----|-----|\n *    | AC 1 |   1 |   1 |\n *    | BC 1 |   1 |   0 |\n *    | BC 2 |   2 |  -1 |\n *\n *    Also `yy` will try to guess the century of two digit year by proximity with `referenceDate`:\n *\n *    `parse('50', 'yy', new Date(2018, 0, 1)) //=> Sat Jan 01 2050 00:00:00`\n *\n *    `parse('75', 'yy', new Date(2018, 0, 1)) //=> Wed Jan 01 1975 00:00:00`\n *\n *    while `uu` will just assign the year as is:\n *\n *    `parse('50', 'uu', new Date(2018, 0, 1)) //=> Sat Jan 01 0050 00:00:00`\n *\n *    `parse('75', 'uu', new Date(2018, 0, 1)) //=> Tue Jan 01 0075 00:00:00`\n *\n *    The same difference is true for local and ISO week-numbering years (`Y` and `R`),\n *    except local week-numbering years are dependent on `options.weekStartsOn`\n *    and `options.firstWeekContainsDate` (compare [setISOWeekYear]{@link https://date-fns.org/docs/setISOWeekYear}\n *    and [setWeekYear]{@link https://date-fns.org/docs/setWeekYear}).\n *\n * 5. These patterns are not in the Unicode Technical Standard #35:\n *    - `i`: ISO day of week\n *    - `I`: ISO week of year\n *    - `R`: ISO week-numbering year\n *    - `o`: ordinal number modifier\n *    - `P`: long localized date\n *    - `p`: long localized time\n *\n * 6. `YY` and `YYYY` tokens represent week-numbering years but they are often confused with years.\n *    You should enable `options.useAdditionalWeekYearTokens` to use them. See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * 7. `D` and `DD` tokens represent days of the year but they are ofthen confused with days of the month.\n *    You should enable `options.useAdditionalDayOfYearTokens` to use them. See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * 8. `P+` tokens do not have a defined priority since they are merely aliases to other tokens based\n *    on the given locale.\n *\n *    using `en-US` locale: `P` => `MM/dd/yyyy`\n *    using `en-US` locale: `p` => `hh:mm a`\n *    using `pt-BR` locale: `P` => `dd/MM/yyyy`\n *    using `pt-BR` locale: `p` => `HH:mm`\n *\n * Values will be assigned to the date in the descending order of its unit's priority.\n * Units of an equal priority overwrite each other in the order of appearance.\n *\n * If no values of higher priority are parsed (e.g. when parsing string 'January 1st' without a year),\n * the values will be taken from 3rd argument `referenceDate` which works as a context of parsing.\n *\n * `referenceDate` must be passed for correct work of the function.\n * If you're not sure which `referenceDate` to supply, create a new instance of Date:\n * `parse('02/11/2014', 'MM/dd/yyyy', new Date())`\n * In this case parsing will be done in the context of the current date.\n * If `referenceDate` is `Invalid Date` or a value not convertible to valid `Date`,\n * then `Invalid Date` will be returned.\n *\n * The result may vary by locale.\n *\n * If `formatString` matches with `dateString` but does not provides tokens, `referenceDate` will be returned.\n *\n * If parsing failed, `Invalid Date` will be returned.\n * Invalid Date is a Date, whose time value is NaN.\n * Time value of Date: http://es5.github.io/#x15.9.1.1\n *\n * @param {String} dateString - the string to parse\n * @param {String} formatString - the string of tokens\n * @param {Date|Number} referenceDate - defines values missing from the parsed dateString\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @param {1|2|3|4|5|6|7} [options.firstWeekContainsDate=1] - the day of January, which is always in the first week of the year\n * @param {Boolean} [options.useAdditionalWeekYearTokens=false] - if true, allows usage of the week-numbering year tokens `YY` and `YYYY`;\n *   see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @param {Boolean} [options.useAdditionalDayOfYearTokens=false] - if true, allows usage of the day of year tokens `D` and `DD`;\n *   see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @returns {Date} the parsed date\n * @throws {TypeError} 3 arguments required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n * @throws {RangeError} `options.firstWeekContainsDate` must be between 1 and 7\n * @throws {RangeError} `options.locale` must contain `match` property\n * @throws {RangeError} use `yyyy` instead of `YYYY` for formatting years using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws {RangeError} use `yy` instead of `YY` for formatting years using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws {RangeError} use `d` instead of `D` for formatting days of the month using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws {RangeError} use `dd` instead of `DD` for formatting days of the month using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws {RangeError} format string contains an unescaped latin alphabet character\n *\n * @example\n * // Parse 11 February 2014 from middle-endian format:\n * var result = parse('02/11/2014', 'MM/dd/yyyy', new Date())\n * //=> Tue Feb 11 2014 00:00:00\n *\n * @example\n * // Parse 28th of February in Esperanto locale in the context of 2010 year:\n * import eo from 'date-fns/locale/eo'\n * var result = parse('28-a de februaro', \"do 'de' MMMM\", new Date(2010, 0, 1), {\n *   locale: eo\n * })\n * //=> Sun Feb 28 2010 00:00:00\n */\nexport default function parse(dirtyDateString, dirtyFormatString, dirtyReferenceDate, options) {\n  var _ref, _options$locale, _ref2, _ref3, _ref4, _options$firstWeekCon, _options$locale2, _options$locale2$opti, _defaultOptions$local, _defaultOptions$local2, _ref5, _ref6, _ref7, _options$weekStartsOn, _options$locale3, _options$locale3$opti, _defaultOptions$local3, _defaultOptions$local4;\n  requiredArgs(3, arguments);\n  var dateString = String(dirtyDateString);\n  var formatString = String(dirtyFormatString);\n  var defaultOptions = getDefaultOptions();\n  var locale = (_ref = (_options$locale = options === null || options === void 0 ? void 0 : options.locale) !== null && _options$locale !== void 0 ? _options$locale : defaultOptions.locale) !== null && _ref !== void 0 ? _ref : defaultLocale;\n  if (!locale.match) {\n    throw new RangeError('locale must contain match property');\n  }\n  var firstWeekContainsDate = toInteger((_ref2 = (_ref3 = (_ref4 = (_options$firstWeekCon = options === null || options === void 0 ? void 0 : options.firstWeekContainsDate) !== null && _options$firstWeekCon !== void 0 ? _options$firstWeekCon : options === null || options === void 0 ? void 0 : (_options$locale2 = options.locale) === null || _options$locale2 === void 0 ? void 0 : (_options$locale2$opti = _options$locale2.options) === null || _options$locale2$opti === void 0 ? void 0 : _options$locale2$opti.firstWeekContainsDate) !== null && _ref4 !== void 0 ? _ref4 : defaultOptions.firstWeekContainsDate) !== null && _ref3 !== void 0 ? _ref3 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 ? void 0 : (_defaultOptions$local2 = _defaultOptions$local.options) === null || _defaultOptions$local2 === void 0 ? void 0 : _defaultOptions$local2.firstWeekContainsDate) !== null && _ref2 !== void 0 ? _ref2 : 1);\n\n  // Test if weekStartsOn is between 1 and 7 _and_ is not NaN\n  if (!(firstWeekContainsDate >= 1 && firstWeekContainsDate <= 7)) {\n    throw new RangeError('firstWeekContainsDate must be between 1 and 7 inclusively');\n  }\n  var weekStartsOn = toInteger((_ref5 = (_ref6 = (_ref7 = (_options$weekStartsOn = options === null || options === void 0 ? void 0 : options.weekStartsOn) !== null && _options$weekStartsOn !== void 0 ? _options$weekStartsOn : options === null || options === void 0 ? void 0 : (_options$locale3 = options.locale) === null || _options$locale3 === void 0 ? void 0 : (_options$locale3$opti = _options$locale3.options) === null || _options$locale3$opti === void 0 ? void 0 : _options$locale3$opti.weekStartsOn) !== null && _ref7 !== void 0 ? _ref7 : defaultOptions.weekStartsOn) !== null && _ref6 !== void 0 ? _ref6 : (_defaultOptions$local3 = defaultOptions.locale) === null || _defaultOptions$local3 === void 0 ? void 0 : (_defaultOptions$local4 = _defaultOptions$local3.options) === null || _defaultOptions$local4 === void 0 ? void 0 : _defaultOptions$local4.weekStartsOn) !== null && _ref5 !== void 0 ? _ref5 : 0);\n\n  // Test if weekStartsOn is between 0 and 6 _and_ is not NaN\n  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {\n    throw new RangeError('weekStartsOn must be between 0 and 6 inclusively');\n  }\n  if (formatString === '') {\n    if (dateString === '') {\n      return toDate(dirtyReferenceDate);\n    } else {\n      return new Date(NaN);\n    }\n  }\n  var subFnOptions = {\n    firstWeekContainsDate: firstWeekContainsDate,\n    weekStartsOn: weekStartsOn,\n    locale: locale\n  };\n\n  // If timezone isn't specified, it will be set to the system timezone\n  var setters = [new DateToSystemTimezoneSetter()];\n  var tokens = formatString.match(longFormattingTokensRegExp).map(function (substring) {\n    var firstCharacter = substring[0];\n    if (firstCharacter in longFormatters) {\n      var longFormatter = longFormatters[firstCharacter];\n      return longFormatter(substring, locale.formatLong);\n    }\n    return substring;\n  }).join('').match(formattingTokensRegExp);\n  var usedTokens = [];\n  var _iterator = _createForOfIteratorHelper(tokens),\n    _step;\n  try {\n    var _loop = function _loop() {\n      var token = _step.value;\n      if (!(options !== null && options !== void 0 && options.useAdditionalWeekYearTokens) && isProtectedWeekYearToken(token)) {\n        throwProtectedError(token, formatString, dirtyDateString);\n      }\n      if (!(options !== null && options !== void 0 && options.useAdditionalDayOfYearTokens) && isProtectedDayOfYearToken(token)) {\n        throwProtectedError(token, formatString, dirtyDateString);\n      }\n      var firstCharacter = token[0];\n      var parser = parsers[firstCharacter];\n      if (parser) {\n        var incompatibleTokens = parser.incompatibleTokens;\n        if (Array.isArray(incompatibleTokens)) {\n          var incompatibleToken = usedTokens.find(function (usedToken) {\n            return incompatibleTokens.includes(usedToken.token) || usedToken.token === firstCharacter;\n          });\n          if (incompatibleToken) {\n            throw new RangeError(\"The format string mustn't contain `\".concat(incompatibleToken.fullToken, \"` and `\").concat(token, \"` at the same time\"));\n          }\n        } else if (parser.incompatibleTokens === '*' && usedTokens.length > 0) {\n          throw new RangeError(\"The format string mustn't contain `\".concat(token, \"` and any other token at the same time\"));\n        }\n        usedTokens.push({\n          token: firstCharacter,\n          fullToken: token\n        });\n        var parseResult = parser.run(dateString, token, locale.match, subFnOptions);\n        if (!parseResult) {\n          return {\n            v: new Date(NaN)\n          };\n        }\n        setters.push(parseResult.setter);\n        dateString = parseResult.rest;\n      } else {\n        if (firstCharacter.match(unescapedLatinCharacterRegExp)) {\n          throw new RangeError('Format string contains an unescaped latin alphabet character `' + firstCharacter + '`');\n        }\n\n        // Replace two single quote characters with one single quote character\n        if (token === \"''\") {\n          token = \"'\";\n        } else if (firstCharacter === \"'\") {\n          token = cleanEscapedString(token);\n        }\n\n        // Cut token from string, or, if string doesn't match the token, return Invalid Date\n        if (dateString.indexOf(token) === 0) {\n          dateString = dateString.slice(token.length);\n        } else {\n          return {\n            v: new Date(NaN)\n          };\n        }\n      }\n    };\n    for (_iterator.s(); !(_step = _iterator.n()).done;) {\n      var _ret = _loop();\n      if (_typeof(_ret) === \"object\") return _ret.v;\n    }\n\n    // Check if the remaining input contains something other than whitespace\n  } catch (err) {\n    _iterator.e(err);\n  } finally {\n    _iterator.f();\n  }\n  if (dateString.length > 0 && notWhitespaceRegExp.test(dateString)) {\n    return new Date(NaN);\n  }\n  var uniquePrioritySetters = setters.map(function (setter) {\n    return setter.priority;\n  }).sort(function (a, b) {\n    return b - a;\n  }).filter(function (priority, index, array) {\n    return array.indexOf(priority) === index;\n  }).map(function (priority) {\n    return setters.filter(function (setter) {\n      return setter.priority === priority;\n    }).sort(function (a, b) {\n      return b.subPriority - a.subPriority;\n    });\n  }).map(function (setterArray) {\n    return setterArray[0];\n  });\n  var date = toDate(dirtyReferenceDate);\n  if (isNaN(date.getTime())) {\n    return new Date(NaN);\n  }\n\n  // Convert the date in system timezone to the same date in UTC+00:00 timezone.\n  var utcDate = subMilliseconds(date, getTimezoneOffsetInMilliseconds(date));\n  var flags = {};\n  var _iterator2 = _createForOfIteratorHelper(uniquePrioritySetters),\n    _step2;\n  try {\n    for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n      var setter = _step2.value;\n      if (!setter.validate(utcDate, subFnOptions)) {\n        return new Date(NaN);\n      }\n      var result = setter.set(utcDate, flags, subFnOptions);\n      // Result is tuple (date, flags)\n      if (Array.isArray(result)) {\n        utcDate = result[0];\n        assign(flags, result[1]);\n        // Result is date\n      } else {\n        utcDate = result;\n      }\n    }\n  } catch (err) {\n    _iterator2.e(err);\n  } finally {\n    _iterator2.f();\n  }\n  return utcDate;\n}\nfunction cleanEscapedString(input) {\n  return input.match(escapedStringRegExp)[1].replace(doubleQuoteRegExp, \"'\");\n}", "map": {"version": 3, "names": ["_typeof", "_createForOfIteratorHelper", "defaultLocale", "subMilliseconds", "toDate", "assign", "longFormatters", "getTimezoneOffsetInMilliseconds", "isProtectedDayOfYearToken", "isProtectedWeekYearToken", "throwProtectedError", "toInteger", "requiredArgs", "DateToSystemTimezoneSetter", "parsers", "getDefaultOptions", "formattingTokensRegExp", "longFormattingTokensRegExp", "escapedStringRegExp", "doubleQuoteRegExp", "notWhitespaceRegExp", "unescapedLatinCharacterRegExp", "parse", "dirtyDateString", "dirtyFormatString", "dirtyReferenceDate", "options", "_ref", "_options$locale", "_ref2", "_ref3", "_ref4", "_options$firstWeekCon", "_options$locale2", "_options$locale2$opti", "_defaultOptions$local", "_defaultOptions$local2", "_ref5", "_ref6", "_ref7", "_options$weekStartsOn", "_options$locale3", "_options$locale3$opti", "_defaultOptions$local3", "_defaultOptions$local4", "arguments", "dateString", "String", "formatString", "defaultOptions", "locale", "match", "RangeError", "firstWeekContainsDate", "weekStartsOn", "Date", "NaN", "subFnOptions", "setters", "tokens", "map", "substring", "firstCharacter", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "formatLong", "join", "usedTokens", "_iterator", "_step", "_loop", "token", "value", "useAdditionalWeekYearTokens", "useAdditionalDayOfYearTokens", "parser", "incompatibleTokens", "Array", "isArray", "incompatibleToken", "find", "usedToken", "includes", "concat", "fullToken", "length", "push", "parseResult", "run", "v", "setter", "rest", "cleanEscapedString", "indexOf", "slice", "s", "n", "done", "_ret", "err", "e", "f", "test", "uniquePrioritySetters", "priority", "sort", "a", "b", "filter", "index", "array", "subPriority", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "date", "isNaN", "getTime", "utcDate", "flags", "_iterator2", "_step2", "validate", "result", "set", "input", "replace"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/parse/index.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _createForOfIteratorHelper from \"@babel/runtime/helpers/esm/createForOfIteratorHelper\";\nimport defaultLocale from \"../_lib/defaultLocale/index.js\";\nimport subMilliseconds from \"../subMilliseconds/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport assign from \"../_lib/assign/index.js\";\nimport longFormatters from \"../_lib/format/longFormatters/index.js\";\nimport getTimezoneOffsetInMilliseconds from \"../_lib/getTimezoneOffsetInMilliseconds/index.js\";\nimport { isProtectedDayOfYearToken, isProtectedWeekYearToken, throwProtectedError } from \"../_lib/protectedTokens/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { DateToSystemTimezoneSetter } from \"./_lib/Setter.js\";\nimport { parsers } from \"./_lib/parsers/index.js\";\nimport { getDefaultOptions } from \"../_lib/defaultOptions/index.js\"; // This RegExp consists of three parts separated by `|`:\n// - [yYQqMLwIdDecihHKkms]o matches any available ordinal number token\n//   (one of the certain letters followed by `o`)\n// - (\\w)\\1* matches any sequences of the same letter\n// - '' matches two quote characters in a row\n// - '(''|[^'])+('|$) matches anything surrounded by two quote characters ('),\n//   except a single quote symbol, which ends the sequence.\n//   Two quote characters do not end the sequence.\n//   If there is no matching single quote\n//   then the sequence will continue until the end of the string.\n// - . matches any single character unmatched by previous parts of the RegExps\nvar formattingTokensRegExp = /[yYQqMLwIdDecihHKkms]o|(\\w)\\1*|''|'(''|[^'])+('|$)|./g;\n\n// This RegExp catches symbols escaped by quotes, and also\n// sequences of symbols P, p, and the combinations like `PPPPPPPppppp`\nvar longFormattingTokensRegExp = /P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;\nvar escapedStringRegExp = /^'([^]*?)'?$/;\nvar doubleQuoteRegExp = /''/g;\nvar notWhitespaceRegExp = /\\S/;\nvar unescapedLatinCharacterRegExp = /[a-zA-Z]/;\n\n/**\n * @name parse\n * @category Common Helpers\n * @summary Parse the date.\n *\n * @description\n * Return the date parsed from string using the given format string.\n *\n * > ⚠️ Please note that the `format` tokens differ from Moment.js and other libraries.\n * > See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * The characters in the format string wrapped between two single quotes characters (') are escaped.\n * Two single quotes in a row, whether inside or outside a quoted sequence, represent a 'real' single quote.\n *\n * Format of the format string is based on Unicode Technical Standard #35:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * with a few additions (see note 5 below the table).\n *\n * Not all tokens are compatible. Combinations that don't make sense or could lead to bugs are prohibited\n * and will throw `RangeError`. For example usage of 24-hour format token with AM/PM token will throw an exception:\n *\n * ```javascript\n * parse('23 AM', 'HH a', new Date())\n * //=> RangeError: The format string mustn't contain `HH` and `a` at the same time\n * ```\n *\n * See the compatibility table: https://docs.google.com/spreadsheets/d/e/2PACX-1vQOPU3xUhplll6dyoMmVUXHKl_8CRDs6_ueLmex3SoqwhuolkuN3O05l4rqx5h1dKX8eb46Ul-CCSrq/pubhtml?gid=0&single=true\n *\n * Accepted format string patterns:\n * | Unit                            |Prior| Pattern | Result examples                   | Notes |\n * |---------------------------------|-----|---------|-----------------------------------|-------|\n * | Era                             | 140 | G..GGG  | AD, BC                            |       |\n * |                                 |     | GGGG    | Anno Domini, Before Christ        | 2     |\n * |                                 |     | GGGGG   | A, B                              |       |\n * | Calendar year                   | 130 | y       | 44, 1, 1900, 2017, 9999           | 4     |\n * |                                 |     | yo      | 44th, 1st, 1900th, 9999999th      | 4,5   |\n * |                                 |     | yy      | 44, 01, 00, 17                    | 4     |\n * |                                 |     | yyy     | 044, 001, 123, 999                | 4     |\n * |                                 |     | yyyy    | 0044, 0001, 1900, 2017            | 4     |\n * |                                 |     | yyyyy   | ...                               | 2,4   |\n * | Local week-numbering year       | 130 | Y       | 44, 1, 1900, 2017, 9000           | 4     |\n * |                                 |     | Yo      | 44th, 1st, 1900th, 9999999th      | 4,5   |\n * |                                 |     | YY      | 44, 01, 00, 17                    | 4,6   |\n * |                                 |     | YYY     | 044, 001, 123, 999                | 4     |\n * |                                 |     | YYYY    | 0044, 0001, 1900, 2017            | 4,6   |\n * |                                 |     | YYYYY   | ...                               | 2,4   |\n * | ISO week-numbering year         | 130 | R       | -43, 1, 1900, 2017, 9999, -9999   | 4,5   |\n * |                                 |     | RR      | -43, 01, 00, 17                   | 4,5   |\n * |                                 |     | RRR     | -043, 001, 123, 999, -999         | 4,5   |\n * |                                 |     | RRRR    | -0043, 0001, 2017, 9999, -9999    | 4,5   |\n * |                                 |     | RRRRR   | ...                               | 2,4,5 |\n * | Extended year                   | 130 | u       | -43, 1, 1900, 2017, 9999, -999    | 4     |\n * |                                 |     | uu      | -43, 01, 99, -99                  | 4     |\n * |                                 |     | uuu     | -043, 001, 123, 999, -999         | 4     |\n * |                                 |     | uuuu    | -0043, 0001, 2017, 9999, -9999    | 4     |\n * |                                 |     | uuuuu   | ...                               | 2,4   |\n * | Quarter (formatting)            | 120 | Q       | 1, 2, 3, 4                        |       |\n * |                                 |     | Qo      | 1st, 2nd, 3rd, 4th                | 5     |\n * |                                 |     | QQ      | 01, 02, 03, 04                    |       |\n * |                                 |     | QQQ     | Q1, Q2, Q3, Q4                    |       |\n * |                                 |     | QQQQ    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 |     | QQQQQ   | 1, 2, 3, 4                        | 4     |\n * | Quarter (stand-alone)           | 120 | q       | 1, 2, 3, 4                        |       |\n * |                                 |     | qo      | 1st, 2nd, 3rd, 4th                | 5     |\n * |                                 |     | qq      | 01, 02, 03, 04                    |       |\n * |                                 |     | qqq     | Q1, Q2, Q3, Q4                    |       |\n * |                                 |     | qqqq    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 |     | qqqqq   | 1, 2, 3, 4                        | 3     |\n * | Month (formatting)              | 110 | M       | 1, 2, ..., 12                     |       |\n * |                                 |     | Mo      | 1st, 2nd, ..., 12th               | 5     |\n * |                                 |     | MM      | 01, 02, ..., 12                   |       |\n * |                                 |     | MMM     | Jan, Feb, ..., Dec                |       |\n * |                                 |     | MMMM    | January, February, ..., December  | 2     |\n * |                                 |     | MMMMM   | J, F, ..., D                      |       |\n * | Month (stand-alone)             | 110 | L       | 1, 2, ..., 12                     |       |\n * |                                 |     | Lo      | 1st, 2nd, ..., 12th               | 5     |\n * |                                 |     | LL      | 01, 02, ..., 12                   |       |\n * |                                 |     | LLL     | Jan, Feb, ..., Dec                |       |\n * |                                 |     | LLLL    | January, February, ..., December  | 2     |\n * |                                 |     | LLLLL   | J, F, ..., D                      |       |\n * | Local week of year              | 100 | w       | 1, 2, ..., 53                     |       |\n * |                                 |     | wo      | 1st, 2nd, ..., 53th               | 5     |\n * |                                 |     | ww      | 01, 02, ..., 53                   |       |\n * | ISO week of year                | 100 | I       | 1, 2, ..., 53                     | 5     |\n * |                                 |     | Io      | 1st, 2nd, ..., 53th               | 5     |\n * |                                 |     | II      | 01, 02, ..., 53                   | 5     |\n * | Day of month                    |  90 | d       | 1, 2, ..., 31                     |       |\n * |                                 |     | do      | 1st, 2nd, ..., 31st               | 5     |\n * |                                 |     | dd      | 01, 02, ..., 31                   |       |\n * | Day of year                     |  90 | D       | 1, 2, ..., 365, 366               | 7     |\n * |                                 |     | Do      | 1st, 2nd, ..., 365th, 366th       | 5     |\n * |                                 |     | DD      | 01, 02, ..., 365, 366             | 7     |\n * |                                 |     | DDD     | 001, 002, ..., 365, 366           |       |\n * |                                 |     | DDDD    | ...                               | 2     |\n * | Day of week (formatting)        |  90 | E..EEE  | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 |     | EEEE    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 |     | EEEEE   | M, T, W, T, F, S, S               |       |\n * |                                 |     | EEEEEE  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | ISO day of week (formatting)    |  90 | i       | 1, 2, 3, ..., 7                   | 5     |\n * |                                 |     | io      | 1st, 2nd, ..., 7th                | 5     |\n * |                                 |     | ii      | 01, 02, ..., 07                   | 5     |\n * |                                 |     | iii     | Mon, Tue, Wed, ..., Sun           | 5     |\n * |                                 |     | iiii    | Monday, Tuesday, ..., Sunday      | 2,5   |\n * |                                 |     | iiiii   | M, T, W, T, F, S, S               | 5     |\n * |                                 |     | iiiiii  | Mo, Tu, We, Th, Fr, Sa, Su        | 5     |\n * | Local day of week (formatting)  |  90 | e       | 2, 3, 4, ..., 1                   |       |\n * |                                 |     | eo      | 2nd, 3rd, ..., 1st                | 5     |\n * |                                 |     | ee      | 02, 03, ..., 01                   |       |\n * |                                 |     | eee     | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 |     | eeee    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 |     | eeeee   | M, T, W, T, F, S, S               |       |\n * |                                 |     | eeeeee  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | Local day of week (stand-alone) |  90 | c       | 2, 3, 4, ..., 1                   |       |\n * |                                 |     | co      | 2nd, 3rd, ..., 1st                | 5     |\n * |                                 |     | cc      | 02, 03, ..., 01                   |       |\n * |                                 |     | ccc     | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 |     | cccc    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 |     | ccccc   | M, T, W, T, F, S, S               |       |\n * |                                 |     | cccccc  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | AM, PM                          |  80 | a..aaa  | AM, PM                            |       |\n * |                                 |     | aaaa    | a.m., p.m.                        | 2     |\n * |                                 |     | aaaaa   | a, p                              |       |\n * | AM, PM, noon, midnight          |  80 | b..bbb  | AM, PM, noon, midnight            |       |\n * |                                 |     | bbbb    | a.m., p.m., noon, midnight        | 2     |\n * |                                 |     | bbbbb   | a, p, n, mi                       |       |\n * | Flexible day period             |  80 | B..BBB  | at night, in the morning, ...     |       |\n * |                                 |     | BBBB    | at night, in the morning, ...     | 2     |\n * |                                 |     | BBBBB   | at night, in the morning, ...     |       |\n * | Hour [1-12]                     |  70 | h       | 1, 2, ..., 11, 12                 |       |\n * |                                 |     | ho      | 1st, 2nd, ..., 11th, 12th         | 5     |\n * |                                 |     | hh      | 01, 02, ..., 11, 12               |       |\n * | Hour [0-23]                     |  70 | H       | 0, 1, 2, ..., 23                  |       |\n * |                                 |     | Ho      | 0th, 1st, 2nd, ..., 23rd          | 5     |\n * |                                 |     | HH      | 00, 01, 02, ..., 23               |       |\n * | Hour [0-11]                     |  70 | K       | 1, 2, ..., 11, 0                  |       |\n * |                                 |     | Ko      | 1st, 2nd, ..., 11th, 0th          | 5     |\n * |                                 |     | KK      | 01, 02, ..., 11, 00               |       |\n * | Hour [1-24]                     |  70 | k       | 24, 1, 2, ..., 23                 |       |\n * |                                 |     | ko      | 24th, 1st, 2nd, ..., 23rd         | 5     |\n * |                                 |     | kk      | 24, 01, 02, ..., 23               |       |\n * | Minute                          |  60 | m       | 0, 1, ..., 59                     |       |\n * |                                 |     | mo      | 0th, 1st, ..., 59th               | 5     |\n * |                                 |     | mm      | 00, 01, ..., 59                   |       |\n * | Second                          |  50 | s       | 0, 1, ..., 59                     |       |\n * |                                 |     | so      | 0th, 1st, ..., 59th               | 5     |\n * |                                 |     | ss      | 00, 01, ..., 59                   |       |\n * | Seconds timestamp               |  40 | t       | 512969520                         |       |\n * |                                 |     | tt      | ...                               | 2     |\n * | Fraction of second              |  30 | S       | 0, 1, ..., 9                      |       |\n * |                                 |     | SS      | 00, 01, ..., 99                   |       |\n * |                                 |     | SSS     | 000, 001, ..., 999                |       |\n * |                                 |     | SSSS    | ...                               | 2     |\n * | Milliseconds timestamp          |  20 | T       | 512969520900                      |       |\n * |                                 |     | TT      | ...                               | 2     |\n * | Timezone (ISO-8601 w/ Z)        |  10 | X       | -08, +0530, Z                     |       |\n * |                                 |     | XX      | -0800, +0530, Z                   |       |\n * |                                 |     | XXX     | -08:00, +05:30, Z                 |       |\n * |                                 |     | XXXX    | -0800, +0530, Z, +123456          | 2     |\n * |                                 |     | XXXXX   | -08:00, +05:30, Z, +12:34:56      |       |\n * | Timezone (ISO-8601 w/o Z)       |  10 | x       | -08, +0530, +00                   |       |\n * |                                 |     | xx      | -0800, +0530, +0000               |       |\n * |                                 |     | xxx     | -08:00, +05:30, +00:00            | 2     |\n * |                                 |     | xxxx    | -0800, +0530, +0000, +123456      |       |\n * |                                 |     | xxxxx   | -08:00, +05:30, +00:00, +12:34:56 |       |\n * | Long localized date             |  NA | P       | 05/29/1453                        | 5,8   |\n * |                                 |     | PP      | May 29, 1453                      |       |\n * |                                 |     | PPP     | May 29th, 1453                    |       |\n * |                                 |     | PPPP    | Sunday, May 29th, 1453            | 2,5,8 |\n * | Long localized time             |  NA | p       | 12:00 AM                          | 5,8   |\n * |                                 |     | pp      | 12:00:00 AM                       |       |\n * | Combination of date and time    |  NA | Pp      | 05/29/1453, 12:00 AM              |       |\n * |                                 |     | PPpp    | May 29, 1453, 12:00:00 AM         |       |\n * |                                 |     | PPPpp   | May 29th, 1453 at ...             |       |\n * |                                 |     | PPPPpp  | Sunday, May 29th, 1453 at ...     | 2,5,8 |\n * Notes:\n * 1. \"Formatting\" units (e.g. formatting quarter) in the default en-US locale\n *    are the same as \"stand-alone\" units, but are different in some languages.\n *    \"Formatting\" units are declined according to the rules of the language\n *    in the context of a date. \"Stand-alone\" units are always nominative singular.\n *    In `format` function, they will produce different result:\n *\n *    `format(new Date(2017, 10, 6), 'do LLLL', {locale: cs}) //=> '6. listopad'`\n *\n *    `format(new Date(2017, 10, 6), 'do MMMM', {locale: cs}) //=> '6. listopadu'`\n *\n *    `parse` will try to match both formatting and stand-alone units interchangably.\n *\n * 2. Any sequence of the identical letters is a pattern, unless it is escaped by\n *    the single quote characters (see below).\n *    If the sequence is longer than listed in table:\n *    - for numerical units (`yyyyyyyy`) `parse` will try to match a number\n *      as wide as the sequence\n *    - for text units (`MMMMMMMM`) `parse` will try to match the widest variation of the unit.\n *      These variations are marked with \"2\" in the last column of the table.\n *\n * 3. `QQQQQ` and `qqqqq` could be not strictly numerical in some locales.\n *    These tokens represent the shortest form of the quarter.\n *\n * 4. The main difference between `y` and `u` patterns are B.C. years:\n *\n *    | Year | `y` | `u` |\n *    |------|-----|-----|\n *    | AC 1 |   1 |   1 |\n *    | BC 1 |   1 |   0 |\n *    | BC 2 |   2 |  -1 |\n *\n *    Also `yy` will try to guess the century of two digit year by proximity with `referenceDate`:\n *\n *    `parse('50', 'yy', new Date(2018, 0, 1)) //=> Sat Jan 01 2050 00:00:00`\n *\n *    `parse('75', 'yy', new Date(2018, 0, 1)) //=> Wed Jan 01 1975 00:00:00`\n *\n *    while `uu` will just assign the year as is:\n *\n *    `parse('50', 'uu', new Date(2018, 0, 1)) //=> Sat Jan 01 0050 00:00:00`\n *\n *    `parse('75', 'uu', new Date(2018, 0, 1)) //=> Tue Jan 01 0075 00:00:00`\n *\n *    The same difference is true for local and ISO week-numbering years (`Y` and `R`),\n *    except local week-numbering years are dependent on `options.weekStartsOn`\n *    and `options.firstWeekContainsDate` (compare [setISOWeekYear]{@link https://date-fns.org/docs/setISOWeekYear}\n *    and [setWeekYear]{@link https://date-fns.org/docs/setWeekYear}).\n *\n * 5. These patterns are not in the Unicode Technical Standard #35:\n *    - `i`: ISO day of week\n *    - `I`: ISO week of year\n *    - `R`: ISO week-numbering year\n *    - `o`: ordinal number modifier\n *    - `P`: long localized date\n *    - `p`: long localized time\n *\n * 6. `YY` and `YYYY` tokens represent week-numbering years but they are often confused with years.\n *    You should enable `options.useAdditionalWeekYearTokens` to use them. See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * 7. `D` and `DD` tokens represent days of the year but they are ofthen confused with days of the month.\n *    You should enable `options.useAdditionalDayOfYearTokens` to use them. See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * 8. `P+` tokens do not have a defined priority since they are merely aliases to other tokens based\n *    on the given locale.\n *\n *    using `en-US` locale: `P` => `MM/dd/yyyy`\n *    using `en-US` locale: `p` => `hh:mm a`\n *    using `pt-BR` locale: `P` => `dd/MM/yyyy`\n *    using `pt-BR` locale: `p` => `HH:mm`\n *\n * Values will be assigned to the date in the descending order of its unit's priority.\n * Units of an equal priority overwrite each other in the order of appearance.\n *\n * If no values of higher priority are parsed (e.g. when parsing string 'January 1st' without a year),\n * the values will be taken from 3rd argument `referenceDate` which works as a context of parsing.\n *\n * `referenceDate` must be passed for correct work of the function.\n * If you're not sure which `referenceDate` to supply, create a new instance of Date:\n * `parse('02/11/2014', 'MM/dd/yyyy', new Date())`\n * In this case parsing will be done in the context of the current date.\n * If `referenceDate` is `Invalid Date` or a value not convertible to valid `Date`,\n * then `Invalid Date` will be returned.\n *\n * The result may vary by locale.\n *\n * If `formatString` matches with `dateString` but does not provides tokens, `referenceDate` will be returned.\n *\n * If parsing failed, `Invalid Date` will be returned.\n * Invalid Date is a Date, whose time value is NaN.\n * Time value of Date: http://es5.github.io/#x15.9.1.1\n *\n * @param {String} dateString - the string to parse\n * @param {String} formatString - the string of tokens\n * @param {Date|Number} referenceDate - defines values missing from the parsed dateString\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @param {1|2|3|4|5|6|7} [options.firstWeekContainsDate=1] - the day of January, which is always in the first week of the year\n * @param {Boolean} [options.useAdditionalWeekYearTokens=false] - if true, allows usage of the week-numbering year tokens `YY` and `YYYY`;\n *   see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @param {Boolean} [options.useAdditionalDayOfYearTokens=false] - if true, allows usage of the day of year tokens `D` and `DD`;\n *   see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @returns {Date} the parsed date\n * @throws {TypeError} 3 arguments required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n * @throws {RangeError} `options.firstWeekContainsDate` must be between 1 and 7\n * @throws {RangeError} `options.locale` must contain `match` property\n * @throws {RangeError} use `yyyy` instead of `YYYY` for formatting years using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws {RangeError} use `yy` instead of `YY` for formatting years using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws {RangeError} use `d` instead of `D` for formatting days of the month using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws {RangeError} use `dd` instead of `DD` for formatting days of the month using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws {RangeError} format string contains an unescaped latin alphabet character\n *\n * @example\n * // Parse 11 February 2014 from middle-endian format:\n * var result = parse('02/11/2014', 'MM/dd/yyyy', new Date())\n * //=> Tue Feb 11 2014 00:00:00\n *\n * @example\n * // Parse 28th of February in Esperanto locale in the context of 2010 year:\n * import eo from 'date-fns/locale/eo'\n * var result = parse('28-a de februaro', \"do 'de' MMMM\", new Date(2010, 0, 1), {\n *   locale: eo\n * })\n * //=> Sun Feb 28 2010 00:00:00\n */\nexport default function parse(dirtyDateString, dirtyFormatString, dirtyReferenceDate, options) {\n  var _ref, _options$locale, _ref2, _ref3, _ref4, _options$firstWeekCon, _options$locale2, _options$locale2$opti, _defaultOptions$local, _defaultOptions$local2, _ref5, _ref6, _ref7, _options$weekStartsOn, _options$locale3, _options$locale3$opti, _defaultOptions$local3, _defaultOptions$local4;\n  requiredArgs(3, arguments);\n  var dateString = String(dirtyDateString);\n  var formatString = String(dirtyFormatString);\n  var defaultOptions = getDefaultOptions();\n  var locale = (_ref = (_options$locale = options === null || options === void 0 ? void 0 : options.locale) !== null && _options$locale !== void 0 ? _options$locale : defaultOptions.locale) !== null && _ref !== void 0 ? _ref : defaultLocale;\n  if (!locale.match) {\n    throw new RangeError('locale must contain match property');\n  }\n  var firstWeekContainsDate = toInteger((_ref2 = (_ref3 = (_ref4 = (_options$firstWeekCon = options === null || options === void 0 ? void 0 : options.firstWeekContainsDate) !== null && _options$firstWeekCon !== void 0 ? _options$firstWeekCon : options === null || options === void 0 ? void 0 : (_options$locale2 = options.locale) === null || _options$locale2 === void 0 ? void 0 : (_options$locale2$opti = _options$locale2.options) === null || _options$locale2$opti === void 0 ? void 0 : _options$locale2$opti.firstWeekContainsDate) !== null && _ref4 !== void 0 ? _ref4 : defaultOptions.firstWeekContainsDate) !== null && _ref3 !== void 0 ? _ref3 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 ? void 0 : (_defaultOptions$local2 = _defaultOptions$local.options) === null || _defaultOptions$local2 === void 0 ? void 0 : _defaultOptions$local2.firstWeekContainsDate) !== null && _ref2 !== void 0 ? _ref2 : 1);\n\n  // Test if weekStartsOn is between 1 and 7 _and_ is not NaN\n  if (!(firstWeekContainsDate >= 1 && firstWeekContainsDate <= 7)) {\n    throw new RangeError('firstWeekContainsDate must be between 1 and 7 inclusively');\n  }\n  var weekStartsOn = toInteger((_ref5 = (_ref6 = (_ref7 = (_options$weekStartsOn = options === null || options === void 0 ? void 0 : options.weekStartsOn) !== null && _options$weekStartsOn !== void 0 ? _options$weekStartsOn : options === null || options === void 0 ? void 0 : (_options$locale3 = options.locale) === null || _options$locale3 === void 0 ? void 0 : (_options$locale3$opti = _options$locale3.options) === null || _options$locale3$opti === void 0 ? void 0 : _options$locale3$opti.weekStartsOn) !== null && _ref7 !== void 0 ? _ref7 : defaultOptions.weekStartsOn) !== null && _ref6 !== void 0 ? _ref6 : (_defaultOptions$local3 = defaultOptions.locale) === null || _defaultOptions$local3 === void 0 ? void 0 : (_defaultOptions$local4 = _defaultOptions$local3.options) === null || _defaultOptions$local4 === void 0 ? void 0 : _defaultOptions$local4.weekStartsOn) !== null && _ref5 !== void 0 ? _ref5 : 0);\n\n  // Test if weekStartsOn is between 0 and 6 _and_ is not NaN\n  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {\n    throw new RangeError('weekStartsOn must be between 0 and 6 inclusively');\n  }\n  if (formatString === '') {\n    if (dateString === '') {\n      return toDate(dirtyReferenceDate);\n    } else {\n      return new Date(NaN);\n    }\n  }\n  var subFnOptions = {\n    firstWeekContainsDate: firstWeekContainsDate,\n    weekStartsOn: weekStartsOn,\n    locale: locale\n  };\n\n  // If timezone isn't specified, it will be set to the system timezone\n  var setters = [new DateToSystemTimezoneSetter()];\n  var tokens = formatString.match(longFormattingTokensRegExp).map(function (substring) {\n    var firstCharacter = substring[0];\n    if (firstCharacter in longFormatters) {\n      var longFormatter = longFormatters[firstCharacter];\n      return longFormatter(substring, locale.formatLong);\n    }\n    return substring;\n  }).join('').match(formattingTokensRegExp);\n  var usedTokens = [];\n  var _iterator = _createForOfIteratorHelper(tokens),\n    _step;\n  try {\n    var _loop = function _loop() {\n      var token = _step.value;\n      if (!(options !== null && options !== void 0 && options.useAdditionalWeekYearTokens) && isProtectedWeekYearToken(token)) {\n        throwProtectedError(token, formatString, dirtyDateString);\n      }\n      if (!(options !== null && options !== void 0 && options.useAdditionalDayOfYearTokens) && isProtectedDayOfYearToken(token)) {\n        throwProtectedError(token, formatString, dirtyDateString);\n      }\n      var firstCharacter = token[0];\n      var parser = parsers[firstCharacter];\n      if (parser) {\n        var incompatibleTokens = parser.incompatibleTokens;\n        if (Array.isArray(incompatibleTokens)) {\n          var incompatibleToken = usedTokens.find(function (usedToken) {\n            return incompatibleTokens.includes(usedToken.token) || usedToken.token === firstCharacter;\n          });\n          if (incompatibleToken) {\n            throw new RangeError(\"The format string mustn't contain `\".concat(incompatibleToken.fullToken, \"` and `\").concat(token, \"` at the same time\"));\n          }\n        } else if (parser.incompatibleTokens === '*' && usedTokens.length > 0) {\n          throw new RangeError(\"The format string mustn't contain `\".concat(token, \"` and any other token at the same time\"));\n        }\n        usedTokens.push({\n          token: firstCharacter,\n          fullToken: token\n        });\n        var parseResult = parser.run(dateString, token, locale.match, subFnOptions);\n        if (!parseResult) {\n          return {\n            v: new Date(NaN)\n          };\n        }\n        setters.push(parseResult.setter);\n        dateString = parseResult.rest;\n      } else {\n        if (firstCharacter.match(unescapedLatinCharacterRegExp)) {\n          throw new RangeError('Format string contains an unescaped latin alphabet character `' + firstCharacter + '`');\n        }\n\n        // Replace two single quote characters with one single quote character\n        if (token === \"''\") {\n          token = \"'\";\n        } else if (firstCharacter === \"'\") {\n          token = cleanEscapedString(token);\n        }\n\n        // Cut token from string, or, if string doesn't match the token, return Invalid Date\n        if (dateString.indexOf(token) === 0) {\n          dateString = dateString.slice(token.length);\n        } else {\n          return {\n            v: new Date(NaN)\n          };\n        }\n      }\n    };\n    for (_iterator.s(); !(_step = _iterator.n()).done;) {\n      var _ret = _loop();\n      if (_typeof(_ret) === \"object\") return _ret.v;\n    }\n\n    // Check if the remaining input contains something other than whitespace\n  } catch (err) {\n    _iterator.e(err);\n  } finally {\n    _iterator.f();\n  }\n  if (dateString.length > 0 && notWhitespaceRegExp.test(dateString)) {\n    return new Date(NaN);\n  }\n  var uniquePrioritySetters = setters.map(function (setter) {\n    return setter.priority;\n  }).sort(function (a, b) {\n    return b - a;\n  }).filter(function (priority, index, array) {\n    return array.indexOf(priority) === index;\n  }).map(function (priority) {\n    return setters.filter(function (setter) {\n      return setter.priority === priority;\n    }).sort(function (a, b) {\n      return b.subPriority - a.subPriority;\n    });\n  }).map(function (setterArray) {\n    return setterArray[0];\n  });\n  var date = toDate(dirtyReferenceDate);\n  if (isNaN(date.getTime())) {\n    return new Date(NaN);\n  }\n\n  // Convert the date in system timezone to the same date in UTC+00:00 timezone.\n  var utcDate = subMilliseconds(date, getTimezoneOffsetInMilliseconds(date));\n  var flags = {};\n  var _iterator2 = _createForOfIteratorHelper(uniquePrioritySetters),\n    _step2;\n  try {\n    for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n      var setter = _step2.value;\n      if (!setter.validate(utcDate, subFnOptions)) {\n        return new Date(NaN);\n      }\n      var result = setter.set(utcDate, flags, subFnOptions);\n      // Result is tuple (date, flags)\n      if (Array.isArray(result)) {\n        utcDate = result[0];\n        assign(flags, result[1]);\n        // Result is date\n      } else {\n        utcDate = result;\n      }\n    }\n  } catch (err) {\n    _iterator2.e(err);\n  } finally {\n    _iterator2.f();\n  }\n  return utcDate;\n}\nfunction cleanEscapedString(input) {\n  return input.match(escapedStringRegExp)[1].replace(doubleQuoteRegExp, \"'\");\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,0BAA0B,MAAM,sDAAsD;AAC7F,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,cAAc,MAAM,wCAAwC;AACnE,OAAOC,+BAA+B,MAAM,kDAAkD;AAC9F,SAASC,yBAAyB,EAAEC,wBAAwB,EAAEC,mBAAmB,QAAQ,kCAAkC;AAC3H,OAAOC,SAAS,MAAM,4BAA4B;AAClD,OAAOC,YAAY,MAAM,+BAA+B;AACxD,SAASC,0BAA0B,QAAQ,kBAAkB;AAC7D,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,iBAAiB,QAAQ,iCAAiC,CAAC,CAAC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,sBAAsB,GAAG,uDAAuD;;AAEpF;AACA;AACA,IAAIC,0BAA0B,GAAG,mCAAmC;AACpE,IAAIC,mBAAmB,GAAG,cAAc;AACxC,IAAIC,iBAAiB,GAAG,KAAK;AAC7B,IAAIC,mBAAmB,GAAG,IAAI;AAC9B,IAAIC,6BAA6B,GAAG,UAAU;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,KAAKA,CAACC,eAAe,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAEC,OAAO,EAAE;EAC7F,IAAIC,IAAI,EAAEC,eAAe,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,qBAAqB,EAAEC,qBAAqB,EAAEC,sBAAsB,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,qBAAqB,EAAEC,sBAAsB,EAAEC,sBAAsB;EAClShC,YAAY,CAAC,CAAC,EAAEiC,SAAS,CAAC;EAC1B,IAAIC,UAAU,GAAGC,MAAM,CAACxB,eAAe,CAAC;EACxC,IAAIyB,YAAY,GAAGD,MAAM,CAACvB,iBAAiB,CAAC;EAC5C,IAAIyB,cAAc,GAAGlC,iBAAiB,CAAC,CAAC;EACxC,IAAImC,MAAM,GAAG,CAACvB,IAAI,GAAG,CAACC,eAAe,GAAGF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACwB,MAAM,MAAM,IAAI,IAAItB,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAGqB,cAAc,CAACC,MAAM,MAAM,IAAI,IAAIvB,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAGzB,aAAa;EAC9O,IAAI,CAACgD,MAAM,CAACC,KAAK,EAAE;IACjB,MAAM,IAAIC,UAAU,CAAC,oCAAoC,CAAC;EAC5D;EACA,IAAIC,qBAAqB,GAAG1C,SAAS,CAAC,CAACkB,KAAK,GAAG,CAACC,KAAK,GAAG,CAACC,KAAK,GAAG,CAACC,qBAAqB,GAAGN,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC2B,qBAAqB,MAAM,IAAI,IAAIrB,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGN,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACO,gBAAgB,GAAGP,OAAO,CAACwB,MAAM,MAAM,IAAI,IAAIjB,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,qBAAqB,GAAGD,gBAAgB,CAACP,OAAO,MAAM,IAAI,IAAIQ,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACmB,qBAAqB,MAAM,IAAI,IAAItB,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGkB,cAAc,CAACI,qBAAqB,MAAM,IAAI,IAAIvB,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACK,qBAAqB,GAAGc,cAAc,CAACC,MAAM,MAAM,IAAI,IAAIf,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,sBAAsB,GAAGD,qBAAqB,CAACT,OAAO,MAAM,IAAI,IAAIU,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACiB,qBAAqB,MAAM,IAAI,IAAIxB,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAAC,CAAC;;EAEx7B;EACA,IAAI,EAAEwB,qBAAqB,IAAI,CAAC,IAAIA,qBAAqB,IAAI,CAAC,CAAC,EAAE;IAC/D,MAAM,IAAID,UAAU,CAAC,2DAA2D,CAAC;EACnF;EACA,IAAIE,YAAY,GAAG3C,SAAS,CAAC,CAAC0B,KAAK,GAAG,CAACC,KAAK,GAAG,CAACC,KAAK,GAAG,CAACC,qBAAqB,GAAGd,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC4B,YAAY,MAAM,IAAI,IAAId,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGd,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACe,gBAAgB,GAAGf,OAAO,CAACwB,MAAM,MAAM,IAAI,IAAIT,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,qBAAqB,GAAGD,gBAAgB,CAACf,OAAO,MAAM,IAAI,IAAIgB,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACY,YAAY,MAAM,IAAI,IAAIf,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGU,cAAc,CAACK,YAAY,MAAM,IAAI,IAAIhB,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACK,sBAAsB,GAAGM,cAAc,CAACC,MAAM,MAAM,IAAI,IAAIP,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,sBAAsB,GAAGD,sBAAsB,CAACjB,OAAO,MAAM,IAAI,IAAIkB,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACU,YAAY,MAAM,IAAI,IAAIjB,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAAC,CAAC;;EAE94B;EACA,IAAI,EAAEiB,YAAY,IAAI,CAAC,IAAIA,YAAY,IAAI,CAAC,CAAC,EAAE;IAC7C,MAAM,IAAIF,UAAU,CAAC,kDAAkD,CAAC;EAC1E;EACA,IAAIJ,YAAY,KAAK,EAAE,EAAE;IACvB,IAAIF,UAAU,KAAK,EAAE,EAAE;MACrB,OAAO1C,MAAM,CAACqB,kBAAkB,CAAC;IACnC,CAAC,MAAM;MACL,OAAO,IAAI8B,IAAI,CAACC,GAAG,CAAC;IACtB;EACF;EACA,IAAIC,YAAY,GAAG;IACjBJ,qBAAqB,EAAEA,qBAAqB;IAC5CC,YAAY,EAAEA,YAAY;IAC1BJ,MAAM,EAAEA;EACV,CAAC;;EAED;EACA,IAAIQ,OAAO,GAAG,CAAC,IAAI7C,0BAA0B,CAAC,CAAC,CAAC;EAChD,IAAI8C,MAAM,GAAGX,YAAY,CAACG,KAAK,CAAClC,0BAA0B,CAAC,CAAC2C,GAAG,CAAC,UAAUC,SAAS,EAAE;IACnF,IAAIC,cAAc,GAAGD,SAAS,CAAC,CAAC,CAAC;IACjC,IAAIC,cAAc,IAAIxD,cAAc,EAAE;MACpC,IAAIyD,aAAa,GAAGzD,cAAc,CAACwD,cAAc,CAAC;MAClD,OAAOC,aAAa,CAACF,SAAS,EAAEX,MAAM,CAACc,UAAU,CAAC;IACpD;IACA,OAAOH,SAAS;EAClB,CAAC,CAAC,CAACI,IAAI,CAAC,EAAE,CAAC,CAACd,KAAK,CAACnC,sBAAsB,CAAC;EACzC,IAAIkD,UAAU,GAAG,EAAE;EACnB,IAAIC,SAAS,GAAGlE,0BAA0B,CAAC0D,MAAM,CAAC;IAChDS,KAAK;EACP,IAAI;IACF,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;MAC3B,IAAIC,KAAK,GAAGF,KAAK,CAACG,KAAK;MACvB,IAAI,EAAE7C,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAAC8C,2BAA2B,CAAC,IAAI/D,wBAAwB,CAAC6D,KAAK,CAAC,EAAE;QACvH5D,mBAAmB,CAAC4D,KAAK,EAAEtB,YAAY,EAAEzB,eAAe,CAAC;MAC3D;MACA,IAAI,EAAEG,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAAC+C,4BAA4B,CAAC,IAAIjE,yBAAyB,CAAC8D,KAAK,CAAC,EAAE;QACzH5D,mBAAmB,CAAC4D,KAAK,EAAEtB,YAAY,EAAEzB,eAAe,CAAC;MAC3D;MACA,IAAIuC,cAAc,GAAGQ,KAAK,CAAC,CAAC,CAAC;MAC7B,IAAII,MAAM,GAAG5D,OAAO,CAACgD,cAAc,CAAC;MACpC,IAAIY,MAAM,EAAE;QACV,IAAIC,kBAAkB,GAAGD,MAAM,CAACC,kBAAkB;QAClD,IAAIC,KAAK,CAACC,OAAO,CAACF,kBAAkB,CAAC,EAAE;UACrC,IAAIG,iBAAiB,GAAGZ,UAAU,CAACa,IAAI,CAAC,UAAUC,SAAS,EAAE;YAC3D,OAAOL,kBAAkB,CAACM,QAAQ,CAACD,SAAS,CAACV,KAAK,CAAC,IAAIU,SAAS,CAACV,KAAK,KAAKR,cAAc;UAC3F,CAAC,CAAC;UACF,IAAIgB,iBAAiB,EAAE;YACrB,MAAM,IAAI1B,UAAU,CAAC,qCAAqC,CAAC8B,MAAM,CAACJ,iBAAiB,CAACK,SAAS,EAAE,SAAS,CAAC,CAACD,MAAM,CAACZ,KAAK,EAAE,oBAAoB,CAAC,CAAC;UAChJ;QACF,CAAC,MAAM,IAAII,MAAM,CAACC,kBAAkB,KAAK,GAAG,IAAIT,UAAU,CAACkB,MAAM,GAAG,CAAC,EAAE;UACrE,MAAM,IAAIhC,UAAU,CAAC,qCAAqC,CAAC8B,MAAM,CAACZ,KAAK,EAAE,wCAAwC,CAAC,CAAC;QACrH;QACAJ,UAAU,CAACmB,IAAI,CAAC;UACdf,KAAK,EAAER,cAAc;UACrBqB,SAAS,EAAEb;QACb,CAAC,CAAC;QACF,IAAIgB,WAAW,GAAGZ,MAAM,CAACa,GAAG,CAACzC,UAAU,EAAEwB,KAAK,EAAEpB,MAAM,CAACC,KAAK,EAAEM,YAAY,CAAC;QAC3E,IAAI,CAAC6B,WAAW,EAAE;UAChB,OAAO;YACLE,CAAC,EAAE,IAAIjC,IAAI,CAACC,GAAG;UACjB,CAAC;QACH;QACAE,OAAO,CAAC2B,IAAI,CAACC,WAAW,CAACG,MAAM,CAAC;QAChC3C,UAAU,GAAGwC,WAAW,CAACI,IAAI;MAC/B,CAAC,MAAM;QACL,IAAI5B,cAAc,CAACX,KAAK,CAAC9B,6BAA6B,CAAC,EAAE;UACvD,MAAM,IAAI+B,UAAU,CAAC,gEAAgE,GAAGU,cAAc,GAAG,GAAG,CAAC;QAC/G;;QAEA;QACA,IAAIQ,KAAK,KAAK,IAAI,EAAE;UAClBA,KAAK,GAAG,GAAG;QACb,CAAC,MAAM,IAAIR,cAAc,KAAK,GAAG,EAAE;UACjCQ,KAAK,GAAGqB,kBAAkB,CAACrB,KAAK,CAAC;QACnC;;QAEA;QACA,IAAIxB,UAAU,CAAC8C,OAAO,CAACtB,KAAK,CAAC,KAAK,CAAC,EAAE;UACnCxB,UAAU,GAAGA,UAAU,CAAC+C,KAAK,CAACvB,KAAK,CAACc,MAAM,CAAC;QAC7C,CAAC,MAAM;UACL,OAAO;YACLI,CAAC,EAAE,IAAIjC,IAAI,CAACC,GAAG;UACjB,CAAC;QACH;MACF;IACF,CAAC;IACD,KAAKW,SAAS,CAAC2B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC1B,KAAK,GAAGD,SAAS,CAAC4B,CAAC,CAAC,CAAC,EAAEC,IAAI,GAAG;MAClD,IAAIC,IAAI,GAAG5B,KAAK,CAAC,CAAC;MAClB,IAAIrE,OAAO,CAACiG,IAAI,CAAC,KAAK,QAAQ,EAAE,OAAOA,IAAI,CAACT,CAAC;IAC/C;;IAEA;EACF,CAAC,CAAC,OAAOU,GAAG,EAAE;IACZ/B,SAAS,CAACgC,CAAC,CAACD,GAAG,CAAC;EAClB,CAAC,SAAS;IACR/B,SAAS,CAACiC,CAAC,CAAC,CAAC;EACf;EACA,IAAItD,UAAU,CAACsC,MAAM,GAAG,CAAC,IAAIhE,mBAAmB,CAACiF,IAAI,CAACvD,UAAU,CAAC,EAAE;IACjE,OAAO,IAAIS,IAAI,CAACC,GAAG,CAAC;EACtB;EACA,IAAI8C,qBAAqB,GAAG5C,OAAO,CAACE,GAAG,CAAC,UAAU6B,MAAM,EAAE;IACxD,OAAOA,MAAM,CAACc,QAAQ;EACxB,CAAC,CAAC,CAACC,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACtB,OAAOA,CAAC,GAAGD,CAAC;EACd,CAAC,CAAC,CAACE,MAAM,CAAC,UAAUJ,QAAQ,EAAEK,KAAK,EAAEC,KAAK,EAAE;IAC1C,OAAOA,KAAK,CAACjB,OAAO,CAACW,QAAQ,CAAC,KAAKK,KAAK;EAC1C,CAAC,CAAC,CAAChD,GAAG,CAAC,UAAU2C,QAAQ,EAAE;IACzB,OAAO7C,OAAO,CAACiD,MAAM,CAAC,UAAUlB,MAAM,EAAE;MACtC,OAAOA,MAAM,CAACc,QAAQ,KAAKA,QAAQ;IACrC,CAAC,CAAC,CAACC,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MACtB,OAAOA,CAAC,CAACI,WAAW,GAAGL,CAAC,CAACK,WAAW;IACtC,CAAC,CAAC;EACJ,CAAC,CAAC,CAAClD,GAAG,CAAC,UAAUmD,WAAW,EAAE;IAC5B,OAAOA,WAAW,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC;EACF,IAAIC,IAAI,GAAG5G,MAAM,CAACqB,kBAAkB,CAAC;EACrC,IAAIwF,KAAK,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE;IACzB,OAAO,IAAI3D,IAAI,CAACC,GAAG,CAAC;EACtB;;EAEA;EACA,IAAI2D,OAAO,GAAGhH,eAAe,CAAC6G,IAAI,EAAEzG,+BAA+B,CAACyG,IAAI,CAAC,CAAC;EAC1E,IAAII,KAAK,GAAG,CAAC,CAAC;EACd,IAAIC,UAAU,GAAGpH,0BAA0B,CAACqG,qBAAqB,CAAC;IAChEgB,MAAM;EACR,IAAI;IACF,KAAKD,UAAU,CAACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAACwB,MAAM,GAAGD,UAAU,CAACtB,CAAC,CAAC,CAAC,EAAEC,IAAI,GAAG;MACrD,IAAIP,MAAM,GAAG6B,MAAM,CAAC/C,KAAK;MACzB,IAAI,CAACkB,MAAM,CAAC8B,QAAQ,CAACJ,OAAO,EAAE1D,YAAY,CAAC,EAAE;QAC3C,OAAO,IAAIF,IAAI,CAACC,GAAG,CAAC;MACtB;MACA,IAAIgE,MAAM,GAAG/B,MAAM,CAACgC,GAAG,CAACN,OAAO,EAAEC,KAAK,EAAE3D,YAAY,CAAC;MACrD;MACA,IAAImB,KAAK,CAACC,OAAO,CAAC2C,MAAM,CAAC,EAAE;QACzBL,OAAO,GAAGK,MAAM,CAAC,CAAC,CAAC;QACnBnH,MAAM,CAAC+G,KAAK,EAAEI,MAAM,CAAC,CAAC,CAAC,CAAC;QACxB;MACF,CAAC,MAAM;QACLL,OAAO,GAAGK,MAAM;MAClB;IACF;EACF,CAAC,CAAC,OAAOtB,GAAG,EAAE;IACZmB,UAAU,CAAClB,CAAC,CAACD,GAAG,CAAC;EACnB,CAAC,SAAS;IACRmB,UAAU,CAACjB,CAAC,CAAC,CAAC;EAChB;EACA,OAAOe,OAAO;AAChB;AACA,SAASxB,kBAAkBA,CAAC+B,KAAK,EAAE;EACjC,OAAOA,KAAK,CAACvE,KAAK,CAACjC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAACyG,OAAO,CAACxG,iBAAiB,EAAE,GAAG,CAAC;AAC5E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}