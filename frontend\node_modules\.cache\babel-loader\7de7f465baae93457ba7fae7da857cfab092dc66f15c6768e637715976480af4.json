{"ast": null, "code": "import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setHours\n * @category Hour Helpers\n * @summary Set the hours to the given date.\n *\n * @description\n * Set the hours to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} hours - the hours of the new date\n * @returns {Date} the new date with the hours set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set 4 hours to 1 September 2014 11:30:00:\n * const result = setHours(new Date(2014, 8, 1, 11, 30), 4)\n * //=> Mon Sep 01 2014 04:30:00\n */\nexport default function setHours(dirtyDate, dirtyHours) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var hours = toInteger(dirtyHours);\n  date.setHours(hours);\n  return date;\n}", "map": {"version": 3, "names": ["toInteger", "toDate", "requiredArgs", "setHours", "dirtyDate", "dirtyHours", "arguments", "date", "hours"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/setHours/index.js"], "sourcesContent": ["import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setHours\n * @category Hour Helpers\n * @summary Set the hours to the given date.\n *\n * @description\n * Set the hours to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} hours - the hours of the new date\n * @returns {Date} the new date with the hours set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set 4 hours to 1 September 2014 11:30:00:\n * const result = setHours(new Date(2014, 8, 1, 11, 30), 4)\n * //=> Mon Sep 01 2014 04:30:00\n */\nexport default function setHours(dirtyDate, dirtyHours) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var hours = toInteger(dirtyHours);\n  date.setHours(hours);\n  return date;\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,4BAA4B;AAClD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,QAAQA,CAACC,SAAS,EAAEC,UAAU,EAAE;EACtDH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGN,MAAM,CAACG,SAAS,CAAC;EAC5B,IAAII,KAAK,GAAGR,SAAS,CAACK,UAAU,CAAC;EACjCE,IAAI,CAACJ,QAAQ,CAACK,KAAK,CAAC;EACpB,OAAOD,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}