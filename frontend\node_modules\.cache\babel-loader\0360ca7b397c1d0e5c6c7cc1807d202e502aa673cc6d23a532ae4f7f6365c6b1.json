{"ast": null, "code": "export const DATE_VALIDATION_PROP_NAMES = ['disablePast', 'disableFuture', 'minDate', 'maxDate', 'shouldDisableDate', 'shouldDisableMonth', 'shouldDisableYear'];\nexport const TIME_VALIDATION_PROP_NAMES = ['disablePast', 'disableFuture', 'minTime', 'maxTime', 'shouldDisableClock', 'shouldDisableTime', 'minutesStep', 'ampm', 'disableIgnoringDatePartForTimeValidation'];\nexport const DATE_TIME_VALIDATION_PROP_NAMES = ['minDateTime', 'maxDateTime'];\nconst VALIDATION_PROP_NAMES = [...DATE_VALIDATION_PROP_NAMES, ...TIME_VALIDATION_PROP_NAMES, ...DATE_TIME_VALIDATION_PROP_NAMES];\n/**\n * Extract the validation props for the props received by a component.\n * Limit the risk of forgetting some of them and reduce the bundle size.\n */\nexport const extractValidationProps = props => VALIDATION_PROP_NAMES.reduce((extractedProps, propName) => {\n  if (props.hasOwnProperty(propName)) {\n    extractedProps[propName] = props[propName];\n  }\n  return extractedProps;\n}, {});", "map": {"version": 3, "names": ["DATE_VALIDATION_PROP_NAMES", "TIME_VALIDATION_PROP_NAMES", "DATE_TIME_VALIDATION_PROP_NAMES", "VALIDATION_PROP_NAMES", "extractValidationProps", "props", "reduce", "extractedProps", "propName", "hasOwnProperty"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/x-date-pickers/internals/utils/validation/extractValidationProps.js"], "sourcesContent": ["export const DATE_VALIDATION_PROP_NAMES = ['disablePast', 'disableFuture', 'minDate', 'maxDate', 'shouldDisableDate', 'shouldDisableMonth', 'shouldDisableYear'];\nexport const TIME_VALIDATION_PROP_NAMES = ['disablePast', 'disableFuture', 'minTime', 'maxTime', 'shouldDisableClock', 'shouldDisableTime', 'minutesStep', 'ampm', 'disableIgnoringDatePartForTimeValidation'];\nexport const DATE_TIME_VALIDATION_PROP_NAMES = ['minDateTime', 'maxDateTime'];\nconst VALIDATION_PROP_NAMES = [...DATE_VALIDATION_PROP_NAMES, ...TIME_VALIDATION_PROP_NAMES, ...DATE_TIME_VALIDATION_PROP_NAMES];\n/**\n * Extract the validation props for the props received by a component.\n * Limit the risk of forgetting some of them and reduce the bundle size.\n */\nexport const extractValidationProps = props => VALIDATION_PROP_NAMES.reduce((extractedProps, propName) => {\n  if (props.hasOwnProperty(propName)) {\n    extractedProps[propName] = props[propName];\n  }\n  return extractedProps;\n}, {});"], "mappings": "AAAA,OAAO,MAAMA,0BAA0B,GAAG,CAAC,aAAa,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,mBAAmB,CAAC;AAChK,OAAO,MAAMC,0BAA0B,GAAG,CAAC,aAAa,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,aAAa,EAAE,MAAM,EAAE,0CAA0C,CAAC;AAC9M,OAAO,MAAMC,+BAA+B,GAAG,CAAC,aAAa,EAAE,aAAa,CAAC;AAC7E,MAAMC,qBAAqB,GAAG,CAAC,GAAGH,0BAA0B,EAAE,GAAGC,0BAA0B,EAAE,GAAGC,+BAA+B,CAAC;AAChI;AACA;AACA;AACA;AACA,OAAO,MAAME,sBAAsB,GAAGC,KAAK,IAAIF,qBAAqB,CAACG,MAAM,CAAC,CAACC,cAAc,EAAEC,QAAQ,KAAK;EACxG,IAAIH,KAAK,CAACI,cAAc,CAACD,QAAQ,CAAC,EAAE;IAClCD,cAAc,CAACC,QAAQ,CAAC,GAAGH,KAAK,CAACG,QAAQ,CAAC;EAC5C;EACA,OAAOD,cAAc;AACvB,CAAC,EAAE,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}