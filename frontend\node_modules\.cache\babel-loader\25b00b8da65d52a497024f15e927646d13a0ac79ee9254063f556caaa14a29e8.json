{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useControlled as useControlled } from '@mui/utils';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useOpenState } from '../useOpenState';\nimport { useLocalizationContext, useUtils } from '../useUtils';\nimport { useValidation } from '../useValidation';\nimport { useValueWithTimezone } from '../useValueWithTimezone';\n\n/**\n * Decide if the new value should be published\n * The published value will be passed to `onChange` if defined.\n */\nconst shouldPublishValue = params => {\n  const {\n    action,\n    hasChanged,\n    dateState,\n    isControlled\n  } = params;\n  const isCurrentValueTheDefaultValue = !isControlled && !dateState.hasBeenModifiedSinceMount;\n\n  // The field is responsible for only calling `onChange` when needed.\n  if (action.name === 'setValueFromField') {\n    return true;\n  }\n  if (action.name === 'setValueFromAction') {\n    // If the component is not controlled, and the value has not been modified since the mount,\n    // Then we want to publish the default value whenever the user pressed the \"Accept\", \"Today\" or \"Clear\" button.\n    if (isCurrentValueTheDefaultValue && ['accept', 'today', 'clear'].includes(action.pickerAction)) {\n      return true;\n    }\n    return hasChanged(dateState.lastPublishedValue);\n  }\n  if (action.name === 'setValueFromView' && action.selectionState !== 'shallow') {\n    // On the first view,\n    // If the value is not controlled, then clicking on any value (including the one equal to `defaultValue`) should call `onChange`\n    if (isCurrentValueTheDefaultValue) {\n      return true;\n    }\n    return hasChanged(dateState.lastPublishedValue);\n  }\n  if (action.name === 'setValueFromShortcut') {\n    // On the first view,\n    // If the value is not controlled, then clicking on any value (including the one equal to `defaultValue`) should call `onChange`\n    if (isCurrentValueTheDefaultValue) {\n      return true;\n    }\n    return hasChanged(dateState.lastPublishedValue);\n  }\n  return false;\n};\n\n/**\n * Decide if the new value should be committed.\n * The committed value will be passed to `onAccept` if defined.\n * It will also be used as a reset target when calling the `cancel` picker action (when clicking on the \"Cancel\" button).\n */\nconst shouldCommitValue = params => {\n  const {\n    action,\n    hasChanged,\n    dateState,\n    isControlled,\n    closeOnSelect\n  } = params;\n  const isCurrentValueTheDefaultValue = !isControlled && !dateState.hasBeenModifiedSinceMount;\n  if (action.name === 'setValueFromAction') {\n    // If the component is not controlled, and the value has not been modified since the mount,\n    // Then we want to commit the default value whenever the user pressed the \"Accept\", \"Today\" or \"Clear\" button.\n    if (isCurrentValueTheDefaultValue && ['accept', 'today', 'clear'].includes(action.pickerAction)) {\n      return true;\n    }\n    return hasChanged(dateState.lastCommittedValue);\n  }\n  if (action.name === 'setValueFromView' && action.selectionState === 'finish' && closeOnSelect) {\n    // On picker where the 1st view is also the last view,\n    // If the value is not controlled, then clicking on any value (including the one equal to `defaultValue`) should call `onAccept`\n    if (isCurrentValueTheDefaultValue) {\n      return true;\n    }\n    return hasChanged(dateState.lastCommittedValue);\n  }\n  if (action.name === 'setValueFromShortcut') {\n    return action.changeImportance === 'accept' && hasChanged(dateState.lastCommittedValue);\n  }\n  return false;\n};\n\n/**\n * Decide if the picker should be closed after the value is updated.\n */\nconst shouldClosePicker = params => {\n  const {\n    action,\n    closeOnSelect\n  } = params;\n  if (action.name === 'setValueFromAction') {\n    return true;\n  }\n  if (action.name === 'setValueFromView') {\n    return action.selectionState === 'finish' && closeOnSelect;\n  }\n  if (action.name === 'setValueFromShortcut') {\n    return action.changeImportance === 'accept';\n  }\n  return false;\n};\n\n/**\n * Manage the value lifecycle of all the pickers.\n */\nexport const usePickerValue = ({\n  props,\n  valueManager,\n  valueType,\n  wrapperVariant,\n  validator\n}) => {\n  const {\n    onAccept,\n    onChange,\n    value: inValue,\n    defaultValue: inDefaultValue,\n    closeOnSelect = wrapperVariant === 'desktop',\n    selectedSections: selectedSectionsProp,\n    onSelectedSectionsChange,\n    timezone: timezoneProp\n  } = props;\n  const {\n    current: defaultValue\n  } = React.useRef(inDefaultValue);\n  const {\n    current: isControlled\n  } = React.useRef(inValue !== undefined);\n\n  /* eslint-disable react-hooks/rules-of-hooks, react-hooks/exhaustive-deps */\n  if (process.env.NODE_ENV !== 'production') {\n    React.useEffect(() => {\n      if (isControlled !== (inValue !== undefined)) {\n        console.error([`MUI: A component is changing the ${isControlled ? '' : 'un'}controlled value of a picker to be ${isControlled ? 'un' : ''}controlled.`, 'Elements should not switch from uncontrolled to controlled (or vice versa).', `Decide between using a controlled or uncontrolled value` + 'for the lifetime of the component.', \"The nature of the state is determined during the first render. It's considered controlled if the value is not `undefined`.\", 'More info: https://fb.me/react-controlled-components'].join('\\n'));\n      }\n    }, [inValue]);\n    React.useEffect(() => {\n      if (!isControlled && defaultValue !== inDefaultValue) {\n        console.error([`MUI: A component is changing the defaultValue of an uncontrolled picker after being initialized. ` + `To suppress this warning opt to use a controlled value.`].join('\\n'));\n      }\n    }, [JSON.stringify(defaultValue)]);\n  }\n  /* eslint-enable react-hooks/rules-of-hooks, react-hooks/exhaustive-deps */\n\n  const utils = useUtils();\n  const adapter = useLocalizationContext();\n  const [selectedSections, setSelectedSections] = useControlled({\n    controlled: selectedSectionsProp,\n    default: null,\n    name: 'usePickerValue',\n    state: 'selectedSections'\n  });\n  const {\n    isOpen,\n    setIsOpen\n  } = useOpenState(props);\n  const [dateState, setDateState] = React.useState(() => {\n    let initialValue;\n    if (inValue !== undefined) {\n      initialValue = inValue;\n    } else if (defaultValue !== undefined) {\n      initialValue = defaultValue;\n    } else {\n      initialValue = valueManager.emptyValue;\n    }\n    return {\n      draft: initialValue,\n      lastPublishedValue: initialValue,\n      lastCommittedValue: initialValue,\n      lastControlledValue: inValue,\n      hasBeenModifiedSinceMount: false\n    };\n  });\n  const {\n    timezone,\n    handleValueChange\n  } = useValueWithTimezone({\n    timezone: timezoneProp,\n    value: inValue,\n    defaultValue,\n    onChange,\n    valueManager\n  });\n  useValidation(_extends({}, props, {\n    value: dateState.draft,\n    timezone\n  }), validator, valueManager.isSameError, valueManager.defaultErrorState);\n  const updateDate = useEventCallback(action => {\n    const updaterParams = {\n      action,\n      dateState,\n      hasChanged: comparison => !valueManager.areValuesEqual(utils, action.value, comparison),\n      isControlled,\n      closeOnSelect\n    };\n    const shouldPublish = shouldPublishValue(updaterParams);\n    const shouldCommit = shouldCommitValue(updaterParams);\n    const shouldClose = shouldClosePicker(updaterParams);\n    setDateState(prev => _extends({}, prev, {\n      draft: action.value,\n      lastPublishedValue: shouldPublish ? action.value : prev.lastPublishedValue,\n      lastCommittedValue: shouldCommit ? action.value : prev.lastCommittedValue,\n      hasBeenModifiedSinceMount: true\n    }));\n    if (shouldPublish) {\n      const validationError = action.name === 'setValueFromField' ? action.context.validationError : validator({\n        adapter,\n        value: action.value,\n        props: _extends({}, props, {\n          value: action.value,\n          timezone\n        })\n      });\n      const context = {\n        validationError\n      };\n\n      // TODO v7: Remove 2nd condition\n      if (action.name === 'setValueFromShortcut' && action.shortcut != null) {\n        context.shortcut = action.shortcut;\n      }\n      handleValueChange(action.value, context);\n    }\n    if (shouldCommit && onAccept) {\n      onAccept(action.value);\n    }\n    if (shouldClose) {\n      setIsOpen(false);\n    }\n  });\n  if (inValue !== undefined && (dateState.lastControlledValue === undefined || !valueManager.areValuesEqual(utils, dateState.lastControlledValue, inValue))) {\n    const isUpdateComingFromPicker = valueManager.areValuesEqual(utils, dateState.draft, inValue);\n    setDateState(prev => _extends({}, prev, {\n      lastControlledValue: inValue\n    }, isUpdateComingFromPicker ? {} : {\n      lastCommittedValue: inValue,\n      lastPublishedValue: inValue,\n      draft: inValue,\n      hasBeenModifiedSinceMount: true\n    }));\n  }\n  const handleClear = useEventCallback(() => {\n    updateDate({\n      value: valueManager.emptyValue,\n      name: 'setValueFromAction',\n      pickerAction: 'clear'\n    });\n  });\n  const handleAccept = useEventCallback(() => {\n    updateDate({\n      value: dateState.lastPublishedValue,\n      name: 'setValueFromAction',\n      pickerAction: 'accept'\n    });\n  });\n  const handleDismiss = useEventCallback(() => {\n    updateDate({\n      value: dateState.lastPublishedValue,\n      name: 'setValueFromAction',\n      pickerAction: 'dismiss'\n    });\n  });\n  const handleCancel = useEventCallback(() => {\n    updateDate({\n      value: dateState.lastCommittedValue,\n      name: 'setValueFromAction',\n      pickerAction: 'cancel'\n    });\n  });\n  const handleSetToday = useEventCallback(() => {\n    updateDate({\n      value: valueManager.getTodayValue(utils, timezone, valueType),\n      name: 'setValueFromAction',\n      pickerAction: 'today'\n    });\n  });\n  const handleOpen = useEventCallback(() => setIsOpen(true));\n  const handleClose = useEventCallback(() => setIsOpen(false));\n  const handleChange = useEventCallback((newValue, selectionState = 'partial') => updateDate({\n    name: 'setValueFromView',\n    value: newValue,\n    selectionState\n  }));\n\n  // TODO v7: Make changeImportance and label mandatory.\n  const handleSelectShortcut = useEventCallback((newValue, changeImportance, shortcut) => updateDate({\n    name: 'setValueFromShortcut',\n    value: newValue,\n    changeImportance: changeImportance != null ? changeImportance : 'accept',\n    shortcut\n  }));\n  const handleChangeFromField = useEventCallback((newValue, context) => updateDate({\n    name: 'setValueFromField',\n    value: newValue,\n    context\n  }));\n  const handleFieldSelectedSectionsChange = useEventCallback(newSelectedSections => {\n    setSelectedSections(newSelectedSections);\n    onSelectedSectionsChange == null || onSelectedSectionsChange(newSelectedSections);\n  });\n  const actions = {\n    onClear: handleClear,\n    onAccept: handleAccept,\n    onDismiss: handleDismiss,\n    onCancel: handleCancel,\n    onSetToday: handleSetToday,\n    onOpen: handleOpen,\n    onClose: handleClose\n  };\n  const fieldResponse = {\n    value: dateState.draft,\n    onChange: handleChangeFromField,\n    selectedSections,\n    onSelectedSectionsChange: handleFieldSelectedSectionsChange\n  };\n  const viewValue = React.useMemo(() => valueManager.cleanValue(utils, dateState.draft), [utils, valueManager, dateState.draft]);\n  const viewResponse = {\n    value: viewValue,\n    onChange: handleChange,\n    onClose: handleClose,\n    open: isOpen,\n    onSelectedSectionsChange: handleFieldSelectedSectionsChange\n  };\n  const isValid = testedValue => {\n    const error = validator({\n      adapter,\n      value: testedValue,\n      props: _extends({}, props, {\n        value: testedValue,\n        timezone\n      })\n    });\n    return !valueManager.hasError(error);\n  };\n  const layoutResponse = _extends({}, actions, {\n    value: viewValue,\n    onChange: handleChange,\n    onSelectShortcut: handleSelectShortcut,\n    isValid\n  });\n  return {\n    open: isOpen,\n    fieldProps: fieldResponse,\n    viewProps: viewResponse,\n    layoutProps: layoutResponse,\n    actions\n  };\n};", "map": {"version": 3, "names": ["_extends", "React", "unstable_useControlled", "useControlled", "useEventCallback", "useOpenState", "useLocalizationContext", "useUtils", "useValidation", "useValueWithTimezone", "shouldPublishValue", "params", "action", "has<PERSON><PERSON>ed", "dateState", "isControlled", "isCurrentValueTheDefaultValue", "hasBeenModifiedSinceMount", "name", "includes", "pickerAction", "lastPublishedValue", "selectionState", "shouldCommitValue", "closeOnSelect", "lastCommittedValue", "changeImportance", "shouldClosePicker", "usePickerValue", "props", "valueManager", "valueType", "wrapperVariant", "validator", "onAccept", "onChange", "value", "inValue", "defaultValue", "inDefaultValue", "selectedSections", "selectedSectionsProp", "onSelectedSectionsChange", "timezone", "timezoneProp", "current", "useRef", "undefined", "process", "env", "NODE_ENV", "useEffect", "console", "error", "join", "JSON", "stringify", "utils", "adapter", "setSelectedSections", "controlled", "default", "state", "isOpen", "setIsOpen", "setDateState", "useState", "initialValue", "emptyValue", "draft", "lastControlledValue", "handleValueChange", "isSameError", "defaultErrorState", "updateDate", "updaterParams", "comparison", "areValuesEqual", "shouldPublish", "shouldCommit", "shouldClose", "prev", "validationError", "context", "shortcut", "isUpdateComingFromPicker", "handleClear", "handleAccept", "handle<PERSON><PERSON><PERSON>", "handleCancel", "handleSetToday", "getTodayValue", "handleOpen", "handleClose", "handleChange", "newValue", "handleSelectShortcut", "handleChangeFromField", "handleFieldSelectedSectionsChange", "newSelectedSections", "actions", "onClear", "on<PERSON><PERSON><PERSON>", "onCancel", "onSetToday", "onOpen", "onClose", "fieldResponse", "viewValue", "useMemo", "cleanValue", "viewResponse", "open", "<PERSON><PERSON><PERSON><PERSON>", "testedValue", "<PERSON><PERSON><PERSON><PERSON>", "layoutResponse", "onSelectShortcut", "fieldProps", "viewProps", "layoutProps"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/x-date-pickers/internals/hooks/usePicker/usePickerValue.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useControlled as useControlled } from '@mui/utils';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useOpenState } from '../useOpenState';\nimport { useLocalizationContext, useUtils } from '../useUtils';\nimport { useValidation } from '../useValidation';\nimport { useValueWithTimezone } from '../useValueWithTimezone';\n\n/**\n * Decide if the new value should be published\n * The published value will be passed to `onChange` if defined.\n */\nconst shouldPublishValue = params => {\n  const {\n    action,\n    hasChanged,\n    dateState,\n    isControlled\n  } = params;\n  const isCurrentValueTheDefaultValue = !isControlled && !dateState.hasBeenModifiedSinceMount;\n\n  // The field is responsible for only calling `onChange` when needed.\n  if (action.name === 'setValueFromField') {\n    return true;\n  }\n  if (action.name === 'setValueFromAction') {\n    // If the component is not controlled, and the value has not been modified since the mount,\n    // Then we want to publish the default value whenever the user pressed the \"Accept\", \"Today\" or \"Clear\" button.\n    if (isCurrentValueTheDefaultValue && ['accept', 'today', 'clear'].includes(action.pickerAction)) {\n      return true;\n    }\n    return hasChanged(dateState.lastPublishedValue);\n  }\n  if (action.name === 'setValueFromView' && action.selectionState !== 'shallow') {\n    // On the first view,\n    // If the value is not controlled, then clicking on any value (including the one equal to `defaultValue`) should call `onChange`\n    if (isCurrentValueTheDefaultValue) {\n      return true;\n    }\n    return hasChanged(dateState.lastPublishedValue);\n  }\n  if (action.name === 'setValueFromShortcut') {\n    // On the first view,\n    // If the value is not controlled, then clicking on any value (including the one equal to `defaultValue`) should call `onChange`\n    if (isCurrentValueTheDefaultValue) {\n      return true;\n    }\n    return hasChanged(dateState.lastPublishedValue);\n  }\n  return false;\n};\n\n/**\n * Decide if the new value should be committed.\n * The committed value will be passed to `onAccept` if defined.\n * It will also be used as a reset target when calling the `cancel` picker action (when clicking on the \"Cancel\" button).\n */\nconst shouldCommitValue = params => {\n  const {\n    action,\n    hasChanged,\n    dateState,\n    isControlled,\n    closeOnSelect\n  } = params;\n  const isCurrentValueTheDefaultValue = !isControlled && !dateState.hasBeenModifiedSinceMount;\n  if (action.name === 'setValueFromAction') {\n    // If the component is not controlled, and the value has not been modified since the mount,\n    // Then we want to commit the default value whenever the user pressed the \"Accept\", \"Today\" or \"Clear\" button.\n    if (isCurrentValueTheDefaultValue && ['accept', 'today', 'clear'].includes(action.pickerAction)) {\n      return true;\n    }\n    return hasChanged(dateState.lastCommittedValue);\n  }\n  if (action.name === 'setValueFromView' && action.selectionState === 'finish' && closeOnSelect) {\n    // On picker where the 1st view is also the last view,\n    // If the value is not controlled, then clicking on any value (including the one equal to `defaultValue`) should call `onAccept`\n    if (isCurrentValueTheDefaultValue) {\n      return true;\n    }\n    return hasChanged(dateState.lastCommittedValue);\n  }\n  if (action.name === 'setValueFromShortcut') {\n    return action.changeImportance === 'accept' && hasChanged(dateState.lastCommittedValue);\n  }\n  return false;\n};\n\n/**\n * Decide if the picker should be closed after the value is updated.\n */\nconst shouldClosePicker = params => {\n  const {\n    action,\n    closeOnSelect\n  } = params;\n  if (action.name === 'setValueFromAction') {\n    return true;\n  }\n  if (action.name === 'setValueFromView') {\n    return action.selectionState === 'finish' && closeOnSelect;\n  }\n  if (action.name === 'setValueFromShortcut') {\n    return action.changeImportance === 'accept';\n  }\n  return false;\n};\n\n/**\n * Manage the value lifecycle of all the pickers.\n */\nexport const usePickerValue = ({\n  props,\n  valueManager,\n  valueType,\n  wrapperVariant,\n  validator\n}) => {\n  const {\n    onAccept,\n    onChange,\n    value: inValue,\n    defaultValue: inDefaultValue,\n    closeOnSelect = wrapperVariant === 'desktop',\n    selectedSections: selectedSectionsProp,\n    onSelectedSectionsChange,\n    timezone: timezoneProp\n  } = props;\n  const {\n    current: defaultValue\n  } = React.useRef(inDefaultValue);\n  const {\n    current: isControlled\n  } = React.useRef(inValue !== undefined);\n\n  /* eslint-disable react-hooks/rules-of-hooks, react-hooks/exhaustive-deps */\n  if (process.env.NODE_ENV !== 'production') {\n    React.useEffect(() => {\n      if (isControlled !== (inValue !== undefined)) {\n        console.error([`MUI: A component is changing the ${isControlled ? '' : 'un'}controlled value of a picker to be ${isControlled ? 'un' : ''}controlled.`, 'Elements should not switch from uncontrolled to controlled (or vice versa).', `Decide between using a controlled or uncontrolled value` + 'for the lifetime of the component.', \"The nature of the state is determined during the first render. It's considered controlled if the value is not `undefined`.\", 'More info: https://fb.me/react-controlled-components'].join('\\n'));\n      }\n    }, [inValue]);\n    React.useEffect(() => {\n      if (!isControlled && defaultValue !== inDefaultValue) {\n        console.error([`MUI: A component is changing the defaultValue of an uncontrolled picker after being initialized. ` + `To suppress this warning opt to use a controlled value.`].join('\\n'));\n      }\n    }, [JSON.stringify(defaultValue)]);\n  }\n  /* eslint-enable react-hooks/rules-of-hooks, react-hooks/exhaustive-deps */\n\n  const utils = useUtils();\n  const adapter = useLocalizationContext();\n  const [selectedSections, setSelectedSections] = useControlled({\n    controlled: selectedSectionsProp,\n    default: null,\n    name: 'usePickerValue',\n    state: 'selectedSections'\n  });\n  const {\n    isOpen,\n    setIsOpen\n  } = useOpenState(props);\n  const [dateState, setDateState] = React.useState(() => {\n    let initialValue;\n    if (inValue !== undefined) {\n      initialValue = inValue;\n    } else if (defaultValue !== undefined) {\n      initialValue = defaultValue;\n    } else {\n      initialValue = valueManager.emptyValue;\n    }\n    return {\n      draft: initialValue,\n      lastPublishedValue: initialValue,\n      lastCommittedValue: initialValue,\n      lastControlledValue: inValue,\n      hasBeenModifiedSinceMount: false\n    };\n  });\n  const {\n    timezone,\n    handleValueChange\n  } = useValueWithTimezone({\n    timezone: timezoneProp,\n    value: inValue,\n    defaultValue,\n    onChange,\n    valueManager\n  });\n  useValidation(_extends({}, props, {\n    value: dateState.draft,\n    timezone\n  }), validator, valueManager.isSameError, valueManager.defaultErrorState);\n  const updateDate = useEventCallback(action => {\n    const updaterParams = {\n      action,\n      dateState,\n      hasChanged: comparison => !valueManager.areValuesEqual(utils, action.value, comparison),\n      isControlled,\n      closeOnSelect\n    };\n    const shouldPublish = shouldPublishValue(updaterParams);\n    const shouldCommit = shouldCommitValue(updaterParams);\n    const shouldClose = shouldClosePicker(updaterParams);\n    setDateState(prev => _extends({}, prev, {\n      draft: action.value,\n      lastPublishedValue: shouldPublish ? action.value : prev.lastPublishedValue,\n      lastCommittedValue: shouldCommit ? action.value : prev.lastCommittedValue,\n      hasBeenModifiedSinceMount: true\n    }));\n    if (shouldPublish) {\n      const validationError = action.name === 'setValueFromField' ? action.context.validationError : validator({\n        adapter,\n        value: action.value,\n        props: _extends({}, props, {\n          value: action.value,\n          timezone\n        })\n      });\n      const context = {\n        validationError\n      };\n\n      // TODO v7: Remove 2nd condition\n      if (action.name === 'setValueFromShortcut' && action.shortcut != null) {\n        context.shortcut = action.shortcut;\n      }\n      handleValueChange(action.value, context);\n    }\n    if (shouldCommit && onAccept) {\n      onAccept(action.value);\n    }\n    if (shouldClose) {\n      setIsOpen(false);\n    }\n  });\n  if (inValue !== undefined && (dateState.lastControlledValue === undefined || !valueManager.areValuesEqual(utils, dateState.lastControlledValue, inValue))) {\n    const isUpdateComingFromPicker = valueManager.areValuesEqual(utils, dateState.draft, inValue);\n    setDateState(prev => _extends({}, prev, {\n      lastControlledValue: inValue\n    }, isUpdateComingFromPicker ? {} : {\n      lastCommittedValue: inValue,\n      lastPublishedValue: inValue,\n      draft: inValue,\n      hasBeenModifiedSinceMount: true\n    }));\n  }\n  const handleClear = useEventCallback(() => {\n    updateDate({\n      value: valueManager.emptyValue,\n      name: 'setValueFromAction',\n      pickerAction: 'clear'\n    });\n  });\n  const handleAccept = useEventCallback(() => {\n    updateDate({\n      value: dateState.lastPublishedValue,\n      name: 'setValueFromAction',\n      pickerAction: 'accept'\n    });\n  });\n  const handleDismiss = useEventCallback(() => {\n    updateDate({\n      value: dateState.lastPublishedValue,\n      name: 'setValueFromAction',\n      pickerAction: 'dismiss'\n    });\n  });\n  const handleCancel = useEventCallback(() => {\n    updateDate({\n      value: dateState.lastCommittedValue,\n      name: 'setValueFromAction',\n      pickerAction: 'cancel'\n    });\n  });\n  const handleSetToday = useEventCallback(() => {\n    updateDate({\n      value: valueManager.getTodayValue(utils, timezone, valueType),\n      name: 'setValueFromAction',\n      pickerAction: 'today'\n    });\n  });\n  const handleOpen = useEventCallback(() => setIsOpen(true));\n  const handleClose = useEventCallback(() => setIsOpen(false));\n  const handleChange = useEventCallback((newValue, selectionState = 'partial') => updateDate({\n    name: 'setValueFromView',\n    value: newValue,\n    selectionState\n  }));\n\n  // TODO v7: Make changeImportance and label mandatory.\n  const handleSelectShortcut = useEventCallback((newValue, changeImportance, shortcut) => updateDate({\n    name: 'setValueFromShortcut',\n    value: newValue,\n    changeImportance: changeImportance != null ? changeImportance : 'accept',\n    shortcut\n  }));\n  const handleChangeFromField = useEventCallback((newValue, context) => updateDate({\n    name: 'setValueFromField',\n    value: newValue,\n    context\n  }));\n  const handleFieldSelectedSectionsChange = useEventCallback(newSelectedSections => {\n    setSelectedSections(newSelectedSections);\n    onSelectedSectionsChange == null || onSelectedSectionsChange(newSelectedSections);\n  });\n  const actions = {\n    onClear: handleClear,\n    onAccept: handleAccept,\n    onDismiss: handleDismiss,\n    onCancel: handleCancel,\n    onSetToday: handleSetToday,\n    onOpen: handleOpen,\n    onClose: handleClose\n  };\n  const fieldResponse = {\n    value: dateState.draft,\n    onChange: handleChangeFromField,\n    selectedSections,\n    onSelectedSectionsChange: handleFieldSelectedSectionsChange\n  };\n  const viewValue = React.useMemo(() => valueManager.cleanValue(utils, dateState.draft), [utils, valueManager, dateState.draft]);\n  const viewResponse = {\n    value: viewValue,\n    onChange: handleChange,\n    onClose: handleClose,\n    open: isOpen,\n    onSelectedSectionsChange: handleFieldSelectedSectionsChange\n  };\n  const isValid = testedValue => {\n    const error = validator({\n      adapter,\n      value: testedValue,\n      props: _extends({}, props, {\n        value: testedValue,\n        timezone\n      })\n    });\n    return !valueManager.hasError(error);\n  };\n  const layoutResponse = _extends({}, actions, {\n    value: viewValue,\n    onChange: handleChange,\n    onSelectShortcut: handleSelectShortcut,\n    isValid\n  });\n  return {\n    open: isOpen,\n    fieldProps: fieldResponse,\n    viewProps: viewResponse,\n    layoutProps: layoutResponse,\n    actions\n  };\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,sBAAsB,IAAIC,aAAa,QAAQ,YAAY;AACpE,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,sBAAsB,EAAEC,QAAQ,QAAQ,aAAa;AAC9D,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,oBAAoB,QAAQ,yBAAyB;;AAE9D;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,GAAGC,MAAM,IAAI;EACnC,MAAM;IACJC,MAAM;IACNC,UAAU;IACVC,SAAS;IACTC;EACF,CAAC,GAAGJ,MAAM;EACV,MAAMK,6BAA6B,GAAG,CAACD,YAAY,IAAI,CAACD,SAAS,CAACG,yBAAyB;;EAE3F;EACA,IAAIL,MAAM,CAACM,IAAI,KAAK,mBAAmB,EAAE;IACvC,OAAO,IAAI;EACb;EACA,IAAIN,MAAM,CAACM,IAAI,KAAK,oBAAoB,EAAE;IACxC;IACA;IACA,IAAIF,6BAA6B,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAACG,QAAQ,CAACP,MAAM,CAACQ,YAAY,CAAC,EAAE;MAC/F,OAAO,IAAI;IACb;IACA,OAAOP,UAAU,CAACC,SAAS,CAACO,kBAAkB,CAAC;EACjD;EACA,IAAIT,MAAM,CAACM,IAAI,KAAK,kBAAkB,IAAIN,MAAM,CAACU,cAAc,KAAK,SAAS,EAAE;IAC7E;IACA;IACA,IAAIN,6BAA6B,EAAE;MACjC,OAAO,IAAI;IACb;IACA,OAAOH,UAAU,CAACC,SAAS,CAACO,kBAAkB,CAAC;EACjD;EACA,IAAIT,MAAM,CAACM,IAAI,KAAK,sBAAsB,EAAE;IAC1C;IACA;IACA,IAAIF,6BAA6B,EAAE;MACjC,OAAO,IAAI;IACb;IACA,OAAOH,UAAU,CAACC,SAAS,CAACO,kBAAkB,CAAC;EACjD;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAME,iBAAiB,GAAGZ,MAAM,IAAI;EAClC,MAAM;IACJC,MAAM;IACNC,UAAU;IACVC,SAAS;IACTC,YAAY;IACZS;EACF,CAAC,GAAGb,MAAM;EACV,MAAMK,6BAA6B,GAAG,CAACD,YAAY,IAAI,CAACD,SAAS,CAACG,yBAAyB;EAC3F,IAAIL,MAAM,CAACM,IAAI,KAAK,oBAAoB,EAAE;IACxC;IACA;IACA,IAAIF,6BAA6B,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAACG,QAAQ,CAACP,MAAM,CAACQ,YAAY,CAAC,EAAE;MAC/F,OAAO,IAAI;IACb;IACA,OAAOP,UAAU,CAACC,SAAS,CAACW,kBAAkB,CAAC;EACjD;EACA,IAAIb,MAAM,CAACM,IAAI,KAAK,kBAAkB,IAAIN,MAAM,CAACU,cAAc,KAAK,QAAQ,IAAIE,aAAa,EAAE;IAC7F;IACA;IACA,IAAIR,6BAA6B,EAAE;MACjC,OAAO,IAAI;IACb;IACA,OAAOH,UAAU,CAACC,SAAS,CAACW,kBAAkB,CAAC;EACjD;EACA,IAAIb,MAAM,CAACM,IAAI,KAAK,sBAAsB,EAAE;IAC1C,OAAON,MAAM,CAACc,gBAAgB,KAAK,QAAQ,IAAIb,UAAU,CAACC,SAAS,CAACW,kBAAkB,CAAC;EACzF;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA,MAAME,iBAAiB,GAAGhB,MAAM,IAAI;EAClC,MAAM;IACJC,MAAM;IACNY;EACF,CAAC,GAAGb,MAAM;EACV,IAAIC,MAAM,CAACM,IAAI,KAAK,oBAAoB,EAAE;IACxC,OAAO,IAAI;EACb;EACA,IAAIN,MAAM,CAACM,IAAI,KAAK,kBAAkB,EAAE;IACtC,OAAON,MAAM,CAACU,cAAc,KAAK,QAAQ,IAAIE,aAAa;EAC5D;EACA,IAAIZ,MAAM,CAACM,IAAI,KAAK,sBAAsB,EAAE;IAC1C,OAAON,MAAM,CAACc,gBAAgB,KAAK,QAAQ;EAC7C;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAME,cAAc,GAAGA,CAAC;EAC7BC,KAAK;EACLC,YAAY;EACZC,SAAS;EACTC,cAAc;EACdC;AACF,CAAC,KAAK;EACJ,MAAM;IACJC,QAAQ;IACRC,QAAQ;IACRC,KAAK,EAAEC,OAAO;IACdC,YAAY,EAAEC,cAAc;IAC5Bf,aAAa,GAAGQ,cAAc,KAAK,SAAS;IAC5CQ,gBAAgB,EAAEC,oBAAoB;IACtCC,wBAAwB;IACxBC,QAAQ,EAAEC;EACZ,CAAC,GAAGf,KAAK;EACT,MAAM;IACJgB,OAAO,EAAEP;EACX,CAAC,GAAGrC,KAAK,CAAC6C,MAAM,CAACP,cAAc,CAAC;EAChC,MAAM;IACJM,OAAO,EAAE9B;EACX,CAAC,GAAGd,KAAK,CAAC6C,MAAM,CAACT,OAAO,KAAKU,SAAS,CAAC;;EAEvC;EACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCjD,KAAK,CAACkD,SAAS,CAAC,MAAM;MACpB,IAAIpC,YAAY,MAAMsB,OAAO,KAAKU,SAAS,CAAC,EAAE;QAC5CK,OAAO,CAACC,KAAK,CAAC,CAAC,oCAAoCtC,YAAY,GAAG,EAAE,GAAG,IAAI,sCAAsCA,YAAY,GAAG,IAAI,GAAG,EAAE,aAAa,EAAE,6EAA6E,EAAE,yDAAyD,GAAG,oCAAoC,EAAE,4HAA4H,EAAE,sDAAsD,CAAC,CAACuC,IAAI,CAAC,IAAI,CAAC,CAAC;MAC5gB;IACF,CAAC,EAAE,CAACjB,OAAO,CAAC,CAAC;IACbpC,KAAK,CAACkD,SAAS,CAAC,MAAM;MACpB,IAAI,CAACpC,YAAY,IAAIuB,YAAY,KAAKC,cAAc,EAAE;QACpDa,OAAO,CAACC,KAAK,CAAC,CAAC,mGAAmG,GAAG,yDAAyD,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAC7L;IACF,CAAC,EAAE,CAACC,IAAI,CAACC,SAAS,CAAClB,YAAY,CAAC,CAAC,CAAC;EACpC;EACA;;EAEA,MAAMmB,KAAK,GAAGlD,QAAQ,CAAC,CAAC;EACxB,MAAMmD,OAAO,GAAGpD,sBAAsB,CAAC,CAAC;EACxC,MAAM,CAACkC,gBAAgB,EAAEmB,mBAAmB,CAAC,GAAGxD,aAAa,CAAC;IAC5DyD,UAAU,EAAEnB,oBAAoB;IAChCoB,OAAO,EAAE,IAAI;IACb3C,IAAI,EAAE,gBAAgB;IACtB4C,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM;IACJC,MAAM;IACNC;EACF,CAAC,GAAG3D,YAAY,CAACwB,KAAK,CAAC;EACvB,MAAM,CAACf,SAAS,EAAEmD,YAAY,CAAC,GAAGhE,KAAK,CAACiE,QAAQ,CAAC,MAAM;IACrD,IAAIC,YAAY;IAChB,IAAI9B,OAAO,KAAKU,SAAS,EAAE;MACzBoB,YAAY,GAAG9B,OAAO;IACxB,CAAC,MAAM,IAAIC,YAAY,KAAKS,SAAS,EAAE;MACrCoB,YAAY,GAAG7B,YAAY;IAC7B,CAAC,MAAM;MACL6B,YAAY,GAAGrC,YAAY,CAACsC,UAAU;IACxC;IACA,OAAO;MACLC,KAAK,EAAEF,YAAY;MACnB9C,kBAAkB,EAAE8C,YAAY;MAChC1C,kBAAkB,EAAE0C,YAAY;MAChCG,mBAAmB,EAAEjC,OAAO;MAC5BpB,yBAAyB,EAAE;IAC7B,CAAC;EACH,CAAC,CAAC;EACF,MAAM;IACJ0B,QAAQ;IACR4B;EACF,CAAC,GAAG9D,oBAAoB,CAAC;IACvBkC,QAAQ,EAAEC,YAAY;IACtBR,KAAK,EAAEC,OAAO;IACdC,YAAY;IACZH,QAAQ;IACRL;EACF,CAAC,CAAC;EACFtB,aAAa,CAACR,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,EAAE;IAChCO,KAAK,EAAEtB,SAAS,CAACuD,KAAK;IACtB1B;EACF,CAAC,CAAC,EAAEV,SAAS,EAAEH,YAAY,CAAC0C,WAAW,EAAE1C,YAAY,CAAC2C,iBAAiB,CAAC;EACxE,MAAMC,UAAU,GAAGtE,gBAAgB,CAACQ,MAAM,IAAI;IAC5C,MAAM+D,aAAa,GAAG;MACpB/D,MAAM;MACNE,SAAS;MACTD,UAAU,EAAE+D,UAAU,IAAI,CAAC9C,YAAY,CAAC+C,cAAc,CAACpB,KAAK,EAAE7C,MAAM,CAACwB,KAAK,EAAEwC,UAAU,CAAC;MACvF7D,YAAY;MACZS;IACF,CAAC;IACD,MAAMsD,aAAa,GAAGpE,kBAAkB,CAACiE,aAAa,CAAC;IACvD,MAAMI,YAAY,GAAGxD,iBAAiB,CAACoD,aAAa,CAAC;IACrD,MAAMK,WAAW,GAAGrD,iBAAiB,CAACgD,aAAa,CAAC;IACpDV,YAAY,CAACgB,IAAI,IAAIjF,QAAQ,CAAC,CAAC,CAAC,EAAEiF,IAAI,EAAE;MACtCZ,KAAK,EAAEzD,MAAM,CAACwB,KAAK;MACnBf,kBAAkB,EAAEyD,aAAa,GAAGlE,MAAM,CAACwB,KAAK,GAAG6C,IAAI,CAAC5D,kBAAkB;MAC1EI,kBAAkB,EAAEsD,YAAY,GAAGnE,MAAM,CAACwB,KAAK,GAAG6C,IAAI,CAACxD,kBAAkB;MACzER,yBAAyB,EAAE;IAC7B,CAAC,CAAC,CAAC;IACH,IAAI6D,aAAa,EAAE;MACjB,MAAMI,eAAe,GAAGtE,MAAM,CAACM,IAAI,KAAK,mBAAmB,GAAGN,MAAM,CAACuE,OAAO,CAACD,eAAe,GAAGjD,SAAS,CAAC;QACvGyB,OAAO;QACPtB,KAAK,EAAExB,MAAM,CAACwB,KAAK;QACnBP,KAAK,EAAE7B,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,EAAE;UACzBO,KAAK,EAAExB,MAAM,CAACwB,KAAK;UACnBO;QACF,CAAC;MACH,CAAC,CAAC;MACF,MAAMwC,OAAO,GAAG;QACdD;MACF,CAAC;;MAED;MACA,IAAItE,MAAM,CAACM,IAAI,KAAK,sBAAsB,IAAIN,MAAM,CAACwE,QAAQ,IAAI,IAAI,EAAE;QACrED,OAAO,CAACC,QAAQ,GAAGxE,MAAM,CAACwE,QAAQ;MACpC;MACAb,iBAAiB,CAAC3D,MAAM,CAACwB,KAAK,EAAE+C,OAAO,CAAC;IAC1C;IACA,IAAIJ,YAAY,IAAI7C,QAAQ,EAAE;MAC5BA,QAAQ,CAACtB,MAAM,CAACwB,KAAK,CAAC;IACxB;IACA,IAAI4C,WAAW,EAAE;MACfhB,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC,CAAC;EACF,IAAI3B,OAAO,KAAKU,SAAS,KAAKjC,SAAS,CAACwD,mBAAmB,KAAKvB,SAAS,IAAI,CAACjB,YAAY,CAAC+C,cAAc,CAACpB,KAAK,EAAE3C,SAAS,CAACwD,mBAAmB,EAAEjC,OAAO,CAAC,CAAC,EAAE;IACzJ,MAAMgD,wBAAwB,GAAGvD,YAAY,CAAC+C,cAAc,CAACpB,KAAK,EAAE3C,SAAS,CAACuD,KAAK,EAAEhC,OAAO,CAAC;IAC7F4B,YAAY,CAACgB,IAAI,IAAIjF,QAAQ,CAAC,CAAC,CAAC,EAAEiF,IAAI,EAAE;MACtCX,mBAAmB,EAAEjC;IACvB,CAAC,EAAEgD,wBAAwB,GAAG,CAAC,CAAC,GAAG;MACjC5D,kBAAkB,EAAEY,OAAO;MAC3BhB,kBAAkB,EAAEgB,OAAO;MAC3BgC,KAAK,EAAEhC,OAAO;MACdpB,yBAAyB,EAAE;IAC7B,CAAC,CAAC,CAAC;EACL;EACA,MAAMqE,WAAW,GAAGlF,gBAAgB,CAAC,MAAM;IACzCsE,UAAU,CAAC;MACTtC,KAAK,EAAEN,YAAY,CAACsC,UAAU;MAC9BlD,IAAI,EAAE,oBAAoB;MAC1BE,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMmE,YAAY,GAAGnF,gBAAgB,CAAC,MAAM;IAC1CsE,UAAU,CAAC;MACTtC,KAAK,EAAEtB,SAAS,CAACO,kBAAkB;MACnCH,IAAI,EAAE,oBAAoB;MAC1BE,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMoE,aAAa,GAAGpF,gBAAgB,CAAC,MAAM;IAC3CsE,UAAU,CAAC;MACTtC,KAAK,EAAEtB,SAAS,CAACO,kBAAkB;MACnCH,IAAI,EAAE,oBAAoB;MAC1BE,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMqE,YAAY,GAAGrF,gBAAgB,CAAC,MAAM;IAC1CsE,UAAU,CAAC;MACTtC,KAAK,EAAEtB,SAAS,CAACW,kBAAkB;MACnCP,IAAI,EAAE,oBAAoB;MAC1BE,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMsE,cAAc,GAAGtF,gBAAgB,CAAC,MAAM;IAC5CsE,UAAU,CAAC;MACTtC,KAAK,EAAEN,YAAY,CAAC6D,aAAa,CAAClC,KAAK,EAAEd,QAAQ,EAAEZ,SAAS,CAAC;MAC7Db,IAAI,EAAE,oBAAoB;MAC1BE,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMwE,UAAU,GAAGxF,gBAAgB,CAAC,MAAM4D,SAAS,CAAC,IAAI,CAAC,CAAC;EAC1D,MAAM6B,WAAW,GAAGzF,gBAAgB,CAAC,MAAM4D,SAAS,CAAC,KAAK,CAAC,CAAC;EAC5D,MAAM8B,YAAY,GAAG1F,gBAAgB,CAAC,CAAC2F,QAAQ,EAAEzE,cAAc,GAAG,SAAS,KAAKoD,UAAU,CAAC;IACzFxD,IAAI,EAAE,kBAAkB;IACxBkB,KAAK,EAAE2D,QAAQ;IACfzE;EACF,CAAC,CAAC,CAAC;;EAEH;EACA,MAAM0E,oBAAoB,GAAG5F,gBAAgB,CAAC,CAAC2F,QAAQ,EAAErE,gBAAgB,EAAE0D,QAAQ,KAAKV,UAAU,CAAC;IACjGxD,IAAI,EAAE,sBAAsB;IAC5BkB,KAAK,EAAE2D,QAAQ;IACfrE,gBAAgB,EAAEA,gBAAgB,IAAI,IAAI,GAAGA,gBAAgB,GAAG,QAAQ;IACxE0D;EACF,CAAC,CAAC,CAAC;EACH,MAAMa,qBAAqB,GAAG7F,gBAAgB,CAAC,CAAC2F,QAAQ,EAAEZ,OAAO,KAAKT,UAAU,CAAC;IAC/ExD,IAAI,EAAE,mBAAmB;IACzBkB,KAAK,EAAE2D,QAAQ;IACfZ;EACF,CAAC,CAAC,CAAC;EACH,MAAMe,iCAAiC,GAAG9F,gBAAgB,CAAC+F,mBAAmB,IAAI;IAChFxC,mBAAmB,CAACwC,mBAAmB,CAAC;IACxCzD,wBAAwB,IAAI,IAAI,IAAIA,wBAAwB,CAACyD,mBAAmB,CAAC;EACnF,CAAC,CAAC;EACF,MAAMC,OAAO,GAAG;IACdC,OAAO,EAAEf,WAAW;IACpBpD,QAAQ,EAAEqD,YAAY;IACtBe,SAAS,EAAEd,aAAa;IACxBe,QAAQ,EAAEd,YAAY;IACtBe,UAAU,EAAEd,cAAc;IAC1Be,MAAM,EAAEb,UAAU;IAClBc,OAAO,EAAEb;EACX,CAAC;EACD,MAAMc,aAAa,GAAG;IACpBvE,KAAK,EAAEtB,SAAS,CAACuD,KAAK;IACtBlC,QAAQ,EAAE8D,qBAAqB;IAC/BzD,gBAAgB;IAChBE,wBAAwB,EAAEwD;EAC5B,CAAC;EACD,MAAMU,SAAS,GAAG3G,KAAK,CAAC4G,OAAO,CAAC,MAAM/E,YAAY,CAACgF,UAAU,CAACrD,KAAK,EAAE3C,SAAS,CAACuD,KAAK,CAAC,EAAE,CAACZ,KAAK,EAAE3B,YAAY,EAAEhB,SAAS,CAACuD,KAAK,CAAC,CAAC;EAC9H,MAAM0C,YAAY,GAAG;IACnB3E,KAAK,EAAEwE,SAAS;IAChBzE,QAAQ,EAAE2D,YAAY;IACtBY,OAAO,EAAEb,WAAW;IACpBmB,IAAI,EAAEjD,MAAM;IACZrB,wBAAwB,EAAEwD;EAC5B,CAAC;EACD,MAAMe,OAAO,GAAGC,WAAW,IAAI;IAC7B,MAAM7D,KAAK,GAAGpB,SAAS,CAAC;MACtByB,OAAO;MACPtB,KAAK,EAAE8E,WAAW;MAClBrF,KAAK,EAAE7B,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,EAAE;QACzBO,KAAK,EAAE8E,WAAW;QAClBvE;MACF,CAAC;IACH,CAAC,CAAC;IACF,OAAO,CAACb,YAAY,CAACqF,QAAQ,CAAC9D,KAAK,CAAC;EACtC,CAAC;EACD,MAAM+D,cAAc,GAAGpH,QAAQ,CAAC,CAAC,CAAC,EAAEoG,OAAO,EAAE;IAC3ChE,KAAK,EAAEwE,SAAS;IAChBzE,QAAQ,EAAE2D,YAAY;IACtBuB,gBAAgB,EAAErB,oBAAoB;IACtCiB;EACF,CAAC,CAAC;EACF,OAAO;IACLD,IAAI,EAAEjD,MAAM;IACZuD,UAAU,EAAEX,aAAa;IACzBY,SAAS,EAAER,YAAY;IACvBS,WAAW,EAAEJ,cAAc;IAC3BhB;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}