{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"eeee 'pase nan lè' p\",\n  yesterday: \"'yè nan lè' p\",\n  today: \"'jodi a' p\",\n  tomorrow: \"'demen nan lè' p'\",\n  nextWeek: \"eeee 'pwochen nan lè' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/ht/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: \"eeee 'pase nan lè' p\",\n  yesterday: \"'yè nan lè' p\",\n  today: \"'jodi a' p\",\n  tomorrow: \"'demen nan lè' p'\",\n  nextWeek: \"eeee 'pwochen nan lè' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,sBAAsB;EAChCC,SAAS,EAAE,eAAe;EAC1BC,KAAK,EAAE,YAAY;EACnBC,QAAQ,EAAE,mBAAmB;EAC7BC,QAAQ,EAAE,yBAAyB;EACnCC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC9E,OAAOX,oBAAoB,CAACQ,KAAK,CAAC;AACpC,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}