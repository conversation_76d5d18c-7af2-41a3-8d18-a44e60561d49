{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"'sidste' eeee 'kl.' p\",\n  yesterday: \"'i går kl.' p\",\n  today: \"'i dag kl.' p\",\n  tomorrow: \"'i morgen kl.' p\",\n  nextWeek: \"'på' eeee 'kl.' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/da/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: \"'sidste' eeee 'kl.' p\",\n  yesterday: \"'i går kl.' p\",\n  today: \"'i dag kl.' p\",\n  tomorrow: \"'i morgen kl.' p\",\n  nextWeek: \"'på' eeee 'kl.' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,uBAAuB;EACjCC,SAAS,EAAE,eAAe;EAC1BC,KAAK,EAAE,eAAe;EACtBC,QAAQ,EAAE,kBAAkB;EAC5BC,QAAQ,EAAE,mBAAmB;EAC7BC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC9E,OAAOX,oBAAoB,CAACQ,KAAK,CAAC;AACpC,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}