{"ast": null, "code": "import { numberToLocale } from \"../localize/index.js\";\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'প্রায় ১ সেকেন্ড',\n    other: 'প্রায় {{count}} সেকেন্ড'\n  },\n  xSeconds: {\n    one: '১ সেকেন্ড',\n    other: '{{count}} সেকেন্ড'\n  },\n  halfAMinute: 'আধ মিনিট',\n  lessThanXMinutes: {\n    one: 'প্রায় ১ মিনিট',\n    other: 'প্রায় {{count}} মিনিট'\n  },\n  xMinutes: {\n    one: '১ মিনিট',\n    other: '{{count}} মিনিট'\n  },\n  aboutXHours: {\n    one: 'প্রায় ১ ঘন্টা',\n    other: 'প্রায় {{count}} ঘন্টা'\n  },\n  xHours: {\n    one: '১ ঘন্টা',\n    other: '{{count}} ঘন্টা'\n  },\n  xDays: {\n    one: '১ দিন',\n    other: '{{count}} দিন'\n  },\n  aboutXWeeks: {\n    one: 'প্রায় ১ সপ্তাহ',\n    other: 'প্রায় {{count}} সপ্তাহ'\n  },\n  xWeeks: {\n    one: '১ সপ্তাহ',\n    other: '{{count}} সপ্তাহ'\n  },\n  aboutXMonths: {\n    one: 'প্রায় ১ মাস',\n    other: 'প্রায় {{count}} মাস'\n  },\n  xMonths: {\n    one: '১ মাস',\n    other: '{{count}} মাস'\n  },\n  aboutXYears: {\n    one: 'প্রায় ১ বছর',\n    other: 'প্রায় {{count}} বছর'\n  },\n  xYears: {\n    one: '১ বছর',\n    other: '{{count}} বছর'\n  },\n  overXYears: {\n    one: '১ বছরের বেশি',\n    other: '{{count}} বছরের বেশি'\n  },\n  almostXYears: {\n    one: 'প্রায় ১ বছর',\n    other: 'প্রায় {{count}} বছর'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', numberToLocale(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + ' এর মধ্যে';\n    } else {\n      return result + ' আগে';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["numberToLocale", "formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/bn/_lib/formatDistance/index.js"], "sourcesContent": ["import { numberToLocale } from \"../localize/index.js\";\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'প্রায় ১ সেকেন্ড',\n    other: 'প্রায় {{count}} সেকেন্ড'\n  },\n  xSeconds: {\n    one: '১ সেকেন্ড',\n    other: '{{count}} সেকেন্ড'\n  },\n  halfAMinute: 'আধ মিনিট',\n  lessThanXMinutes: {\n    one: 'প্রায় ১ মিনিট',\n    other: 'প্রায় {{count}} মিনিট'\n  },\n  xMinutes: {\n    one: '১ মিনিট',\n    other: '{{count}} মিনিট'\n  },\n  aboutXHours: {\n    one: 'প্রায় ১ ঘন্টা',\n    other: 'প্রায় {{count}} ঘন্টা'\n  },\n  xHours: {\n    one: '১ ঘন্টা',\n    other: '{{count}} ঘন্টা'\n  },\n  xDays: {\n    one: '১ দিন',\n    other: '{{count}} দিন'\n  },\n  aboutXWeeks: {\n    one: 'প্রায় ১ সপ্তাহ',\n    other: 'প্রায় {{count}} সপ্তাহ'\n  },\n  xWeeks: {\n    one: '১ সপ্তাহ',\n    other: '{{count}} সপ্তাহ'\n  },\n  aboutXMonths: {\n    one: 'প্রায় ১ মাস',\n    other: 'প্রায় {{count}} মাস'\n  },\n  xMonths: {\n    one: '১ মাস',\n    other: '{{count}} মাস'\n  },\n  aboutXYears: {\n    one: 'প্রায় ১ বছর',\n    other: 'প্রায় {{count}} বছর'\n  },\n  xYears: {\n    one: '১ বছর',\n    other: '{{count}} বছর'\n  },\n  overXYears: {\n    one: '১ বছরের বেশি',\n    other: '{{count}} বছরের বেশি'\n  },\n  almostXYears: {\n    one: 'প্রায় ১ বছর',\n    other: 'প্রায় {{count}} বছর'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', numberToLocale(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + ' এর মধ্যে';\n    } else {\n      return result + ' আগে';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,SAASA,cAAc,QAAQ,sBAAsB;AACrD,IAAIC,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,UAAU;EACvBC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAE1B,cAAc,CAACsB,KAAK,CAAC,CAAC;EACvE;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACI,SAAS,EAAE;IAC/D,IAAIJ,OAAO,CAACK,UAAU,IAAIL,OAAO,CAACK,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOJ,MAAM,GAAG,WAAW;IAC7B,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,MAAM;IACxB;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}