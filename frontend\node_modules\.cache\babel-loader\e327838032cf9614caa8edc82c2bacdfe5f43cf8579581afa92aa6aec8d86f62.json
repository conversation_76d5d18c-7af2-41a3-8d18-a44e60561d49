{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { millisecondsInHour } from \"../constants/index.js\";\n/**\n * @name hoursToMilliseconds\n * @category  Conversion Helpers\n * @summary Convert hours to milliseconds.\n *\n * @description\n * Convert a number of hours to a full number of milliseconds.\n *\n * @param {number} hours - number of hours to be converted\n *\n * @returns {number} the number of hours converted to milliseconds\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 hours to milliseconds:\n * const result = hoursToMilliseconds(2)\n * //=> 7200000\n */\nexport default function hoursToMilliseconds(hours) {\n  requiredArgs(1, arguments);\n  return Math.floor(hours * millisecondsInHour);\n}", "map": {"version": 3, "names": ["requiredArgs", "millisecondsInHour", "hoursToMilliseconds", "hours", "arguments", "Math", "floor"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/hoursToMilliseconds/index.js"], "sourcesContent": ["import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { millisecondsInHour } from \"../constants/index.js\";\n/**\n * @name hoursToMilliseconds\n * @category  Conversion Helpers\n * @summary Convert hours to milliseconds.\n *\n * @description\n * Convert a number of hours to a full number of milliseconds.\n *\n * @param {number} hours - number of hours to be converted\n *\n * @returns {number} the number of hours converted to milliseconds\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 hours to milliseconds:\n * const result = hoursToMilliseconds(2)\n * //=> 7200000\n */\nexport default function hoursToMilliseconds(hours) {\n  requiredArgs(1, arguments);\n  return Math.floor(hours * millisecondsInHour);\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EACjDH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,OAAOC,IAAI,CAACC,KAAK,CAACH,KAAK,GAAGF,kBAAkB,CAAC;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}