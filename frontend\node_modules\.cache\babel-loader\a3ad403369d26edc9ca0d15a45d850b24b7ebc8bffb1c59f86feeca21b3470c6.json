{"ast": null, "code": "import differenceInMilliseconds from \"../differenceInMilliseconds/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { getRoundingMethod } from \"../_lib/roundingMethods/index.js\";\n/**\n * @name differenceInSeconds\n * @category Second Helpers\n * @summary Get the number of seconds between the given dates.\n *\n * @description\n * Get the number of seconds between the given dates.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @param {Object} [options] - an object with options.\n * @param {String} [options.roundingMethod='trunc'] - a rounding method (`ceil`, `floor`, `round` or `trunc`)\n * @returns {Number} the number of seconds\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many seconds are between\n * // 2 July 2014 12:30:07.999 and 2 July 2014 12:30:20.000?\n * const result = differenceInSeconds(\n *   new Date(2014, 6, 2, 12, 30, 20, 0),\n *   new Date(2014, 6, 2, 12, 30, 7, 999)\n * )\n * //=> 12\n */\nexport default function differenceInSeconds(dateLeft, dateRight, options) {\n  requiredArgs(2, arguments);\n  var diff = differenceInMilliseconds(dateLeft, dateRight) / 1000;\n  return getRoundingMethod(options === null || options === void 0 ? void 0 : options.roundingMethod)(diff);\n}", "map": {"version": 3, "names": ["differenceInMilliseconds", "requiredArgs", "getRoundingMethod", "differenceInSeconds", "dateLeft", "dateRight", "options", "arguments", "diff", "roundingMethod"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/differenceInSeconds/index.js"], "sourcesContent": ["import differenceInMilliseconds from \"../differenceInMilliseconds/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { getRoundingMethod } from \"../_lib/roundingMethods/index.js\";\n/**\n * @name differenceInSeconds\n * @category Second Helpers\n * @summary Get the number of seconds between the given dates.\n *\n * @description\n * Get the number of seconds between the given dates.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @param {Object} [options] - an object with options.\n * @param {String} [options.roundingMethod='trunc'] - a rounding method (`ceil`, `floor`, `round` or `trunc`)\n * @returns {Number} the number of seconds\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many seconds are between\n * // 2 July 2014 12:30:07.999 and 2 July 2014 12:30:20.000?\n * const result = differenceInSeconds(\n *   new Date(2014, 6, 2, 12, 30, 20, 0),\n *   new Date(2014, 6, 2, 12, 30, 7, 999)\n * )\n * //=> 12\n */\nexport default function differenceInSeconds(dateLeft, dateRight, options) {\n  requiredArgs(2, arguments);\n  var diff = differenceInMilliseconds(dateLeft, dateRight) / 1000;\n  return getRoundingMethod(options === null || options === void 0 ? void 0 : options.roundingMethod)(diff);\n}"], "mappings": "AAAA,OAAOA,wBAAwB,MAAM,sCAAsC;AAC3E,OAAOC,YAAY,MAAM,+BAA+B;AACxD,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,mBAAmBA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAE;EACxEL,YAAY,CAAC,CAAC,EAAEM,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGR,wBAAwB,CAACI,QAAQ,EAAEC,SAAS,CAAC,GAAG,IAAI;EAC/D,OAAOH,iBAAiB,CAACI,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,cAAc,CAAC,CAACD,IAAI,CAAC;AAC1G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}