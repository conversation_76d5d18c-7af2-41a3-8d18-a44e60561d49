const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Attendance = require('../models/Attendance');
const Student = require('../models/Student');
const { protect, adminOnly, adminOrCoach } = require('../middleware/auth');

const router = express.Router();

// Apply protection to all routes
router.use(protect);

// @desc    Get all attendance records
// @route   GET /api/attendance
// @access  Private (Admin/Coach)
router.get('/', [
  adminOrCoach,
  query('page').optional().isInt({ min: 1 }).withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('حد النتائج يجب أن يكون بين 1 و 100'),
  query('date').optional().isISO8601().withMessage('التاريخ يجب أن يكون تاريخ صحيح'),
  query('student').optional().isMongoId().withMessage('معرف الطالب غير صحيح'),
  query('classType').optional().isIn(['regular', 'private', 'makeup', 'competition']).withMessage('نوع الحصة غير صحيح'),
  query('status').optional().isIn(['present', 'absent', 'late', 'excused']).withMessage('حالة الحضور غير صحيحة')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const { date, student, classType, status } = req.query;

    // Build query
    let query = {};

    if (date) {
      const startDate = new Date(date);
      const endDate = new Date(date);
      endDate.setDate(endDate.getDate() + 1);
      query.date = { $gte: startDate, $lt: endDate };
    }

    if (student) {
      query.student = student;
    }

    if (classType) {
      query.classType = classType;
    }

    if (status) {
      query.status = status;
    }

    // Get attendance records with pagination
    const attendanceRecords = await Attendance.find(query)
      .populate('student', 'fullName studentId phone beltLevel')
      .populate('instructor', 'fullName')
      .populate('createdBy', 'fullName')
      .populate('updatedBy', 'fullName')
      .sort({ date: -1, timeIn: -1 })
      .skip(skip)
      .limit(limit);

    // Get total count for pagination
    const total = await Attendance.countDocuments(query);

    res.status(200).json({
      success: true,
      count: attendanceRecords.length,
      total,
      pagination: {
        page,
        limit,
        pages: Math.ceil(total / limit)
      },
      data: attendanceRecords
    });

  } catch (error) {
    console.error('Get attendance error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Get single attendance record
// @route   GET /api/attendance/:id
// @access  Private (Admin/Coach)
router.get('/:id', adminOrCoach, async (req, res) => {
  try {
    const attendance = await Attendance.findById(req.params.id)
      .populate('student', 'fullName studentId phone beltLevel')
      .populate('instructor', 'fullName')
      .populate('createdBy', 'fullName')
      .populate('updatedBy', 'fullName');

    if (!attendance) {
      return res.status(404).json({
        success: false,
        message: 'سجل الحضور غير موجود'
      });
    }

    res.status(200).json({
      success: true,
      data: attendance
    });

  } catch (error) {
    console.error('Get attendance record error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Create new attendance record
// @route   POST /api/attendance
// @access  Private (Admin/Coach)
router.post('/', [
  adminOrCoach,
  body('student')
    .isMongoId()
    .withMessage('معرف الطالب غير صحيح'),
  body('date')
    .isISO8601()
    .withMessage('التاريخ يجب أن يكون تاريخ صحيح'),
  body('timeIn')
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('وقت الدخول يجب أن يكون بصيغة HH:MM'),
  body('timeOut')
    .optional()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('وقت الخروج يجب أن يكون بصيغة HH:MM'),
  body('classType')
    .isIn(['regular', 'private', 'makeup', 'competition'])
    .withMessage('نوع الحصة يجب أن يكون عادية، خاصة، تعويضية، أو منافسة'),
  body('status')
    .isIn(['present', 'absent', 'late', 'excused'])
    .withMessage('حالة الحضور يجب أن تكون حاضر، غائب، متأخر، أو معذور'),
  body('instructor')
    .optional()
    .isMongoId()
    .withMessage('معرف المدرب غير صحيح'),
  body('performanceRating')
    .optional()
    .isInt({ min: 1, max: 5 })
    .withMessage('تقييم الأداء يجب أن يكون بين 1 و 5')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    // Check if student exists
    const student = await Student.findById(req.body.student);
    if (!student) {
      return res.status(404).json({
        success: false,
        message: 'الطالب غير موجود'
      });
    }

    // Create attendance record
    const attendance = await Attendance.create({
      ...req.body,
      createdBy: req.user._id
    });

    // Populate the created attendance record
    await attendance.populate([
      { path: 'student', select: 'fullName studentId phone beltLevel' },
      { path: 'instructor', select: 'fullName' }
    ]);

    res.status(201).json({
      success: true,
      message: 'تم تسجيل الحضور بنجاح',
      data: attendance
    });

  } catch (error) {
    console.error('Create attendance error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Update attendance record
// @route   PUT /api/attendance/:id
// @access  Private (Admin/Coach)
router.put('/:id', [
  adminOrCoach,
  body('date')
    .optional()
    .isISO8601()
    .withMessage('التاريخ يجب أن يكون تاريخ صحيح'),
  body('timeIn')
    .optional()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('وقت الدخول يجب أن يكون بصيغة HH:MM'),
  body('timeOut')
    .optional()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('وقت الخروج يجب أن يكون بصيغة HH:MM'),
  body('classType')
    .optional()
    .isIn(['regular', 'private', 'makeup', 'competition'])
    .withMessage('نوع الحصة يجب أن يكون عادية، خاصة، تعويضية، أو منافسة'),
  body('status')
    .optional()
    .isIn(['present', 'absent', 'late', 'excused'])
    .withMessage('حالة الحضور يجب أن تكون حاضر، غائب، متأخر، أو معذور'),
  body('instructor')
    .optional()
    .isMongoId()
    .withMessage('معرف المدرب غير صحيح'),
  body('performanceRating')
    .optional()
    .isInt({ min: 1, max: 5 })
    .withMessage('تقييم الأداء يجب أن يكون بين 1 و 5')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const attendance = await Attendance.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedBy: req.user._id },
      { new: true, runValidators: true }
    ).populate([
      { path: 'student', select: 'fullName studentId phone beltLevel' },
      { path: 'instructor', select: 'fullName' }
    ]);

    if (!attendance) {
      return res.status(404).json({
        success: false,
        message: 'سجل الحضور غير موجود'
      });
    }

    res.status(200).json({
      success: true,
      message: 'تم تحديث سجل الحضور بنجاح',
      data: attendance
    });

  } catch (error) {
    console.error('Update attendance error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Delete attendance record
// @route   DELETE /api/attendance/:id
// @access  Private (Admin only)
router.delete('/:id', adminOnly, async (req, res) => {
  try {
    const attendance = await Attendance.findByIdAndDelete(req.params.id);

    if (!attendance) {
      return res.status(404).json({
        success: false,
        message: 'سجل الحضور غير موجود'
      });
    }

    res.status(200).json({
      success: true,
      message: 'تم حذف سجل الحضور بنجاح'
    });

  } catch (error) {
    console.error('Delete attendance error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Get student attendance records
// @route   GET /api/attendance/student/:studentId
// @access  Private (Admin/Coach)
router.get('/student/:studentId', [
  adminOrCoach,
  query('startDate').optional().isISO8601().withMessage('تاريخ البداية يجب أن يكون تاريخ صحيح'),
  query('endDate').optional().isISO8601().withMessage('تاريخ النهاية يجب أن يكون تاريخ صحيح')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const { startDate, endDate } = req.query;
    let dateQuery = {};

    if (startDate || endDate) {
      dateQuery.date = {};
      if (startDate) dateQuery.date.$gte = new Date(startDate);
      if (endDate) dateQuery.date.$lte = new Date(endDate);
    }

    const attendanceRecords = await Attendance.find({
      student: req.params.studentId,
      ...dateQuery
    })
      .populate('instructor', 'fullName')
      .sort({ date: -1 });

    res.status(200).json({
      success: true,
      count: attendanceRecords.length,
      data: attendanceRecords
    });

  } catch (error) {
    console.error('Get student attendance error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Get attendance statistics for a student
// @route   GET /api/attendance/student/:studentId/stats
// @access  Private (Admin/Coach)
router.get('/student/:studentId/stats', [
  adminOrCoach,
  query('startDate').optional().isISO8601().withMessage('تاريخ البداية يجب أن يكون تاريخ صحيح'),
  query('endDate').optional().isISO8601().withMessage('تاريخ النهاية يجب أن يكون تاريخ صحيح')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const { startDate, endDate } = req.query;
    const stats = await Attendance.getStudentStats(req.params.studentId, startDate, endDate);

    res.status(200).json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Get student attendance stats error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Get attendance by date range
// @route   GET /api/attendance/range/:startDate/:endDate
// @access  Private (Admin/Coach)
router.get('/range/:startDate/:endDate', adminOrCoach, async (req, res) => {
  try {
    const { startDate, endDate } = req.params;

    // Validate dates
    if (!Date.parse(startDate) || !Date.parse(endDate)) {
      return res.status(400).json({
        success: false,
        message: 'تواريخ غير صحيحة'
      });
    }

    const attendanceRecords = await Attendance.find({
      date: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    })
      .populate('student', 'fullName studentId phone beltLevel')
      .populate('instructor', 'fullName')
      .sort({ date: -1, timeIn: -1 });

    res.status(200).json({
      success: true,
      count: attendanceRecords.length,
      data: attendanceRecords
    });

  } catch (error) {
    console.error('Get attendance by range error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

module.exports = router;
