{"name": "taekwondo-crm-backend", "version": "1.0.0", "description": "Backend API for Taekwondo Academy CRM System", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["crm", "taekwondo", "academy", "management", "arabic"], "author": "Taekwondo Academy", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "xlsx": "^0.18.5", "moment": "^2.29.4", "moment-timezone": "^0.5.43"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}