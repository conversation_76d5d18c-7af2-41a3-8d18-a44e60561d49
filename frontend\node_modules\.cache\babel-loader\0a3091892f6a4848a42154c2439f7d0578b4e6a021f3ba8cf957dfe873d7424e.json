{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"slots\", \"slotProps\", \"components\", \"componentsProps\", \"currentMonth\", \"disabled\", \"disableFuture\", \"disablePast\", \"maxDate\", \"minDate\", \"onMonthChange\", \"onViewChange\", \"view\", \"reduceAnimations\", \"views\", \"labelId\", \"className\", \"timezone\"],\n  _excluded2 = [\"ownerState\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport Fade from '@mui/material/Fade';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { useSlotProps } from '@mui/base/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/utils';\nimport IconButton from '@mui/material/IconButton';\nimport { useLocaleText, useUtils } from '../internals/hooks/useUtils';\nimport { PickersFadeTransitionGroup } from '../DateCalendar/PickersFadeTransitionGroup';\nimport { ArrowDropDownIcon } from '../icons';\nimport { PickersArrowSwitcher } from '../internals/components/PickersArrowSwitcher';\nimport { usePreviousMonthDisabled, useNextMonthDisabled } from '../internals/hooks/date-helpers-hooks';\nimport { getPickersCalendarHeaderUtilityClass, pickersCalendarHeaderClasses } from './pickersCalendarHeaderClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    labelContainer: ['labelContainer'],\n    label: ['label'],\n    switchViewButton: ['switchViewButton'],\n    switchViewIcon: ['switchViewIcon']\n  };\n  return composeClasses(slots, getPickersCalendarHeaderUtilityClass, classes);\n};\nconst PickersCalendarHeaderRoot = styled('div', {\n  name: 'MuiPickersCalendarHeader',\n  slot: 'Root',\n  overridesResolver: (_, styles) => styles.root\n})({\n  display: 'flex',\n  alignItems: 'center',\n  marginTop: 16,\n  marginBottom: 8,\n  paddingLeft: 24,\n  paddingRight: 12,\n  // prevent jumping in safari\n  maxHeight: 30,\n  minHeight: 30\n});\nconst PickersCalendarHeaderLabelContainer = styled('div', {\n  name: 'MuiPickersCalendarHeader',\n  slot: 'LabelContainer',\n  overridesResolver: (_, styles) => styles.labelContainer\n})(({\n  theme\n}) => _extends({\n  display: 'flex',\n  overflow: 'hidden',\n  alignItems: 'center',\n  cursor: 'pointer',\n  marginRight: 'auto'\n}, theme.typography.body1, {\n  fontWeight: theme.typography.fontWeightMedium\n}));\nconst PickersCalendarHeaderLabel = styled('div', {\n  name: 'MuiPickersCalendarHeader',\n  slot: 'Label',\n  overridesResolver: (_, styles) => styles.label\n})({\n  marginRight: 6\n});\nconst PickersCalendarHeaderSwitchViewButton = styled(IconButton, {\n  name: 'MuiPickersCalendarHeader',\n  slot: 'SwitchViewButton',\n  overridesResolver: (_, styles) => styles.switchViewButton\n})(({\n  ownerState\n}) => _extends({\n  marginRight: 'auto'\n}, ownerState.view === 'year' && {\n  [`.${pickersCalendarHeaderClasses.switchViewIcon}`]: {\n    transform: 'rotate(180deg)'\n  }\n}));\nconst PickersCalendarHeaderSwitchViewIcon = styled(ArrowDropDownIcon, {\n  name: 'MuiPickersCalendarHeader',\n  slot: 'SwitchViewIcon',\n  overridesResolver: (_, styles) => styles.switchViewIcon\n})(({\n  theme\n}) => ({\n  willChange: 'transform',\n  transition: theme.transitions.create('transform'),\n  transform: 'rotate(0deg)'\n}));\n/**\n * Demos:\n *\n * - [DateCalendar](https://mui.com/x/react-date-pickers/date-calendar/)\n * - [DateRangeCalendar](https://mui.com/x/react-date-pickers/date-range-calendar/)\n * - [Custom slots and subcomponents](https://mui.com/x/react-date-pickers/custom-components/)\n *\n * API:\n *\n * - [PickersCalendarHeader API](https://mui.com/x/api/date-pickers/pickers-calendar-header/)\n */\nconst PickersCalendarHeader = /*#__PURE__*/React.forwardRef(function PickersCalendarHeader(inProps, ref) {\n  var _ref, _slots$switchViewButt, _ref2, _slots$switchViewIcon;\n  const localeText = useLocaleText();\n  const utils = useUtils();\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiPickersCalendarHeader'\n  });\n  const {\n      slots,\n      slotProps,\n      components,\n      currentMonth: month,\n      disabled,\n      disableFuture,\n      disablePast,\n      maxDate,\n      minDate,\n      onMonthChange,\n      onViewChange,\n      view,\n      reduceAnimations,\n      views,\n      labelId,\n      className,\n      timezone\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses(props);\n  const SwitchViewButton = (_ref = (_slots$switchViewButt = slots == null ? void 0 : slots.switchViewButton) != null ? _slots$switchViewButt : components == null ? void 0 : components.SwitchViewButton) != null ? _ref : PickersCalendarHeaderSwitchViewButton;\n  const switchViewButtonProps = useSlotProps({\n    elementType: SwitchViewButton,\n    externalSlotProps: slotProps == null ? void 0 : slotProps.switchViewButton,\n    additionalProps: {\n      size: 'small',\n      'aria-label': localeText.calendarViewSwitchingButtonAriaLabel(view)\n    },\n    ownerState,\n    className: classes.switchViewButton\n  });\n  const SwitchViewIcon = (_ref2 = (_slots$switchViewIcon = slots == null ? void 0 : slots.switchViewIcon) != null ? _slots$switchViewIcon : components == null ? void 0 : components.SwitchViewIcon) != null ? _ref2 : PickersCalendarHeaderSwitchViewIcon;\n  // The spread is here to avoid this bug mui/material-ui#34056\n  const _useSlotProps = useSlotProps({\n      elementType: SwitchViewIcon,\n      externalSlotProps: slotProps == null ? void 0 : slotProps.switchViewIcon,\n      ownerState: undefined,\n      className: classes.switchViewIcon\n    }),\n    switchViewIconProps = _objectWithoutPropertiesLoose(_useSlotProps, _excluded2);\n  const selectNextMonth = () => onMonthChange(utils.addMonths(month, 1), 'left');\n  const selectPreviousMonth = () => onMonthChange(utils.addMonths(month, -1), 'right');\n  const isNextMonthDisabled = useNextMonthDisabled(month, {\n    disableFuture,\n    maxDate,\n    timezone\n  });\n  const isPreviousMonthDisabled = usePreviousMonthDisabled(month, {\n    disablePast,\n    minDate,\n    timezone\n  });\n  const handleToggleView = () => {\n    if (views.length === 1 || !onViewChange || disabled) {\n      return;\n    }\n    if (views.length === 2) {\n      onViewChange(views.find(el => el !== view) || views[0]);\n    } else {\n      // switching only between first 2\n      const nextIndexToOpen = views.indexOf(view) !== 0 ? 0 : 1;\n      onViewChange(views[nextIndexToOpen]);\n    }\n  };\n\n  // No need to display more information\n  if (views.length === 1 && views[0] === 'year') {\n    return null;\n  }\n  return /*#__PURE__*/_jsxs(PickersCalendarHeaderRoot, _extends({}, other, {\n    ownerState: ownerState,\n    className: clsx(className, classes.root),\n    ref: ref,\n    children: [/*#__PURE__*/_jsxs(PickersCalendarHeaderLabelContainer, {\n      role: \"presentation\",\n      onClick: handleToggleView,\n      ownerState: ownerState\n      // putting this on the label item element below breaks when using transition\n      ,\n\n      \"aria-live\": \"polite\",\n      className: classes.labelContainer,\n      children: [/*#__PURE__*/_jsx(PickersFadeTransitionGroup, {\n        reduceAnimations: reduceAnimations,\n        transKey: utils.format(month, 'monthAndYear'),\n        children: /*#__PURE__*/_jsx(PickersCalendarHeaderLabel, {\n          id: labelId,\n          ownerState: ownerState,\n          className: classes.label,\n          children: utils.format(month, 'monthAndYear')\n        })\n      }), views.length > 1 && !disabled && /*#__PURE__*/_jsx(SwitchViewButton, _extends({}, switchViewButtonProps, {\n        children: /*#__PURE__*/_jsx(SwitchViewIcon, _extends({}, switchViewIconProps))\n      }))]\n    }), /*#__PURE__*/_jsx(Fade, {\n      in: view === 'day',\n      children: /*#__PURE__*/_jsx(PickersArrowSwitcher, {\n        slots: slots,\n        slotProps: slotProps,\n        onGoToPrevious: selectPreviousMonth,\n        isPreviousDisabled: isPreviousMonthDisabled,\n        previousLabel: localeText.previousMonth,\n        onGoToNext: selectNextMonth,\n        isNextDisabled: isNextMonthDisabled,\n        nextLabel: localeText.nextMonth\n      })\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? PickersCalendarHeader.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * className applied to the root element.\n   */\n  className: PropTypes.string,\n  /**\n   * Overridable components.\n   * @default {}\n   * @deprecated Please use `slots`.\n   */\n  components: PropTypes.object,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   * @deprecated Please use `slotProps`.\n   */\n  componentsProps: PropTypes.object,\n  currentMonth: PropTypes.any.isRequired,\n  disabled: PropTypes.bool,\n  disableFuture: PropTypes.bool,\n  disablePast: PropTypes.bool,\n  labelId: PropTypes.string,\n  maxDate: PropTypes.any.isRequired,\n  minDate: PropTypes.any.isRequired,\n  onMonthChange: PropTypes.func.isRequired,\n  onViewChange: PropTypes.func,\n  reduceAnimations: PropTypes.bool.isRequired,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  timezone: PropTypes.string.isRequired,\n  view: PropTypes.oneOf(['day', 'month', 'year']).isRequired,\n  views: PropTypes.arrayOf(PropTypes.oneOf(['day', 'month', 'year']).isRequired).isRequired\n} : void 0;\nexport { PickersCalendarHeader };", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "_excluded2", "React", "PropTypes", "clsx", "Fade", "styled", "useThemeProps", "useSlotProps", "unstable_composeClasses", "composeClasses", "IconButton", "useLocaleText", "useUtils", "PickersFadeTransitionGroup", "ArrowDropDownIcon", "PickersArrowSwitcher", "usePreviousMonthDisabled", "useNextMonthDisabled", "getPickersCalendarHeaderUtilityClass", "pickersCalendarHeaderClasses", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "slots", "root", "labelContainer", "label", "switchViewButton", "switchViewIcon", "PickersCalendarHeaderRoot", "name", "slot", "overridesResolver", "_", "styles", "display", "alignItems", "marginTop", "marginBottom", "paddingLeft", "paddingRight", "maxHeight", "minHeight", "PickersCalendarHeaderLabelContainer", "theme", "overflow", "cursor", "marginRight", "typography", "body1", "fontWeight", "fontWeightMedium", "PickersCalendarHeaderLabel", "PickersCalendarHeaderSwitchViewButton", "view", "transform", "PickersCalendarHeaderSwitchViewIcon", "<PERSON><PERSON><PERSON><PERSON>", "transition", "transitions", "create", "Pickers<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardRef", "inProps", "ref", "_ref", "_slots$switchViewButt", "_ref2", "_slots$switchViewIcon", "localeText", "utils", "props", "slotProps", "components", "currentMonth", "month", "disabled", "disableFuture", "disablePast", "maxDate", "minDate", "onMonthChange", "onViewChange", "reduceAnimations", "views", "labelId", "className", "timezone", "other", "SwitchViewButton", "switchViewButtonProps", "elementType", "externalSlotProps", "additionalProps", "size", "calendarViewSwitchingButtonAriaLabel", "SwitchViewIcon", "_useSlotProps", "undefined", "switchViewIconProps", "selectNextMonth", "addMonths", "selectPreviousMonth", "isNextMonthDisabled", "isPreviousMonthDisabled", "handleToggleView", "length", "find", "el", "nextIndexToOpen", "indexOf", "children", "role", "onClick", "transKey", "format", "id", "in", "onGoToPrevious", "isPreviousDisabled", "previousLabel", "previousMonth", "onGoToNext", "isNextDisabled", "next<PERSON><PERSON><PERSON>", "nextMonth", "process", "env", "NODE_ENV", "propTypes", "object", "string", "componentsProps", "any", "isRequired", "bool", "func", "sx", "oneOfType", "arrayOf", "oneOf"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/x-date-pickers/PickersCalendarHeader/PickersCalendarHeader.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"slots\", \"slotProps\", \"components\", \"componentsProps\", \"currentMonth\", \"disabled\", \"disableFuture\", \"disablePast\", \"maxDate\", \"minDate\", \"onMonthChange\", \"onViewChange\", \"view\", \"reduceAnimations\", \"views\", \"labelId\", \"className\", \"timezone\"],\n  _excluded2 = [\"ownerState\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport Fade from '@mui/material/Fade';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { useSlotProps } from '@mui/base/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/utils';\nimport IconButton from '@mui/material/IconButton';\nimport { useLocaleText, useUtils } from '../internals/hooks/useUtils';\nimport { PickersFadeTransitionGroup } from '../DateCalendar/PickersFadeTransitionGroup';\nimport { ArrowDropDownIcon } from '../icons';\nimport { PickersArrowSwitcher } from '../internals/components/PickersArrowSwitcher';\nimport { usePreviousMonthDisabled, useNextMonthDisabled } from '../internals/hooks/date-helpers-hooks';\nimport { getPickersCalendarHeaderUtilityClass, pickersCalendarHeaderClasses } from './pickersCalendarHeaderClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    labelContainer: ['labelContainer'],\n    label: ['label'],\n    switchViewButton: ['switchViewButton'],\n    switchViewIcon: ['switchViewIcon']\n  };\n  return composeClasses(slots, getPickersCalendarHeaderUtilityClass, classes);\n};\nconst PickersCalendarHeaderRoot = styled('div', {\n  name: 'MuiPickersCalendarHeader',\n  slot: 'Root',\n  overridesResolver: (_, styles) => styles.root\n})({\n  display: 'flex',\n  alignItems: 'center',\n  marginTop: 16,\n  marginBottom: 8,\n  paddingLeft: 24,\n  paddingRight: 12,\n  // prevent jumping in safari\n  maxHeight: 30,\n  minHeight: 30\n});\nconst PickersCalendarHeaderLabelContainer = styled('div', {\n  name: 'MuiPickersCalendarHeader',\n  slot: 'LabelContainer',\n  overridesResolver: (_, styles) => styles.labelContainer\n})(({\n  theme\n}) => _extends({\n  display: 'flex',\n  overflow: 'hidden',\n  alignItems: 'center',\n  cursor: 'pointer',\n  marginRight: 'auto'\n}, theme.typography.body1, {\n  fontWeight: theme.typography.fontWeightMedium\n}));\nconst PickersCalendarHeaderLabel = styled('div', {\n  name: 'MuiPickersCalendarHeader',\n  slot: 'Label',\n  overridesResolver: (_, styles) => styles.label\n})({\n  marginRight: 6\n});\nconst PickersCalendarHeaderSwitchViewButton = styled(IconButton, {\n  name: 'MuiPickersCalendarHeader',\n  slot: 'SwitchViewButton',\n  overridesResolver: (_, styles) => styles.switchViewButton\n})(({\n  ownerState\n}) => _extends({\n  marginRight: 'auto'\n}, ownerState.view === 'year' && {\n  [`.${pickersCalendarHeaderClasses.switchViewIcon}`]: {\n    transform: 'rotate(180deg)'\n  }\n}));\nconst PickersCalendarHeaderSwitchViewIcon = styled(ArrowDropDownIcon, {\n  name: 'MuiPickersCalendarHeader',\n  slot: 'SwitchViewIcon',\n  overridesResolver: (_, styles) => styles.switchViewIcon\n})(({\n  theme\n}) => ({\n  willChange: 'transform',\n  transition: theme.transitions.create('transform'),\n  transform: 'rotate(0deg)'\n}));\n/**\n * Demos:\n *\n * - [DateCalendar](https://mui.com/x/react-date-pickers/date-calendar/)\n * - [DateRangeCalendar](https://mui.com/x/react-date-pickers/date-range-calendar/)\n * - [Custom slots and subcomponents](https://mui.com/x/react-date-pickers/custom-components/)\n *\n * API:\n *\n * - [PickersCalendarHeader API](https://mui.com/x/api/date-pickers/pickers-calendar-header/)\n */\nconst PickersCalendarHeader = /*#__PURE__*/React.forwardRef(function PickersCalendarHeader(inProps, ref) {\n  var _ref, _slots$switchViewButt, _ref2, _slots$switchViewIcon;\n  const localeText = useLocaleText();\n  const utils = useUtils();\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiPickersCalendarHeader'\n  });\n  const {\n      slots,\n      slotProps,\n      components,\n      currentMonth: month,\n      disabled,\n      disableFuture,\n      disablePast,\n      maxDate,\n      minDate,\n      onMonthChange,\n      onViewChange,\n      view,\n      reduceAnimations,\n      views,\n      labelId,\n      className,\n      timezone\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses(props);\n  const SwitchViewButton = (_ref = (_slots$switchViewButt = slots == null ? void 0 : slots.switchViewButton) != null ? _slots$switchViewButt : components == null ? void 0 : components.SwitchViewButton) != null ? _ref : PickersCalendarHeaderSwitchViewButton;\n  const switchViewButtonProps = useSlotProps({\n    elementType: SwitchViewButton,\n    externalSlotProps: slotProps == null ? void 0 : slotProps.switchViewButton,\n    additionalProps: {\n      size: 'small',\n      'aria-label': localeText.calendarViewSwitchingButtonAriaLabel(view)\n    },\n    ownerState,\n    className: classes.switchViewButton\n  });\n  const SwitchViewIcon = (_ref2 = (_slots$switchViewIcon = slots == null ? void 0 : slots.switchViewIcon) != null ? _slots$switchViewIcon : components == null ? void 0 : components.SwitchViewIcon) != null ? _ref2 : PickersCalendarHeaderSwitchViewIcon;\n  // The spread is here to avoid this bug mui/material-ui#34056\n  const _useSlotProps = useSlotProps({\n      elementType: SwitchViewIcon,\n      externalSlotProps: slotProps == null ? void 0 : slotProps.switchViewIcon,\n      ownerState: undefined,\n      className: classes.switchViewIcon\n    }),\n    switchViewIconProps = _objectWithoutPropertiesLoose(_useSlotProps, _excluded2);\n  const selectNextMonth = () => onMonthChange(utils.addMonths(month, 1), 'left');\n  const selectPreviousMonth = () => onMonthChange(utils.addMonths(month, -1), 'right');\n  const isNextMonthDisabled = useNextMonthDisabled(month, {\n    disableFuture,\n    maxDate,\n    timezone\n  });\n  const isPreviousMonthDisabled = usePreviousMonthDisabled(month, {\n    disablePast,\n    minDate,\n    timezone\n  });\n  const handleToggleView = () => {\n    if (views.length === 1 || !onViewChange || disabled) {\n      return;\n    }\n    if (views.length === 2) {\n      onViewChange(views.find(el => el !== view) || views[0]);\n    } else {\n      // switching only between first 2\n      const nextIndexToOpen = views.indexOf(view) !== 0 ? 0 : 1;\n      onViewChange(views[nextIndexToOpen]);\n    }\n  };\n\n  // No need to display more information\n  if (views.length === 1 && views[0] === 'year') {\n    return null;\n  }\n  return /*#__PURE__*/_jsxs(PickersCalendarHeaderRoot, _extends({}, other, {\n    ownerState: ownerState,\n    className: clsx(className, classes.root),\n    ref: ref,\n    children: [/*#__PURE__*/_jsxs(PickersCalendarHeaderLabelContainer, {\n      role: \"presentation\",\n      onClick: handleToggleView,\n      ownerState: ownerState\n      // putting this on the label item element below breaks when using transition\n      ,\n      \"aria-live\": \"polite\",\n      className: classes.labelContainer,\n      children: [/*#__PURE__*/_jsx(PickersFadeTransitionGroup, {\n        reduceAnimations: reduceAnimations,\n        transKey: utils.format(month, 'monthAndYear'),\n        children: /*#__PURE__*/_jsx(PickersCalendarHeaderLabel, {\n          id: labelId,\n          ownerState: ownerState,\n          className: classes.label,\n          children: utils.format(month, 'monthAndYear')\n        })\n      }), views.length > 1 && !disabled && /*#__PURE__*/_jsx(SwitchViewButton, _extends({}, switchViewButtonProps, {\n        children: /*#__PURE__*/_jsx(SwitchViewIcon, _extends({}, switchViewIconProps))\n      }))]\n    }), /*#__PURE__*/_jsx(Fade, {\n      in: view === 'day',\n      children: /*#__PURE__*/_jsx(PickersArrowSwitcher, {\n        slots: slots,\n        slotProps: slotProps,\n        onGoToPrevious: selectPreviousMonth,\n        isPreviousDisabled: isPreviousMonthDisabled,\n        previousLabel: localeText.previousMonth,\n        onGoToNext: selectNextMonth,\n        isNextDisabled: isNextMonthDisabled,\n        nextLabel: localeText.nextMonth\n      })\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? PickersCalendarHeader.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * className applied to the root element.\n   */\n  className: PropTypes.string,\n  /**\n   * Overridable components.\n   * @default {}\n   * @deprecated Please use `slots`.\n   */\n  components: PropTypes.object,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   * @deprecated Please use `slotProps`.\n   */\n  componentsProps: PropTypes.object,\n  currentMonth: PropTypes.any.isRequired,\n  disabled: PropTypes.bool,\n  disableFuture: PropTypes.bool,\n  disablePast: PropTypes.bool,\n  labelId: PropTypes.string,\n  maxDate: PropTypes.any.isRequired,\n  minDate: PropTypes.any.isRequired,\n  onMonthChange: PropTypes.func.isRequired,\n  onViewChange: PropTypes.func,\n  reduceAnimations: PropTypes.bool.isRequired,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  timezone: PropTypes.string.isRequired,\n  view: PropTypes.oneOf(['day', 'month', 'year']).isRequired,\n  views: PropTypes.arrayOf(PropTypes.oneOf(['day', 'month', 'year']).isRequired).isRequired\n} : void 0;\nexport { PickersCalendarHeader };"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,cAAc,EAAE,UAAU,EAAE,eAAe,EAAE,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,EAAE,cAAc,EAAE,MAAM,EAAE,kBAAkB,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,CAAC;EACnQC,UAAU,GAAG,CAAC,YAAY,CAAC;AAC7B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,IAAI,MAAM,oBAAoB;AACrC,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,YAAY;AACtE,OAAOC,UAAU,MAAM,0BAA0B;AACjD,SAASC,aAAa,EAAEC,QAAQ,QAAQ,6BAA6B;AACrE,SAASC,0BAA0B,QAAQ,4CAA4C;AACvF,SAASC,iBAAiB,QAAQ,UAAU;AAC5C,SAASC,oBAAoB,QAAQ,8CAA8C;AACnF,SAASC,wBAAwB,EAAEC,oBAAoB,QAAQ,uCAAuC;AACtG,SAASC,oCAAoC,EAAEC,4BAA4B,QAAQ,gCAAgC;AACnH,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,cAAc,EAAE,CAAC,gBAAgB,CAAC;IAClCC,KAAK,EAAE,CAAC,OAAO,CAAC;IAChBC,gBAAgB,EAAE,CAAC,kBAAkB,CAAC;IACtCC,cAAc,EAAE,CAAC,gBAAgB;EACnC,CAAC;EACD,OAAOvB,cAAc,CAACkB,KAAK,EAAET,oCAAoC,EAAEQ,OAAO,CAAC;AAC7E,CAAC;AACD,MAAMO,yBAAyB,GAAG5B,MAAM,CAAC,KAAK,EAAE;EAC9C6B,IAAI,EAAE,0BAA0B;EAChCC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACV;AAC3C,CAAC,CAAC,CAAC;EACDW,OAAO,EAAE,MAAM;EACfC,UAAU,EAAE,QAAQ;EACpBC,SAAS,EAAE,EAAE;EACbC,YAAY,EAAE,CAAC;EACfC,WAAW,EAAE,EAAE;EACfC,YAAY,EAAE,EAAE;EAChB;EACAC,SAAS,EAAE,EAAE;EACbC,SAAS,EAAE;AACb,CAAC,CAAC;AACF,MAAMC,mCAAmC,GAAG1C,MAAM,CAAC,KAAK,EAAE;EACxD6B,IAAI,EAAE,0BAA0B;EAChCC,IAAI,EAAE,gBAAgB;EACtBC,iBAAiB,EAAEA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACT;AAC3C,CAAC,CAAC,CAAC,CAAC;EACFmB;AACF,CAAC,KAAKlD,QAAQ,CAAC;EACbyC,OAAO,EAAE,MAAM;EACfU,QAAQ,EAAE,QAAQ;EAClBT,UAAU,EAAE,QAAQ;EACpBU,MAAM,EAAE,SAAS;EACjBC,WAAW,EAAE;AACf,CAAC,EAAEH,KAAK,CAACI,UAAU,CAACC,KAAK,EAAE;EACzBC,UAAU,EAAEN,KAAK,CAACI,UAAU,CAACG;AAC/B,CAAC,CAAC,CAAC;AACH,MAAMC,0BAA0B,GAAGnD,MAAM,CAAC,KAAK,EAAE;EAC/C6B,IAAI,EAAE,0BAA0B;EAChCC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACR;AAC3C,CAAC,CAAC,CAAC;EACDqB,WAAW,EAAE;AACf,CAAC,CAAC;AACF,MAAMM,qCAAqC,GAAGpD,MAAM,CAACK,UAAU,EAAE;EAC/DwB,IAAI,EAAE,0BAA0B;EAChCC,IAAI,EAAE,kBAAkB;EACxBC,iBAAiB,EAAEA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACP;AAC3C,CAAC,CAAC,CAAC,CAAC;EACFN;AACF,CAAC,KAAK3B,QAAQ,CAAC;EACbqD,WAAW,EAAE;AACf,CAAC,EAAE1B,UAAU,CAACiC,IAAI,KAAK,MAAM,IAAI;EAC/B,CAAC,IAAIvC,4BAA4B,CAACa,cAAc,EAAE,GAAG;IACnD2B,SAAS,EAAE;EACb;AACF,CAAC,CAAC,CAAC;AACH,MAAMC,mCAAmC,GAAGvD,MAAM,CAACS,iBAAiB,EAAE;EACpEoB,IAAI,EAAE,0BAA0B;EAChCC,IAAI,EAAE,gBAAgB;EACtBC,iBAAiB,EAAEA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC3C,CAAC,CAAC,CAAC,CAAC;EACFgB;AACF,CAAC,MAAM;EACLa,UAAU,EAAE,WAAW;EACvBC,UAAU,EAAEd,KAAK,CAACe,WAAW,CAACC,MAAM,CAAC,WAAW,CAAC;EACjDL,SAAS,EAAE;AACb,CAAC,CAAC,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,qBAAqB,GAAG,aAAahE,KAAK,CAACiE,UAAU,CAAC,SAASD,qBAAqBA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACvG,IAAIC,IAAI,EAAEC,qBAAqB,EAAEC,KAAK,EAAEC,qBAAqB;EAC7D,MAAMC,UAAU,GAAG9D,aAAa,CAAC,CAAC;EAClC,MAAM+D,KAAK,GAAG9D,QAAQ,CAAC,CAAC;EACxB,MAAM+D,KAAK,GAAGrE,aAAa,CAAC;IAC1BqE,KAAK,EAAER,OAAO;IACdjC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFP,KAAK;MACLiD,SAAS;MACTC,UAAU;MACVC,YAAY,EAAEC,KAAK;MACnBC,QAAQ;MACRC,aAAa;MACbC,WAAW;MACXC,OAAO;MACPC,OAAO;MACPC,aAAa;MACbC,YAAY;MACZ5B,IAAI;MACJ6B,gBAAgB;MAChBC,KAAK;MACLC,OAAO;MACPC,SAAS;MACTC;IACF,CAAC,GAAGhB,KAAK;IACTiB,KAAK,GAAG/F,6BAA6B,CAAC8E,KAAK,EAAE5E,SAAS,CAAC;EACzD,MAAM0B,UAAU,GAAGkD,KAAK;EACxB,MAAMjD,OAAO,GAAGF,iBAAiB,CAACmD,KAAK,CAAC;EACxC,MAAMkB,gBAAgB,GAAG,CAACxB,IAAI,GAAG,CAACC,qBAAqB,GAAG3C,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACI,gBAAgB,KAAK,IAAI,GAAGuC,qBAAqB,GAAGO,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACgB,gBAAgB,KAAK,IAAI,GAAGxB,IAAI,GAAGZ,qCAAqC;EAC9P,MAAMqC,qBAAqB,GAAGvF,YAAY,CAAC;IACzCwF,WAAW,EAAEF,gBAAgB;IAC7BG,iBAAiB,EAAEpB,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC7C,gBAAgB;IAC1EkE,eAAe,EAAE;MACfC,IAAI,EAAE,OAAO;MACb,YAAY,EAAEzB,UAAU,CAAC0B,oCAAoC,CAACzC,IAAI;IACpE,CAAC;IACDjC,UAAU;IACViE,SAAS,EAAEhE,OAAO,CAACK;EACrB,CAAC,CAAC;EACF,MAAMqE,cAAc,GAAG,CAAC7B,KAAK,GAAG,CAACC,qBAAqB,GAAG7C,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACK,cAAc,KAAK,IAAI,GAAGwC,qBAAqB,GAAGK,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACuB,cAAc,KAAK,IAAI,GAAG7B,KAAK,GAAGX,mCAAmC;EACxP;EACA,MAAMyC,aAAa,GAAG9F,YAAY,CAAC;MAC/BwF,WAAW,EAAEK,cAAc;MAC3BJ,iBAAiB,EAAEpB,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC5C,cAAc;MACxEP,UAAU,EAAE6E,SAAS;MACrBZ,SAAS,EAAEhE,OAAO,CAACM;IACrB,CAAC,CAAC;IACFuE,mBAAmB,GAAG1G,6BAA6B,CAACwG,aAAa,EAAErG,UAAU,CAAC;EAChF,MAAMwG,eAAe,GAAGA,CAAA,KAAMnB,aAAa,CAACX,KAAK,CAAC+B,SAAS,CAAC1B,KAAK,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;EAC9E,MAAM2B,mBAAmB,GAAGA,CAAA,KAAMrB,aAAa,CAACX,KAAK,CAAC+B,SAAS,CAAC1B,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;EACpF,MAAM4B,mBAAmB,GAAG1F,oBAAoB,CAAC8D,KAAK,EAAE;IACtDE,aAAa;IACbE,OAAO;IACPQ;EACF,CAAC,CAAC;EACF,MAAMiB,uBAAuB,GAAG5F,wBAAwB,CAAC+D,KAAK,EAAE;IAC9DG,WAAW;IACXE,OAAO;IACPO;EACF,CAAC,CAAC;EACF,MAAMkB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIrB,KAAK,CAACsB,MAAM,KAAK,CAAC,IAAI,CAACxB,YAAY,IAAIN,QAAQ,EAAE;MACnD;IACF;IACA,IAAIQ,KAAK,CAACsB,MAAM,KAAK,CAAC,EAAE;MACtBxB,YAAY,CAACE,KAAK,CAACuB,IAAI,CAACC,EAAE,IAAIA,EAAE,KAAKtD,IAAI,CAAC,IAAI8B,KAAK,CAAC,CAAC,CAAC,CAAC;IACzD,CAAC,MAAM;MACL;MACA,MAAMyB,eAAe,GAAGzB,KAAK,CAAC0B,OAAO,CAACxD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;MACzD4B,YAAY,CAACE,KAAK,CAACyB,eAAe,CAAC,CAAC;IACtC;EACF,CAAC;;EAED;EACA,IAAIzB,KAAK,CAACsB,MAAM,KAAK,CAAC,IAAItB,KAAK,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;IAC7C,OAAO,IAAI;EACb;EACA,OAAO,aAAajE,KAAK,CAACU,yBAAyB,EAAEnC,QAAQ,CAAC,CAAC,CAAC,EAAE8F,KAAK,EAAE;IACvEnE,UAAU,EAAEA,UAAU;IACtBiE,SAAS,EAAEvF,IAAI,CAACuF,SAAS,EAAEhE,OAAO,CAACE,IAAI,CAAC;IACxCwC,GAAG,EAAEA,GAAG;IACR+C,QAAQ,EAAE,CAAC,aAAa5F,KAAK,CAACwB,mCAAmC,EAAE;MACjEqE,IAAI,EAAE,cAAc;MACpBC,OAAO,EAAER,gBAAgB;MACzBpF,UAAU,EAAEA;MACZ;MAAA;;MAEA,WAAW,EAAE,QAAQ;MACrBiE,SAAS,EAAEhE,OAAO,CAACG,cAAc;MACjCsF,QAAQ,EAAE,CAAC,aAAa9F,IAAI,CAACR,0BAA0B,EAAE;QACvD0E,gBAAgB,EAAEA,gBAAgB;QAClC+B,QAAQ,EAAE5C,KAAK,CAAC6C,MAAM,CAACxC,KAAK,EAAE,cAAc,CAAC;QAC7CoC,QAAQ,EAAE,aAAa9F,IAAI,CAACmC,0BAA0B,EAAE;UACtDgE,EAAE,EAAE/B,OAAO;UACXhE,UAAU,EAAEA,UAAU;UACtBiE,SAAS,EAAEhE,OAAO,CAACI,KAAK;UACxBqF,QAAQ,EAAEzC,KAAK,CAAC6C,MAAM,CAACxC,KAAK,EAAE,cAAc;QAC9C,CAAC;MACH,CAAC,CAAC,EAAES,KAAK,CAACsB,MAAM,GAAG,CAAC,IAAI,CAAC9B,QAAQ,IAAI,aAAa3D,IAAI,CAACwE,gBAAgB,EAAE/F,QAAQ,CAAC,CAAC,CAAC,EAAEgG,qBAAqB,EAAE;QAC3GqB,QAAQ,EAAE,aAAa9F,IAAI,CAAC+E,cAAc,EAAEtG,QAAQ,CAAC,CAAC,CAAC,EAAEyG,mBAAmB,CAAC;MAC/E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,EAAE,aAAalF,IAAI,CAACjB,IAAI,EAAE;MAC1BqH,EAAE,EAAE/D,IAAI,KAAK,KAAK;MAClByD,QAAQ,EAAE,aAAa9F,IAAI,CAACN,oBAAoB,EAAE;QAChDY,KAAK,EAAEA,KAAK;QACZiD,SAAS,EAAEA,SAAS;QACpB8C,cAAc,EAAEhB,mBAAmB;QACnCiB,kBAAkB,EAAEf,uBAAuB;QAC3CgB,aAAa,EAAEnD,UAAU,CAACoD,aAAa;QACvCC,UAAU,EAAEtB,eAAe;QAC3BuB,cAAc,EAAEpB,mBAAmB;QACnCqB,SAAS,EAAEvD,UAAU,CAACwD;MACxB,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGnE,qBAAqB,CAACoE,SAAS,GAAG;EACxE;EACA;EACA;EACA;EACA;AACF;AACA;EACE3G,OAAO,EAAExB,SAAS,CAACoI,MAAM;EACzB;AACF;AACA;EACE5C,SAAS,EAAExF,SAAS,CAACqI,MAAM;EAC3B;AACF;AACA;AACA;AACA;EACE1D,UAAU,EAAE3E,SAAS,CAACoI,MAAM;EAC5B;AACF;AACA;AACA;AACA;EACEE,eAAe,EAAEtI,SAAS,CAACoI,MAAM;EACjCxD,YAAY,EAAE5E,SAAS,CAACuI,GAAG,CAACC,UAAU;EACtC1D,QAAQ,EAAE9E,SAAS,CAACyI,IAAI;EACxB1D,aAAa,EAAE/E,SAAS,CAACyI,IAAI;EAC7BzD,WAAW,EAAEhF,SAAS,CAACyI,IAAI;EAC3BlD,OAAO,EAAEvF,SAAS,CAACqI,MAAM;EACzBpD,OAAO,EAAEjF,SAAS,CAACuI,GAAG,CAACC,UAAU;EACjCtD,OAAO,EAAElF,SAAS,CAACuI,GAAG,CAACC,UAAU;EACjCrD,aAAa,EAAEnF,SAAS,CAAC0I,IAAI,CAACF,UAAU;EACxCpD,YAAY,EAAEpF,SAAS,CAAC0I,IAAI;EAC5BrD,gBAAgB,EAAErF,SAAS,CAACyI,IAAI,CAACD,UAAU;EAC3C;AACF;AACA;AACA;EACE9D,SAAS,EAAE1E,SAAS,CAACoI,MAAM;EAC3B;AACF;AACA;AACA;EACE3G,KAAK,EAAEzB,SAAS,CAACoI,MAAM;EACvB;AACF;AACA;EACEO,EAAE,EAAE3I,SAAS,CAAC4I,SAAS,CAAC,CAAC5I,SAAS,CAAC6I,OAAO,CAAC7I,SAAS,CAAC4I,SAAS,CAAC,CAAC5I,SAAS,CAAC0I,IAAI,EAAE1I,SAAS,CAACoI,MAAM,EAAEpI,SAAS,CAACyI,IAAI,CAAC,CAAC,CAAC,EAAEzI,SAAS,CAAC0I,IAAI,EAAE1I,SAAS,CAACoI,MAAM,CAAC,CAAC;EACvJ3C,QAAQ,EAAEzF,SAAS,CAACqI,MAAM,CAACG,UAAU;EACrChF,IAAI,EAAExD,SAAS,CAAC8I,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAACN,UAAU;EAC1DlD,KAAK,EAAEtF,SAAS,CAAC6I,OAAO,CAAC7I,SAAS,CAAC8I,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAACN,UAAU,CAAC,CAACA;AACjF,CAAC,GAAG,KAAK,CAAC;AACV,SAASzE,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}