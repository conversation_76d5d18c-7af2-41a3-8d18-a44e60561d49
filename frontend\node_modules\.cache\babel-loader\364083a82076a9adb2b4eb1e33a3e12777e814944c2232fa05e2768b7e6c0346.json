{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['p.n.e.', 'n.e.'],\n  abbreviated: ['p.n.e.', 'n.e.'],\n  wide: ['przed naszą erą', 'naszej ery']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['I kw.', 'II kw.', 'III kw.', 'IV kw.'],\n  wide: ['I kwartał', 'II kwartał', 'III kwartał', 'IV kwartał']\n};\nvar monthValues = {\n  narrow: ['S', 'L', 'M', 'K', 'M', 'C', 'L', 'S', 'W', 'P', 'L', 'G'],\n  abbreviated: ['sty', 'lut', 'mar', 'kwi', 'maj', 'cze', 'lip', 'sie', 'wrz', 'paź', 'lis', 'gru'],\n  wide: ['stycze<PERSON>', 'luty', 'marzec', 'kwiecie<PERSON>', 'maj', 'czerwiec', 'lipiec', 'sierpie<PERSON>', 'wrzesień', 'październik', 'listopad', 'grudzień']\n};\nvar monthFormattingValues = {\n  narrow: ['s', 'l', 'm', 'k', 'm', 'c', 'l', 's', 'w', 'p', 'l', 'g'],\n  abbreviated: ['sty', 'lut', 'mar', 'kwi', 'maj', 'cze', 'lip', 'sie', 'wrz', 'paź', 'lis', 'gru'],\n  wide: ['stycznia', 'lutego', 'marca', 'kwietnia', 'maja', 'czerwca', 'lipca', 'sierpnia', 'września', 'października', 'listopada', 'grudnia']\n};\nvar dayValues = {\n  narrow: ['N', 'P', 'W', 'Ś', 'C', 'P', 'S'],\n  short: ['nie', 'pon', 'wto', 'śro', 'czw', 'pią', 'sob'],\n  abbreviated: ['niedz.', 'pon.', 'wt.', 'śr.', 'czw.', 'pt.', 'sob.'],\n  wide: ['niedziela', 'poniedziałek', 'wtorek', 'środa', 'czwartek', 'piątek', 'sobota']\n};\nvar dayFormattingValues = {\n  narrow: ['n', 'p', 'w', 'ś', 'c', 'p', 's'],\n  short: ['nie', 'pon', 'wto', 'śro', 'czw', 'pią', 'sob'],\n  abbreviated: ['niedz.', 'pon.', 'wt.', 'śr.', 'czw.', 'pt.', 'sob.'],\n  wide: ['niedziela', 'poniedziałek', 'wtorek', 'środa', 'czwartek', 'piątek', 'sobota']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'a',\n    pm: 'p',\n    midnight: 'półn.',\n    noon: 'poł',\n    morning: 'rano',\n    afternoon: 'popoł.',\n    evening: 'wiecz.',\n    night: 'noc'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'północ',\n    noon: 'południe',\n    morning: 'rano',\n    afternoon: 'popołudnie',\n    evening: 'wieczór',\n    night: 'noc'\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'północ',\n    noon: 'południe',\n    morning: 'rano',\n    afternoon: 'popołudnie',\n    evening: 'wieczór',\n    night: 'noc'\n  }\n};\nvar dayPeriodFormattingValues = {\n  narrow: {\n    am: 'a',\n    pm: 'p',\n    midnight: 'o półn.',\n    noon: 'w poł.',\n    morning: 'rano',\n    afternoon: 'po poł.',\n    evening: 'wiecz.',\n    night: 'w nocy'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'o północy',\n    noon: 'w południe',\n    morning: 'rano',\n    afternoon: 'po południu',\n    evening: 'wieczorem',\n    night: 'w nocy'\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'o północy',\n    noon: 'w południe',\n    morning: 'rano',\n    afternoon: 'po południu',\n    evening: 'wieczorem',\n    night: 'w nocy'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  return String(dirtyNumber);\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: monthFormattingValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide',\n    formattingValues: dayFormattingValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: dayPeriodFormattingValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "monthFormattingValues", "dayV<PERSON><PERSON>", "short", "dayFormattingValues", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "dayPeriodFormattingValues", "ordinalNumber", "dirtyNumber", "_options", "String", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "formattingValues", "defaultFormattingWidth", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/pl/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['p.n.e.', 'n.e.'],\n  abbreviated: ['p.n.e.', 'n.e.'],\n  wide: ['przed naszą erą', 'naszej ery']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['I kw.', 'II kw.', 'III kw.', 'IV kw.'],\n  wide: ['I kwartał', 'II kwartał', 'III kwartał', 'IV kwartał']\n};\nvar monthValues = {\n  narrow: ['S', 'L', 'M', 'K', 'M', 'C', 'L', 'S', 'W', 'P', 'L', 'G'],\n  abbreviated: ['sty', 'lut', 'mar', 'kwi', 'maj', 'cze', 'lip', 'sie', 'wrz', 'paź', 'lis', 'gru'],\n  wide: ['stycze<PERSON>', 'luty', 'marzec', 'kwiecie<PERSON>', 'maj', 'czerwiec', 'lipiec', 'sierpie<PERSON>', 'wrzesień', 'październik', 'listopad', 'grudzień']\n};\nvar monthFormattingValues = {\n  narrow: ['s', 'l', 'm', 'k', 'm', 'c', 'l', 's', 'w', 'p', 'l', 'g'],\n  abbreviated: ['sty', 'lut', 'mar', 'kwi', 'maj', 'cze', 'lip', 'sie', 'wrz', 'paź', 'lis', 'gru'],\n  wide: ['stycznia', 'lutego', 'marca', 'kwietnia', 'maja', 'czerwca', 'lipca', 'sierpnia', 'września', 'października', 'listopada', 'grudnia']\n};\nvar dayValues = {\n  narrow: ['N', 'P', 'W', 'Ś', 'C', 'P', 'S'],\n  short: ['nie', 'pon', 'wto', 'śro', 'czw', 'pią', 'sob'],\n  abbreviated: ['niedz.', 'pon.', 'wt.', 'śr.', 'czw.', 'pt.', 'sob.'],\n  wide: ['niedziela', 'poniedziałek', 'wtorek', 'środa', 'czwartek', 'piątek', 'sobota']\n};\nvar dayFormattingValues = {\n  narrow: ['n', 'p', 'w', 'ś', 'c', 'p', 's'],\n  short: ['nie', 'pon', 'wto', 'śro', 'czw', 'pią', 'sob'],\n  abbreviated: ['niedz.', 'pon.', 'wt.', 'śr.', 'czw.', 'pt.', 'sob.'],\n  wide: ['niedziela', 'poniedziałek', 'wtorek', 'środa', 'czwartek', 'piątek', 'sobota']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'a',\n    pm: 'p',\n    midnight: 'półn.',\n    noon: 'poł',\n    morning: 'rano',\n    afternoon: 'popoł.',\n    evening: 'wiecz.',\n    night: 'noc'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'północ',\n    noon: 'południe',\n    morning: 'rano',\n    afternoon: 'popołudnie',\n    evening: 'wieczór',\n    night: 'noc'\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'północ',\n    noon: 'południe',\n    morning: 'rano',\n    afternoon: 'popołudnie',\n    evening: 'wieczór',\n    night: 'noc'\n  }\n};\nvar dayPeriodFormattingValues = {\n  narrow: {\n    am: 'a',\n    pm: 'p',\n    midnight: 'o półn.',\n    noon: 'w poł.',\n    morning: 'rano',\n    afternoon: 'po poł.',\n    evening: 'wiecz.',\n    night: 'w nocy'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'o północy',\n    noon: 'w południe',\n    morning: 'rano',\n    afternoon: 'po południu',\n    evening: 'wieczorem',\n    night: 'w nocy'\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'o północy',\n    noon: 'w południe',\n    morning: 'rano',\n    afternoon: 'po południu',\n    evening: 'wieczorem',\n    night: 'w nocy'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  return String(dirtyNumber);\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: monthFormattingValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide',\n    formattingValues: dayFormattingValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: dayPeriodFormattingValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;EAC1BC,WAAW,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;EAC/BC,IAAI,EAAE,CAAC,iBAAiB,EAAE,YAAY;AACxC,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC;EACrDC,IAAI,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY;AAC/D,CAAC;AACD,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACjGC,IAAI,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,EAAE,UAAU,EAAE,UAAU;AAC5I,CAAC;AACD,IAAIG,qBAAqB,GAAG;EAC1BL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACjGC,IAAI,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,cAAc,EAAE,WAAW,EAAE,SAAS;AAC9I,CAAC;AACD,IAAII,SAAS,GAAG;EACdN,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CO,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACxDN,WAAW,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;EACpEC,IAAI,EAAE,CAAC,WAAW,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ;AACvF,CAAC;AACD,IAAIM,mBAAmB,GAAG;EACxBR,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CO,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACxDN,WAAW,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;EACpEC,IAAI,EAAE,CAAC,WAAW,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ;AACvF,CAAC;AACD,IAAIO,eAAe,GAAG;EACpBT,MAAM,EAAE;IACNU,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT,CAAC;EACDhB,WAAW,EAAE;IACXS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT,CAAC;EACDf,IAAI,EAAE;IACJQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BlB,MAAM,EAAE;IACNU,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT,CAAC;EACDhB,WAAW,EAAE;IACXS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE;EACT,CAAC;EACDf,IAAI,EAAE;IACJQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,QAAQ,EAAE;EAChE,OAAOC,MAAM,CAACF,WAAW,CAAC;AAC5B,CAAC;AACD,IAAIG,QAAQ,GAAG;EACbJ,aAAa,EAAEA,aAAa;EAC5BK,GAAG,EAAE1B,eAAe,CAAC;IACnB2B,MAAM,EAAE1B,SAAS;IACjB2B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE7B,eAAe,CAAC;IACvB2B,MAAM,EAAEtB,aAAa;IACrBuB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAE/B,eAAe,CAAC;IACrB2B,MAAM,EAAErB,WAAW;IACnBsB,YAAY,EAAE,MAAM;IACpBI,gBAAgB,EAAEzB,qBAAqB;IACvC0B,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EACFC,GAAG,EAAElC,eAAe,CAAC;IACnB2B,MAAM,EAAEnB,SAAS;IACjBoB,YAAY,EAAE,MAAM;IACpBI,gBAAgB,EAAEtB,mBAAmB;IACrCuB,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EACFE,SAAS,EAAEnC,eAAe,CAAC;IACzB2B,MAAM,EAAEhB,eAAe;IACvBiB,YAAY,EAAE,MAAM;IACpBI,gBAAgB,EAAEZ,yBAAyB;IAC3Ca,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeR,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}