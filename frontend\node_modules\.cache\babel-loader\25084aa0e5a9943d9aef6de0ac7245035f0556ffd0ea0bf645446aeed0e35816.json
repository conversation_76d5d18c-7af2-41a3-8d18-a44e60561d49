{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['v.Chr.', 'n.Chr.'],\n  abbreviated: ['v.Chr.', 'n.Chr.'],\n  wide: ['vor <PERSON><PERSON>', 'nach <PERSON><PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['1. Quartal', '2. Quartal', '3. Quartal', '4. Quartal']\n};\n\n// Note: in German, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['<PERSON>ä<PERSON>', 'Feb', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 'Aug', 'Sep', 'Okt', '<PERSON>', 'Dez'],\n  wide: ['Jänner', 'Februar', 'März', 'April', 'Mai', 'Juni', 'Juli', 'August', 'September', 'Oktober', 'November', 'Dezember']\n};\n\n// https://st.unicode.org/cldr-apps/v#/de_AT/Gregorian/\nvar formattingMonthValues = {\n  narrow: monthValues.narrow,\n  abbreviated: ['Jän.', 'Feb.', 'März', 'Apr.', 'Mai', 'Juni', 'Juli', 'Aug.', 'Sep.', 'Okt.', 'Nov.', 'Dez.'],\n  wide: monthValues.wide\n};\nvar dayValues = {\n  narrow: ['S', 'M', 'D', 'M', 'D', 'F', 'S'],\n  short: ['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa'],\n  abbreviated: ['So.', 'Mo.', 'Di.', 'Mi.', 'Do.', 'Fr.', 'Sa.'],\n  wide: ['Sonntag', 'Montag', 'Dienstag', 'Mittwoch', 'Donnerstag', 'Freitag', 'Samstag']\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/de.html#1881\nvar dayPeriodValues = {\n  narrow: {\n    am: 'vm.',\n    pm: 'nm.',\n    midnight: 'Mitternacht',\n    noon: 'Mittag',\n    morning: 'Morgen',\n    afternoon: 'Nachm.',\n    evening: 'Abend',\n    night: 'Nacht'\n  },\n  abbreviated: {\n    am: 'vorm.',\n    pm: 'nachm.',\n    midnight: 'Mitternacht',\n    noon: 'Mittag',\n    morning: 'Morgen',\n    afternoon: 'Nachmittag',\n    evening: 'Abend',\n    night: 'Nacht'\n  },\n  wide: {\n    am: 'vormittags',\n    pm: 'nachmittags',\n    midnight: 'Mitternacht',\n    noon: 'Mittag',\n    morning: 'Morgen',\n    afternoon: 'Nachmittag',\n    evening: 'Abend',\n    night: 'Nacht'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'vm.',\n    pm: 'nm.',\n    midnight: 'Mitternacht',\n    noon: 'Mittag',\n    morning: 'morgens',\n    afternoon: 'nachm.',\n    evening: 'abends',\n    night: 'nachts'\n  },\n  abbreviated: {\n    am: 'vorm.',\n    pm: 'nachm.',\n    midnight: 'Mitternacht',\n    noon: 'Mittag',\n    morning: 'morgens',\n    afternoon: 'nachmittags',\n    evening: 'abends',\n    night: 'nachts'\n  },\n  wide: {\n    am: 'vormittags',\n    pm: 'nachmittags',\n    midnight: 'Mitternacht',\n    noon: 'Mittag',\n    morning: 'morgens',\n    afternoon: 'nachmittags',\n    evening: 'abends',\n    night: 'nachts'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber) {\n  var number = Number(dirtyNumber);\n  return number + '.';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    formattingValues: formattingMonthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "formattingMonthValues", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "number", "Number", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "formattingValues", "day", "<PERSON><PERSON><PERSON><PERSON>", "defaultFormattingWidth"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/de-AT/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['v.Chr.', 'n.Chr.'],\n  abbreviated: ['v.Chr.', 'n.Chr.'],\n  wide: ['vor <PERSON><PERSON>', 'nach <PERSON><PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['1. Quartal', '2. Quartal', '3. Quartal', '4. Quartal']\n};\n\n// Note: in German, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['<PERSON>ä<PERSON>', 'Feb', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 'Aug', 'Sep', 'Okt', '<PERSON>', 'Dez'],\n  wide: ['Jänner', 'Februar', 'März', 'April', 'Mai', 'Juni', 'Juli', 'August', 'September', 'Oktober', 'November', 'Dezember']\n};\n\n// https://st.unicode.org/cldr-apps/v#/de_AT/Gregorian/\nvar formattingMonthValues = {\n  narrow: monthValues.narrow,\n  abbreviated: ['Jän.', 'Feb.', 'März', 'Apr.', 'Mai', 'Juni', 'Juli', 'Aug.', 'Sep.', 'Okt.', 'Nov.', 'Dez.'],\n  wide: monthValues.wide\n};\nvar dayValues = {\n  narrow: ['S', 'M', 'D', 'M', 'D', 'F', 'S'],\n  short: ['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa'],\n  abbreviated: ['So.', 'Mo.', 'Di.', 'Mi.', 'Do.', 'Fr.', 'Sa.'],\n  wide: ['Sonntag', 'Montag', 'Dienstag', 'Mittwoch', 'Donnerstag', 'Freitag', 'Samstag']\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/de.html#1881\nvar dayPeriodValues = {\n  narrow: {\n    am: 'vm.',\n    pm: 'nm.',\n    midnight: 'Mitternacht',\n    noon: 'Mittag',\n    morning: 'Morgen',\n    afternoon: 'Nachm.',\n    evening: 'Abend',\n    night: 'Nacht'\n  },\n  abbreviated: {\n    am: 'vorm.',\n    pm: 'nachm.',\n    midnight: 'Mitternacht',\n    noon: 'Mittag',\n    morning: 'Morgen',\n    afternoon: 'Nachmittag',\n    evening: 'Abend',\n    night: 'Nacht'\n  },\n  wide: {\n    am: 'vormittags',\n    pm: 'nachmittags',\n    midnight: 'Mitternacht',\n    noon: 'Mittag',\n    morning: 'Morgen',\n    afternoon: 'Nachmittag',\n    evening: 'Abend',\n    night: 'Nacht'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'vm.',\n    pm: 'nm.',\n    midnight: 'Mitternacht',\n    noon: 'Mittag',\n    morning: 'morgens',\n    afternoon: 'nachm.',\n    evening: 'abends',\n    night: 'nachts'\n  },\n  abbreviated: {\n    am: 'vorm.',\n    pm: 'nachm.',\n    midnight: 'Mitternacht',\n    noon: 'Mittag',\n    morning: 'morgens',\n    afternoon: 'nachmittags',\n    evening: 'abends',\n    night: 'nachts'\n  },\n  wide: {\n    am: 'vormittags',\n    pm: 'nachmittags',\n    midnight: 'Mitternacht',\n    noon: 'Mittag',\n    morning: 'morgens',\n    afternoon: 'nachmittags',\n    evening: 'abends',\n    night: 'nachts'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber) {\n  var number = Number(dirtyNumber);\n  return number + '.';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    formattingValues: formattingMonthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;EAC5BC,WAAW,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;EACjCC,IAAI,EAAE,CAAC,cAAc,EAAE,eAAe;AACxC,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY;AAC/D,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACjGC,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU;AAC9H,CAAC;;AAED;AACA,IAAIG,qBAAqB,GAAG;EAC1BL,MAAM,EAAEI,WAAW,CAACJ,MAAM;EAC1BC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EAC5GC,IAAI,EAAEE,WAAW,CAACF;AACpB,CAAC;AACD,IAAII,SAAS,GAAG;EACdN,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CO,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDN,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS;AACxF,CAAC;;AAED;AACA,IAAIM,eAAe,GAAG;EACpBR,MAAM,EAAE;IACNS,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,KAAK;IACTC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDf,WAAW,EAAE;IACXQ,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,QAAQ;IACZC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDd,IAAI,EAAE;IACJO,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,aAAa;IACjBC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BjB,MAAM,EAAE;IACNS,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,KAAK;IACTC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT,CAAC;EACDf,WAAW,EAAE;IACXQ,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,QAAQ;IACZC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT,CAAC;EACDd,IAAI,EAAE;IACJO,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,aAAa;IACjBC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAE;EACtD,IAAIC,MAAM,GAAGC,MAAM,CAACF,WAAW,CAAC;EAChC,OAAOC,MAAM,GAAG,GAAG;AACrB,CAAC;AACD,IAAIE,QAAQ,GAAG;EACbJ,aAAa,EAAEA,aAAa;EAC5BK,GAAG,EAAEzB,eAAe,CAAC;IACnB0B,MAAM,EAAEzB,SAAS;IACjB0B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE5B,eAAe,CAAC;IACvB0B,MAAM,EAAErB,aAAa;IACrBsB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAE9B,eAAe,CAAC;IACrB0B,MAAM,EAAEpB,WAAW;IACnByB,gBAAgB,EAAExB,qBAAqB;IACvCoB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,GAAG,EAAEhC,eAAe,CAAC;IACnB0B,MAAM,EAAElB,SAAS;IACjBmB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFM,SAAS,EAAEjC,eAAe,CAAC;IACzB0B,MAAM,EAAEhB,eAAe;IACvBiB,YAAY,EAAE,MAAM;IACpBI,gBAAgB,EAAEZ,yBAAyB;IAC3Ce,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}