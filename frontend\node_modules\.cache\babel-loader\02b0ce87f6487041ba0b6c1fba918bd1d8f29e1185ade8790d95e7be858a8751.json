{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\"; // https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1621 - #1630\nvar eraValues = {\n  narrow: ['ઈસપૂ', 'ઈસ'],\n  abbreviated: ['ઈ.સ.પૂર્વે', 'ઈ.સ.'],\n  wide: ['ઈસવીસન પૂર્વે', 'ઈસવીસન']\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1631 - #1654\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['1લો ત્રિમાસ', '2જો ત્રિમાસ', '3જો ત્રિમાસ', '4થો ત્રિમાસ']\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\n\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1655 - #1726\nvar monthValues = {\n  narrow: ['જા', 'ફે', 'મા', 'એ', 'મે', 'જૂ', 'જુ', 'ઓ', 'સ', 'ઓ', 'ન', 'ડિ'],\n  abbreviated: ['જાન્યુ', 'ફેબ્રુ', 'માર્ચ', 'એપ્રિલ', 'મે', 'જૂન', 'જુલાઈ', 'ઑગસ્ટ', 'સપ્ટે', 'ઓક્ટો', 'નવે', 'ડિસે'],\n  wide: ['જાન્યુઆરી', 'ફેબ્રુઆરી', 'માર્ચ', 'એપ્રિલ', 'મે', 'જૂન', 'જુલાઇ', 'ઓગસ્ટ', 'સપ્ટેમ્બર', 'ઓક્ટોબર', 'નવેમ્બર', 'ડિસેમ્બર']\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1727 - #1768\nvar dayValues = {\n  narrow: ['ર', 'સો', 'મં', 'બુ', 'ગુ', 'શુ', 'શ'],\n  short: ['ર', 'સો', 'મં', 'બુ', 'ગુ', 'શુ', 'શ'],\n  abbreviated: ['રવિ', 'સોમ', 'મંગળ', 'બુધ', 'ગુરુ', 'શુક્ર', 'શનિ'],\n  wide: ['રવિવાર' /* Sunday */, 'સોમવાર' /* Monday */, 'મંગળવાર' /* Tuesday */, 'બુધવાર' /* Wednesday */, 'ગુરુવાર' /* Thursday */, 'શુક્રવાર' /* Friday */, 'શનિવાર' /* Saturday */]\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1783 - #1824\nvar dayPeriodValues = {\n  narrow: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'મ.રાત્રિ',\n    noon: 'બ.',\n    morning: 'સવારે',\n    afternoon: 'બપોરે',\n    evening: 'સાંજે',\n    night: 'રાત્રે'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: '​મધ્યરાત્રિ',\n    noon: 'બપોરે',\n    morning: 'સવારે',\n    afternoon: 'બપોરે',\n    evening: 'સાંજે',\n    night: 'રાત્રે'\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: '​મધ્યરાત્રિ',\n    noon: 'બપોરે',\n    morning: 'સવારે',\n    afternoon: 'બપોરે',\n    evening: 'સાંજે',\n    night: 'રાત્રે'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'મ.રાત્રિ',\n    noon: 'બપોરે',\n    morning: 'સવારે',\n    afternoon: 'બપોરે',\n    evening: 'સાંજે',\n    night: 'રાત્રે'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'મધ્યરાત્રિ',\n    noon: 'બપોરે',\n    morning: 'સવારે',\n    afternoon: 'બપોરે',\n    evening: 'સાંજે',\n    night: 'રાત્રે'\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: '​મધ્યરાત્રિ',\n    noon: 'બપોરે',\n    morning: 'સવારે',\n    afternoon: 'બપોરે',\n    evening: 'સાંજે',\n    night: 'રાત્રે'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  return String(dirtyNumber);\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "String", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/gu/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\"; // https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1621 - #1630\nvar eraValues = {\n  narrow: ['ઈસપૂ', 'ઈસ'],\n  abbreviated: ['ઈ.સ.પૂર્વે', 'ઈ.સ.'],\n  wide: ['ઈસવીસન પૂર્વે', 'ઈસવીસન']\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1631 - #1654\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['1લો ત્રિમાસ', '2જો ત્રિમાસ', '3જો ત્રિમાસ', '4થો ત્રિમાસ']\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\n\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1655 - #1726\nvar monthValues = {\n  narrow: ['જા', 'ફે', 'મા', 'એ', 'મે', 'જૂ', 'જુ', 'ઓ', 'સ', 'ઓ', 'ન', 'ડિ'],\n  abbreviated: ['જાન્યુ', 'ફેબ્રુ', 'માર્ચ', 'એપ્રિલ', 'મે', 'જૂન', 'જુલાઈ', 'ઑગસ્ટ', 'સપ્ટે', 'ઓક્ટો', 'નવે', 'ડિસે'],\n  wide: ['જાન્યુઆરી', 'ફેબ્રુઆરી', 'માર્ચ', 'એપ્રિલ', 'મે', 'જૂન', 'જુલાઇ', 'ઓગસ્ટ', 'સપ્ટેમ્બર', 'ઓક્ટોબર', 'નવેમ્બર', 'ડિસેમ્બર']\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1727 - #1768\nvar dayValues = {\n  narrow: ['ર', 'સો', 'મં', 'બુ', 'ગુ', 'શુ', 'શ'],\n  short: ['ર', 'સો', 'મં', 'બુ', 'ગુ', 'શુ', 'શ'],\n  abbreviated: ['રવિ', 'સોમ', 'મંગળ', 'બુધ', 'ગુરુ', 'શુક્ર', 'શનિ'],\n  wide: ['રવિવાર' /* Sunday */, 'સોમવાર' /* Monday */, 'મંગળવાર' /* Tuesday */, 'બુધવાર' /* Wednesday */, 'ગુરુવાર' /* Thursday */, 'શુક્રવાર' /* Friday */, 'શનિવાર' /* Saturday */]\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1783 - #1824\nvar dayPeriodValues = {\n  narrow: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'મ.રાત્રિ',\n    noon: 'બ.',\n    morning: 'સવારે',\n    afternoon: 'બપોરે',\n    evening: 'સાંજે',\n    night: 'રાત્રે'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: '​મધ્યરાત્રિ',\n    noon: 'બપોરે',\n    morning: 'સવારે',\n    afternoon: 'બપોરે',\n    evening: 'સાંજે',\n    night: 'રાત્રે'\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: '​મધ્યરાત્રિ',\n    noon: 'બપોરે',\n    morning: 'સવારે',\n    afternoon: 'બપોરે',\n    evening: 'સાંજે',\n    night: 'રાત્રે'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'મ.રાત્રિ',\n    noon: 'બપોરે',\n    morning: 'સવારે',\n    afternoon: 'બપોરે',\n    evening: 'સાંજે',\n    night: 'રાત્રે'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'મધ્યરાત્રિ',\n    noon: 'બપોરે',\n    morning: 'સવારે',\n    afternoon: 'બપોરે',\n    evening: 'સાંજે',\n    night: 'રાત્રે'\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: '​મધ્યરાત્રિ',\n    noon: 'બપોરે',\n    morning: 'સવારે',\n    afternoon: 'બપોરે',\n    evening: 'સાંજે',\n    night: 'રાત્રે'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  return String(dirtyNumber);\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC,CAAC,CAAC;AACtE;AACA,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC;EACtBC,WAAW,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC;EACnCC,IAAI,EAAE,CAAC,eAAe,EAAE,QAAQ;AAClC,CAAC;;AAED;AACA;AACA,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa;AACnE,CAAC;;AAED;AACA;AACA;AACA;;AAEA;AACA;AACA,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;EAC3EC,WAAW,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC;EACpHC,IAAI,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU;AAClI,CAAC;;AAED;AACA;AACA,IAAIG,SAAS,GAAG;EACdL,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;EAChDM,KAAK,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;EAC/CL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC;EAClEC,IAAI,EAAE,CAAC,QAAQ,CAAC,cAAc,QAAQ,CAAC,cAAc,SAAS,CAAC,eAAe,QAAQ,CAAC,iBAAiB,SAAS,CAAC,gBAAgB,UAAU,CAAC,cAAc,QAAQ,CAAC;AACtK,CAAC;;AAED;AACA;AACA,IAAIK,eAAe,GAAG;EACpBP,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BhB,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,QAAQ,EAAE;EAChE,OAAOC,MAAM,CAACF,WAAW,CAAC;AAC5B,CAAC;AACD,IAAIG,QAAQ,GAAG;EACbJ,aAAa,EAAEA,aAAa;EAC5BK,GAAG,EAAExB,eAAe,CAAC;IACnByB,MAAM,EAAExB,SAAS;IACjByB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE3B,eAAe,CAAC;IACvByB,MAAM,EAAEpB,aAAa;IACrBqB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAE7B,eAAe,CAAC;IACrByB,MAAM,EAAEnB,WAAW;IACnBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFI,GAAG,EAAE9B,eAAe,CAAC;IACnByB,MAAM,EAAElB,SAAS;IACjBmB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,SAAS,EAAE/B,eAAe,CAAC;IACzByB,MAAM,EAAEhB,eAAe;IACvBiB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEd,yBAAyB;IAC3Ce,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}