{"ast": null, "code": "export default function addLeadingZeros(number, targetLength) {\n  var sign = number < 0 ? '-' : '';\n  var output = Math.abs(number).toString();\n  while (output.length < targetLength) {\n    output = '0' + output;\n  }\n  return sign + output;\n}", "map": {"version": 3, "names": ["addLeadingZeros", "number", "targetLength", "sign", "output", "Math", "abs", "toString", "length"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/_lib/addLeadingZeros/index.js"], "sourcesContent": ["export default function addLeadingZeros(number, targetLength) {\n  var sign = number < 0 ? '-' : '';\n  var output = Math.abs(number).toString();\n  while (output.length < targetLength) {\n    output = '0' + output;\n  }\n  return sign + output;\n}"], "mappings": "AAAA,eAAe,SAASA,eAAeA,CAACC,MAAM,EAAEC,YAAY,EAAE;EAC5D,IAAIC,IAAI,GAAGF,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;EAChC,IAAIG,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACL,MAAM,CAAC,CAACM,QAAQ,CAAC,CAAC;EACxC,OAAOH,MAAM,CAACI,MAAM,GAAGN,YAAY,EAAE;IACnCE,MAAM,GAAG,GAAG,GAAGA,MAAM;EACvB;EACA,OAAOD,IAAI,GAAGC,MAAM;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}