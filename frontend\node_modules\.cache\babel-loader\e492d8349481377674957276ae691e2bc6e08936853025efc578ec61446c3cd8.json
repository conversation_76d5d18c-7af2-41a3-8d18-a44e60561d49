{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"'ថ្ងៃ'eeee'ស​ប្តា​ហ៍​មុនម៉ោង' p\",\n  yesterday: \"'ម្សិលមិញនៅម៉ោង' p\",\n  today: \"'ថ្ងៃនេះម៉ោង' p\",\n  tomorrow: \"'ថ្ងៃស្អែកម៉ោង' p\",\n  nextWeek: \"'ថ្ងៃ'eeee'ស​ប្តា​ហ៍​ក្រោយម៉ោង' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/km/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: \"'ថ្ងៃ'eeee'ស​ប្តា​ហ៍​មុនម៉ោង' p\",\n  yesterday: \"'ម្សិលមិញនៅម៉ោង' p\",\n  today: \"'ថ្ងៃនេះម៉ោង' p\",\n  tomorrow: \"'ថ្ងៃស្អែកម៉ោង' p\",\n  nextWeek: \"'ថ្ងៃ'eeee'ស​ប្តា​ហ៍​ក្រោយម៉ោង' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,iCAAiC;EAC3CC,SAAS,EAAE,oBAAoB;EAC/BC,KAAK,EAAE,iBAAiB;EACxBC,QAAQ,EAAE,mBAAmB;EAC7BC,QAAQ,EAAE,mCAAmC;EAC7CC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC9E,OAAOX,oBAAoB,CAACQ,KAAK,CAAC;AACpC,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}