{"name": "taekwondo-crm-frontend", "version": "1.0.0", "description": "Frontend React application for Taekwondo Academy CRM System", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.8.1", "axios": "^1.6.2", "@mui/material": "^5.15.0", "@mui/icons-material": "^5.15.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@emotion/cache": "^11.11.0", "@mui/x-data-grid": "^6.18.2", "@mui/x-date-pickers": "^6.18.2", "@mui/lab": "^5.0.0-alpha.155", "recharts": "^2.8.0", "date-fns": "^2.30.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.4.0", "react-hot-toast": "^2.4.1", "xlsx": "^0.18.5", "file-saver": "^2.0.5", "stylis": "^4.3.0", "stylis-plugin-rtl": "^2.1.1", "dayjs": "^1.11.10"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}