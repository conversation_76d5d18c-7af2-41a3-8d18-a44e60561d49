{"ast": null, "code": "import { createIsAfterIgnoreDatePart } from '../time-utils';\nexport const validateTime = ({\n  adapter,\n  value,\n  props\n}) => {\n  if (value === null) {\n    return null;\n  }\n  const {\n    minTime,\n    maxTime,\n    minutesStep,\n    shouldDisableClock,\n    shouldDisableTime,\n    disableIgnoringDatePartForTimeValidation = false,\n    disablePast,\n    disableFuture,\n    timezone\n  } = props;\n  const now = adapter.utils.dateWithTimezone(undefined, timezone);\n  const isAfter = createIsAfterIgnoreDatePart(disableIgnoringDatePartForTimeValidation, adapter.utils);\n  switch (true) {\n    case !adapter.utils.isValid(value):\n      return 'invalidDate';\n    case Boolean(minTime && isAfter(minTime, value)):\n      return 'minTime';\n    case Boolean(maxTime && isAfter(value, maxTime)):\n      return 'maxTime';\n    case Boolean(disableFuture && adapter.utils.isAfter(value, now)):\n      return 'disableFuture';\n    case Boolean(disablePast && adapter.utils.isBefore(value, now)):\n      return 'disablePast';\n    case Boolean(shouldDisableTime && shouldDisableTime(value, 'hours')):\n      return 'shouldDisableTime-hours';\n    case Boolean(shouldDisableTime && shouldDisableTime(value, 'minutes')):\n      return 'shouldDisableTime-minutes';\n    case Boolean(shouldDisableTime && shouldDisableTime(value, 'seconds')):\n      return 'shouldDisableTime-seconds';\n    case Boolean(shouldDisableClock && shouldDisableClock(adapter.utils.getHours(value), 'hours')):\n      return 'shouldDisableClock-hours';\n    case Boolean(shouldDisableClock && shouldDisableClock(adapter.utils.getMinutes(value), 'minutes')):\n      return 'shouldDisableClock-minutes';\n    case Boolean(shouldDisableClock && shouldDisableClock(adapter.utils.getSeconds(value), 'seconds')):\n      return 'shouldDisableClock-seconds';\n    case Boolean(minutesStep && adapter.utils.getMinutes(value) % minutesStep !== 0):\n      return 'minutesStep';\n    default:\n      return null;\n  }\n};", "map": {"version": 3, "names": ["createIsAfterIgnoreDatePart", "validateTime", "adapter", "value", "props", "minTime", "maxTime", "minutesStep", "shouldDisableClock", "shouldDisableTime", "disableIgnoringDatePartForTimeValidation", "disablePast", "disableFuture", "timezone", "now", "utils", "dateWithTimezone", "undefined", "isAfter", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "isBefore", "getHours", "getMinutes", "getSeconds"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/x-date-pickers/internals/utils/validation/validateTime.js"], "sourcesContent": ["import { createIsAfterIgnoreDatePart } from '../time-utils';\nexport const validateTime = ({\n  adapter,\n  value,\n  props\n}) => {\n  if (value === null) {\n    return null;\n  }\n  const {\n    minTime,\n    maxTime,\n    minutesStep,\n    shouldDisableClock,\n    shouldDisableTime,\n    disableIgnoringDatePartForTimeValidation = false,\n    disablePast,\n    disableFuture,\n    timezone\n  } = props;\n  const now = adapter.utils.dateWithTimezone(undefined, timezone);\n  const isAfter = createIsAfterIgnoreDatePart(disableIgnoringDatePartForTimeValidation, adapter.utils);\n  switch (true) {\n    case !adapter.utils.isValid(value):\n      return 'invalidDate';\n    case Boolean(minTime && isAfter(minTime, value)):\n      return 'minTime';\n    case Boolean(maxTime && isAfter(value, maxTime)):\n      return 'maxTime';\n    case Boolean(disableFuture && adapter.utils.isAfter(value, now)):\n      return 'disableFuture';\n    case Boolean(disablePast && adapter.utils.isBefore(value, now)):\n      return 'disablePast';\n    case Boolean(shouldDisableTime && shouldDisableTime(value, 'hours')):\n      return 'shouldDisableTime-hours';\n    case Boolean(shouldDisableTime && shouldDisableTime(value, 'minutes')):\n      return 'shouldDisableTime-minutes';\n    case Boolean(shouldDisableTime && shouldDisableTime(value, 'seconds')):\n      return 'shouldDisableTime-seconds';\n    case Boolean(shouldDisableClock && shouldDisableClock(adapter.utils.getHours(value), 'hours')):\n      return 'shouldDisableClock-hours';\n    case Boolean(shouldDisableClock && shouldDisableClock(adapter.utils.getMinutes(value), 'minutes')):\n      return 'shouldDisableClock-minutes';\n    case Boolean(shouldDisableClock && shouldDisableClock(adapter.utils.getSeconds(value), 'seconds')):\n      return 'shouldDisableClock-seconds';\n    case Boolean(minutesStep && adapter.utils.getMinutes(value) % minutesStep !== 0):\n      return 'minutesStep';\n    default:\n      return null;\n  }\n};"], "mappings": "AAAA,SAASA,2BAA2B,QAAQ,eAAe;AAC3D,OAAO,MAAMC,YAAY,GAAGA,CAAC;EAC3BC,OAAO;EACPC,KAAK;EACLC;AACF,CAAC,KAAK;EACJ,IAAID,KAAK,KAAK,IAAI,EAAE;IAClB,OAAO,IAAI;EACb;EACA,MAAM;IACJE,OAAO;IACPC,OAAO;IACPC,WAAW;IACXC,kBAAkB;IAClBC,iBAAiB;IACjBC,wCAAwC,GAAG,KAAK;IAChDC,WAAW;IACXC,aAAa;IACbC;EACF,CAAC,GAAGT,KAAK;EACT,MAAMU,GAAG,GAAGZ,OAAO,CAACa,KAAK,CAACC,gBAAgB,CAACC,SAAS,EAAEJ,QAAQ,CAAC;EAC/D,MAAMK,OAAO,GAAGlB,2BAA2B,CAACU,wCAAwC,EAAER,OAAO,CAACa,KAAK,CAAC;EACpG,QAAQ,IAAI;IACV,KAAK,CAACb,OAAO,CAACa,KAAK,CAACI,OAAO,CAAChB,KAAK,CAAC;MAChC,OAAO,aAAa;IACtB,KAAKiB,OAAO,CAACf,OAAO,IAAIa,OAAO,CAACb,OAAO,EAAEF,KAAK,CAAC,CAAC;MAC9C,OAAO,SAAS;IAClB,KAAKiB,OAAO,CAACd,OAAO,IAAIY,OAAO,CAACf,KAAK,EAAEG,OAAO,CAAC,CAAC;MAC9C,OAAO,SAAS;IAClB,KAAKc,OAAO,CAACR,aAAa,IAAIV,OAAO,CAACa,KAAK,CAACG,OAAO,CAACf,KAAK,EAAEW,GAAG,CAAC,CAAC;MAC9D,OAAO,eAAe;IACxB,KAAKM,OAAO,CAACT,WAAW,IAAIT,OAAO,CAACa,KAAK,CAACM,QAAQ,CAAClB,KAAK,EAAEW,GAAG,CAAC,CAAC;MAC7D,OAAO,aAAa;IACtB,KAAKM,OAAO,CAACX,iBAAiB,IAAIA,iBAAiB,CAACN,KAAK,EAAE,OAAO,CAAC,CAAC;MAClE,OAAO,yBAAyB;IAClC,KAAKiB,OAAO,CAACX,iBAAiB,IAAIA,iBAAiB,CAACN,KAAK,EAAE,SAAS,CAAC,CAAC;MACpE,OAAO,2BAA2B;IACpC,KAAKiB,OAAO,CAACX,iBAAiB,IAAIA,iBAAiB,CAACN,KAAK,EAAE,SAAS,CAAC,CAAC;MACpE,OAAO,2BAA2B;IACpC,KAAKiB,OAAO,CAACZ,kBAAkB,IAAIA,kBAAkB,CAACN,OAAO,CAACa,KAAK,CAACO,QAAQ,CAACnB,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;MAC5F,OAAO,0BAA0B;IACnC,KAAKiB,OAAO,CAACZ,kBAAkB,IAAIA,kBAAkB,CAACN,OAAO,CAACa,KAAK,CAACQ,UAAU,CAACpB,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC;MAChG,OAAO,4BAA4B;IACrC,KAAKiB,OAAO,CAACZ,kBAAkB,IAAIA,kBAAkB,CAACN,OAAO,CAACa,KAAK,CAACS,UAAU,CAACrB,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC;MAChG,OAAO,4BAA4B;IACrC,KAAKiB,OAAO,CAACb,WAAW,IAAIL,OAAO,CAACa,KAAK,CAACQ,UAAU,CAACpB,KAAK,CAAC,GAAGI,WAAW,KAAK,CAAC,CAAC;MAC9E,OAAO,aAAa;IACtB;MACE,OAAO,IAAI;EACf;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}