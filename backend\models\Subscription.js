const mongoose = require('mongoose');

const SubscriptionSchema = new mongoose.Schema({
  student: {
    type: mongoose.Schema.ObjectId,
    ref: 'Student',
    required: [true, 'الطالب مطلوب']
  },
  subscriptionType: {
    type: String,
    required: [true, 'نوع الاشتراك مطلوب'],
    enum: {
      values: ['monthly', 'quarterly', 'semi-annual', 'annual'],
      message: 'نوع الاشتراك يجب أن يكون شهري، ربع سنوي، نصف سنوي، أو سنوي'
    }
  },
  startDate: {
    type: Date,
    required: [true, 'تاريخ بداية الاشتراك مطلوب']
  },
  endDate: {
    type: Date,
    required: [true, 'تاريخ نهاية الاشتراك مطلوب']
  },
  amount: {
    type: Number,
    required: [true, 'مبلغ الاشتراك مطلوب'],
    min: [0, 'مبلغ الاشتراك يجب أن يكون أكبر من أو يساوي صفر']
  },
  currency: {
    type: String,
    default: 'SAR',
    enum: {
      values: ['SAR', 'USD', 'EUR'],
      message: 'العملة يجب أن تكون ريال سعودي، دولار أمريكي، أو يورو'
    }
  },
  paymentStatus: {
    type: String,
    required: [true, 'حالة الدفع مطلوبة'],
    enum: {
      values: ['paid', 'pending', 'overdue', 'cancelled'],
      message: 'حالة الدفع يجب أن تكون مدفوع، معلق، متأخر، أو ملغي'
    },
    default: 'pending'
  },
  paymentMethod: {
    type: String,
    enum: {
      values: ['cash', 'card', 'bank_transfer', 'online'],
      message: 'طريقة الدفع يجب أن تكون نقد، بطاقة، تحويل بنكي، أو أونلاين'
    }
  },
  paymentDate: {
    type: Date
  },
  receiptNumber: {
    type: String,
    trim: true
  },
  discount: {
    amount: {
      type: Number,
      default: 0,
      min: [0, 'مبلغ الخصم يجب أن يكون أكبر من أو يساوي صفر']
    },
    percentage: {
      type: Number,
      default: 0,
      min: [0, 'نسبة الخصم يجب أن تكون أكبر من أو تساوي صفر'],
      max: [100, 'نسبة الخصم يجب أن تكون أقل من أو تساوي 100']
    },
    reason: {
      type: String,
      trim: true,
      maxlength: [200, 'سبب الخصم يجب أن يكون أقل من 200 حرف']
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  autoRenew: {
    type: Boolean,
    default: false
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [500, 'الملاحظات يجب أن تكون أقل من 500 حرف']
  },
  createdBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Create indexes for better performance
SubscriptionSchema.index({ student: 1 });
SubscriptionSchema.index({ startDate: 1 });
SubscriptionSchema.index({ endDate: 1 });
SubscriptionSchema.index({ paymentStatus: 1 });
SubscriptionSchema.index({ isActive: 1 });
SubscriptionSchema.index({ subscriptionType: 1 });

// Virtual for subscription status
SubscriptionSchema.virtual('status').get(function() {
  const now = new Date();
  const endDate = new Date(this.endDate);
  const daysUntilExpiry = Math.ceil((endDate - now) / (1000 * 60 * 60 * 24));
  
  if (!this.isActive) return 'inactive';
  if (endDate < now) return 'expired';
  if (daysUntilExpiry <= 7) return 'expiring_soon';
  return 'active';
});

// Virtual for subscription type in Arabic
SubscriptionSchema.virtual('subscriptionTypeArabic').get(function() {
  const typeMap = {
    'monthly': 'شهري',
    'quarterly': 'ربع سنوي',
    'semi-annual': 'نصف سنوي',
    'annual': 'سنوي'
  };
  return typeMap[this.subscriptionType] || this.subscriptionType;
});

// Virtual for payment status in Arabic
SubscriptionSchema.virtual('paymentStatusArabic').get(function() {
  const statusMap = {
    'paid': 'مدفوع',
    'pending': 'معلق',
    'overdue': 'متأخر',
    'cancelled': 'ملغي'
  };
  return statusMap[this.paymentStatus] || this.paymentStatus;
});

// Virtual for payment method in Arabic
SubscriptionSchema.virtual('paymentMethodArabic').get(function() {
  if (!this.paymentMethod) return null;
  const methodMap = {
    'cash': 'نقد',
    'card': 'بطاقة',
    'bank_transfer': 'تحويل بنكي',
    'online': 'أونلاين'
  };
  return methodMap[this.paymentMethod] || this.paymentMethod;
});

// Virtual for status in Arabic
SubscriptionSchema.virtual('statusArabic').get(function() {
  const statusMap = {
    'active': 'نشط',
    'expired': 'منتهي الصلاحية',
    'expiring_soon': 'ينتهي قريباً',
    'inactive': 'غير نشط'
  };
  return statusMap[this.status] || this.status;
});

// Virtual for days until expiry
SubscriptionSchema.virtual('daysUntilExpiry').get(function() {
  const now = new Date();
  const endDate = new Date(this.endDate);
  return Math.ceil((endDate - now) / (1000 * 60 * 60 * 24));
});

// Virtual for final amount after discount
SubscriptionSchema.virtual('finalAmount').get(function() {
  let finalAmount = this.amount;
  
  if (this.discount.percentage > 0) {
    finalAmount = finalAmount * (1 - this.discount.percentage / 100);
  }
  
  if (this.discount.amount > 0) {
    finalAmount = finalAmount - this.discount.amount;
  }
  
  return Math.max(0, finalAmount);
});

// Pre-save middleware to validate dates
SubscriptionSchema.pre('save', function(next) {
  if (this.startDate >= this.endDate) {
    next(new Error('تاريخ نهاية الاشتراك يجب أن يكون بعد تاريخ البداية'));
  }
  
  // Set payment date if status is paid and no payment date is set
  if (this.paymentStatus === 'paid' && !this.paymentDate) {
    this.paymentDate = new Date();
  }
  
  next();
});

// Static method to get expiring subscriptions
SubscriptionSchema.statics.getExpiringSoon = function(days = 7) {
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + days);
  
  return this.find({
    isActive: true,
    endDate: { $lte: futureDate, $gte: new Date() }
  }).populate('student', 'fullName phone studentId');
};

// Static method to get expired subscriptions
SubscriptionSchema.statics.getExpired = function() {
  return this.find({
    isActive: true,
    endDate: { $lt: new Date() }
  }).populate('student', 'fullName phone studentId');
};

// Static method to get active subscriptions
SubscriptionSchema.statics.getActive = function() {
  return this.find({
    isActive: true,
    endDate: { $gte: new Date() }
  }).populate('student', 'fullName phone studentId');
};

// Instance method to mark as paid
SubscriptionSchema.methods.markAsPaid = async function(paymentMethod, receiptNumber, updatedBy) {
  this.paymentStatus = 'paid';
  this.paymentDate = new Date();
  this.paymentMethod = paymentMethod;
  this.receiptNumber = receiptNumber;
  this.updatedBy = updatedBy;
  await this.save();
};

// Instance method to cancel subscription
SubscriptionSchema.methods.cancel = async function(updatedBy) {
  this.paymentStatus = 'cancelled';
  this.isActive = false;
  this.updatedBy = updatedBy;
  await this.save();
};

module.exports = mongoose.model('Subscription', SubscriptionSchema);
