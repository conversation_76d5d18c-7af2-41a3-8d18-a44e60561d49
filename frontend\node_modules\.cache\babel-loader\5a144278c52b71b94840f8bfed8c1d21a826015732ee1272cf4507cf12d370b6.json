{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\n\n/**\n * @ignore - internal component.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction Ripple(props) {\n  const {\n    className,\n    classes,\n    pulsate = false,\n    rippleX,\n    rippleY,\n    rippleSize,\n    in: inProp,\n    onExited,\n    timeout\n  } = props;\n  const [leaving, setLeaving] = React.useState(false);\n  const rippleClassName = clsx(className, classes.ripple, classes.rippleVisible, pulsate && classes.ripplePulsate);\n  const rippleStyles = {\n    width: rippleSize,\n    height: rippleSize,\n    top: -(rippleSize / 2) + rippleY,\n    left: -(rippleSize / 2) + rippleX\n  };\n  const childClassName = clsx(classes.child, leaving && classes.childLeaving, pulsate && classes.childPulsate);\n  if (!inProp && !leaving) {\n    setLeaving(true);\n  }\n  React.useEffect(() => {\n    if (!inProp && onExited != null) {\n      // react-transition-group#onExited\n      const timeoutId = setTimeout(onExited, timeout);\n      return () => {\n        clearTimeout(timeoutId);\n      };\n    }\n    return undefined;\n  }, [onExited, inProp, timeout]);\n  return /*#__PURE__*/_jsx(\"span\", {\n    className: rippleClassName,\n    style: rippleStyles,\n    children: /*#__PURE__*/_jsx(\"span\", {\n      className: childClassName\n    })\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? Ripple.propTypes = {\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object.isRequired,\n  className: PropTypes.string,\n  /**\n   * @ignore - injected from TransitionGroup\n   */\n  in: PropTypes.bool,\n  /**\n   * @ignore - injected from TransitionGroup\n   */\n  onExited: PropTypes.func,\n  /**\n   * If `true`, the ripple pulsates, typically indicating the keyboard focus state of an element.\n   */\n  pulsate: PropTypes.bool,\n  /**\n   * Diameter of the ripple.\n   */\n  rippleSize: PropTypes.number,\n  /**\n   * Horizontal position of the ripple center.\n   */\n  rippleX: PropTypes.number,\n  /**\n   * Vertical position of the ripple center.\n   */\n  rippleY: PropTypes.number,\n  /**\n   * exit delay\n   */\n  timeout: PropTypes.number.isRequired\n} : void 0;\nexport default Ripple;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "jsx", "_jsx", "<PERSON><PERSON><PERSON>", "props", "className", "classes", "pulsate", "rippleX", "rippleY", "rippleSize", "in", "inProp", "onExited", "timeout", "leaving", "setLeaving", "useState", "rippleClassName", "ripple", "rippleVisible", "ripplePulsate", "rippleStyles", "width", "height", "top", "left", "childClassName", "child", "childLeaving", "child<PERSON><PERSON>sate", "useEffect", "timeoutId", "setTimeout", "clearTimeout", "undefined", "style", "children", "process", "env", "NODE_ENV", "propTypes", "object", "isRequired", "string", "bool", "func", "number"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/material/ButtonBase/Ripple.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\n\n/**\n * @ignore - internal component.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction Ripple(props) {\n  const {\n    className,\n    classes,\n    pulsate = false,\n    rippleX,\n    rippleY,\n    rippleSize,\n    in: inProp,\n    onExited,\n    timeout\n  } = props;\n  const [leaving, setLeaving] = React.useState(false);\n  const rippleClassName = clsx(className, classes.ripple, classes.rippleVisible, pulsate && classes.ripplePulsate);\n  const rippleStyles = {\n    width: rippleSize,\n    height: rippleSize,\n    top: -(rippleSize / 2) + rippleY,\n    left: -(rippleSize / 2) + rippleX\n  };\n  const childClassName = clsx(classes.child, leaving && classes.childLeaving, pulsate && classes.childPulsate);\n  if (!inProp && !leaving) {\n    setLeaving(true);\n  }\n  React.useEffect(() => {\n    if (!inProp && onExited != null) {\n      // react-transition-group#onExited\n      const timeoutId = setTimeout(onExited, timeout);\n      return () => {\n        clearTimeout(timeoutId);\n      };\n    }\n    return undefined;\n  }, [onExited, inProp, timeout]);\n  return /*#__PURE__*/_jsx(\"span\", {\n    className: rippleClassName,\n    style: rippleStyles,\n    children: /*#__PURE__*/_jsx(\"span\", {\n      className: childClassName\n    })\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? Ripple.propTypes = {\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object.isRequired,\n  className: PropTypes.string,\n  /**\n   * @ignore - injected from TransitionGroup\n   */\n  in: PropTypes.bool,\n  /**\n   * @ignore - injected from TransitionGroup\n   */\n  onExited: PropTypes.func,\n  /**\n   * If `true`, the ripple pulsates, typically indicating the keyboard focus state of an element.\n   */\n  pulsate: PropTypes.bool,\n  /**\n   * Diameter of the ripple.\n   */\n  rippleSize: PropTypes.number,\n  /**\n   * Horizontal position of the ripple center.\n   */\n  rippleX: PropTypes.number,\n  /**\n   * Vertical position of the ripple center.\n   */\n  rippleY: PropTypes.number,\n  /**\n   * exit delay\n   */\n  timeout: PropTypes.number.isRequired\n} : void 0;\nexport default Ripple;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;;AAEvB;AACA;AACA;AACA,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,MAAMA,CAACC,KAAK,EAAE;EACrB,MAAM;IACJC,SAAS;IACTC,OAAO;IACPC,OAAO,GAAG,KAAK;IACfC,OAAO;IACPC,OAAO;IACPC,UAAU;IACVC,EAAE,EAAEC,MAAM;IACVC,QAAQ;IACRC;EACF,CAAC,GAAGV,KAAK;EACT,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGlB,KAAK,CAACmB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMC,eAAe,GAAGlB,IAAI,CAACK,SAAS,EAAEC,OAAO,CAACa,MAAM,EAAEb,OAAO,CAACc,aAAa,EAAEb,OAAO,IAAID,OAAO,CAACe,aAAa,CAAC;EAChH,MAAMC,YAAY,GAAG;IACnBC,KAAK,EAAEb,UAAU;IACjBc,MAAM,EAAEd,UAAU;IAClBe,GAAG,EAAE,EAAEf,UAAU,GAAG,CAAC,CAAC,GAAGD,OAAO;IAChCiB,IAAI,EAAE,EAAEhB,UAAU,GAAG,CAAC,CAAC,GAAGF;EAC5B,CAAC;EACD,MAAMmB,cAAc,GAAG3B,IAAI,CAACM,OAAO,CAACsB,KAAK,EAAEb,OAAO,IAAIT,OAAO,CAACuB,YAAY,EAAEtB,OAAO,IAAID,OAAO,CAACwB,YAAY,CAAC;EAC5G,IAAI,CAAClB,MAAM,IAAI,CAACG,OAAO,EAAE;IACvBC,UAAU,CAAC,IAAI,CAAC;EAClB;EACAlB,KAAK,CAACiC,SAAS,CAAC,MAAM;IACpB,IAAI,CAACnB,MAAM,IAAIC,QAAQ,IAAI,IAAI,EAAE;MAC/B;MACA,MAAMmB,SAAS,GAAGC,UAAU,CAACpB,QAAQ,EAAEC,OAAO,CAAC;MAC/C,OAAO,MAAM;QACXoB,YAAY,CAACF,SAAS,CAAC;MACzB,CAAC;IACH;IACA,OAAOG,SAAS;EAClB,CAAC,EAAE,CAACtB,QAAQ,EAAED,MAAM,EAAEE,OAAO,CAAC,CAAC;EAC/B,OAAO,aAAaZ,IAAI,CAAC,MAAM,EAAE;IAC/BG,SAAS,EAAEa,eAAe;IAC1BkB,KAAK,EAAEd,YAAY;IACnBe,QAAQ,EAAE,aAAanC,IAAI,CAAC,MAAM,EAAE;MAClCG,SAAS,EAAEsB;IACb,CAAC;EACH,CAAC,CAAC;AACJ;AACAW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGrC,MAAM,CAACsC,SAAS,GAAG;EACzD;AACF;AACA;EACEnC,OAAO,EAAEP,SAAS,CAAC2C,MAAM,CAACC,UAAU;EACpCtC,SAAS,EAAEN,SAAS,CAAC6C,MAAM;EAC3B;AACF;AACA;EACEjC,EAAE,EAAEZ,SAAS,CAAC8C,IAAI;EAClB;AACF;AACA;EACEhC,QAAQ,EAAEd,SAAS,CAAC+C,IAAI;EACxB;AACF;AACA;EACEvC,OAAO,EAAER,SAAS,CAAC8C,IAAI;EACvB;AACF;AACA;EACEnC,UAAU,EAAEX,SAAS,CAACgD,MAAM;EAC5B;AACF;AACA;EACEvC,OAAO,EAAET,SAAS,CAACgD,MAAM;EACzB;AACF;AACA;EACEtC,OAAO,EAAEV,SAAS,CAACgD,MAAM;EACzB;AACF;AACA;EACEjC,OAAO,EAAEf,SAAS,CAACgD,MAAM,CAACJ;AAC5B,CAAC,GAAG,KAAK,CAAC;AACV,eAAexC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}