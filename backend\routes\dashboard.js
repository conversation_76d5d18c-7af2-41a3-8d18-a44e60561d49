const express = require('express');
const { query, validationResult } = require('express-validator');
const Student = require('../models/Student');
const Subscription = require('../models/Subscription');
const Attendance = require('../models/Attendance');
const User = require('../models/User');
const { protect, adminOrCoach } = require('../middleware/auth');

const router = express.Router();

// Apply protection to all routes
router.use(protect);
router.use(adminOrCoach);

// @desc    Get dashboard overview statistics
// @route   GET /api/dashboard/overview
// @access  Private (Admin/Coach)
router.get('/overview', async (req, res) => {
  try {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - now.getDay());

    // Get basic counts
    const [
      totalStudents,
      activeStudents,
      totalSubscriptions,
      activeSubscriptions,
      expiredSubscriptions,
      expiringSoonSubscriptions,
      todayAttendance,
      weekAttendance,
      monthAttendance
    ] = await Promise.all([
      Student.countDocuments({ isActive: true }),
      Student.countDocuments({ isActive: true }),
      Subscription.countDocuments({ isActive: true }),
      Subscription.countDocuments({
        isActive: true,
        endDate: { $gte: now }
      }),
      Subscription.countDocuments({
        isActive: true,
        endDate: { $lt: now }
      }),
      Subscription.countDocuments({
        isActive: true,
        endDate: {
          $gte: now,
          $lte: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
        }
      }),
      Attendance.countDocuments({
        date: {
          $gte: new Date(now.getFullYear(), now.getMonth(), now.getDate()),
          $lt: new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1)
        }
      }),
      Attendance.countDocuments({
        date: { $gte: startOfWeek }
      }),
      Attendance.countDocuments({
        date: { $gte: startOfMonth }
      })
    ]);

    // Calculate revenue for current month
    const monthlyRevenue = await Subscription.aggregate([
      {
        $match: {
          paymentStatus: 'paid',
          paymentDate: { $gte: startOfMonth }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$finalAmount' }
        }
      }
    ]);

    // Get belt level distribution
    const beltDistribution = await Student.aggregate([
      {
        $match: { isActive: true }
      },
      {
        $group: {
          _id: '$beltLevel',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        students: {
          total: totalStudents,
          active: activeStudents
        },
        subscriptions: {
          total: totalSubscriptions,
          active: activeSubscriptions,
          expired: expiredSubscriptions,
          expiringSoon: expiringSoonSubscriptions
        },
        attendance: {
          today: todayAttendance,
          thisWeek: weekAttendance,
          thisMonth: monthAttendance
        },
        revenue: {
          thisMonth: monthlyRevenue[0]?.total || 0
        },
        beltDistribution: beltDistribution.map(item => ({
          beltLevel: item._id,
          count: item.count,
          beltLevelArabic: getBeltLevelArabic(item._id)
        }))
      }
    });

  } catch (error) {
    console.error('Get dashboard overview error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Get attendance statistics
// @route   GET /api/dashboard/attendance-stats
// @access  Private (Admin/Coach)
router.get('/attendance-stats', [
  query('period').optional().isIn(['week', 'month', 'year']).withMessage('الفترة يجب أن تكون أسبوع، شهر، أو سنة'),
  query('startDate').optional().isISO8601().withMessage('تاريخ البداية يجب أن يكون تاريخ صحيح'),
  query('endDate').optional().isISO8601().withMessage('تاريخ النهاية يجب أن يكون تاريخ صحيح')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const { period = 'month', startDate, endDate } = req.query;
    const now = new Date();
    let dateRange = {};

    if (startDate && endDate) {
      dateRange = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    } else {
      switch (period) {
        case 'week':
          const startOfWeek = new Date(now);
          startOfWeek.setDate(now.getDate() - now.getDay());
          dateRange = { $gte: startOfWeek };
          break;
        case 'month':
          const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
          dateRange = { $gte: startOfMonth };
          break;
        case 'year':
          const startOfYear = new Date(now.getFullYear(), 0, 1);
          dateRange = { $gte: startOfYear };
          break;
      }
    }

    // Get attendance by status
    const attendanceByStatus = await Attendance.aggregate([
      {
        $match: { date: dateRange }
      },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get attendance by class type
    const attendanceByClassType = await Attendance.aggregate([
      {
        $match: { date: dateRange }
      },
      {
        $group: {
          _id: '$classType',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get daily attendance for the period
    const dailyAttendance = await Attendance.aggregate([
      {
        $match: { date: dateRange }
      },
      {
        $group: {
          _id: {
            year: { $year: '$date' },
            month: { $month: '$date' },
            day: { $dayOfMonth: '$date' }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        byStatus: attendanceByStatus.map(item => ({
          status: item._id,
          statusArabic: getStatusArabic(item._id),
          count: item.count
        })),
        byClassType: attendanceByClassType.map(item => ({
          classType: item._id,
          classTypeArabic: getClassTypeArabic(item._id),
          count: item.count
        })),
        daily: dailyAttendance.map(item => ({
          date: `${item._id.year}-${String(item._id.month).padStart(2, '0')}-${String(item._id.day).padStart(2, '0')}`,
          count: item.count
        }))
      }
    });

  } catch (error) {
    console.error('Get attendance stats error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Get revenue statistics
// @route   GET /api/dashboard/revenue-stats
// @access  Private (Admin/Coach)
router.get('/revenue-stats', [
  query('period').optional().isIn(['month', 'quarter', 'year']).withMessage('الفترة يجب أن تكون شهر، ربع سنة، أو سنة'),
  query('year').optional().isInt({ min: 2020, max: 2030 }).withMessage('السنة يجب أن تكون بين 2020 و 2030')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const { period = 'month', year } = req.query;
    const currentYear = year ? parseInt(year) : new Date().getFullYear();

    let groupBy = {};
    let matchCondition = {
      paymentStatus: 'paid',
      paymentDate: {
        $gte: new Date(currentYear, 0, 1),
        $lt: new Date(currentYear + 1, 0, 1)
      }
    };

    switch (period) {
      case 'month':
        groupBy = {
          year: { $year: '$paymentDate' },
          month: { $month: '$paymentDate' }
        };
        break;
      case 'quarter':
        groupBy = {
          year: { $year: '$paymentDate' },
          quarter: {
            $ceil: { $divide: [{ $month: '$paymentDate' }, 3] }
          }
        };
        break;
      case 'year':
        groupBy = {
          year: { $year: '$paymentDate' }
        };
        matchCondition.paymentDate = {
          $gte: new Date(currentYear - 4, 0, 1),
          $lt: new Date(currentYear + 1, 0, 1)
        };
        break;
    }

    const revenueStats = await Subscription.aggregate([
      { $match: matchCondition },
      {
        $group: {
          _id: groupBy,
          totalRevenue: { $sum: '$finalAmount' },
          subscriptionCount: { $sum: 1 },
          averageAmount: { $avg: '$finalAmount' }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.quarter': 1 } }
    ]);

    // Get revenue by subscription type
    const revenueByType = await Subscription.aggregate([
      { $match: matchCondition },
      {
        $group: {
          _id: '$subscriptionType',
          totalRevenue: { $sum: '$finalAmount' },
          count: { $sum: 1 }
        }
      }
    ]);

    // Get revenue by payment method
    const revenueByPaymentMethod = await Subscription.aggregate([
      { $match: matchCondition },
      {
        $group: {
          _id: '$paymentMethod',
          totalRevenue: { $sum: '$finalAmount' },
          count: { $sum: 1 }
        }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        periodStats: revenueStats,
        bySubscriptionType: revenueByType.map(item => ({
          subscriptionType: item._id,
          subscriptionTypeArabic: getSubscriptionTypeArabic(item._id),
          totalRevenue: item.totalRevenue,
          count: item.count
        })),
        byPaymentMethod: revenueByPaymentMethod.map(item => ({
          paymentMethod: item._id,
          paymentMethodArabic: getPaymentMethodArabic(item._id),
          totalRevenue: item.totalRevenue,
          count: item.count
        }))
      }
    });

  } catch (error) {
    console.error('Get revenue stats error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Get recent activities
// @route   GET /api/dashboard/recent-activities
// @access  Private (Admin/Coach)
router.get('/recent-activities', [
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('الحد الأقصى يجب أن يكون بين 1 و 50')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const limit = parseInt(req.query.limit) || 10;

    // Get recent students
    const recentStudents = await Student.find({ isActive: true })
      .populate('createdBy', 'fullName')
      .sort({ createdAt: -1 })
      .limit(limit)
      .select('fullName studentId createdAt createdBy');

    // Get recent subscriptions
    const recentSubscriptions = await Subscription.find({ isActive: true })
      .populate('student', 'fullName studentId')
      .populate('createdBy', 'fullName')
      .sort({ createdAt: -1 })
      .limit(limit)
      .select('subscriptionType amount paymentStatus createdAt createdBy student');

    // Get recent attendance
    const recentAttendance = await Attendance.find()
      .populate('student', 'fullName studentId')
      .populate('createdBy', 'fullName')
      .sort({ createdAt: -1 })
      .limit(limit)
      .select('date status classType createdAt createdBy student');

    res.status(200).json({
      success: true,
      data: {
        recentStudents: recentStudents.map(student => ({
          ...student.toObject(),
          type: 'student',
          typeArabic: 'طالب جديد'
        })),
        recentSubscriptions: recentSubscriptions.map(subscription => ({
          ...subscription.toObject(),
          type: 'subscription',
          typeArabic: 'اشتراك جديد',
          subscriptionTypeArabic: getSubscriptionTypeArabic(subscription.subscriptionType)
        })),
        recentAttendance: recentAttendance.map(attendance => ({
          ...attendance.toObject(),
          type: 'attendance',
          typeArabic: 'حضور',
          statusArabic: getStatusArabic(attendance.status),
          classTypeArabic: getClassTypeArabic(attendance.classType)
        }))
      }
    });

  } catch (error) {
    console.error('Get recent activities error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// Helper functions for Arabic translations
function getBeltLevelArabic(beltLevel) {
  const translations = {
    'white': 'أبيض',
    'yellow': 'أصفر',
    'orange': 'برتقالي',
    'green': 'أخضر',
    'blue': 'أزرق',
    'brown': 'بني',
    'red': 'أحمر',
    'black-1st': 'أسود - الدان الأول',
    'black-2nd': 'أسود - الدان الثاني',
    'black-3rd': 'أسود - الدان الثالث',
    'black-4th': 'أسود - الدان الرابع',
    'black-5th': 'أسود - الدان الخامس',
    'black-6th': 'أسود - الدان السادس',
    'black-7th': 'أسود - الدان السابع',
    'black-8th': 'أسود - الدان الثامن',
    'black-9th': 'أسود - الدان التاسع'
  };
  return translations[beltLevel] || beltLevel;
}

function getStatusArabic(status) {
  const translations = {
    'present': 'حاضر',
    'absent': 'غائب',
    'late': 'متأخر',
    'excused': 'معذور'
  };
  return translations[status] || status;
}

function getClassTypeArabic(classType) {
  const translations = {
    'regular': 'عادية',
    'private': 'خاصة',
    'makeup': 'تعويضية',
    'competition': 'منافسة'
  };
  return translations[classType] || classType;
}

function getSubscriptionTypeArabic(subscriptionType) {
  const translations = {
    'monthly': 'شهري',
    'quarterly': 'ربع سنوي',
    'semi-annual': 'نصف سنوي',
    'annual': 'سنوي'
  };
  return translations[subscriptionType] || subscriptionType;
}

function getPaymentMethodArabic(paymentMethod) {
  const translations = {
    'cash': 'نقدي',
    'card': 'بطاقة',
    'bank_transfer': 'تحويل بنكي',
    'online': 'دفع إلكتروني'
  };
  return translations[paymentMethod] || paymentMethod;
}

module.exports = router;
