{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'mai puțin de o secundă',\n    other: 'mai puțin de {{count}} secunde'\n  },\n  xSeconds: {\n    one: '1 secundă',\n    other: '{{count}} secunde'\n  },\n  halfAMinute: 'jumătate de minut',\n  lessThanXMinutes: {\n    one: 'mai puțin de un minut',\n    other: 'mai puțin de {{count}} minute'\n  },\n  xMinutes: {\n    one: '1 minut',\n    other: '{{count}} minute'\n  },\n  aboutXHours: {\n    one: 'circa 1 oră',\n    other: 'circa {{count}} ore'\n  },\n  xHours: {\n    one: '1 oră',\n    other: '{{count}} ore'\n  },\n  xDays: {\n    one: '1 zi',\n    other: '{{count}} zile'\n  },\n  aboutXWeeks: {\n    one: 'circa o săptămână',\n    other: 'circa {{count}} săptămâni'\n  },\n  xWeeks: {\n    one: '1 săptămână',\n    other: '{{count}} săptămâni'\n  },\n  aboutXMonths: {\n    one: 'circa 1 lună',\n    other: 'circa {{count}} luni'\n  },\n  xMonths: {\n    one: '1 lună',\n    other: '{{count}} luni'\n  },\n  aboutXYears: {\n    one: 'circa 1 an',\n    other: 'circa {{count}} ani'\n  },\n  xYears: {\n    one: '1 an',\n    other: '{{count}} ani'\n  },\n  overXYears: {\n    one: 'peste 1 an',\n    other: 'peste {{count}} ani'\n  },\n  almostXYears: {\n    one: 'aproape 1 an',\n    other: 'aproape {{count}} ani'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'în ' + result;\n    } else {\n      return result + ' în urmă';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/ro/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'mai puțin de o secundă',\n    other: 'mai puțin de {{count}} secunde'\n  },\n  xSeconds: {\n    one: '1 secundă',\n    other: '{{count}} secunde'\n  },\n  halfAMinute: 'jumătate de minut',\n  lessThanXMinutes: {\n    one: 'mai puțin de un minut',\n    other: 'mai puțin de {{count}} minute'\n  },\n  xMinutes: {\n    one: '1 minut',\n    other: '{{count}} minute'\n  },\n  aboutXHours: {\n    one: 'circa 1 oră',\n    other: 'circa {{count}} ore'\n  },\n  xHours: {\n    one: '1 oră',\n    other: '{{count}} ore'\n  },\n  xDays: {\n    one: '1 zi',\n    other: '{{count}} zile'\n  },\n  aboutXWeeks: {\n    one: 'circa o săptămână',\n    other: 'circa {{count}} săptămâni'\n  },\n  xWeeks: {\n    one: '1 săptămână',\n    other: '{{count}} săptămâni'\n  },\n  aboutXMonths: {\n    one: 'circa 1 lună',\n    other: 'circa {{count}} luni'\n  },\n  xMonths: {\n    one: '1 lună',\n    other: '{{count}} luni'\n  },\n  aboutXYears: {\n    one: 'circa 1 an',\n    other: 'circa {{count}} ani'\n  },\n  xYears: {\n    one: '1 an',\n    other: '{{count}} ani'\n  },\n  overXYears: {\n    one: 'peste 1 an',\n    other: 'peste {{count}} ani'\n  },\n  almostXYears: {\n    one: 'aproape 1 an',\n    other: 'aproape {{count}} ani'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'în ' + result;\n    } else {\n      return result + ' în urmă';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,wBAAwB;IAC7BC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,mBAAmB;EAChCC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,uBAAuB;IAC5BC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAGL,MAAM;IACvB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,UAAU;IAC5B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}