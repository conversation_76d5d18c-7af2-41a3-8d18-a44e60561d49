{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { getMonthsInYear } from '../../utils/date-utils';\nexport const getDateSectionConfigFromFormatToken = (utils, formatToken) => {\n  const config = utils.formatTokenMap[formatToken];\n  if (config == null) {\n    throw new Error([`MUI: The token \"${formatToken}\" is not supported by the Date and Time Pickers.`, 'Please try using another token or open an issue on https://github.com/mui/mui-x/issues/new/choose if you think it should be supported.'].join('\\n'));\n  }\n  if (typeof config === 'string') {\n    return {\n      type: config,\n      contentType: config === 'meridiem' ? 'letter' : 'digit',\n      maxLength: undefined\n    };\n  }\n  return {\n    type: config.sectionType,\n    contentType: config.contentType,\n    maxLength: config.maxLength\n  };\n};\nconst getDeltaFromKeyCode = keyCode => {\n  switch (keyCode) {\n    case 'ArrowUp':\n      return 1;\n    case 'ArrowDown':\n      return -1;\n    case 'PageUp':\n      return 5;\n    case 'PageDown':\n      return -5;\n    default:\n      return 0;\n  }\n};\nexport const getDaysInWeekStr = (utils, timezone, format) => {\n  const elements = [];\n  const now = utils.dateWithTimezone(undefined, timezone);\n  const startDate = utils.startOfWeek(now);\n  const endDate = utils.endOfWeek(now);\n  let current = startDate;\n  while (utils.isBefore(current, endDate)) {\n    elements.push(current);\n    current = utils.addDays(current, 1);\n  }\n  return elements.map(weekDay => utils.formatByString(weekDay, format));\n};\nexport const getLetterEditingOptions = (utils, timezone, sectionType, format) => {\n  switch (sectionType) {\n    case 'month':\n      {\n        return getMonthsInYear(utils, utils.dateWithTimezone(undefined, timezone)).map(month => utils.formatByString(month, format));\n      }\n    case 'weekDay':\n      {\n        return getDaysInWeekStr(utils, timezone, format);\n      }\n    case 'meridiem':\n      {\n        const now = utils.dateWithTimezone(undefined, timezone);\n        return [utils.startOfDay(now), utils.endOfDay(now)].map(date => utils.formatByString(date, format));\n      }\n    default:\n      {\n        return [];\n      }\n  }\n};\nexport const cleanLeadingZeros = (utils, valueStr, size) => {\n  let cleanValueStr = valueStr;\n\n  // Remove the leading zeros\n  cleanValueStr = Number(cleanValueStr).toString();\n\n  // Add enough leading zeros to fill the section\n  while (cleanValueStr.length < size) {\n    cleanValueStr = `0${cleanValueStr}`;\n  }\n  return cleanValueStr;\n};\nexport const cleanDigitSectionValue = (utils, timezone, value, sectionBoundaries, section) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (section.type !== 'day' && section.contentType === 'digit-with-letter') {\n      throw new Error([`MUI: The token \"${section.format}\" is a digit format with letter in it.'\n             This type of format is only supported for 'day' sections`].join('\\n'));\n    }\n  }\n  if (section.type === 'day' && section.contentType === 'digit-with-letter') {\n    const date = utils.setDate(sectionBoundaries.longestMonth, value);\n    return utils.formatByString(date, section.format);\n  }\n\n  // queryValue without leading `0` (`01` => `1`)\n  const valueStr = value.toString();\n  if (section.hasLeadingZerosInInput) {\n    return cleanLeadingZeros(utils, valueStr, section.maxLength);\n  }\n  return valueStr;\n};\nexport const adjustSectionValue = (utils, timezone, section, keyCode, sectionsValueBoundaries, activeDate, stepsAttributes) => {\n  const delta = getDeltaFromKeyCode(keyCode);\n  const isStart = keyCode === 'Home';\n  const isEnd = keyCode === 'End';\n  const shouldSetAbsolute = section.value === '' || isStart || isEnd;\n  const adjustDigitSection = () => {\n    const sectionBoundaries = sectionsValueBoundaries[section.type]({\n      currentDate: activeDate,\n      format: section.format,\n      contentType: section.contentType\n    });\n    const getCleanValue = value => cleanDigitSectionValue(utils, timezone, value, sectionBoundaries, section);\n    const step = section.type === 'minutes' && stepsAttributes != null && stepsAttributes.minutesStep ? stepsAttributes.minutesStep : 1;\n    const currentSectionValue = parseInt(section.value, 10);\n    let newSectionValueNumber = currentSectionValue + delta * step;\n    if (shouldSetAbsolute) {\n      if (section.type === 'year' && !isEnd && !isStart) {\n        return utils.formatByString(utils.dateWithTimezone(undefined, timezone), section.format);\n      }\n      if (delta > 0 || isStart) {\n        newSectionValueNumber = sectionBoundaries.minimum;\n      } else {\n        newSectionValueNumber = sectionBoundaries.maximum;\n      }\n    }\n    if (newSectionValueNumber % step !== 0) {\n      if (delta < 0 || isStart) {\n        newSectionValueNumber += step - (step + newSectionValueNumber) % step; // for JS -3 % 5 = -3 (should be 2)\n      }\n      if (delta > 0 || isEnd) {\n        newSectionValueNumber -= newSectionValueNumber % step;\n      }\n    }\n    if (newSectionValueNumber > sectionBoundaries.maximum) {\n      return getCleanValue(sectionBoundaries.minimum + (newSectionValueNumber - sectionBoundaries.maximum - 1) % (sectionBoundaries.maximum - sectionBoundaries.minimum + 1));\n    }\n    if (newSectionValueNumber < sectionBoundaries.minimum) {\n      return getCleanValue(sectionBoundaries.maximum - (sectionBoundaries.minimum - newSectionValueNumber - 1) % (sectionBoundaries.maximum - sectionBoundaries.minimum + 1));\n    }\n    return getCleanValue(newSectionValueNumber);\n  };\n  const adjustLetterSection = () => {\n    const options = getLetterEditingOptions(utils, timezone, section.type, section.format);\n    if (options.length === 0) {\n      return section.value;\n    }\n    if (shouldSetAbsolute) {\n      if (delta > 0 || isStart) {\n        return options[0];\n      }\n      return options[options.length - 1];\n    }\n    const currentOptionIndex = options.indexOf(section.value);\n    const newOptionIndex = (currentOptionIndex + options.length + delta) % options.length;\n    return options[newOptionIndex];\n  };\n  if (section.contentType === 'digit' || section.contentType === 'digit-with-letter') {\n    return adjustDigitSection();\n  }\n  return adjustLetterSection();\n};\nexport const getSectionVisibleValue = (section, target) => {\n  let value = section.value || section.placeholder;\n  const hasLeadingZeros = target === 'non-input' ? section.hasLeadingZerosInFormat : section.hasLeadingZerosInInput;\n  if (target === 'non-input' && section.hasLeadingZerosInInput && !section.hasLeadingZerosInFormat) {\n    value = Number(value).toString();\n  }\n\n  // In the input, we add an empty character at the end of each section without leading zeros.\n  // This makes sure that `onChange` will always be fired.\n  // Otherwise, when your input value equals `1/dd/yyyy` (format `M/DD/YYYY` on DayJs),\n  // If you press `1`, on the first section, the new value is also `1/dd/yyyy`,\n  // So the browser will not fire the input `onChange`.\n  const shouldAddInvisibleSpace = ['input-rtl', 'input-ltr'].includes(target) && section.contentType === 'digit' && !hasLeadingZeros && value.length === 1;\n  if (shouldAddInvisibleSpace) {\n    value = `${value}\\u200e`;\n  }\n  if (target === 'input-rtl') {\n    value = `\\u2068${value}\\u2069`;\n  }\n  return value;\n};\nexport const cleanString = dirtyString => dirtyString.replace(/[\\u2066\\u2067\\u2068\\u2069]/g, '');\nexport const addPositionPropertiesToSections = (sections, isRTL) => {\n  let position = 0;\n  let positionInInput = isRTL ? 1 : 0;\n  const newSections = [];\n  for (let i = 0; i < sections.length; i += 1) {\n    const section = sections[i];\n    const renderedValue = getSectionVisibleValue(section, isRTL ? 'input-rtl' : 'input-ltr');\n    const sectionStr = `${section.startSeparator}${renderedValue}${section.endSeparator}`;\n    const sectionLength = cleanString(sectionStr).length;\n    const sectionLengthInInput = sectionStr.length;\n\n    // The ...InInput values consider the unicode characters but do include them in their indexes\n    const cleanedValue = cleanString(renderedValue);\n    const startInInput = positionInInput + renderedValue.indexOf(cleanedValue[0]) + section.startSeparator.length;\n    const endInInput = startInInput + cleanedValue.length;\n    newSections.push(_extends({}, section, {\n      start: position,\n      end: position + sectionLength,\n      startInInput,\n      endInInput\n    }));\n    position += sectionLength;\n    // Move position to the end of string associated to the current section\n    positionInInput += sectionLengthInInput;\n  }\n  return newSections;\n};\nconst getSectionPlaceholder = (utils, timezone, localeText, sectionConfig, sectionFormat) => {\n  switch (sectionConfig.type) {\n    case 'year':\n      {\n        return localeText.fieldYearPlaceholder({\n          digitAmount: utils.formatByString(utils.dateWithTimezone(undefined, timezone), sectionFormat).length,\n          format: sectionFormat\n        });\n      }\n    case 'month':\n      {\n        return localeText.fieldMonthPlaceholder({\n          contentType: sectionConfig.contentType,\n          format: sectionFormat\n        });\n      }\n    case 'day':\n      {\n        return localeText.fieldDayPlaceholder({\n          format: sectionFormat\n        });\n      }\n    case 'weekDay':\n      {\n        return localeText.fieldWeekDayPlaceholder({\n          contentType: sectionConfig.contentType,\n          format: sectionFormat\n        });\n      }\n    case 'hours':\n      {\n        return localeText.fieldHoursPlaceholder({\n          format: sectionFormat\n        });\n      }\n    case 'minutes':\n      {\n        return localeText.fieldMinutesPlaceholder({\n          format: sectionFormat\n        });\n      }\n    case 'seconds':\n      {\n        return localeText.fieldSecondsPlaceholder({\n          format: sectionFormat\n        });\n      }\n    case 'meridiem':\n      {\n        return localeText.fieldMeridiemPlaceholder({\n          format: sectionFormat\n        });\n      }\n    default:\n      {\n        return sectionFormat;\n      }\n  }\n};\nexport const changeSectionValueFormat = (utils, valueStr, currentFormat, newFormat) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (getDateSectionConfigFromFormatToken(utils, currentFormat).type === 'weekDay') {\n      throw new Error(\"changeSectionValueFormat doesn't support week day formats\");\n    }\n  }\n  return utils.formatByString(utils.parse(valueStr, currentFormat), newFormat);\n};\nconst isFourDigitYearFormat = (utils, timezone, format) => utils.formatByString(utils.dateWithTimezone(undefined, timezone), format).length === 4;\nexport const doesSectionFormatHaveLeadingZeros = (utils, timezone, contentType, sectionType, format) => {\n  if (contentType !== 'digit') {\n    return false;\n  }\n  const now = utils.dateWithTimezone(undefined, timezone);\n  switch (sectionType) {\n    // We can't use `changeSectionValueFormat`, because  `utils.parse('1', 'YYYY')` returns `1971` instead of `1`.\n    case 'year':\n      {\n        if (isFourDigitYearFormat(utils, timezone, format)) {\n          const formatted0001 = utils.formatByString(utils.setYear(now, 1), format);\n          return formatted0001 === '0001';\n        }\n        const formatted2001 = utils.formatByString(utils.setYear(now, 2001), format);\n        return formatted2001 === '01';\n      }\n    case 'month':\n      {\n        return utils.formatByString(utils.startOfYear(now), format).length > 1;\n      }\n    case 'day':\n      {\n        return utils.formatByString(utils.startOfMonth(now), format).length > 1;\n      }\n    case 'weekDay':\n      {\n        return utils.formatByString(utils.startOfWeek(now), format).length > 1;\n      }\n    case 'hours':\n      {\n        return utils.formatByString(utils.setHours(now, 1), format).length > 1;\n      }\n    case 'minutes':\n      {\n        return utils.formatByString(utils.setMinutes(now, 1), format).length > 1;\n      }\n    case 'seconds':\n      {\n        return utils.formatByString(utils.setSeconds(now, 1), format).length > 1;\n      }\n    default:\n      {\n        throw new Error('Invalid section type');\n      }\n  }\n};\nconst getEscapedPartsFromFormat = (utils, format) => {\n  const escapedParts = [];\n  const {\n    start: startChar,\n    end: endChar\n  } = utils.escapedCharacters;\n  const regExp = new RegExp(`(\\\\${startChar}[^\\\\${endChar}]*\\\\${endChar})+`, 'g');\n  let match = null;\n  // eslint-disable-next-line no-cond-assign\n  while (match = regExp.exec(format)) {\n    escapedParts.push({\n      start: match.index,\n      end: regExp.lastIndex - 1\n    });\n  }\n  return escapedParts;\n};\nexport const splitFormatIntoSections = (utils, timezone, localeText, format, date, formatDensity, shouldRespectLeadingZeros, isRTL) => {\n  let startSeparator = '';\n  const sections = [];\n  const now = utils.date();\n  const commitToken = token => {\n    if (token === '') {\n      return null;\n    }\n    const sectionConfig = getDateSectionConfigFromFormatToken(utils, token);\n    const hasLeadingZerosInFormat = doesSectionFormatHaveLeadingZeros(utils, timezone, sectionConfig.contentType, sectionConfig.type, token);\n    const hasLeadingZerosInInput = shouldRespectLeadingZeros ? hasLeadingZerosInFormat : sectionConfig.contentType === 'digit';\n    const isValidDate = date != null && utils.isValid(date);\n    let sectionValue = isValidDate ? utils.formatByString(date, token) : '';\n    let maxLength = null;\n    if (hasLeadingZerosInInput) {\n      if (hasLeadingZerosInFormat) {\n        maxLength = sectionValue === '' ? utils.formatByString(now, token).length : sectionValue.length;\n      } else {\n        if (sectionConfig.maxLength == null) {\n          throw new Error(`MUI: The token ${token} should have a 'maxDigitNumber' property on it's adapter`);\n        }\n        maxLength = sectionConfig.maxLength;\n        if (isValidDate) {\n          sectionValue = cleanLeadingZeros(utils, sectionValue, maxLength);\n        }\n      }\n    }\n    sections.push(_extends({}, sectionConfig, {\n      format: token,\n      maxLength,\n      value: sectionValue,\n      placeholder: getSectionPlaceholder(utils, timezone, localeText, sectionConfig, token),\n      hasLeadingZeros: hasLeadingZerosInFormat,\n      hasLeadingZerosInFormat,\n      hasLeadingZerosInInput,\n      startSeparator: sections.length === 0 ? startSeparator : '',\n      endSeparator: '',\n      modified: false\n    }));\n    return null;\n  };\n\n  // Expand the provided format\n  let formatExpansionOverflow = 10;\n  let prevFormat = format;\n  let nextFormat = utils.expandFormat(format);\n  while (nextFormat !== prevFormat) {\n    prevFormat = nextFormat;\n    nextFormat = utils.expandFormat(prevFormat);\n    formatExpansionOverflow -= 1;\n    if (formatExpansionOverflow < 0) {\n      throw new Error('MUI: The format expansion seems to be  enter in an infinite loop. Please open an issue with the format passed to the picker component');\n    }\n  }\n  const expandedFormat = nextFormat;\n\n  // Get start/end indexes of escaped sections\n  const escapedParts = getEscapedPartsFromFormat(utils, expandedFormat);\n\n  // This RegExp test if the beginning of a string correspond to a supported token\n  const isTokenStartRegExp = new RegExp(`^(${Object.keys(utils.formatTokenMap).sort((a, b) => b.length - a.length) // Sort to put longest word first\n  .join('|')})`, 'g') // used to get access to lastIndex state\n  ;\n  let currentTokenValue = '';\n  for (let i = 0; i < expandedFormat.length; i += 1) {\n    const escapedPartOfCurrentChar = escapedParts.find(escapeIndex => escapeIndex.start <= i && escapeIndex.end >= i);\n    const char = expandedFormat[i];\n    const isEscapedChar = escapedPartOfCurrentChar != null;\n    const potentialToken = `${currentTokenValue}${expandedFormat.slice(i)}`;\n    const regExpMatch = isTokenStartRegExp.test(potentialToken);\n    if (!isEscapedChar && char.match(/([A-Za-z]+)/) && regExpMatch) {\n      currentTokenValue = potentialToken.slice(0, isTokenStartRegExp.lastIndex);\n      i += isTokenStartRegExp.lastIndex - 1;\n    } else {\n      // If we are on the opening or closing character of an escaped part of the format,\n      // Then we ignore this character.\n      const isEscapeBoundary = isEscapedChar && (escapedPartOfCurrentChar == null ? void 0 : escapedPartOfCurrentChar.start) === i || (escapedPartOfCurrentChar == null ? void 0 : escapedPartOfCurrentChar.end) === i;\n      if (!isEscapeBoundary) {\n        commitToken(currentTokenValue);\n        currentTokenValue = '';\n        if (sections.length === 0) {\n          startSeparator += char;\n        } else {\n          sections[sections.length - 1].endSeparator += char;\n        }\n      }\n    }\n  }\n  commitToken(currentTokenValue);\n  return sections.map(section => {\n    const cleanSeparator = separator => {\n      let cleanedSeparator = separator;\n      if (isRTL && cleanedSeparator !== null && cleanedSeparator.includes(' ')) {\n        cleanedSeparator = `\\u2069${cleanedSeparator}\\u2066`;\n      }\n      if (formatDensity === 'spacious' && ['/', '.', '-'].includes(cleanedSeparator)) {\n        cleanedSeparator = ` ${cleanedSeparator} `;\n      }\n      return cleanedSeparator;\n    };\n    section.startSeparator = cleanSeparator(section.startSeparator);\n    section.endSeparator = cleanSeparator(section.endSeparator);\n    return section;\n  });\n};\n\n/**\n * Some date libraries like `dayjs` don't support parsing from date with escaped characters.\n * To make sure that the parsing works, we are building a format and a date without any separator.\n */\nexport const getDateFromDateSections = (utils, sections) => {\n  // If we have both a day and a weekDay section,\n  // Then we skip the weekDay in the parsing because libraries like dayjs can't parse complicated formats containing a weekDay.\n  // dayjs(dayjs().format('dddd MMMM D YYYY'), 'dddd MMMM D YYYY')) // returns `Invalid Date` even if the format is valid.\n  const shouldSkipWeekDays = sections.some(section => section.type === 'day');\n  const sectionFormats = [];\n  const sectionValues = [];\n  for (let i = 0; i < sections.length; i += 1) {\n    const section = sections[i];\n    const shouldSkip = shouldSkipWeekDays && section.type === 'weekDay';\n    if (!shouldSkip) {\n      sectionFormats.push(section.format);\n      sectionValues.push(getSectionVisibleValue(section, 'non-input'));\n    }\n  }\n  const formatWithoutSeparator = sectionFormats.join(' ');\n  const dateWithoutSeparatorStr = sectionValues.join(' ');\n  return utils.parse(dateWithoutSeparatorStr, formatWithoutSeparator);\n};\nexport const createDateStrForInputFromSections = (sections, isRTL) => {\n  const formattedSections = sections.map(section => {\n    const dateValue = getSectionVisibleValue(section, isRTL ? 'input-rtl' : 'input-ltr');\n    return `${section.startSeparator}${dateValue}${section.endSeparator}`;\n  });\n  const dateStr = formattedSections.join('');\n  if (!isRTL) {\n    return dateStr;\n  }\n\n  // \\u2066: start left-to-right isolation\n  // \\u2067: start right-to-left isolation\n  // \\u2068: start first strong character isolation\n  // \\u2069: pop isolation\n  // wrap into an isolated group such that separators can split the string in smaller ones by adding \\u2069\\u2068\n  return `\\u2066${dateStr}\\u2069`;\n};\nexport const getSectionsBoundaries = (utils, timezone) => {\n  const today = utils.dateWithTimezone(undefined, timezone);\n  const endOfYear = utils.endOfYear(today);\n  const endOfDay = utils.endOfDay(today);\n  const {\n    maxDaysInMonth,\n    longestMonth\n  } = getMonthsInYear(utils, today).reduce((acc, month) => {\n    const daysInMonth = utils.getDaysInMonth(month);\n    if (daysInMonth > acc.maxDaysInMonth) {\n      return {\n        maxDaysInMonth: daysInMonth,\n        longestMonth: month\n      };\n    }\n    return acc;\n  }, {\n    maxDaysInMonth: 0,\n    longestMonth: null\n  });\n  return {\n    year: ({\n      format\n    }) => ({\n      minimum: 0,\n      maximum: isFourDigitYearFormat(utils, timezone, format) ? 9999 : 99\n    }),\n    month: () => ({\n      minimum: 1,\n      // Assumption: All years have the same amount of months\n      maximum: utils.getMonth(endOfYear) + 1\n    }),\n    day: ({\n      currentDate\n    }) => ({\n      minimum: 1,\n      maximum: currentDate != null && utils.isValid(currentDate) ? utils.getDaysInMonth(currentDate) : maxDaysInMonth,\n      longestMonth: longestMonth\n    }),\n    weekDay: ({\n      format,\n      contentType\n    }) => {\n      if (contentType === 'digit') {\n        const daysInWeek = getDaysInWeekStr(utils, timezone, format).map(Number);\n        return {\n          minimum: Math.min(...daysInWeek),\n          maximum: Math.max(...daysInWeek)\n        };\n      }\n      return {\n        minimum: 1,\n        maximum: 7\n      };\n    },\n    hours: ({\n      format\n    }) => {\n      const lastHourInDay = utils.getHours(endOfDay);\n      const hasMeridiem = utils.formatByString(utils.endOfDay(today), format) !== lastHourInDay.toString();\n      if (hasMeridiem) {\n        return {\n          minimum: 1,\n          maximum: Number(utils.formatByString(utils.startOfDay(today), format))\n        };\n      }\n      return {\n        minimum: 0,\n        maximum: lastHourInDay\n      };\n    },\n    minutes: () => ({\n      minimum: 0,\n      // Assumption: All years have the same amount of minutes\n      maximum: utils.getMinutes(endOfDay)\n    }),\n    seconds: () => ({\n      minimum: 0,\n      // Assumption: All years have the same amount of seconds\n      maximum: utils.getSeconds(endOfDay)\n    }),\n    meridiem: () => ({\n      minimum: 0,\n      maximum: 0\n    })\n  };\n};\nlet warnedOnceInvalidSection = false;\nexport const validateSections = (sections, valueType) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnceInvalidSection) {\n      const supportedSections = [];\n      if (['date', 'date-time'].includes(valueType)) {\n        supportedSections.push('weekDay', 'day', 'month', 'year');\n      }\n      if (['time', 'date-time'].includes(valueType)) {\n        supportedSections.push('hours', 'minutes', 'seconds', 'meridiem');\n      }\n      const invalidSection = sections.find(section => !supportedSections.includes(section.type));\n      if (invalidSection) {\n        console.warn(`MUI: The field component you are using is not compatible with the \"${invalidSection.type} date section.`, `The supported date sections are [\"${supportedSections.join('\", \"')}\"]\\`.`);\n        warnedOnceInvalidSection = true;\n      }\n    }\n  }\n};\nconst transferDateSectionValue = (utils, timezone, section, dateToTransferFrom, dateToTransferTo) => {\n  switch (section.type) {\n    case 'year':\n      {\n        return utils.setYear(dateToTransferTo, utils.getYear(dateToTransferFrom));\n      }\n    case 'month':\n      {\n        return utils.setMonth(dateToTransferTo, utils.getMonth(dateToTransferFrom));\n      }\n    case 'weekDay':\n      {\n        const formattedDaysInWeek = getDaysInWeekStr(utils, timezone, section.format);\n        const dayInWeekStrOfActiveDate = utils.formatByString(dateToTransferFrom, section.format);\n        const dayInWeekOfActiveDate = formattedDaysInWeek.indexOf(dayInWeekStrOfActiveDate);\n        const dayInWeekOfNewSectionValue = formattedDaysInWeek.indexOf(section.value);\n        const diff = dayInWeekOfNewSectionValue - dayInWeekOfActiveDate;\n        return utils.addDays(dateToTransferFrom, diff);\n      }\n    case 'day':\n      {\n        return utils.setDate(dateToTransferTo, utils.getDate(dateToTransferFrom));\n      }\n    case 'meridiem':\n      {\n        const isAM = utils.getHours(dateToTransferFrom) < 12;\n        const mergedDateHours = utils.getHours(dateToTransferTo);\n        if (isAM && mergedDateHours >= 12) {\n          return utils.addHours(dateToTransferTo, -12);\n        }\n        if (!isAM && mergedDateHours < 12) {\n          return utils.addHours(dateToTransferTo, 12);\n        }\n        return dateToTransferTo;\n      }\n    case 'hours':\n      {\n        return utils.setHours(dateToTransferTo, utils.getHours(dateToTransferFrom));\n      }\n    case 'minutes':\n      {\n        return utils.setMinutes(dateToTransferTo, utils.getMinutes(dateToTransferFrom));\n      }\n    case 'seconds':\n      {\n        return utils.setSeconds(dateToTransferTo, utils.getSeconds(dateToTransferFrom));\n      }\n    default:\n      {\n        return dateToTransferTo;\n      }\n  }\n};\nconst reliableSectionModificationOrder = {\n  year: 1,\n  month: 2,\n  day: 3,\n  weekDay: 4,\n  hours: 5,\n  minutes: 6,\n  seconds: 7,\n  meridiem: 8\n};\nexport const mergeDateIntoReferenceDate = (utils, timezone, dateToTransferFrom, sections, referenceDate, shouldLimitToEditedSections) =>\n// cloning sections before sort to avoid mutating it\n[...sections].sort((a, b) => reliableSectionModificationOrder[a.type] - reliableSectionModificationOrder[b.type]).reduce((mergedDate, section) => {\n  if (!shouldLimitToEditedSections || section.modified) {\n    return transferDateSectionValue(utils, timezone, section, dateToTransferFrom, mergedDate);\n  }\n  return mergedDate;\n}, referenceDate);\nexport const isAndroid = () => navigator.userAgent.toLowerCase().indexOf('android') > -1;\nexport const getSectionOrder = (sections, isRTL) => {\n  const neighbors = {};\n  if (!isRTL) {\n    sections.forEach((_, index) => {\n      const leftIndex = index === 0 ? null : index - 1;\n      const rightIndex = index === sections.length - 1 ? null : index + 1;\n      neighbors[index] = {\n        leftIndex,\n        rightIndex\n      };\n    });\n    return {\n      neighbors,\n      startIndex: 0,\n      endIndex: sections.length - 1\n    };\n  }\n  const rtl2ltr = {};\n  const ltr2rtl = {};\n  let groupedSectionsStart = 0;\n  let groupedSectionsEnd = 0;\n  let RTLIndex = sections.length - 1;\n  while (RTLIndex >= 0) {\n    groupedSectionsEnd = sections.findIndex(\n    // eslint-disable-next-line @typescript-eslint/no-loop-func\n    (section, index) => {\n      var _section$endSeparator;\n      return index >= groupedSectionsStart && ((_section$endSeparator = section.endSeparator) == null ? void 0 : _section$endSeparator.includes(' ')) &&\n      // Special case where the spaces were not there in the initial input\n      section.endSeparator !== ' / ';\n    });\n    if (groupedSectionsEnd === -1) {\n      groupedSectionsEnd = sections.length - 1;\n    }\n    for (let i = groupedSectionsEnd; i >= groupedSectionsStart; i -= 1) {\n      ltr2rtl[i] = RTLIndex;\n      rtl2ltr[RTLIndex] = i;\n      RTLIndex -= 1;\n    }\n    groupedSectionsStart = groupedSectionsEnd + 1;\n  }\n  sections.forEach((_, index) => {\n    const rtlIndex = ltr2rtl[index];\n    const leftIndex = rtlIndex === 0 ? null : rtl2ltr[rtlIndex - 1];\n    const rightIndex = rtlIndex === sections.length - 1 ? null : rtl2ltr[rtlIndex + 1];\n    neighbors[index] = {\n      leftIndex,\n      rightIndex\n    };\n  });\n  return {\n    neighbors,\n    startIndex: rtl2ltr[0],\n    endIndex: rtl2ltr[sections.length - 1]\n  };\n};", "map": {"version": 3, "names": ["_extends", "getMonthsInYear", "getDateSectionConfigFromFormatToken", "utils", "formatToken", "config", "formatTokenMap", "Error", "join", "type", "contentType", "max<PERSON><PERSON><PERSON>", "undefined", "sectionType", "getDeltaFromKeyCode", "keyCode", "getDaysInWeekStr", "timezone", "format", "elements", "now", "dateWithTimezone", "startDate", "startOfWeek", "endDate", "endOfWeek", "current", "isBefore", "push", "addDays", "map", "weekDay", "formatByString", "getLetterEditingOptions", "month", "startOfDay", "endOfDay", "date", "cleanLeadingZeros", "valueStr", "size", "cleanValueStr", "Number", "toString", "length", "cleanDigitSectionValue", "value", "sectionBoundaries", "section", "process", "env", "NODE_ENV", "setDate", "longestMonth", "hasLeadingZerosInInput", "adjustSectionValue", "sectionsValueBoundaries", "activeDate", "stepsAttributes", "delta", "isStart", "isEnd", "shouldSetAbsolute", "adjustDigitSection", "currentDate", "getCleanValue", "step", "minutesStep", "currentSectionValue", "parseInt", "newSectionValueNumber", "minimum", "maximum", "adjustLetterSection", "options", "currentOptionIndex", "indexOf", "newOptionIndex", "getSectionVisibleValue", "target", "placeholder", "hasLeadingZeros", "hasLeadingZerosInFormat", "shouldAddInvisibleSpace", "includes", "cleanString", "dirtyString", "replace", "addPositionPropertiesToSections", "sections", "isRTL", "position", "positionInInput", "newSections", "i", "renderedValue", "sectionStr", "startSeparator", "endSeparator", "sectionLength", "sectionLengthInInput", "cleanedValue", "startInInput", "endInInput", "start", "end", "getSectionPlaceholder", "localeText", "sectionConfig", "sectionFormat", "fieldYearPlaceholder", "digitAmount", "fieldMonthPlaceholder", "fieldDayPlaceholder", "fieldWeekDayPlaceholder", "fieldHoursPlaceholder", "fieldMinutesPlaceholder", "fieldSecondsPlaceholder", "fieldMeridiemPlaceholder", "changeSectionValueFormat", "currentFormat", "newFormat", "parse", "isFourDigitYearFormat", "doesSectionFormatHaveLeadingZeros", "formatted0001", "setYear", "formatted2001", "startOfYear", "startOfMonth", "setHours", "setMinutes", "setSeconds", "getEscapedPartsFromFormat", "escapedParts", "startChar", "endChar", "escapedCharacters", "regExp", "RegExp", "match", "exec", "index", "lastIndex", "splitFormatIntoSections", "formatDensity", "shouldRespectLeadingZeros", "commitToken", "token", "isValidDate", "<PERSON><PERSON><PERSON><PERSON>", "sectionValue", "modified", "formatExpansionOverflow", "prevFormat", "nextFormat", "expandFormat", "expandedFormat", "isTokenStartRegExp", "Object", "keys", "sort", "a", "b", "currentTokenValue", "escapedPartOfCurrentChar", "find", "escapeIndex", "char", "isEscapedChar", "potentialToken", "slice", "regExpMatch", "test", "isEscapeBoundary", "cleanSeparator", "separator", "cleanedSeparator", "getDateFromDateSections", "shouldSkipWeekDays", "some", "sectionFormats", "sectionValues", "shouldSkip", "formatWithoutSeparator", "dateWithoutSeparatorStr", "createDateStrForInputFromSections", "formattedSections", "dateValue", "dateStr", "getSectionsBoundaries", "today", "endOfYear", "maxDaysInMonth", "reduce", "acc", "daysInMonth", "getDaysInMonth", "year", "getMonth", "day", "daysInWeek", "Math", "min", "max", "hours", "lastHourInDay", "getHours", "hasMeridiem", "minutes", "getMinutes", "seconds", "getSeconds", "meridiem", "warnedOnceInvalidSection", "validateSections", "valueType", "supportedSections", "invalidSection", "console", "warn", "transferDateSectionValue", "dateToTransferFrom", "dateToTransferTo", "getYear", "setMonth", "formattedDaysInWeek", "dayInWeekStrOfActiveDate", "dayInWeekOfActiveDate", "dayInWeekOfNewSectionValue", "diff", "getDate", "isAM", "mergedDateHours", "addHours", "reliableSectionModificationOrder", "mergeDateIntoReferenceDate", "referenceDate", "shouldLimitToEditedSections", "mergedDate", "isAndroid", "navigator", "userAgent", "toLowerCase", "getSectionOrder", "neighbors", "for<PERSON>ach", "_", "leftIndex", "rightIndex", "startIndex", "endIndex", "rtl2ltr", "ltr2rtl", "groupedSectionsStart", "groupedSectionsEnd", "RTLIndex", "findIndex", "_section$endSeparator", "rtlIndex"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useField/useField.utils.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { getMonthsInYear } from '../../utils/date-utils';\nexport const getDateSectionConfigFromFormatToken = (utils, formatToken) => {\n  const config = utils.formatTokenMap[formatToken];\n  if (config == null) {\n    throw new Error([`MUI: The token \"${formatToken}\" is not supported by the Date and Time Pickers.`, 'Please try using another token or open an issue on https://github.com/mui/mui-x/issues/new/choose if you think it should be supported.'].join('\\n'));\n  }\n  if (typeof config === 'string') {\n    return {\n      type: config,\n      contentType: config === 'meridiem' ? 'letter' : 'digit',\n      maxLength: undefined\n    };\n  }\n  return {\n    type: config.sectionType,\n    contentType: config.contentType,\n    maxLength: config.maxLength\n  };\n};\nconst getDeltaFromKeyCode = keyCode => {\n  switch (keyCode) {\n    case 'ArrowUp':\n      return 1;\n    case 'ArrowDown':\n      return -1;\n    case 'PageUp':\n      return 5;\n    case 'PageDown':\n      return -5;\n    default:\n      return 0;\n  }\n};\nexport const getDaysInWeekStr = (utils, timezone, format) => {\n  const elements = [];\n  const now = utils.dateWithTimezone(undefined, timezone);\n  const startDate = utils.startOfWeek(now);\n  const endDate = utils.endOfWeek(now);\n  let current = startDate;\n  while (utils.isBefore(current, endDate)) {\n    elements.push(current);\n    current = utils.addDays(current, 1);\n  }\n  return elements.map(weekDay => utils.formatByString(weekDay, format));\n};\nexport const getLetterEditingOptions = (utils, timezone, sectionType, format) => {\n  switch (sectionType) {\n    case 'month':\n      {\n        return getMonthsInYear(utils, utils.dateWithTimezone(undefined, timezone)).map(month => utils.formatByString(month, format));\n      }\n    case 'weekDay':\n      {\n        return getDaysInWeekStr(utils, timezone, format);\n      }\n    case 'meridiem':\n      {\n        const now = utils.dateWithTimezone(undefined, timezone);\n        return [utils.startOfDay(now), utils.endOfDay(now)].map(date => utils.formatByString(date, format));\n      }\n    default:\n      {\n        return [];\n      }\n  }\n};\nexport const cleanLeadingZeros = (utils, valueStr, size) => {\n  let cleanValueStr = valueStr;\n\n  // Remove the leading zeros\n  cleanValueStr = Number(cleanValueStr).toString();\n\n  // Add enough leading zeros to fill the section\n  while (cleanValueStr.length < size) {\n    cleanValueStr = `0${cleanValueStr}`;\n  }\n  return cleanValueStr;\n};\nexport const cleanDigitSectionValue = (utils, timezone, value, sectionBoundaries, section) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (section.type !== 'day' && section.contentType === 'digit-with-letter') {\n      throw new Error([`MUI: The token \"${section.format}\" is a digit format with letter in it.'\n             This type of format is only supported for 'day' sections`].join('\\n'));\n    }\n  }\n  if (section.type === 'day' && section.contentType === 'digit-with-letter') {\n    const date = utils.setDate(sectionBoundaries.longestMonth, value);\n    return utils.formatByString(date, section.format);\n  }\n\n  // queryValue without leading `0` (`01` => `1`)\n  const valueStr = value.toString();\n  if (section.hasLeadingZerosInInput) {\n    return cleanLeadingZeros(utils, valueStr, section.maxLength);\n  }\n  return valueStr;\n};\nexport const adjustSectionValue = (utils, timezone, section, keyCode, sectionsValueBoundaries, activeDate, stepsAttributes) => {\n  const delta = getDeltaFromKeyCode(keyCode);\n  const isStart = keyCode === 'Home';\n  const isEnd = keyCode === 'End';\n  const shouldSetAbsolute = section.value === '' || isStart || isEnd;\n  const adjustDigitSection = () => {\n    const sectionBoundaries = sectionsValueBoundaries[section.type]({\n      currentDate: activeDate,\n      format: section.format,\n      contentType: section.contentType\n    });\n    const getCleanValue = value => cleanDigitSectionValue(utils, timezone, value, sectionBoundaries, section);\n    const step = section.type === 'minutes' && stepsAttributes != null && stepsAttributes.minutesStep ? stepsAttributes.minutesStep : 1;\n    const currentSectionValue = parseInt(section.value, 10);\n    let newSectionValueNumber = currentSectionValue + delta * step;\n    if (shouldSetAbsolute) {\n      if (section.type === 'year' && !isEnd && !isStart) {\n        return utils.formatByString(utils.dateWithTimezone(undefined, timezone), section.format);\n      }\n      if (delta > 0 || isStart) {\n        newSectionValueNumber = sectionBoundaries.minimum;\n      } else {\n        newSectionValueNumber = sectionBoundaries.maximum;\n      }\n    }\n    if (newSectionValueNumber % step !== 0) {\n      if (delta < 0 || isStart) {\n        newSectionValueNumber += step - (step + newSectionValueNumber) % step; // for JS -3 % 5 = -3 (should be 2)\n      }\n      if (delta > 0 || isEnd) {\n        newSectionValueNumber -= newSectionValueNumber % step;\n      }\n    }\n    if (newSectionValueNumber > sectionBoundaries.maximum) {\n      return getCleanValue(sectionBoundaries.minimum + (newSectionValueNumber - sectionBoundaries.maximum - 1) % (sectionBoundaries.maximum - sectionBoundaries.minimum + 1));\n    }\n    if (newSectionValueNumber < sectionBoundaries.minimum) {\n      return getCleanValue(sectionBoundaries.maximum - (sectionBoundaries.minimum - newSectionValueNumber - 1) % (sectionBoundaries.maximum - sectionBoundaries.minimum + 1));\n    }\n    return getCleanValue(newSectionValueNumber);\n  };\n  const adjustLetterSection = () => {\n    const options = getLetterEditingOptions(utils, timezone, section.type, section.format);\n    if (options.length === 0) {\n      return section.value;\n    }\n    if (shouldSetAbsolute) {\n      if (delta > 0 || isStart) {\n        return options[0];\n      }\n      return options[options.length - 1];\n    }\n    const currentOptionIndex = options.indexOf(section.value);\n    const newOptionIndex = (currentOptionIndex + options.length + delta) % options.length;\n    return options[newOptionIndex];\n  };\n  if (section.contentType === 'digit' || section.contentType === 'digit-with-letter') {\n    return adjustDigitSection();\n  }\n  return adjustLetterSection();\n};\nexport const getSectionVisibleValue = (section, target) => {\n  let value = section.value || section.placeholder;\n  const hasLeadingZeros = target === 'non-input' ? section.hasLeadingZerosInFormat : section.hasLeadingZerosInInput;\n  if (target === 'non-input' && section.hasLeadingZerosInInput && !section.hasLeadingZerosInFormat) {\n    value = Number(value).toString();\n  }\n\n  // In the input, we add an empty character at the end of each section without leading zeros.\n  // This makes sure that `onChange` will always be fired.\n  // Otherwise, when your input value equals `1/dd/yyyy` (format `M/DD/YYYY` on DayJs),\n  // If you press `1`, on the first section, the new value is also `1/dd/yyyy`,\n  // So the browser will not fire the input `onChange`.\n  const shouldAddInvisibleSpace = ['input-rtl', 'input-ltr'].includes(target) && section.contentType === 'digit' && !hasLeadingZeros && value.length === 1;\n  if (shouldAddInvisibleSpace) {\n    value = `${value}\\u200e`;\n  }\n  if (target === 'input-rtl') {\n    value = `\\u2068${value}\\u2069`;\n  }\n  return value;\n};\nexport const cleanString = dirtyString => dirtyString.replace(/[\\u2066\\u2067\\u2068\\u2069]/g, '');\nexport const addPositionPropertiesToSections = (sections, isRTL) => {\n  let position = 0;\n  let positionInInput = isRTL ? 1 : 0;\n  const newSections = [];\n  for (let i = 0; i < sections.length; i += 1) {\n    const section = sections[i];\n    const renderedValue = getSectionVisibleValue(section, isRTL ? 'input-rtl' : 'input-ltr');\n    const sectionStr = `${section.startSeparator}${renderedValue}${section.endSeparator}`;\n    const sectionLength = cleanString(sectionStr).length;\n    const sectionLengthInInput = sectionStr.length;\n\n    // The ...InInput values consider the unicode characters but do include them in their indexes\n    const cleanedValue = cleanString(renderedValue);\n    const startInInput = positionInInput + renderedValue.indexOf(cleanedValue[0]) + section.startSeparator.length;\n    const endInInput = startInInput + cleanedValue.length;\n    newSections.push(_extends({}, section, {\n      start: position,\n      end: position + sectionLength,\n      startInInput,\n      endInInput\n    }));\n    position += sectionLength;\n    // Move position to the end of string associated to the current section\n    positionInInput += sectionLengthInInput;\n  }\n  return newSections;\n};\nconst getSectionPlaceholder = (utils, timezone, localeText, sectionConfig, sectionFormat) => {\n  switch (sectionConfig.type) {\n    case 'year':\n      {\n        return localeText.fieldYearPlaceholder({\n          digitAmount: utils.formatByString(utils.dateWithTimezone(undefined, timezone), sectionFormat).length,\n          format: sectionFormat\n        });\n      }\n    case 'month':\n      {\n        return localeText.fieldMonthPlaceholder({\n          contentType: sectionConfig.contentType,\n          format: sectionFormat\n        });\n      }\n    case 'day':\n      {\n        return localeText.fieldDayPlaceholder({\n          format: sectionFormat\n        });\n      }\n    case 'weekDay':\n      {\n        return localeText.fieldWeekDayPlaceholder({\n          contentType: sectionConfig.contentType,\n          format: sectionFormat\n        });\n      }\n    case 'hours':\n      {\n        return localeText.fieldHoursPlaceholder({\n          format: sectionFormat\n        });\n      }\n    case 'minutes':\n      {\n        return localeText.fieldMinutesPlaceholder({\n          format: sectionFormat\n        });\n      }\n    case 'seconds':\n      {\n        return localeText.fieldSecondsPlaceholder({\n          format: sectionFormat\n        });\n      }\n    case 'meridiem':\n      {\n        return localeText.fieldMeridiemPlaceholder({\n          format: sectionFormat\n        });\n      }\n    default:\n      {\n        return sectionFormat;\n      }\n  }\n};\nexport const changeSectionValueFormat = (utils, valueStr, currentFormat, newFormat) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (getDateSectionConfigFromFormatToken(utils, currentFormat).type === 'weekDay') {\n      throw new Error(\"changeSectionValueFormat doesn't support week day formats\");\n    }\n  }\n  return utils.formatByString(utils.parse(valueStr, currentFormat), newFormat);\n};\nconst isFourDigitYearFormat = (utils, timezone, format) => utils.formatByString(utils.dateWithTimezone(undefined, timezone), format).length === 4;\nexport const doesSectionFormatHaveLeadingZeros = (utils, timezone, contentType, sectionType, format) => {\n  if (contentType !== 'digit') {\n    return false;\n  }\n  const now = utils.dateWithTimezone(undefined, timezone);\n  switch (sectionType) {\n    // We can't use `changeSectionValueFormat`, because  `utils.parse('1', 'YYYY')` returns `1971` instead of `1`.\n    case 'year':\n      {\n        if (isFourDigitYearFormat(utils, timezone, format)) {\n          const formatted0001 = utils.formatByString(utils.setYear(now, 1), format);\n          return formatted0001 === '0001';\n        }\n        const formatted2001 = utils.formatByString(utils.setYear(now, 2001), format);\n        return formatted2001 === '01';\n      }\n    case 'month':\n      {\n        return utils.formatByString(utils.startOfYear(now), format).length > 1;\n      }\n    case 'day':\n      {\n        return utils.formatByString(utils.startOfMonth(now), format).length > 1;\n      }\n    case 'weekDay':\n      {\n        return utils.formatByString(utils.startOfWeek(now), format).length > 1;\n      }\n    case 'hours':\n      {\n        return utils.formatByString(utils.setHours(now, 1), format).length > 1;\n      }\n    case 'minutes':\n      {\n        return utils.formatByString(utils.setMinutes(now, 1), format).length > 1;\n      }\n    case 'seconds':\n      {\n        return utils.formatByString(utils.setSeconds(now, 1), format).length > 1;\n      }\n    default:\n      {\n        throw new Error('Invalid section type');\n      }\n  }\n};\nconst getEscapedPartsFromFormat = (utils, format) => {\n  const escapedParts = [];\n  const {\n    start: startChar,\n    end: endChar\n  } = utils.escapedCharacters;\n  const regExp = new RegExp(`(\\\\${startChar}[^\\\\${endChar}]*\\\\${endChar})+`, 'g');\n  let match = null;\n  // eslint-disable-next-line no-cond-assign\n  while (match = regExp.exec(format)) {\n    escapedParts.push({\n      start: match.index,\n      end: regExp.lastIndex - 1\n    });\n  }\n  return escapedParts;\n};\nexport const splitFormatIntoSections = (utils, timezone, localeText, format, date, formatDensity, shouldRespectLeadingZeros, isRTL) => {\n  let startSeparator = '';\n  const sections = [];\n  const now = utils.date();\n  const commitToken = token => {\n    if (token === '') {\n      return null;\n    }\n    const sectionConfig = getDateSectionConfigFromFormatToken(utils, token);\n    const hasLeadingZerosInFormat = doesSectionFormatHaveLeadingZeros(utils, timezone, sectionConfig.contentType, sectionConfig.type, token);\n    const hasLeadingZerosInInput = shouldRespectLeadingZeros ? hasLeadingZerosInFormat : sectionConfig.contentType === 'digit';\n    const isValidDate = date != null && utils.isValid(date);\n    let sectionValue = isValidDate ? utils.formatByString(date, token) : '';\n    let maxLength = null;\n    if (hasLeadingZerosInInput) {\n      if (hasLeadingZerosInFormat) {\n        maxLength = sectionValue === '' ? utils.formatByString(now, token).length : sectionValue.length;\n      } else {\n        if (sectionConfig.maxLength == null) {\n          throw new Error(`MUI: The token ${token} should have a 'maxDigitNumber' property on it's adapter`);\n        }\n        maxLength = sectionConfig.maxLength;\n        if (isValidDate) {\n          sectionValue = cleanLeadingZeros(utils, sectionValue, maxLength);\n        }\n      }\n    }\n    sections.push(_extends({}, sectionConfig, {\n      format: token,\n      maxLength,\n      value: sectionValue,\n      placeholder: getSectionPlaceholder(utils, timezone, localeText, sectionConfig, token),\n      hasLeadingZeros: hasLeadingZerosInFormat,\n      hasLeadingZerosInFormat,\n      hasLeadingZerosInInput,\n      startSeparator: sections.length === 0 ? startSeparator : '',\n      endSeparator: '',\n      modified: false\n    }));\n    return null;\n  };\n\n  // Expand the provided format\n  let formatExpansionOverflow = 10;\n  let prevFormat = format;\n  let nextFormat = utils.expandFormat(format);\n  while (nextFormat !== prevFormat) {\n    prevFormat = nextFormat;\n    nextFormat = utils.expandFormat(prevFormat);\n    formatExpansionOverflow -= 1;\n    if (formatExpansionOverflow < 0) {\n      throw new Error('MUI: The format expansion seems to be  enter in an infinite loop. Please open an issue with the format passed to the picker component');\n    }\n  }\n  const expandedFormat = nextFormat;\n\n  // Get start/end indexes of escaped sections\n  const escapedParts = getEscapedPartsFromFormat(utils, expandedFormat);\n\n  // This RegExp test if the beginning of a string correspond to a supported token\n  const isTokenStartRegExp = new RegExp(`^(${Object.keys(utils.formatTokenMap).sort((a, b) => b.length - a.length) // Sort to put longest word first\n  .join('|')})`, 'g') // used to get access to lastIndex state\n  ;\n  let currentTokenValue = '';\n  for (let i = 0; i < expandedFormat.length; i += 1) {\n    const escapedPartOfCurrentChar = escapedParts.find(escapeIndex => escapeIndex.start <= i && escapeIndex.end >= i);\n    const char = expandedFormat[i];\n    const isEscapedChar = escapedPartOfCurrentChar != null;\n    const potentialToken = `${currentTokenValue}${expandedFormat.slice(i)}`;\n    const regExpMatch = isTokenStartRegExp.test(potentialToken);\n    if (!isEscapedChar && char.match(/([A-Za-z]+)/) && regExpMatch) {\n      currentTokenValue = potentialToken.slice(0, isTokenStartRegExp.lastIndex);\n      i += isTokenStartRegExp.lastIndex - 1;\n    } else {\n      // If we are on the opening or closing character of an escaped part of the format,\n      // Then we ignore this character.\n      const isEscapeBoundary = isEscapedChar && (escapedPartOfCurrentChar == null ? void 0 : escapedPartOfCurrentChar.start) === i || (escapedPartOfCurrentChar == null ? void 0 : escapedPartOfCurrentChar.end) === i;\n      if (!isEscapeBoundary) {\n        commitToken(currentTokenValue);\n        currentTokenValue = '';\n        if (sections.length === 0) {\n          startSeparator += char;\n        } else {\n          sections[sections.length - 1].endSeparator += char;\n        }\n      }\n    }\n  }\n  commitToken(currentTokenValue);\n  return sections.map(section => {\n    const cleanSeparator = separator => {\n      let cleanedSeparator = separator;\n      if (isRTL && cleanedSeparator !== null && cleanedSeparator.includes(' ')) {\n        cleanedSeparator = `\\u2069${cleanedSeparator}\\u2066`;\n      }\n      if (formatDensity === 'spacious' && ['/', '.', '-'].includes(cleanedSeparator)) {\n        cleanedSeparator = ` ${cleanedSeparator} `;\n      }\n      return cleanedSeparator;\n    };\n    section.startSeparator = cleanSeparator(section.startSeparator);\n    section.endSeparator = cleanSeparator(section.endSeparator);\n    return section;\n  });\n};\n\n/**\n * Some date libraries like `dayjs` don't support parsing from date with escaped characters.\n * To make sure that the parsing works, we are building a format and a date without any separator.\n */\nexport const getDateFromDateSections = (utils, sections) => {\n  // If we have both a day and a weekDay section,\n  // Then we skip the weekDay in the parsing because libraries like dayjs can't parse complicated formats containing a weekDay.\n  // dayjs(dayjs().format('dddd MMMM D YYYY'), 'dddd MMMM D YYYY')) // returns `Invalid Date` even if the format is valid.\n  const shouldSkipWeekDays = sections.some(section => section.type === 'day');\n  const sectionFormats = [];\n  const sectionValues = [];\n  for (let i = 0; i < sections.length; i += 1) {\n    const section = sections[i];\n    const shouldSkip = shouldSkipWeekDays && section.type === 'weekDay';\n    if (!shouldSkip) {\n      sectionFormats.push(section.format);\n      sectionValues.push(getSectionVisibleValue(section, 'non-input'));\n    }\n  }\n  const formatWithoutSeparator = sectionFormats.join(' ');\n  const dateWithoutSeparatorStr = sectionValues.join(' ');\n  return utils.parse(dateWithoutSeparatorStr, formatWithoutSeparator);\n};\nexport const createDateStrForInputFromSections = (sections, isRTL) => {\n  const formattedSections = sections.map(section => {\n    const dateValue = getSectionVisibleValue(section, isRTL ? 'input-rtl' : 'input-ltr');\n    return `${section.startSeparator}${dateValue}${section.endSeparator}`;\n  });\n  const dateStr = formattedSections.join('');\n  if (!isRTL) {\n    return dateStr;\n  }\n\n  // \\u2066: start left-to-right isolation\n  // \\u2067: start right-to-left isolation\n  // \\u2068: start first strong character isolation\n  // \\u2069: pop isolation\n  // wrap into an isolated group such that separators can split the string in smaller ones by adding \\u2069\\u2068\n  return `\\u2066${dateStr}\\u2069`;\n};\nexport const getSectionsBoundaries = (utils, timezone) => {\n  const today = utils.dateWithTimezone(undefined, timezone);\n  const endOfYear = utils.endOfYear(today);\n  const endOfDay = utils.endOfDay(today);\n  const {\n    maxDaysInMonth,\n    longestMonth\n  } = getMonthsInYear(utils, today).reduce((acc, month) => {\n    const daysInMonth = utils.getDaysInMonth(month);\n    if (daysInMonth > acc.maxDaysInMonth) {\n      return {\n        maxDaysInMonth: daysInMonth,\n        longestMonth: month\n      };\n    }\n    return acc;\n  }, {\n    maxDaysInMonth: 0,\n    longestMonth: null\n  });\n  return {\n    year: ({\n      format\n    }) => ({\n      minimum: 0,\n      maximum: isFourDigitYearFormat(utils, timezone, format) ? 9999 : 99\n    }),\n    month: () => ({\n      minimum: 1,\n      // Assumption: All years have the same amount of months\n      maximum: utils.getMonth(endOfYear) + 1\n    }),\n    day: ({\n      currentDate\n    }) => ({\n      minimum: 1,\n      maximum: currentDate != null && utils.isValid(currentDate) ? utils.getDaysInMonth(currentDate) : maxDaysInMonth,\n      longestMonth: longestMonth\n    }),\n    weekDay: ({\n      format,\n      contentType\n    }) => {\n      if (contentType === 'digit') {\n        const daysInWeek = getDaysInWeekStr(utils, timezone, format).map(Number);\n        return {\n          minimum: Math.min(...daysInWeek),\n          maximum: Math.max(...daysInWeek)\n        };\n      }\n      return {\n        minimum: 1,\n        maximum: 7\n      };\n    },\n    hours: ({\n      format\n    }) => {\n      const lastHourInDay = utils.getHours(endOfDay);\n      const hasMeridiem = utils.formatByString(utils.endOfDay(today), format) !== lastHourInDay.toString();\n      if (hasMeridiem) {\n        return {\n          minimum: 1,\n          maximum: Number(utils.formatByString(utils.startOfDay(today), format))\n        };\n      }\n      return {\n        minimum: 0,\n        maximum: lastHourInDay\n      };\n    },\n    minutes: () => ({\n      minimum: 0,\n      // Assumption: All years have the same amount of minutes\n      maximum: utils.getMinutes(endOfDay)\n    }),\n    seconds: () => ({\n      minimum: 0,\n      // Assumption: All years have the same amount of seconds\n      maximum: utils.getSeconds(endOfDay)\n    }),\n    meridiem: () => ({\n      minimum: 0,\n      maximum: 0\n    })\n  };\n};\nlet warnedOnceInvalidSection = false;\nexport const validateSections = (sections, valueType) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnceInvalidSection) {\n      const supportedSections = [];\n      if (['date', 'date-time'].includes(valueType)) {\n        supportedSections.push('weekDay', 'day', 'month', 'year');\n      }\n      if (['time', 'date-time'].includes(valueType)) {\n        supportedSections.push('hours', 'minutes', 'seconds', 'meridiem');\n      }\n      const invalidSection = sections.find(section => !supportedSections.includes(section.type));\n      if (invalidSection) {\n        console.warn(`MUI: The field component you are using is not compatible with the \"${invalidSection.type} date section.`, `The supported date sections are [\"${supportedSections.join('\", \"')}\"]\\`.`);\n        warnedOnceInvalidSection = true;\n      }\n    }\n  }\n};\nconst transferDateSectionValue = (utils, timezone, section, dateToTransferFrom, dateToTransferTo) => {\n  switch (section.type) {\n    case 'year':\n      {\n        return utils.setYear(dateToTransferTo, utils.getYear(dateToTransferFrom));\n      }\n    case 'month':\n      {\n        return utils.setMonth(dateToTransferTo, utils.getMonth(dateToTransferFrom));\n      }\n    case 'weekDay':\n      {\n        const formattedDaysInWeek = getDaysInWeekStr(utils, timezone, section.format);\n        const dayInWeekStrOfActiveDate = utils.formatByString(dateToTransferFrom, section.format);\n        const dayInWeekOfActiveDate = formattedDaysInWeek.indexOf(dayInWeekStrOfActiveDate);\n        const dayInWeekOfNewSectionValue = formattedDaysInWeek.indexOf(section.value);\n        const diff = dayInWeekOfNewSectionValue - dayInWeekOfActiveDate;\n        return utils.addDays(dateToTransferFrom, diff);\n      }\n    case 'day':\n      {\n        return utils.setDate(dateToTransferTo, utils.getDate(dateToTransferFrom));\n      }\n    case 'meridiem':\n      {\n        const isAM = utils.getHours(dateToTransferFrom) < 12;\n        const mergedDateHours = utils.getHours(dateToTransferTo);\n        if (isAM && mergedDateHours >= 12) {\n          return utils.addHours(dateToTransferTo, -12);\n        }\n        if (!isAM && mergedDateHours < 12) {\n          return utils.addHours(dateToTransferTo, 12);\n        }\n        return dateToTransferTo;\n      }\n    case 'hours':\n      {\n        return utils.setHours(dateToTransferTo, utils.getHours(dateToTransferFrom));\n      }\n    case 'minutes':\n      {\n        return utils.setMinutes(dateToTransferTo, utils.getMinutes(dateToTransferFrom));\n      }\n    case 'seconds':\n      {\n        return utils.setSeconds(dateToTransferTo, utils.getSeconds(dateToTransferFrom));\n      }\n    default:\n      {\n        return dateToTransferTo;\n      }\n  }\n};\nconst reliableSectionModificationOrder = {\n  year: 1,\n  month: 2,\n  day: 3,\n  weekDay: 4,\n  hours: 5,\n  minutes: 6,\n  seconds: 7,\n  meridiem: 8\n};\nexport const mergeDateIntoReferenceDate = (utils, timezone, dateToTransferFrom, sections, referenceDate, shouldLimitToEditedSections) =>\n// cloning sections before sort to avoid mutating it\n[...sections].sort((a, b) => reliableSectionModificationOrder[a.type] - reliableSectionModificationOrder[b.type]).reduce((mergedDate, section) => {\n  if (!shouldLimitToEditedSections || section.modified) {\n    return transferDateSectionValue(utils, timezone, section, dateToTransferFrom, mergedDate);\n  }\n  return mergedDate;\n}, referenceDate);\nexport const isAndroid = () => navigator.userAgent.toLowerCase().indexOf('android') > -1;\nexport const getSectionOrder = (sections, isRTL) => {\n  const neighbors = {};\n  if (!isRTL) {\n    sections.forEach((_, index) => {\n      const leftIndex = index === 0 ? null : index - 1;\n      const rightIndex = index === sections.length - 1 ? null : index + 1;\n      neighbors[index] = {\n        leftIndex,\n        rightIndex\n      };\n    });\n    return {\n      neighbors,\n      startIndex: 0,\n      endIndex: sections.length - 1\n    };\n  }\n  const rtl2ltr = {};\n  const ltr2rtl = {};\n  let groupedSectionsStart = 0;\n  let groupedSectionsEnd = 0;\n  let RTLIndex = sections.length - 1;\n  while (RTLIndex >= 0) {\n    groupedSectionsEnd = sections.findIndex(\n    // eslint-disable-next-line @typescript-eslint/no-loop-func\n    (section, index) => {\n      var _section$endSeparator;\n      return index >= groupedSectionsStart && ((_section$endSeparator = section.endSeparator) == null ? void 0 : _section$endSeparator.includes(' ')) &&\n      // Special case where the spaces were not there in the initial input\n      section.endSeparator !== ' / ';\n    });\n    if (groupedSectionsEnd === -1) {\n      groupedSectionsEnd = sections.length - 1;\n    }\n    for (let i = groupedSectionsEnd; i >= groupedSectionsStart; i -= 1) {\n      ltr2rtl[i] = RTLIndex;\n      rtl2ltr[RTLIndex] = i;\n      RTLIndex -= 1;\n    }\n    groupedSectionsStart = groupedSectionsEnd + 1;\n  }\n  sections.forEach((_, index) => {\n    const rtlIndex = ltr2rtl[index];\n    const leftIndex = rtlIndex === 0 ? null : rtl2ltr[rtlIndex - 1];\n    const rightIndex = rtlIndex === sections.length - 1 ? null : rtl2ltr[rtlIndex + 1];\n    neighbors[index] = {\n      leftIndex,\n      rightIndex\n    };\n  });\n  return {\n    neighbors,\n    startIndex: rtl2ltr[0],\n    endIndex: rtl2ltr[sections.length - 1]\n  };\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,eAAe,QAAQ,wBAAwB;AACxD,OAAO,MAAMC,mCAAmC,GAAGA,CAACC,KAAK,EAAEC,WAAW,KAAK;EACzE,MAAMC,MAAM,GAAGF,KAAK,CAACG,cAAc,CAACF,WAAW,CAAC;EAChD,IAAIC,MAAM,IAAI,IAAI,EAAE;IAClB,MAAM,IAAIE,KAAK,CAAC,CAAC,mBAAmBH,WAAW,kDAAkD,EAAE,wIAAwI,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC,CAAC;EAC1P;EACA,IAAI,OAAOH,MAAM,KAAK,QAAQ,EAAE;IAC9B,OAAO;MACLI,IAAI,EAAEJ,MAAM;MACZK,WAAW,EAAEL,MAAM,KAAK,UAAU,GAAG,QAAQ,GAAG,OAAO;MACvDM,SAAS,EAAEC;IACb,CAAC;EACH;EACA,OAAO;IACLH,IAAI,EAAEJ,MAAM,CAACQ,WAAW;IACxBH,WAAW,EAAEL,MAAM,CAACK,WAAW;IAC/BC,SAAS,EAAEN,MAAM,CAACM;EACpB,CAAC;AACH,CAAC;AACD,MAAMG,mBAAmB,GAAGC,OAAO,IAAI;EACrC,QAAQA,OAAO;IACb,KAAK,SAAS;MACZ,OAAO,CAAC;IACV,KAAK,WAAW;MACd,OAAO,CAAC,CAAC;IACX,KAAK,QAAQ;MACX,OAAO,CAAC;IACV,KAAK,UAAU;MACb,OAAO,CAAC,CAAC;IACX;MACE,OAAO,CAAC;EACZ;AACF,CAAC;AACD,OAAO,MAAMC,gBAAgB,GAAGA,CAACb,KAAK,EAAEc,QAAQ,EAAEC,MAAM,KAAK;EAC3D,MAAMC,QAAQ,GAAG,EAAE;EACnB,MAAMC,GAAG,GAAGjB,KAAK,CAACkB,gBAAgB,CAACT,SAAS,EAAEK,QAAQ,CAAC;EACvD,MAAMK,SAAS,GAAGnB,KAAK,CAACoB,WAAW,CAACH,GAAG,CAAC;EACxC,MAAMI,OAAO,GAAGrB,KAAK,CAACsB,SAAS,CAACL,GAAG,CAAC;EACpC,IAAIM,OAAO,GAAGJ,SAAS;EACvB,OAAOnB,KAAK,CAACwB,QAAQ,CAACD,OAAO,EAAEF,OAAO,CAAC,EAAE;IACvCL,QAAQ,CAACS,IAAI,CAACF,OAAO,CAAC;IACtBA,OAAO,GAAGvB,KAAK,CAAC0B,OAAO,CAACH,OAAO,EAAE,CAAC,CAAC;EACrC;EACA,OAAOP,QAAQ,CAACW,GAAG,CAACC,OAAO,IAAI5B,KAAK,CAAC6B,cAAc,CAACD,OAAO,EAAEb,MAAM,CAAC,CAAC;AACvE,CAAC;AACD,OAAO,MAAMe,uBAAuB,GAAGA,CAAC9B,KAAK,EAAEc,QAAQ,EAAEJ,WAAW,EAAEK,MAAM,KAAK;EAC/E,QAAQL,WAAW;IACjB,KAAK,OAAO;MACV;QACE,OAAOZ,eAAe,CAACE,KAAK,EAAEA,KAAK,CAACkB,gBAAgB,CAACT,SAAS,EAAEK,QAAQ,CAAC,CAAC,CAACa,GAAG,CAACI,KAAK,IAAI/B,KAAK,CAAC6B,cAAc,CAACE,KAAK,EAAEhB,MAAM,CAAC,CAAC;MAC9H;IACF,KAAK,SAAS;MACZ;QACE,OAAOF,gBAAgB,CAACb,KAAK,EAAEc,QAAQ,EAAEC,MAAM,CAAC;MAClD;IACF,KAAK,UAAU;MACb;QACE,MAAME,GAAG,GAAGjB,KAAK,CAACkB,gBAAgB,CAACT,SAAS,EAAEK,QAAQ,CAAC;QACvD,OAAO,CAACd,KAAK,CAACgC,UAAU,CAACf,GAAG,CAAC,EAAEjB,KAAK,CAACiC,QAAQ,CAAChB,GAAG,CAAC,CAAC,CAACU,GAAG,CAACO,IAAI,IAAIlC,KAAK,CAAC6B,cAAc,CAACK,IAAI,EAAEnB,MAAM,CAAC,CAAC;MACrG;IACF;MACE;QACE,OAAO,EAAE;MACX;EACJ;AACF,CAAC;AACD,OAAO,MAAMoB,iBAAiB,GAAGA,CAACnC,KAAK,EAAEoC,QAAQ,EAAEC,IAAI,KAAK;EAC1D,IAAIC,aAAa,GAAGF,QAAQ;;EAE5B;EACAE,aAAa,GAAGC,MAAM,CAACD,aAAa,CAAC,CAACE,QAAQ,CAAC,CAAC;;EAEhD;EACA,OAAOF,aAAa,CAACG,MAAM,GAAGJ,IAAI,EAAE;IAClCC,aAAa,GAAG,IAAIA,aAAa,EAAE;EACrC;EACA,OAAOA,aAAa;AACtB,CAAC;AACD,OAAO,MAAMI,sBAAsB,GAAGA,CAAC1C,KAAK,EAAEc,QAAQ,EAAE6B,KAAK,EAAEC,iBAAiB,EAAEC,OAAO,KAAK;EAC5F,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIH,OAAO,CAACvC,IAAI,KAAK,KAAK,IAAIuC,OAAO,CAACtC,WAAW,KAAK,mBAAmB,EAAE;MACzE,MAAM,IAAIH,KAAK,CAAC,CAAC,mBAAmByC,OAAO,CAAC9B,MAAM;AACxD,sEAAsE,CAAC,CAACV,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/E;EACF;EACA,IAAIwC,OAAO,CAACvC,IAAI,KAAK,KAAK,IAAIuC,OAAO,CAACtC,WAAW,KAAK,mBAAmB,EAAE;IACzE,MAAM2B,IAAI,GAAGlC,KAAK,CAACiD,OAAO,CAACL,iBAAiB,CAACM,YAAY,EAAEP,KAAK,CAAC;IACjE,OAAO3C,KAAK,CAAC6B,cAAc,CAACK,IAAI,EAAEW,OAAO,CAAC9B,MAAM,CAAC;EACnD;;EAEA;EACA,MAAMqB,QAAQ,GAAGO,KAAK,CAACH,QAAQ,CAAC,CAAC;EACjC,IAAIK,OAAO,CAACM,sBAAsB,EAAE;IAClC,OAAOhB,iBAAiB,CAACnC,KAAK,EAAEoC,QAAQ,EAAES,OAAO,CAACrC,SAAS,CAAC;EAC9D;EACA,OAAO4B,QAAQ;AACjB,CAAC;AACD,OAAO,MAAMgB,kBAAkB,GAAGA,CAACpD,KAAK,EAAEc,QAAQ,EAAE+B,OAAO,EAAEjC,OAAO,EAAEyC,uBAAuB,EAAEC,UAAU,EAAEC,eAAe,KAAK;EAC7H,MAAMC,KAAK,GAAG7C,mBAAmB,CAACC,OAAO,CAAC;EAC1C,MAAM6C,OAAO,GAAG7C,OAAO,KAAK,MAAM;EAClC,MAAM8C,KAAK,GAAG9C,OAAO,KAAK,KAAK;EAC/B,MAAM+C,iBAAiB,GAAGd,OAAO,CAACF,KAAK,KAAK,EAAE,IAAIc,OAAO,IAAIC,KAAK;EAClE,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMhB,iBAAiB,GAAGS,uBAAuB,CAACR,OAAO,CAACvC,IAAI,CAAC,CAAC;MAC9DuD,WAAW,EAAEP,UAAU;MACvBvC,MAAM,EAAE8B,OAAO,CAAC9B,MAAM;MACtBR,WAAW,EAAEsC,OAAO,CAACtC;IACvB,CAAC,CAAC;IACF,MAAMuD,aAAa,GAAGnB,KAAK,IAAID,sBAAsB,CAAC1C,KAAK,EAAEc,QAAQ,EAAE6B,KAAK,EAAEC,iBAAiB,EAAEC,OAAO,CAAC;IACzG,MAAMkB,IAAI,GAAGlB,OAAO,CAACvC,IAAI,KAAK,SAAS,IAAIiD,eAAe,IAAI,IAAI,IAAIA,eAAe,CAACS,WAAW,GAAGT,eAAe,CAACS,WAAW,GAAG,CAAC;IACnI,MAAMC,mBAAmB,GAAGC,QAAQ,CAACrB,OAAO,CAACF,KAAK,EAAE,EAAE,CAAC;IACvD,IAAIwB,qBAAqB,GAAGF,mBAAmB,GAAGT,KAAK,GAAGO,IAAI;IAC9D,IAAIJ,iBAAiB,EAAE;MACrB,IAAId,OAAO,CAACvC,IAAI,KAAK,MAAM,IAAI,CAACoD,KAAK,IAAI,CAACD,OAAO,EAAE;QACjD,OAAOzD,KAAK,CAAC6B,cAAc,CAAC7B,KAAK,CAACkB,gBAAgB,CAACT,SAAS,EAAEK,QAAQ,CAAC,EAAE+B,OAAO,CAAC9B,MAAM,CAAC;MAC1F;MACA,IAAIyC,KAAK,GAAG,CAAC,IAAIC,OAAO,EAAE;QACxBU,qBAAqB,GAAGvB,iBAAiB,CAACwB,OAAO;MACnD,CAAC,MAAM;QACLD,qBAAqB,GAAGvB,iBAAiB,CAACyB,OAAO;MACnD;IACF;IACA,IAAIF,qBAAqB,GAAGJ,IAAI,KAAK,CAAC,EAAE;MACtC,IAAIP,KAAK,GAAG,CAAC,IAAIC,OAAO,EAAE;QACxBU,qBAAqB,IAAIJ,IAAI,GAAG,CAACA,IAAI,GAAGI,qBAAqB,IAAIJ,IAAI,CAAC,CAAC;MACzE;MACA,IAAIP,KAAK,GAAG,CAAC,IAAIE,KAAK,EAAE;QACtBS,qBAAqB,IAAIA,qBAAqB,GAAGJ,IAAI;MACvD;IACF;IACA,IAAII,qBAAqB,GAAGvB,iBAAiB,CAACyB,OAAO,EAAE;MACrD,OAAOP,aAAa,CAAClB,iBAAiB,CAACwB,OAAO,GAAG,CAACD,qBAAqB,GAAGvB,iBAAiB,CAACyB,OAAO,GAAG,CAAC,KAAKzB,iBAAiB,CAACyB,OAAO,GAAGzB,iBAAiB,CAACwB,OAAO,GAAG,CAAC,CAAC,CAAC;IACzK;IACA,IAAID,qBAAqB,GAAGvB,iBAAiB,CAACwB,OAAO,EAAE;MACrD,OAAON,aAAa,CAAClB,iBAAiB,CAACyB,OAAO,GAAG,CAACzB,iBAAiB,CAACwB,OAAO,GAAGD,qBAAqB,GAAG,CAAC,KAAKvB,iBAAiB,CAACyB,OAAO,GAAGzB,iBAAiB,CAACwB,OAAO,GAAG,CAAC,CAAC,CAAC;IACzK;IACA,OAAON,aAAa,CAACK,qBAAqB,CAAC;EAC7C,CAAC;EACD,MAAMG,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,OAAO,GAAGzC,uBAAuB,CAAC9B,KAAK,EAAEc,QAAQ,EAAE+B,OAAO,CAACvC,IAAI,EAAEuC,OAAO,CAAC9B,MAAM,CAAC;IACtF,IAAIwD,OAAO,CAAC9B,MAAM,KAAK,CAAC,EAAE;MACxB,OAAOI,OAAO,CAACF,KAAK;IACtB;IACA,IAAIgB,iBAAiB,EAAE;MACrB,IAAIH,KAAK,GAAG,CAAC,IAAIC,OAAO,EAAE;QACxB,OAAOc,OAAO,CAAC,CAAC,CAAC;MACnB;MACA,OAAOA,OAAO,CAACA,OAAO,CAAC9B,MAAM,GAAG,CAAC,CAAC;IACpC;IACA,MAAM+B,kBAAkB,GAAGD,OAAO,CAACE,OAAO,CAAC5B,OAAO,CAACF,KAAK,CAAC;IACzD,MAAM+B,cAAc,GAAG,CAACF,kBAAkB,GAAGD,OAAO,CAAC9B,MAAM,GAAGe,KAAK,IAAIe,OAAO,CAAC9B,MAAM;IACrF,OAAO8B,OAAO,CAACG,cAAc,CAAC;EAChC,CAAC;EACD,IAAI7B,OAAO,CAACtC,WAAW,KAAK,OAAO,IAAIsC,OAAO,CAACtC,WAAW,KAAK,mBAAmB,EAAE;IAClF,OAAOqD,kBAAkB,CAAC,CAAC;EAC7B;EACA,OAAOU,mBAAmB,CAAC,CAAC;AAC9B,CAAC;AACD,OAAO,MAAMK,sBAAsB,GAAGA,CAAC9B,OAAO,EAAE+B,MAAM,KAAK;EACzD,IAAIjC,KAAK,GAAGE,OAAO,CAACF,KAAK,IAAIE,OAAO,CAACgC,WAAW;EAChD,MAAMC,eAAe,GAAGF,MAAM,KAAK,WAAW,GAAG/B,OAAO,CAACkC,uBAAuB,GAAGlC,OAAO,CAACM,sBAAsB;EACjH,IAAIyB,MAAM,KAAK,WAAW,IAAI/B,OAAO,CAACM,sBAAsB,IAAI,CAACN,OAAO,CAACkC,uBAAuB,EAAE;IAChGpC,KAAK,GAAGJ,MAAM,CAACI,KAAK,CAAC,CAACH,QAAQ,CAAC,CAAC;EAClC;;EAEA;EACA;EACA;EACA;EACA;EACA,MAAMwC,uBAAuB,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC,CAACC,QAAQ,CAACL,MAAM,CAAC,IAAI/B,OAAO,CAACtC,WAAW,KAAK,OAAO,IAAI,CAACuE,eAAe,IAAInC,KAAK,CAACF,MAAM,KAAK,CAAC;EACxJ,IAAIuC,uBAAuB,EAAE;IAC3BrC,KAAK,GAAG,GAAGA,KAAK,QAAQ;EAC1B;EACA,IAAIiC,MAAM,KAAK,WAAW,EAAE;IAC1BjC,KAAK,GAAG,SAASA,KAAK,QAAQ;EAChC;EACA,OAAOA,KAAK;AACd,CAAC;AACD,OAAO,MAAMuC,WAAW,GAAGC,WAAW,IAAIA,WAAW,CAACC,OAAO,CAAC,6BAA6B,EAAE,EAAE,CAAC;AAChG,OAAO,MAAMC,+BAA+B,GAAGA,CAACC,QAAQ,EAAEC,KAAK,KAAK;EAClE,IAAIC,QAAQ,GAAG,CAAC;EAChB,IAAIC,eAAe,GAAGF,KAAK,GAAG,CAAC,GAAG,CAAC;EACnC,MAAMG,WAAW,GAAG,EAAE;EACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,QAAQ,CAAC7C,MAAM,EAAEkD,CAAC,IAAI,CAAC,EAAE;IAC3C,MAAM9C,OAAO,GAAGyC,QAAQ,CAACK,CAAC,CAAC;IAC3B,MAAMC,aAAa,GAAGjB,sBAAsB,CAAC9B,OAAO,EAAE0C,KAAK,GAAG,WAAW,GAAG,WAAW,CAAC;IACxF,MAAMM,UAAU,GAAG,GAAGhD,OAAO,CAACiD,cAAc,GAAGF,aAAa,GAAG/C,OAAO,CAACkD,YAAY,EAAE;IACrF,MAAMC,aAAa,GAAGd,WAAW,CAACW,UAAU,CAAC,CAACpD,MAAM;IACpD,MAAMwD,oBAAoB,GAAGJ,UAAU,CAACpD,MAAM;;IAE9C;IACA,MAAMyD,YAAY,GAAGhB,WAAW,CAACU,aAAa,CAAC;IAC/C,MAAMO,YAAY,GAAGV,eAAe,GAAGG,aAAa,CAACnB,OAAO,CAACyB,YAAY,CAAC,CAAC,CAAC,CAAC,GAAGrD,OAAO,CAACiD,cAAc,CAACrD,MAAM;IAC7G,MAAM2D,UAAU,GAAGD,YAAY,GAAGD,YAAY,CAACzD,MAAM;IACrDiD,WAAW,CAACjE,IAAI,CAAC5B,QAAQ,CAAC,CAAC,CAAC,EAAEgD,OAAO,EAAE;MACrCwD,KAAK,EAAEb,QAAQ;MACfc,GAAG,EAAEd,QAAQ,GAAGQ,aAAa;MAC7BG,YAAY;MACZC;IACF,CAAC,CAAC,CAAC;IACHZ,QAAQ,IAAIQ,aAAa;IACzB;IACAP,eAAe,IAAIQ,oBAAoB;EACzC;EACA,OAAOP,WAAW;AACpB,CAAC;AACD,MAAMa,qBAAqB,GAAGA,CAACvG,KAAK,EAAEc,QAAQ,EAAE0F,UAAU,EAAEC,aAAa,EAAEC,aAAa,KAAK;EAC3F,QAAQD,aAAa,CAACnG,IAAI;IACxB,KAAK,MAAM;MACT;QACE,OAAOkG,UAAU,CAACG,oBAAoB,CAAC;UACrCC,WAAW,EAAE5G,KAAK,CAAC6B,cAAc,CAAC7B,KAAK,CAACkB,gBAAgB,CAACT,SAAS,EAAEK,QAAQ,CAAC,EAAE4F,aAAa,CAAC,CAACjE,MAAM;UACpG1B,MAAM,EAAE2F;QACV,CAAC,CAAC;MACJ;IACF,KAAK,OAAO;MACV;QACE,OAAOF,UAAU,CAACK,qBAAqB,CAAC;UACtCtG,WAAW,EAAEkG,aAAa,CAAClG,WAAW;UACtCQ,MAAM,EAAE2F;QACV,CAAC,CAAC;MACJ;IACF,KAAK,KAAK;MACR;QACE,OAAOF,UAAU,CAACM,mBAAmB,CAAC;UACpC/F,MAAM,EAAE2F;QACV,CAAC,CAAC;MACJ;IACF,KAAK,SAAS;MACZ;QACE,OAAOF,UAAU,CAACO,uBAAuB,CAAC;UACxCxG,WAAW,EAAEkG,aAAa,CAAClG,WAAW;UACtCQ,MAAM,EAAE2F;QACV,CAAC,CAAC;MACJ;IACF,KAAK,OAAO;MACV;QACE,OAAOF,UAAU,CAACQ,qBAAqB,CAAC;UACtCjG,MAAM,EAAE2F;QACV,CAAC,CAAC;MACJ;IACF,KAAK,SAAS;MACZ;QACE,OAAOF,UAAU,CAACS,uBAAuB,CAAC;UACxClG,MAAM,EAAE2F;QACV,CAAC,CAAC;MACJ;IACF,KAAK,SAAS;MACZ;QACE,OAAOF,UAAU,CAACU,uBAAuB,CAAC;UACxCnG,MAAM,EAAE2F;QACV,CAAC,CAAC;MACJ;IACF,KAAK,UAAU;MACb;QACE,OAAOF,UAAU,CAACW,wBAAwB,CAAC;UACzCpG,MAAM,EAAE2F;QACV,CAAC,CAAC;MACJ;IACF;MACE;QACE,OAAOA,aAAa;MACtB;EACJ;AACF,CAAC;AACD,OAAO,MAAMU,wBAAwB,GAAGA,CAACpH,KAAK,EAAEoC,QAAQ,EAAEiF,aAAa,EAAEC,SAAS,KAAK;EACrF,IAAIxE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIjD,mCAAmC,CAACC,KAAK,EAAEqH,aAAa,CAAC,CAAC/G,IAAI,KAAK,SAAS,EAAE;MAChF,MAAM,IAAIF,KAAK,CAAC,2DAA2D,CAAC;IAC9E;EACF;EACA,OAAOJ,KAAK,CAAC6B,cAAc,CAAC7B,KAAK,CAACuH,KAAK,CAACnF,QAAQ,EAAEiF,aAAa,CAAC,EAAEC,SAAS,CAAC;AAC9E,CAAC;AACD,MAAME,qBAAqB,GAAGA,CAACxH,KAAK,EAAEc,QAAQ,EAAEC,MAAM,KAAKf,KAAK,CAAC6B,cAAc,CAAC7B,KAAK,CAACkB,gBAAgB,CAACT,SAAS,EAAEK,QAAQ,CAAC,EAAEC,MAAM,CAAC,CAAC0B,MAAM,KAAK,CAAC;AACjJ,OAAO,MAAMgF,iCAAiC,GAAGA,CAACzH,KAAK,EAAEc,QAAQ,EAAEP,WAAW,EAAEG,WAAW,EAAEK,MAAM,KAAK;EACtG,IAAIR,WAAW,KAAK,OAAO,EAAE;IAC3B,OAAO,KAAK;EACd;EACA,MAAMU,GAAG,GAAGjB,KAAK,CAACkB,gBAAgB,CAACT,SAAS,EAAEK,QAAQ,CAAC;EACvD,QAAQJ,WAAW;IACjB;IACA,KAAK,MAAM;MACT;QACE,IAAI8G,qBAAqB,CAACxH,KAAK,EAAEc,QAAQ,EAAEC,MAAM,CAAC,EAAE;UAClD,MAAM2G,aAAa,GAAG1H,KAAK,CAAC6B,cAAc,CAAC7B,KAAK,CAAC2H,OAAO,CAAC1G,GAAG,EAAE,CAAC,CAAC,EAAEF,MAAM,CAAC;UACzE,OAAO2G,aAAa,KAAK,MAAM;QACjC;QACA,MAAME,aAAa,GAAG5H,KAAK,CAAC6B,cAAc,CAAC7B,KAAK,CAAC2H,OAAO,CAAC1G,GAAG,EAAE,IAAI,CAAC,EAAEF,MAAM,CAAC;QAC5E,OAAO6G,aAAa,KAAK,IAAI;MAC/B;IACF,KAAK,OAAO;MACV;QACE,OAAO5H,KAAK,CAAC6B,cAAc,CAAC7B,KAAK,CAAC6H,WAAW,CAAC5G,GAAG,CAAC,EAAEF,MAAM,CAAC,CAAC0B,MAAM,GAAG,CAAC;MACxE;IACF,KAAK,KAAK;MACR;QACE,OAAOzC,KAAK,CAAC6B,cAAc,CAAC7B,KAAK,CAAC8H,YAAY,CAAC7G,GAAG,CAAC,EAAEF,MAAM,CAAC,CAAC0B,MAAM,GAAG,CAAC;MACzE;IACF,KAAK,SAAS;MACZ;QACE,OAAOzC,KAAK,CAAC6B,cAAc,CAAC7B,KAAK,CAACoB,WAAW,CAACH,GAAG,CAAC,EAAEF,MAAM,CAAC,CAAC0B,MAAM,GAAG,CAAC;MACxE;IACF,KAAK,OAAO;MACV;QACE,OAAOzC,KAAK,CAAC6B,cAAc,CAAC7B,KAAK,CAAC+H,QAAQ,CAAC9G,GAAG,EAAE,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAC0B,MAAM,GAAG,CAAC;MACxE;IACF,KAAK,SAAS;MACZ;QACE,OAAOzC,KAAK,CAAC6B,cAAc,CAAC7B,KAAK,CAACgI,UAAU,CAAC/G,GAAG,EAAE,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAC0B,MAAM,GAAG,CAAC;MAC1E;IACF,KAAK,SAAS;MACZ;QACE,OAAOzC,KAAK,CAAC6B,cAAc,CAAC7B,KAAK,CAACiI,UAAU,CAAChH,GAAG,EAAE,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAC0B,MAAM,GAAG,CAAC;MAC1E;IACF;MACE;QACE,MAAM,IAAIrC,KAAK,CAAC,sBAAsB,CAAC;MACzC;EACJ;AACF,CAAC;AACD,MAAM8H,yBAAyB,GAAGA,CAAClI,KAAK,EAAEe,MAAM,KAAK;EACnD,MAAMoH,YAAY,GAAG,EAAE;EACvB,MAAM;IACJ9B,KAAK,EAAE+B,SAAS;IAChB9B,GAAG,EAAE+B;EACP,CAAC,GAAGrI,KAAK,CAACsI,iBAAiB;EAC3B,MAAMC,MAAM,GAAG,IAAIC,MAAM,CAAC,MAAMJ,SAAS,OAAOC,OAAO,OAAOA,OAAO,IAAI,EAAE,GAAG,CAAC;EAC/E,IAAII,KAAK,GAAG,IAAI;EAChB;EACA,OAAOA,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC3H,MAAM,CAAC,EAAE;IAClCoH,YAAY,CAAC1G,IAAI,CAAC;MAChB4E,KAAK,EAAEoC,KAAK,CAACE,KAAK;MAClBrC,GAAG,EAAEiC,MAAM,CAACK,SAAS,GAAG;IAC1B,CAAC,CAAC;EACJ;EACA,OAAOT,YAAY;AACrB,CAAC;AACD,OAAO,MAAMU,uBAAuB,GAAGA,CAAC7I,KAAK,EAAEc,QAAQ,EAAE0F,UAAU,EAAEzF,MAAM,EAAEmB,IAAI,EAAE4G,aAAa,EAAEC,yBAAyB,EAAExD,KAAK,KAAK;EACrI,IAAIO,cAAc,GAAG,EAAE;EACvB,MAAMR,QAAQ,GAAG,EAAE;EACnB,MAAMrE,GAAG,GAAGjB,KAAK,CAACkC,IAAI,CAAC,CAAC;EACxB,MAAM8G,WAAW,GAAGC,KAAK,IAAI;IAC3B,IAAIA,KAAK,KAAK,EAAE,EAAE;MAChB,OAAO,IAAI;IACb;IACA,MAAMxC,aAAa,GAAG1G,mCAAmC,CAACC,KAAK,EAAEiJ,KAAK,CAAC;IACvE,MAAMlE,uBAAuB,GAAG0C,iCAAiC,CAACzH,KAAK,EAAEc,QAAQ,EAAE2F,aAAa,CAAClG,WAAW,EAAEkG,aAAa,CAACnG,IAAI,EAAE2I,KAAK,CAAC;IACxI,MAAM9F,sBAAsB,GAAG4F,yBAAyB,GAAGhE,uBAAuB,GAAG0B,aAAa,CAAClG,WAAW,KAAK,OAAO;IAC1H,MAAM2I,WAAW,GAAGhH,IAAI,IAAI,IAAI,IAAIlC,KAAK,CAACmJ,OAAO,CAACjH,IAAI,CAAC;IACvD,IAAIkH,YAAY,GAAGF,WAAW,GAAGlJ,KAAK,CAAC6B,cAAc,CAACK,IAAI,EAAE+G,KAAK,CAAC,GAAG,EAAE;IACvE,IAAIzI,SAAS,GAAG,IAAI;IACpB,IAAI2C,sBAAsB,EAAE;MAC1B,IAAI4B,uBAAuB,EAAE;QAC3BvE,SAAS,GAAG4I,YAAY,KAAK,EAAE,GAAGpJ,KAAK,CAAC6B,cAAc,CAACZ,GAAG,EAAEgI,KAAK,CAAC,CAACxG,MAAM,GAAG2G,YAAY,CAAC3G,MAAM;MACjG,CAAC,MAAM;QACL,IAAIgE,aAAa,CAACjG,SAAS,IAAI,IAAI,EAAE;UACnC,MAAM,IAAIJ,KAAK,CAAC,kBAAkB6I,KAAK,0DAA0D,CAAC;QACpG;QACAzI,SAAS,GAAGiG,aAAa,CAACjG,SAAS;QACnC,IAAI0I,WAAW,EAAE;UACfE,YAAY,GAAGjH,iBAAiB,CAACnC,KAAK,EAAEoJ,YAAY,EAAE5I,SAAS,CAAC;QAClE;MACF;IACF;IACA8E,QAAQ,CAAC7D,IAAI,CAAC5B,QAAQ,CAAC,CAAC,CAAC,EAAE4G,aAAa,EAAE;MACxC1F,MAAM,EAAEkI,KAAK;MACbzI,SAAS;MACTmC,KAAK,EAAEyG,YAAY;MACnBvE,WAAW,EAAE0B,qBAAqB,CAACvG,KAAK,EAAEc,QAAQ,EAAE0F,UAAU,EAAEC,aAAa,EAAEwC,KAAK,CAAC;MACrFnE,eAAe,EAAEC,uBAAuB;MACxCA,uBAAuB;MACvB5B,sBAAsB;MACtB2C,cAAc,EAAER,QAAQ,CAAC7C,MAAM,KAAK,CAAC,GAAGqD,cAAc,GAAG,EAAE;MAC3DC,YAAY,EAAE,EAAE;MAChBsD,QAAQ,EAAE;IACZ,CAAC,CAAC,CAAC;IACH,OAAO,IAAI;EACb,CAAC;;EAED;EACA,IAAIC,uBAAuB,GAAG,EAAE;EAChC,IAAIC,UAAU,GAAGxI,MAAM;EACvB,IAAIyI,UAAU,GAAGxJ,KAAK,CAACyJ,YAAY,CAAC1I,MAAM,CAAC;EAC3C,OAAOyI,UAAU,KAAKD,UAAU,EAAE;IAChCA,UAAU,GAAGC,UAAU;IACvBA,UAAU,GAAGxJ,KAAK,CAACyJ,YAAY,CAACF,UAAU,CAAC;IAC3CD,uBAAuB,IAAI,CAAC;IAC5B,IAAIA,uBAAuB,GAAG,CAAC,EAAE;MAC/B,MAAM,IAAIlJ,KAAK,CAAC,uIAAuI,CAAC;IAC1J;EACF;EACA,MAAMsJ,cAAc,GAAGF,UAAU;;EAEjC;EACA,MAAMrB,YAAY,GAAGD,yBAAyB,CAAClI,KAAK,EAAE0J,cAAc,CAAC;;EAErE;EACA,MAAMC,kBAAkB,GAAG,IAAInB,MAAM,CAAC,KAAKoB,MAAM,CAACC,IAAI,CAAC7J,KAAK,CAACG,cAAc,CAAC,CAAC2J,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACvH,MAAM,GAAGsH,CAAC,CAACtH,MAAM,CAAC,CAAC;EAAA,CAChHpC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;EAAA;EAEpB,IAAI4J,iBAAiB,GAAG,EAAE;EAC1B,KAAK,IAAItE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+D,cAAc,CAACjH,MAAM,EAAEkD,CAAC,IAAI,CAAC,EAAE;IACjD,MAAMuE,wBAAwB,GAAG/B,YAAY,CAACgC,IAAI,CAACC,WAAW,IAAIA,WAAW,CAAC/D,KAAK,IAAIV,CAAC,IAAIyE,WAAW,CAAC9D,GAAG,IAAIX,CAAC,CAAC;IACjH,MAAM0E,IAAI,GAAGX,cAAc,CAAC/D,CAAC,CAAC;IAC9B,MAAM2E,aAAa,GAAGJ,wBAAwB,IAAI,IAAI;IACtD,MAAMK,cAAc,GAAG,GAAGN,iBAAiB,GAAGP,cAAc,CAACc,KAAK,CAAC7E,CAAC,CAAC,EAAE;IACvE,MAAM8E,WAAW,GAAGd,kBAAkB,CAACe,IAAI,CAACH,cAAc,CAAC;IAC3D,IAAI,CAACD,aAAa,IAAID,IAAI,CAAC5B,KAAK,CAAC,aAAa,CAAC,IAAIgC,WAAW,EAAE;MAC9DR,iBAAiB,GAAGM,cAAc,CAACC,KAAK,CAAC,CAAC,EAAEb,kBAAkB,CAACf,SAAS,CAAC;MACzEjD,CAAC,IAAIgE,kBAAkB,CAACf,SAAS,GAAG,CAAC;IACvC,CAAC,MAAM;MACL;MACA;MACA,MAAM+B,gBAAgB,GAAGL,aAAa,IAAI,CAACJ,wBAAwB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,wBAAwB,CAAC7D,KAAK,MAAMV,CAAC,IAAI,CAACuE,wBAAwB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,wBAAwB,CAAC5D,GAAG,MAAMX,CAAC;MAChN,IAAI,CAACgF,gBAAgB,EAAE;QACrB3B,WAAW,CAACiB,iBAAiB,CAAC;QAC9BA,iBAAiB,GAAG,EAAE;QACtB,IAAI3E,QAAQ,CAAC7C,MAAM,KAAK,CAAC,EAAE;UACzBqD,cAAc,IAAIuE,IAAI;QACxB,CAAC,MAAM;UACL/E,QAAQ,CAACA,QAAQ,CAAC7C,MAAM,GAAG,CAAC,CAAC,CAACsD,YAAY,IAAIsE,IAAI;QACpD;MACF;IACF;EACF;EACArB,WAAW,CAACiB,iBAAiB,CAAC;EAC9B,OAAO3E,QAAQ,CAAC3D,GAAG,CAACkB,OAAO,IAAI;IAC7B,MAAM+H,cAAc,GAAGC,SAAS,IAAI;MAClC,IAAIC,gBAAgB,GAAGD,SAAS;MAChC,IAAItF,KAAK,IAAIuF,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,CAAC7F,QAAQ,CAAC,GAAG,CAAC,EAAE;QACxE6F,gBAAgB,GAAG,SAASA,gBAAgB,QAAQ;MACtD;MACA,IAAIhC,aAAa,KAAK,UAAU,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC7D,QAAQ,CAAC6F,gBAAgB,CAAC,EAAE;QAC9EA,gBAAgB,GAAG,IAAIA,gBAAgB,GAAG;MAC5C;MACA,OAAOA,gBAAgB;IACzB,CAAC;IACDjI,OAAO,CAACiD,cAAc,GAAG8E,cAAc,CAAC/H,OAAO,CAACiD,cAAc,CAAC;IAC/DjD,OAAO,CAACkD,YAAY,GAAG6E,cAAc,CAAC/H,OAAO,CAACkD,YAAY,CAAC;IAC3D,OAAOlD,OAAO;EAChB,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMkI,uBAAuB,GAAGA,CAAC/K,KAAK,EAAEsF,QAAQ,KAAK;EAC1D;EACA;EACA;EACA,MAAM0F,kBAAkB,GAAG1F,QAAQ,CAAC2F,IAAI,CAACpI,OAAO,IAAIA,OAAO,CAACvC,IAAI,KAAK,KAAK,CAAC;EAC3E,MAAM4K,cAAc,GAAG,EAAE;EACzB,MAAMC,aAAa,GAAG,EAAE;EACxB,KAAK,IAAIxF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,QAAQ,CAAC7C,MAAM,EAAEkD,CAAC,IAAI,CAAC,EAAE;IAC3C,MAAM9C,OAAO,GAAGyC,QAAQ,CAACK,CAAC,CAAC;IAC3B,MAAMyF,UAAU,GAAGJ,kBAAkB,IAAInI,OAAO,CAACvC,IAAI,KAAK,SAAS;IACnE,IAAI,CAAC8K,UAAU,EAAE;MACfF,cAAc,CAACzJ,IAAI,CAACoB,OAAO,CAAC9B,MAAM,CAAC;MACnCoK,aAAa,CAAC1J,IAAI,CAACkD,sBAAsB,CAAC9B,OAAO,EAAE,WAAW,CAAC,CAAC;IAClE;EACF;EACA,MAAMwI,sBAAsB,GAAGH,cAAc,CAAC7K,IAAI,CAAC,GAAG,CAAC;EACvD,MAAMiL,uBAAuB,GAAGH,aAAa,CAAC9K,IAAI,CAAC,GAAG,CAAC;EACvD,OAAOL,KAAK,CAACuH,KAAK,CAAC+D,uBAAuB,EAAED,sBAAsB,CAAC;AACrE,CAAC;AACD,OAAO,MAAME,iCAAiC,GAAGA,CAACjG,QAAQ,EAAEC,KAAK,KAAK;EACpE,MAAMiG,iBAAiB,GAAGlG,QAAQ,CAAC3D,GAAG,CAACkB,OAAO,IAAI;IAChD,MAAM4I,SAAS,GAAG9G,sBAAsB,CAAC9B,OAAO,EAAE0C,KAAK,GAAG,WAAW,GAAG,WAAW,CAAC;IACpF,OAAO,GAAG1C,OAAO,CAACiD,cAAc,GAAG2F,SAAS,GAAG5I,OAAO,CAACkD,YAAY,EAAE;EACvE,CAAC,CAAC;EACF,MAAM2F,OAAO,GAAGF,iBAAiB,CAACnL,IAAI,CAAC,EAAE,CAAC;EAC1C,IAAI,CAACkF,KAAK,EAAE;IACV,OAAOmG,OAAO;EAChB;;EAEA;EACA;EACA;EACA;EACA;EACA,OAAO,SAASA,OAAO,QAAQ;AACjC,CAAC;AACD,OAAO,MAAMC,qBAAqB,GAAGA,CAAC3L,KAAK,EAAEc,QAAQ,KAAK;EACxD,MAAM8K,KAAK,GAAG5L,KAAK,CAACkB,gBAAgB,CAACT,SAAS,EAAEK,QAAQ,CAAC;EACzD,MAAM+K,SAAS,GAAG7L,KAAK,CAAC6L,SAAS,CAACD,KAAK,CAAC;EACxC,MAAM3J,QAAQ,GAAGjC,KAAK,CAACiC,QAAQ,CAAC2J,KAAK,CAAC;EACtC,MAAM;IACJE,cAAc;IACd5I;EACF,CAAC,GAAGpD,eAAe,CAACE,KAAK,EAAE4L,KAAK,CAAC,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEjK,KAAK,KAAK;IACvD,MAAMkK,WAAW,GAAGjM,KAAK,CAACkM,cAAc,CAACnK,KAAK,CAAC;IAC/C,IAAIkK,WAAW,GAAGD,GAAG,CAACF,cAAc,EAAE;MACpC,OAAO;QACLA,cAAc,EAAEG,WAAW;QAC3B/I,YAAY,EAAEnB;MAChB,CAAC;IACH;IACA,OAAOiK,GAAG;EACZ,CAAC,EAAE;IACDF,cAAc,EAAE,CAAC;IACjB5I,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,OAAO;IACLiJ,IAAI,EAAEA,CAAC;MACLpL;IACF,CAAC,MAAM;MACLqD,OAAO,EAAE,CAAC;MACVC,OAAO,EAAEmD,qBAAqB,CAACxH,KAAK,EAAEc,QAAQ,EAAEC,MAAM,CAAC,GAAG,IAAI,GAAG;IACnE,CAAC,CAAC;IACFgB,KAAK,EAAEA,CAAA,MAAO;MACZqC,OAAO,EAAE,CAAC;MACV;MACAC,OAAO,EAAErE,KAAK,CAACoM,QAAQ,CAACP,SAAS,CAAC,GAAG;IACvC,CAAC,CAAC;IACFQ,GAAG,EAAEA,CAAC;MACJxI;IACF,CAAC,MAAM;MACLO,OAAO,EAAE,CAAC;MACVC,OAAO,EAAER,WAAW,IAAI,IAAI,IAAI7D,KAAK,CAACmJ,OAAO,CAACtF,WAAW,CAAC,GAAG7D,KAAK,CAACkM,cAAc,CAACrI,WAAW,CAAC,GAAGiI,cAAc;MAC/G5I,YAAY,EAAEA;IAChB,CAAC,CAAC;IACFtB,OAAO,EAAEA,CAAC;MACRb,MAAM;MACNR;IACF,CAAC,KAAK;MACJ,IAAIA,WAAW,KAAK,OAAO,EAAE;QAC3B,MAAM+L,UAAU,GAAGzL,gBAAgB,CAACb,KAAK,EAAEc,QAAQ,EAAEC,MAAM,CAAC,CAACY,GAAG,CAACY,MAAM,CAAC;QACxE,OAAO;UACL6B,OAAO,EAAEmI,IAAI,CAACC,GAAG,CAAC,GAAGF,UAAU,CAAC;UAChCjI,OAAO,EAAEkI,IAAI,CAACE,GAAG,CAAC,GAAGH,UAAU;QACjC,CAAC;MACH;MACA,OAAO;QACLlI,OAAO,EAAE,CAAC;QACVC,OAAO,EAAE;MACX,CAAC;IACH,CAAC;IACDqI,KAAK,EAAEA,CAAC;MACN3L;IACF,CAAC,KAAK;MACJ,MAAM4L,aAAa,GAAG3M,KAAK,CAAC4M,QAAQ,CAAC3K,QAAQ,CAAC;MAC9C,MAAM4K,WAAW,GAAG7M,KAAK,CAAC6B,cAAc,CAAC7B,KAAK,CAACiC,QAAQ,CAAC2J,KAAK,CAAC,EAAE7K,MAAM,CAAC,KAAK4L,aAAa,CAACnK,QAAQ,CAAC,CAAC;MACpG,IAAIqK,WAAW,EAAE;QACf,OAAO;UACLzI,OAAO,EAAE,CAAC;UACVC,OAAO,EAAE9B,MAAM,CAACvC,KAAK,CAAC6B,cAAc,CAAC7B,KAAK,CAACgC,UAAU,CAAC4J,KAAK,CAAC,EAAE7K,MAAM,CAAC;QACvE,CAAC;MACH;MACA,OAAO;QACLqD,OAAO,EAAE,CAAC;QACVC,OAAO,EAAEsI;MACX,CAAC;IACH,CAAC;IACDG,OAAO,EAAEA,CAAA,MAAO;MACd1I,OAAO,EAAE,CAAC;MACV;MACAC,OAAO,EAAErE,KAAK,CAAC+M,UAAU,CAAC9K,QAAQ;IACpC,CAAC,CAAC;IACF+K,OAAO,EAAEA,CAAA,MAAO;MACd5I,OAAO,EAAE,CAAC;MACV;MACAC,OAAO,EAAErE,KAAK,CAACiN,UAAU,CAAChL,QAAQ;IACpC,CAAC,CAAC;IACFiL,QAAQ,EAAEA,CAAA,MAAO;MACf9I,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX,CAAC;EACH,CAAC;AACH,CAAC;AACD,IAAI8I,wBAAwB,GAAG,KAAK;AACpC,OAAO,MAAMC,gBAAgB,GAAGA,CAAC9H,QAAQ,EAAE+H,SAAS,KAAK;EACvD,IAAIvK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI,CAACmK,wBAAwB,EAAE;MAC7B,MAAMG,iBAAiB,GAAG,EAAE;MAC5B,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAACrI,QAAQ,CAACoI,SAAS,CAAC,EAAE;QAC7CC,iBAAiB,CAAC7L,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC;MAC3D;MACA,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAACwD,QAAQ,CAACoI,SAAS,CAAC,EAAE;QAC7CC,iBAAiB,CAAC7L,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC;MACnE;MACA,MAAM8L,cAAc,GAAGjI,QAAQ,CAAC6E,IAAI,CAACtH,OAAO,IAAI,CAACyK,iBAAiB,CAACrI,QAAQ,CAACpC,OAAO,CAACvC,IAAI,CAAC,CAAC;MAC1F,IAAIiN,cAAc,EAAE;QAClBC,OAAO,CAACC,IAAI,CAAC,sEAAsEF,cAAc,CAACjN,IAAI,gBAAgB,EAAE,qCAAqCgN,iBAAiB,CAACjN,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACnM8M,wBAAwB,GAAG,IAAI;MACjC;IACF;EACF;AACF,CAAC;AACD,MAAMO,wBAAwB,GAAGA,CAAC1N,KAAK,EAAEc,QAAQ,EAAE+B,OAAO,EAAE8K,kBAAkB,EAAEC,gBAAgB,KAAK;EACnG,QAAQ/K,OAAO,CAACvC,IAAI;IAClB,KAAK,MAAM;MACT;QACE,OAAON,KAAK,CAAC2H,OAAO,CAACiG,gBAAgB,EAAE5N,KAAK,CAAC6N,OAAO,CAACF,kBAAkB,CAAC,CAAC;MAC3E;IACF,KAAK,OAAO;MACV;QACE,OAAO3N,KAAK,CAAC8N,QAAQ,CAACF,gBAAgB,EAAE5N,KAAK,CAACoM,QAAQ,CAACuB,kBAAkB,CAAC,CAAC;MAC7E;IACF,KAAK,SAAS;MACZ;QACE,MAAMI,mBAAmB,GAAGlN,gBAAgB,CAACb,KAAK,EAAEc,QAAQ,EAAE+B,OAAO,CAAC9B,MAAM,CAAC;QAC7E,MAAMiN,wBAAwB,GAAGhO,KAAK,CAAC6B,cAAc,CAAC8L,kBAAkB,EAAE9K,OAAO,CAAC9B,MAAM,CAAC;QACzF,MAAMkN,qBAAqB,GAAGF,mBAAmB,CAACtJ,OAAO,CAACuJ,wBAAwB,CAAC;QACnF,MAAME,0BAA0B,GAAGH,mBAAmB,CAACtJ,OAAO,CAAC5B,OAAO,CAACF,KAAK,CAAC;QAC7E,MAAMwL,IAAI,GAAGD,0BAA0B,GAAGD,qBAAqB;QAC/D,OAAOjO,KAAK,CAAC0B,OAAO,CAACiM,kBAAkB,EAAEQ,IAAI,CAAC;MAChD;IACF,KAAK,KAAK;MACR;QACE,OAAOnO,KAAK,CAACiD,OAAO,CAAC2K,gBAAgB,EAAE5N,KAAK,CAACoO,OAAO,CAACT,kBAAkB,CAAC,CAAC;MAC3E;IACF,KAAK,UAAU;MACb;QACE,MAAMU,IAAI,GAAGrO,KAAK,CAAC4M,QAAQ,CAACe,kBAAkB,CAAC,GAAG,EAAE;QACpD,MAAMW,eAAe,GAAGtO,KAAK,CAAC4M,QAAQ,CAACgB,gBAAgB,CAAC;QACxD,IAAIS,IAAI,IAAIC,eAAe,IAAI,EAAE,EAAE;UACjC,OAAOtO,KAAK,CAACuO,QAAQ,CAACX,gBAAgB,EAAE,CAAC,EAAE,CAAC;QAC9C;QACA,IAAI,CAACS,IAAI,IAAIC,eAAe,GAAG,EAAE,EAAE;UACjC,OAAOtO,KAAK,CAACuO,QAAQ,CAACX,gBAAgB,EAAE,EAAE,CAAC;QAC7C;QACA,OAAOA,gBAAgB;MACzB;IACF,KAAK,OAAO;MACV;QACE,OAAO5N,KAAK,CAAC+H,QAAQ,CAAC6F,gBAAgB,EAAE5N,KAAK,CAAC4M,QAAQ,CAACe,kBAAkB,CAAC,CAAC;MAC7E;IACF,KAAK,SAAS;MACZ;QACE,OAAO3N,KAAK,CAACgI,UAAU,CAAC4F,gBAAgB,EAAE5N,KAAK,CAAC+M,UAAU,CAACY,kBAAkB,CAAC,CAAC;MACjF;IACF,KAAK,SAAS;MACZ;QACE,OAAO3N,KAAK,CAACiI,UAAU,CAAC2F,gBAAgB,EAAE5N,KAAK,CAACiN,UAAU,CAACU,kBAAkB,CAAC,CAAC;MACjF;IACF;MACE;QACE,OAAOC,gBAAgB;MACzB;EACJ;AACF,CAAC;AACD,MAAMY,gCAAgC,GAAG;EACvCrC,IAAI,EAAE,CAAC;EACPpK,KAAK,EAAE,CAAC;EACRsK,GAAG,EAAE,CAAC;EACNzK,OAAO,EAAE,CAAC;EACV8K,KAAK,EAAE,CAAC;EACRI,OAAO,EAAE,CAAC;EACVE,OAAO,EAAE,CAAC;EACVE,QAAQ,EAAE;AACZ,CAAC;AACD,OAAO,MAAMuB,0BAA0B,GAAGA,CAACzO,KAAK,EAAEc,QAAQ,EAAE6M,kBAAkB,EAAErI,QAAQ,EAAEoJ,aAAa,EAAEC,2BAA2B;AACpI;AACA,CAAC,GAAGrJ,QAAQ,CAAC,CAACwE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKwE,gCAAgC,CAACzE,CAAC,CAACzJ,IAAI,CAAC,GAAGkO,gCAAgC,CAACxE,CAAC,CAAC1J,IAAI,CAAC,CAAC,CAACyL,MAAM,CAAC,CAAC6C,UAAU,EAAE/L,OAAO,KAAK;EAChJ,IAAI,CAAC8L,2BAA2B,IAAI9L,OAAO,CAACwG,QAAQ,EAAE;IACpD,OAAOqE,wBAAwB,CAAC1N,KAAK,EAAEc,QAAQ,EAAE+B,OAAO,EAAE8K,kBAAkB,EAAEiB,UAAU,CAAC;EAC3F;EACA,OAAOA,UAAU;AACnB,CAAC,EAAEF,aAAa,CAAC;AACjB,OAAO,MAAMG,SAAS,GAAGA,CAAA,KAAMC,SAAS,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC,CAACvK,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AACxF,OAAO,MAAMwK,eAAe,GAAGA,CAAC3J,QAAQ,EAAEC,KAAK,KAAK;EAClD,MAAM2J,SAAS,GAAG,CAAC,CAAC;EACpB,IAAI,CAAC3J,KAAK,EAAE;IACVD,QAAQ,CAAC6J,OAAO,CAAC,CAACC,CAAC,EAAEzG,KAAK,KAAK;MAC7B,MAAM0G,SAAS,GAAG1G,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAK,GAAG,CAAC;MAChD,MAAM2G,UAAU,GAAG3G,KAAK,KAAKrD,QAAQ,CAAC7C,MAAM,GAAG,CAAC,GAAG,IAAI,GAAGkG,KAAK,GAAG,CAAC;MACnEuG,SAAS,CAACvG,KAAK,CAAC,GAAG;QACjB0G,SAAS;QACTC;MACF,CAAC;IACH,CAAC,CAAC;IACF,OAAO;MACLJ,SAAS;MACTK,UAAU,EAAE,CAAC;MACbC,QAAQ,EAAElK,QAAQ,CAAC7C,MAAM,GAAG;IAC9B,CAAC;EACH;EACA,MAAMgN,OAAO,GAAG,CAAC,CAAC;EAClB,MAAMC,OAAO,GAAG,CAAC,CAAC;EAClB,IAAIC,oBAAoB,GAAG,CAAC;EAC5B,IAAIC,kBAAkB,GAAG,CAAC;EAC1B,IAAIC,QAAQ,GAAGvK,QAAQ,CAAC7C,MAAM,GAAG,CAAC;EAClC,OAAOoN,QAAQ,IAAI,CAAC,EAAE;IACpBD,kBAAkB,GAAGtK,QAAQ,CAACwK,SAAS;IACvC;IACA,CAACjN,OAAO,EAAE8F,KAAK,KAAK;MAClB,IAAIoH,qBAAqB;MACzB,OAAOpH,KAAK,IAAIgH,oBAAoB,KAAK,CAACI,qBAAqB,GAAGlN,OAAO,CAACkD,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGgK,qBAAqB,CAAC9K,QAAQ,CAAC,GAAG,CAAC,CAAC;MAC/I;MACApC,OAAO,CAACkD,YAAY,KAAK,KAAK;IAChC,CAAC,CAAC;IACF,IAAI6J,kBAAkB,KAAK,CAAC,CAAC,EAAE;MAC7BA,kBAAkB,GAAGtK,QAAQ,CAAC7C,MAAM,GAAG,CAAC;IAC1C;IACA,KAAK,IAAIkD,CAAC,GAAGiK,kBAAkB,EAAEjK,CAAC,IAAIgK,oBAAoB,EAAEhK,CAAC,IAAI,CAAC,EAAE;MAClE+J,OAAO,CAAC/J,CAAC,CAAC,GAAGkK,QAAQ;MACrBJ,OAAO,CAACI,QAAQ,CAAC,GAAGlK,CAAC;MACrBkK,QAAQ,IAAI,CAAC;IACf;IACAF,oBAAoB,GAAGC,kBAAkB,GAAG,CAAC;EAC/C;EACAtK,QAAQ,CAAC6J,OAAO,CAAC,CAACC,CAAC,EAAEzG,KAAK,KAAK;IAC7B,MAAMqH,QAAQ,GAAGN,OAAO,CAAC/G,KAAK,CAAC;IAC/B,MAAM0G,SAAS,GAAGW,QAAQ,KAAK,CAAC,GAAG,IAAI,GAAGP,OAAO,CAACO,QAAQ,GAAG,CAAC,CAAC;IAC/D,MAAMV,UAAU,GAAGU,QAAQ,KAAK1K,QAAQ,CAAC7C,MAAM,GAAG,CAAC,GAAG,IAAI,GAAGgN,OAAO,CAACO,QAAQ,GAAG,CAAC,CAAC;IAClFd,SAAS,CAACvG,KAAK,CAAC,GAAG;MACjB0G,SAAS;MACTC;IACF,CAAC;EACH,CAAC,CAAC;EACF,OAAO;IACLJ,SAAS;IACTK,UAAU,EAAEE,OAAO,CAAC,CAAC,CAAC;IACtBD,QAAQ,EAAEC,OAAO,CAACnK,QAAQ,CAAC7C,MAAM,GAAG,CAAC;EACvC,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}