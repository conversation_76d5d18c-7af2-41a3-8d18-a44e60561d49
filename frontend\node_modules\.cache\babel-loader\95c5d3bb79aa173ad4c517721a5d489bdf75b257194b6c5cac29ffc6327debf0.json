{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - MOE Student S4M\\\\Desktop\\\\CRM Project\\\\frontend\\\\src\\\\pages\\\\Auth\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  var _location$state, _location$state$from;\n  console.log('🚀 Login component rendered');\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    login,\n    error,\n    loading\n  } = useAuth();\n  const [username, setUsername] = useState('');\n  const [password, setPassword] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  // Get redirect path from location state\n  const from = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : (_location$state$from = _location$state.from) === null || _location$state$from === void 0 ? void 0 : _location$state$from.pathname) || '/dashboard';\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Clear field error when user starts typing\n    if (formErrors[name]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n\n    // Clear auth error\n    if (error) {\n      clearError();\n    }\n  };\n  const handleTogglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      handleSubmit();\n    }\n  };\n  const validateForm = () => {\n    const errors = {};\n    if (!formData.username.trim()) {\n      errors.username = 'اسم المستخدم مطلوب';\n    }\n    if (!formData.password) {\n      errors.password = 'كلمة المرور مطلوبة';\n    }\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n  const handleSubmit = async e => {\n    console.log('🔥 handleSubmit called!', e);\n    if (e) {\n      e.preventDefault();\n      e.stopPropagation();\n    }\n    console.log('Login button clicked with data:', formData);\n    if (!validateForm()) {\n      console.log('Form validation failed');\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      console.log('Attempting login...');\n      const result = await login(formData.username, formData.password);\n      console.log('Login result:', result);\n      if (result.success) {\n        console.log('Login successful, setting success state');\n        setLoginSuccess(true);\n        // Small delay to show success state before redirect\n        setTimeout(() => {\n          console.log('Navigating to:', from);\n          navigate(from, {\n            replace: true\n          });\n        }, 1000);\n      } else {\n        console.log('Login failed:', result.error);\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Auto-fill demo credentials\n  const fillDemoCredentials = type => {\n    if (type === 'admin') {\n      setFormData({\n        username: 'admin',\n        password: '123456'\n      });\n    } else if (type === 'coach') {\n      setFormData({\n        username: 'coach',\n        password: '123456'\n      });\n    }\n    clearError();\n    setFormErrors({});\n  };\n\n  // Clear errors when component mounts\n  useEffect(() => {\n    clearError();\n  }, [clearError]);\n  return /*#__PURE__*/_jsxDEV(Container, {\n    component: \"main\",\n    maxWidth: \"sm\",\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        minHeight: '100vh',\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        py: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        elevation: 8,\n        sx: {\n          p: 4,\n          width: '100%',\n          maxWidth: 400,\n          borderRadius: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            mb: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            sx: {\n              m: 1,\n              bgcolor: 'primary.main',\n              width: 64,\n              height: 64\n            },\n            children: /*#__PURE__*/_jsxDEV(SportsMartialArts, {\n              sx: {\n                fontSize: 32\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"h1\",\n            variant: \"h4\",\n            fontWeight: \"bold\",\n            gutterBottom: true,\n            children: \"\\u0623\\u0643\\u0627\\u062F\\u064A\\u0645\\u064A\\u0629 \\u0627\\u0644\\u062A\\u0627\\u064A\\u0643\\u0648\\u0646\\u062F\\u0648\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            textAlign: \"center\",\n            children: \"\\u0646\\u0638\\u0627\\u0645 \\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0623\\u0643\\u0627\\u062F\\u064A\\u0645\\u064A\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), loginSuccess && /*#__PURE__*/_jsxDEV(Fade, {\n          in: loginSuccess,\n          children: /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"success\",\n            sx: {\n              mb: 3\n            },\n            children: \"\\u062A\\u0645 \\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644 \\u0628\\u0646\\u062C\\u0627\\u062D! \\u062C\\u0627\\u0631\\u064A \\u0627\\u0644\\u062A\\u0648\\u062C\\u064A\\u0647...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 13\n        }, this), error && !loginSuccess && /*#__PURE__*/_jsxDEV(Fade, {\n          in: !!error,\n          children: /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"error\",\n            sx: {\n              mb: 3\n            },\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            margin: \"normal\",\n            required: true,\n            fullWidth: true,\n            id: \"username\",\n            label: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\",\n            name: \"username\",\n            autoComplete: \"username\",\n            autoFocus: true,\n            value: formData.username,\n            onChange: handleChange,\n            onKeyPress: handleKeyPress,\n            error: !!formErrors.username,\n            helperText: formErrors.username,\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"start\",\n                children: /*#__PURE__*/_jsxDEV(Person, {\n                  color: \"action\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 19\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            margin: \"normal\",\n            required: true,\n            fullWidth: true,\n            name: \"password\",\n            label: \"\\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\",\n            type: showPassword ? 'text' : 'password',\n            id: \"password\",\n            autoComplete: \"current-password\",\n            value: formData.password,\n            onChange: handleChange,\n            onKeyPress: handleKeyPress,\n            error: !!formErrors.password,\n            helperText: formErrors.password,\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"start\",\n                children: /*#__PURE__*/_jsxDEV(Lock, {\n                  color: \"action\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this),\n              endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"end\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  \"aria-label\": \"toggle password visibility\",\n                  onClick: handleTogglePasswordVisibility,\n                  edge: \"end\",\n                  children: showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 39\n                  }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 59\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"button\",\n            fullWidth: true,\n            variant: \"contained\",\n            disabled: loading || isSubmitting || loginSuccess,\n            onClick: e => {\n              console.log('🎯 Button clicked!', e);\n              handleSubmit(e);\n            },\n            sx: {\n              mt: 3,\n              mb: 2,\n              py: 1.5,\n              fontSize: '1.1rem',\n              fontWeight: 600,\n              position: 'relative'\n            },\n            children: [isSubmitting && /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20,\n              sx: {\n                position: 'absolute',\n                left: '50%',\n                top: '50%',\n                marginLeft: '-10px',\n                marginTop: '-10px',\n                color: 'inherit'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this), loginSuccess ? 'تم بنجاح! جاري التوجيه...' : isSubmitting ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: 'grey.50',\n            borderRadius: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            fontWeight: \"bold\",\n            gutterBottom: true,\n            children: \"\\u062D\\u0633\\u0627\\u0628\\u0627\\u062A \\u062A\\u062C\\u0631\\u064A\\u0628\\u064A\\u0629:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 1,\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              variant: \"outlined\",\n              onClick: () => fillDemoCredentials('admin'),\n              disabled: isSubmitting || loginSuccess,\n              sx: {\n                fontSize: '0.75rem'\n              },\n              children: \"\\u0627\\u0644\\u0645\\u062F\\u064A\\u0631: admin / 123456\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              variant: \"outlined\",\n              onClick: () => fillDemoCredentials('coach'),\n              disabled: isSubmitting || loginSuccess,\n              sx: {\n                fontSize: '0.75rem'\n              },\n              children: \"\\u0627\\u0644\\u0645\\u062F\\u0631\\u0628: coach / 123456\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"\\u0627\\u0646\\u0642\\u0631 \\u0639\\u0644\\u0649 \\u0623\\u064A \\u062D\\u0633\\u0627\\u0628 \\u0644\\u0645\\u0644\\u0621 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u062A\\u0644\\u0642\\u0627\\u0626\\u064A\\u0627\\u064B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 123,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"sCWf4BYzofVx218xh2Ij1QU9iLU=\", false, function () {\n  return [useNavigate, useLocation, useAuth];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useAuth", "useNavigate", "useLocation", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "_location$state", "_location$state$from", "console", "log", "navigate", "location", "login", "error", "loading", "username", "setUsername", "password", "setPassword", "isSubmitting", "setIsSubmitting", "from", "state", "pathname", "handleChange", "e", "name", "value", "target", "setFormData", "prev", "formErrors", "setFormErrors", "clearError", "handleTogglePasswordVisibility", "setShowPassword", "showPassword", "handleKeyPress", "key", "preventDefault", "handleSubmit", "validateForm", "errors", "formData", "trim", "Object", "keys", "length", "stopPropagation", "result", "success", "setLoginSuccess", "setTimeout", "replace", "fillDemoCredentials", "type", "useEffect", "Container", "component", "max<PERSON><PERSON><PERSON>", "children", "Box", "sx", "minHeight", "display", "flexDirection", "alignItems", "justifyContent", "py", "Paper", "elevation", "p", "width", "borderRadius", "mb", "Avatar", "m", "bgcolor", "height", "SportsMartialArts", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Typography", "variant", "fontWeight", "gutterBottom", "color", "textAlign", "loginSuccess", "Fade", "in", "<PERSON><PERSON>", "severity", "TextField", "margin", "required", "fullWidth", "id", "label", "autoComplete", "autoFocus", "onChange", "onKeyPress", "helperText", "InputProps", "startAdornment", "InputAdornment", "position", "Person", "Lock", "endAdornment", "IconButton", "onClick", "edge", "VisibilityOff", "Visibility", "<PERSON><PERSON>", "disabled", "mt", "CircularProgress", "size", "left", "top", "marginLeft", "marginTop", "gap", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/src/pages/Auth/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useNavigate, useLocation } from 'react-router-dom';\n\nconst Login = () => {\n  console.log('🚀 Login component rendered');\n\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { login, error, loading } = useAuth();\n\n  const [username, setUsername] = useState('');\n  const [password, setPassword] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  // Get redirect path from location state\n  const from = location.state?.from?.pathname || '/dashboard';\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value,\n    }));\n    \n    // Clear field error when user starts typing\n    if (formErrors[name]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: '',\n      }));\n    }\n    \n    // Clear auth error\n    if (error) {\n      clearError();\n    }\n  };\n\n  const handleTogglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      handleSubmit();\n    }\n  };\n\n  const validateForm = () => {\n    const errors = {};\n    \n    if (!formData.username.trim()) {\n      errors.username = 'اسم المستخدم مطلوب';\n    }\n    \n    if (!formData.password) {\n      errors.password = 'كلمة المرور مطلوبة';\n    }\n    \n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    console.log('🔥 handleSubmit called!', e);\n\n    if (e) {\n      e.preventDefault();\n      e.stopPropagation();\n    }\n\n    console.log('Login button clicked with data:', formData);\n\n    if (!validateForm()) {\n      console.log('Form validation failed');\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    try {\n      console.log('Attempting login...');\n      const result = await login(formData.username, formData.password);\n      console.log('Login result:', result);\n\n      if (result.success) {\n        console.log('Login successful, setting success state');\n        setLoginSuccess(true);\n        // Small delay to show success state before redirect\n        setTimeout(() => {\n          console.log('Navigating to:', from);\n          navigate(from, { replace: true });\n        }, 1000);\n      } else {\n        console.log('Login failed:', result.error);\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Auto-fill demo credentials\n  const fillDemoCredentials = (type) => {\n    if (type === 'admin') {\n      setFormData({ username: 'admin', password: '123456' });\n    } else if (type === 'coach') {\n      setFormData({ username: 'coach', password: '123456' });\n    }\n    clearError();\n    setFormErrors({});\n  };\n\n  // Clear errors when component mounts\n  useEffect(() => {\n    clearError();\n  }, [clearError]);\n\n  return (\n    <Container component=\"main\" maxWidth=\"sm\">\n      <Box\n        sx={{\n          minHeight: '100vh',\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          py: 4,\n        }}\n      >\n        <Paper\n          elevation={8}\n          sx={{\n            p: 4,\n            width: '100%',\n            maxWidth: 400,\n            borderRadius: 3,\n          }}\n        >\n          {/* Logo and Title */}\n          <Box\n            sx={{\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              mb: 4,\n            }}\n          >\n            <Avatar\n              sx={{\n                m: 1,\n                bgcolor: 'primary.main',\n                width: 64,\n                height: 64,\n              }}\n            >\n              <SportsMartialArts sx={{ fontSize: 32 }} />\n            </Avatar>\n            <Typography component=\"h1\" variant=\"h4\" fontWeight=\"bold\" gutterBottom>\n              أكاديمية التايكوندو\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\" textAlign=\"center\">\n              نظام إدارة الأكاديمية\n            </Typography>\n          </Box>\n\n          {/* Success Alert */}\n          {loginSuccess && (\n            <Fade in={loginSuccess}>\n              <Alert severity=\"success\" sx={{ mb: 3 }}>\n                تم تسجيل الدخول بنجاح! جاري التوجيه...\n              </Alert>\n            </Fade>\n          )}\n\n          {/* Error Alert */}\n          {error && !loginSuccess && (\n            <Fade in={!!error}>\n              <Alert severity=\"error\" sx={{ mb: 3 }}>\n                {error}\n              </Alert>\n            </Fade>\n          )}\n\n          {/* Login Form */}\n          <Box sx={{ width: '100%' }}>\n            <TextField\n              margin=\"normal\"\n              required\n              fullWidth\n              id=\"username\"\n              label=\"اسم المستخدم\"\n              name=\"username\"\n              autoComplete=\"username\"\n              autoFocus\n              value={formData.username}\n              onChange={handleChange}\n              onKeyPress={handleKeyPress}\n              error={!!formErrors.username}\n              helperText={formErrors.username}\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <Person color=\"action\" />\n                  </InputAdornment>\n                ),\n              }}\n            />\n            \n            <TextField\n              margin=\"normal\"\n              required\n              fullWidth\n              name=\"password\"\n              label=\"كلمة المرور\"\n              type={showPassword ? 'text' : 'password'}\n              id=\"password\"\n              autoComplete=\"current-password\"\n              value={formData.password}\n              onChange={handleChange}\n              onKeyPress={handleKeyPress}\n              error={!!formErrors.password}\n              helperText={formErrors.password}\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <Lock color=\"action\" />\n                  </InputAdornment>\n                ),\n                endAdornment: (\n                  <InputAdornment position=\"end\">\n                    <IconButton\n                      aria-label=\"toggle password visibility\"\n                      onClick={handleTogglePasswordVisibility}\n                      edge=\"end\"\n                    >\n                      {showPassword ? <VisibilityOff /> : <Visibility />}\n                    </IconButton>\n                  </InputAdornment>\n                ),\n              }}\n            />\n            \n            <Button\n              type=\"button\"\n              fullWidth\n              variant=\"contained\"\n              disabled={loading || isSubmitting || loginSuccess}\n              onClick={(e) => {\n                console.log('🎯 Button clicked!', e);\n                handleSubmit(e);\n              }}\n              sx={{\n                mt: 3,\n                mb: 2,\n                py: 1.5,\n                fontSize: '1.1rem',\n                fontWeight: 600,\n                position: 'relative',\n              }}\n            >\n              {isSubmitting && (\n                <CircularProgress\n                  size={20}\n                  sx={{\n                    position: 'absolute',\n                    left: '50%',\n                    top: '50%',\n                    marginLeft: '-10px',\n                    marginTop: '-10px',\n                    color: 'inherit',\n                  }}\n                />\n              )}\n              {loginSuccess\n                ? 'تم بنجاح! جاري التوجيه...'\n                : isSubmitting\n                ? 'جاري تسجيل الدخول...'\n                : 'تسجيل الدخول'\n              }\n            </Button>\n          </Box>\n\n          {/* Demo Accounts Info */}\n          <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 2 }}>\n            <Typography variant=\"body2\" fontWeight=\"bold\" gutterBottom>\n              حسابات تجريبية:\n            </Typography>\n            <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>\n              <Button\n                size=\"small\"\n                variant=\"outlined\"\n                onClick={() => fillDemoCredentials('admin')}\n                disabled={isSubmitting || loginSuccess}\n                sx={{ fontSize: '0.75rem' }}\n              >\n                المدير: admin / 123456\n              </Button>\n              <Button\n                size=\"small\"\n                variant=\"outlined\"\n                onClick={() => fillDemoCredentials('coach')}\n                disabled={isSubmitting || loginSuccess}\n                sx={{ fontSize: '0.75rem' }}\n              >\n                المدرب: coach / 123456\n              </Button>\n            </Box>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              انقر على أي حساب لملء البيانات تلقائياً\n            </Typography>\n          </Box>\n        </Paper>\n      </Box>\n    </Container>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,oBAAA;EAClBC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;EAE1C,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEW,KAAK;IAAEC,KAAK;IAAEC;EAAQ,CAAC,GAAGf,OAAO,CAAC,CAAC;EAE3C,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAMuB,IAAI,GAAG,EAAAf,eAAA,GAAAK,QAAQ,CAACW,KAAK,cAAAhB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBe,IAAI,cAAAd,oBAAA,uBAApBA,oBAAA,CAAsBgB,QAAQ,KAAI,YAAY;EAE3D,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCC,WAAW,CAACC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACJ,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;;IAEH;IACA,IAAII,UAAU,CAACL,IAAI,CAAC,EAAE;MACpBM,aAAa,CAACF,IAAI,KAAK;QACrB,GAAGA,IAAI;QACP,CAACJ,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;;IAEA;IACA,IAAIb,KAAK,EAAE;MACToB,UAAU,CAAC,CAAC;IACd;EACF,CAAC;EAED,MAAMC,8BAA8B,GAAGA,CAAA,KAAM;IAC3CC,eAAe,CAAC,CAACC,YAAY,CAAC;EAChC,CAAC;EAED,MAAMC,cAAc,GAAIZ,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACa,GAAG,KAAK,OAAO,EAAE;MACrBb,CAAC,CAACc,cAAc,CAAC,CAAC;MAClBC,YAAY,CAAC,CAAC;IAChB;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,MAAM,GAAG,CAAC,CAAC;IAEjB,IAAI,CAACC,QAAQ,CAAC5B,QAAQ,CAAC6B,IAAI,CAAC,CAAC,EAAE;MAC7BF,MAAM,CAAC3B,QAAQ,GAAG,oBAAoB;IACxC;IAEA,IAAI,CAAC4B,QAAQ,CAAC1B,QAAQ,EAAE;MACtByB,MAAM,CAACzB,QAAQ,GAAG,oBAAoB;IACxC;IAEAe,aAAa,CAACU,MAAM,CAAC;IACrB,OAAOG,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACK,MAAM,KAAK,CAAC;EACzC,CAAC;EAED,MAAMP,YAAY,GAAG,MAAOf,CAAC,IAAK;IAChCjB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEgB,CAAC,CAAC;IAEzC,IAAIA,CAAC,EAAE;MACLA,CAAC,CAACc,cAAc,CAAC,CAAC;MAClBd,CAAC,CAACuB,eAAe,CAAC,CAAC;IACrB;IAEAxC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEkC,QAAQ,CAAC;IAExD,IAAI,CAACF,YAAY,CAAC,CAAC,EAAE;MACnBjC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC;IACF;IAEAW,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACFZ,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MAClC,MAAMwC,MAAM,GAAG,MAAMrC,KAAK,CAAC+B,QAAQ,CAAC5B,QAAQ,EAAE4B,QAAQ,CAAC1B,QAAQ,CAAC;MAChET,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEwC,MAAM,CAAC;MAEpC,IAAIA,MAAM,CAACC,OAAO,EAAE;QAClB1C,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;QACtD0C,eAAe,CAAC,IAAI,CAAC;QACrB;QACAC,UAAU,CAAC,MAAM;UACf5C,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEY,IAAI,CAAC;UACnCX,QAAQ,CAACW,IAAI,EAAE;YAAEgC,OAAO,EAAE;UAAK,CAAC,CAAC;QACnC,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL7C,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEwC,MAAM,CAACpC,KAAK,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC,CAAC,SAAS;MACRO,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMkC,mBAAmB,GAAIC,IAAI,IAAK;IACpC,IAAIA,IAAI,KAAK,OAAO,EAAE;MACpB1B,WAAW,CAAC;QAAEd,QAAQ,EAAE,OAAO;QAAEE,QAAQ,EAAE;MAAS,CAAC,CAAC;IACxD,CAAC,MAAM,IAAIsC,IAAI,KAAK,OAAO,EAAE;MAC3B1B,WAAW,CAAC;QAAEd,QAAQ,EAAE,OAAO;QAAEE,QAAQ,EAAE;MAAS,CAAC,CAAC;IACxD;IACAgB,UAAU,CAAC,CAAC;IACZD,aAAa,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC;;EAED;EACAwB,SAAS,CAAC,MAAM;IACdvB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAEhB,oBACE9B,OAAA,CAACsD,SAAS;IAACC,SAAS,EAAC,MAAM;IAACC,QAAQ,EAAC,IAAI;IAAAC,QAAA,eACvCzD,OAAA,CAAC0D,GAAG;MACFC,EAAE,EAAE;QACFC,SAAS,EAAE,OAAO;QAClBC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,EAAE,EAAE;MACN,CAAE;MAAAR,QAAA,eAEFzD,OAAA,CAACkE,KAAK;QACJC,SAAS,EAAE,CAAE;QACbR,EAAE,EAAE;UACFS,CAAC,EAAE,CAAC;UACJC,KAAK,EAAE,MAAM;UACbb,QAAQ,EAAE,GAAG;UACbc,YAAY,EAAE;QAChB,CAAE;QAAAb,QAAA,gBAGFzD,OAAA,CAAC0D,GAAG;UACFC,EAAE,EAAE;YACFE,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE,QAAQ;YACvBC,UAAU,EAAE,QAAQ;YACpBQ,EAAE,EAAE;UACN,CAAE;UAAAd,QAAA,gBAEFzD,OAAA,CAACwE,MAAM;YACLb,EAAE,EAAE;cACFc,CAAC,EAAE,CAAC;cACJC,OAAO,EAAE,cAAc;cACvBL,KAAK,EAAE,EAAE;cACTM,MAAM,EAAE;YACV,CAAE;YAAAlB,QAAA,eAEFzD,OAAA,CAAC4E,iBAAiB;cAACjB,EAAE,EAAE;gBAAEkB,QAAQ,EAAE;cAAG;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACTjF,OAAA,CAACkF,UAAU;YAAC3B,SAAS,EAAC,IAAI;YAAC4B,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACC,YAAY;YAAA5B,QAAA,EAAC;UAEvE;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjF,OAAA,CAACkF,UAAU;YAACC,OAAO,EAAC,OAAO;YAACG,KAAK,EAAC,gBAAgB;YAACC,SAAS,EAAC,QAAQ;YAAA9B,QAAA,EAAC;UAEtE;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAGLO,YAAY,iBACXxF,OAAA,CAACyF,IAAI;UAACC,EAAE,EAAEF,YAAa;UAAA/B,QAAA,eACrBzD,OAAA,CAAC2F,KAAK;YAACC,QAAQ,EAAC,SAAS;YAACjC,EAAE,EAAE;cAAEY,EAAE,EAAE;YAAE,CAAE;YAAAd,QAAA,EAAC;UAEzC;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACP,EAGAvE,KAAK,IAAI,CAAC8E,YAAY,iBACrBxF,OAAA,CAACyF,IAAI;UAACC,EAAE,EAAE,CAAC,CAAChF,KAAM;UAAA+C,QAAA,eAChBzD,OAAA,CAAC2F,KAAK;YAACC,QAAQ,EAAC,OAAO;YAACjC,EAAE,EAAE;cAAEY,EAAE,EAAE;YAAE,CAAE;YAAAd,QAAA,EACnC/C;UAAK;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACP,eAGDjF,OAAA,CAAC0D,GAAG;UAACC,EAAE,EAAE;YAAEU,KAAK,EAAE;UAAO,CAAE;UAAAZ,QAAA,gBACzBzD,OAAA,CAAC6F,SAAS;YACRC,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRC,SAAS;YACTC,EAAE,EAAC,UAAU;YACbC,KAAK,EAAC,qEAAc;YACpB3E,IAAI,EAAC,UAAU;YACf4E,YAAY,EAAC,UAAU;YACvBC,SAAS;YACT5E,KAAK,EAAEgB,QAAQ,CAAC5B,QAAS;YACzByF,QAAQ,EAAEhF,YAAa;YACvBiF,UAAU,EAAEpE,cAAe;YAC3BxB,KAAK,EAAE,CAAC,CAACkB,UAAU,CAAChB,QAAS;YAC7B2F,UAAU,EAAE3E,UAAU,CAAChB,QAAS;YAChC4F,UAAU,EAAE;cACVC,cAAc,eACZzG,OAAA,CAAC0G,cAAc;gBAACC,QAAQ,EAAC,OAAO;gBAAAlD,QAAA,eAC9BzD,OAAA,CAAC4G,MAAM;kBAACtB,KAAK,EAAC;gBAAQ;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAEpB;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFjF,OAAA,CAAC6F,SAAS;YACRC,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRC,SAAS;YACTzE,IAAI,EAAC,UAAU;YACf2E,KAAK,EAAC,+DAAa;YACnB9C,IAAI,EAAEnB,YAAY,GAAG,MAAM,GAAG,UAAW;YACzCgE,EAAE,EAAC,UAAU;YACbE,YAAY,EAAC,kBAAkB;YAC/B3E,KAAK,EAAEgB,QAAQ,CAAC1B,QAAS;YACzBuF,QAAQ,EAAEhF,YAAa;YACvBiF,UAAU,EAAEpE,cAAe;YAC3BxB,KAAK,EAAE,CAAC,CAACkB,UAAU,CAACd,QAAS;YAC7ByF,UAAU,EAAE3E,UAAU,CAACd,QAAS;YAChC0F,UAAU,EAAE;cACVC,cAAc,eACZzG,OAAA,CAAC0G,cAAc;gBAACC,QAAQ,EAAC,OAAO;gBAAAlD,QAAA,eAC9BzD,OAAA,CAAC6G,IAAI;kBAACvB,KAAK,EAAC;gBAAQ;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CACjB;cACD6B,YAAY,eACV9G,OAAA,CAAC0G,cAAc;gBAACC,QAAQ,EAAC,KAAK;gBAAAlD,QAAA,eAC5BzD,OAAA,CAAC+G,UAAU;kBACT,cAAW,4BAA4B;kBACvCC,OAAO,EAAEjF,8BAA+B;kBACxCkF,IAAI,EAAC,KAAK;kBAAAxD,QAAA,EAETxB,YAAY,gBAAGjC,OAAA,CAACkH,aAAa;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGjF,OAAA,CAACmH,UAAU;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAEpB;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFjF,OAAA,CAACoH,MAAM;YACLhE,IAAI,EAAC,QAAQ;YACb4C,SAAS;YACTb,OAAO,EAAC,WAAW;YACnBkC,QAAQ,EAAE1G,OAAO,IAAIK,YAAY,IAAIwE,YAAa;YAClDwB,OAAO,EAAG1F,CAAC,IAAK;cACdjB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEgB,CAAC,CAAC;cACpCe,YAAY,CAACf,CAAC,CAAC;YACjB,CAAE;YACFqC,EAAE,EAAE;cACF2D,EAAE,EAAE,CAAC;cACL/C,EAAE,EAAE,CAAC;cACLN,EAAE,EAAE,GAAG;cACPY,QAAQ,EAAE,QAAQ;cAClBO,UAAU,EAAE,GAAG;cACfuB,QAAQ,EAAE;YACZ,CAAE;YAAAlD,QAAA,GAEDzC,YAAY,iBACXhB,OAAA,CAACuH,gBAAgB;cACfC,IAAI,EAAE,EAAG;cACT7D,EAAE,EAAE;gBACFgD,QAAQ,EAAE,UAAU;gBACpBc,IAAI,EAAE,KAAK;gBACXC,GAAG,EAAE,KAAK;gBACVC,UAAU,EAAE,OAAO;gBACnBC,SAAS,EAAE,OAAO;gBAClBtC,KAAK,EAAE;cACT;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACF,EACAO,YAAY,GACT,2BAA2B,GAC3BxE,YAAY,GACZ,sBAAsB,GACtB,cAAc;UAAA;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNjF,OAAA,CAAC0D,GAAG;UAACC,EAAE,EAAE;YAAE2D,EAAE,EAAE,CAAC;YAAElD,CAAC,EAAE,CAAC;YAAEM,OAAO,EAAE,SAAS;YAAEJ,YAAY,EAAE;UAAE,CAAE;UAAAb,QAAA,gBAC5DzD,OAAA,CAACkF,UAAU;YAACC,OAAO,EAAC,OAAO;YAACC,UAAU,EAAC,MAAM;YAACC,YAAY;YAAA5B,QAAA,EAAC;UAE3D;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjF,OAAA,CAAC0D,GAAG;YAACC,EAAE,EAAE;cAAEE,OAAO,EAAE,MAAM;cAAEgE,GAAG,EAAE,CAAC;cAAEtD,EAAE,EAAE;YAAE,CAAE;YAAAd,QAAA,gBAC1CzD,OAAA,CAACoH,MAAM;cACLI,IAAI,EAAC,OAAO;cACZrC,OAAO,EAAC,UAAU;cAClB6B,OAAO,EAAEA,CAAA,KAAM7D,mBAAmB,CAAC,OAAO,CAAE;cAC5CkE,QAAQ,EAAErG,YAAY,IAAIwE,YAAa;cACvC7B,EAAE,EAAE;gBAAEkB,QAAQ,EAAE;cAAU,CAAE;cAAApB,QAAA,EAC7B;YAED;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjF,OAAA,CAACoH,MAAM;cACLI,IAAI,EAAC,OAAO;cACZrC,OAAO,EAAC,UAAU;cAClB6B,OAAO,EAAEA,CAAA,KAAM7D,mBAAmB,CAAC,OAAO,CAAE;cAC5CkE,QAAQ,EAAErG,YAAY,IAAIwE,YAAa;cACvC7B,EAAE,EAAE;gBAAEkB,QAAQ,EAAE;cAAU,CAAE;cAAApB,QAAA,EAC7B;YAED;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNjF,OAAA,CAACkF,UAAU;YAACC,OAAO,EAAC,SAAS;YAACG,KAAK,EAAC,gBAAgB;YAAA7B,QAAA,EAAC;UAErD;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAAC/E,EAAA,CA3TID,KAAK;EAAA,QAGQJ,WAAW,EACXC,WAAW,EACMF,OAAO;AAAA;AAAAkI,EAAA,GALrC7H,KAAK;AA6TX,eAAeA,KAAK;AAAC,IAAA6H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}