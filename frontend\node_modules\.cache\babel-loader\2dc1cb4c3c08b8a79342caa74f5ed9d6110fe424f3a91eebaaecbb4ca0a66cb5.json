{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - MOE Student S4M\\\\Desktop\\\\CRM Project\\\\frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport axios from 'axios';\n\n// Initial state\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst initialState = {\n  user: null,\n  token: localStorage.getItem('token'),\n  loading: true,\n  error: null\n};\n\n// Action types\nconst AUTH_ACTIONS = {\n  LOGIN_START: 'LOGIN_START',\n  LOGIN_SUCCESS: 'LOGIN_SUCCESS',\n  LOGIN_FAILURE: 'LOGIN_FAILURE',\n  LOGOUT: 'LOGOUT',\n  LOAD_USER_START: 'LOAD_USER_START',\n  LOAD_USER_SUCCESS: 'LOAD_USER_SUCCESS',\n  LOAD_USER_FAILURE: 'LOAD_USER_FAILURE',\n  UPDATE_PROFILE: 'UPDATE_PROFILE',\n  CLEAR_ERROR: 'CLEAR_ERROR'\n};\n\n// Reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case AUTH_ACTIONS.LOGIN_START:\n    case AUTH_ACTIONS.LOAD_USER_START:\n      return {\n        ...state,\n        loading: true,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGIN_SUCCESS:\n      localStorage.setItem('token', action.payload.token);\n      localStorage.setItem('user', JSON.stringify(action.payload.user));\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        loading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.LOAD_USER_SUCCESS:\n      return {\n        ...state,\n        user: action.payload,\n        loading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGIN_FAILURE:\n    case AUTH_ACTIONS.LOAD_USER_FAILURE:\n      localStorage.removeItem('token');\n      return {\n        ...state,\n        user: null,\n        token: null,\n        loading: false,\n        error: action.payload\n      };\n    case AUTH_ACTIONS.LOGOUT:\n      localStorage.removeItem('token');\n      return {\n        ...state,\n        user: null,\n        token: null,\n        loading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.UPDATE_PROFILE:\n      return {\n        ...state,\n        user: {\n          ...state.user,\n          ...action.payload\n        }\n      };\n    case AUTH_ACTIONS.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = /*#__PURE__*/createContext();\n\n// API base URL\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\n// Axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL\n});\n\n// Auth provider component\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Set up axios interceptor for token\n  useEffect(() => {\n    const token = state.token;\n    if (token) {\n      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete api.defaults.headers.common['Authorization'];\n    }\n\n    // Response interceptor for handling token expiration\n    const responseInterceptor = api.interceptors.response.use(response => response, error => {\n      var _error$response;\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 && token) {\n        dispatch({\n          type: AUTH_ACTIONS.LOGOUT\n        });\n      }\n      return Promise.reject(error);\n    });\n    return () => {\n      api.interceptors.response.eject(responseInterceptor);\n    };\n  }, [state.token]);\n\n  // Load user on app start\n  useEffect(() => {\n    if (state.token && !state.user) {\n      loadUser();\n    } else if (!state.token) {\n      dispatch({\n        type: AUTH_ACTIONS.LOAD_USER_FAILURE,\n        payload: null\n      });\n    }\n  }, [state.token, state.user]);\n\n  // Login function\n  const login = async (username, password) => {\n    try {\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_START\n      });\n      const response = await api.post('/auth/login', {\n        username,\n        password\n      });\n      const {\n        token,\n        data\n      } = response.data;\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_SUCCESS,\n        payload: {\n          token,\n          user: data\n        }\n      });\n      return {\n        success: true,\n        user: data\n      };\n    } catch (error) {\n      var _error$response2, _error$response3, _error$response4, _error$response4$data;\n      console.error('Login error:', error);\n      let errorMessage = 'حدث خطأ في تسجيل الدخول';\n      if (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) === 401) {\n        errorMessage = 'اسم المستخدم أو كلمة المرور غير صحيحة';\n      } else if (((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status) === 400) {\n        errorMessage = 'يرجى إدخال اسم المستخدم وكلمة المرور';\n      } else if ((_error$response4 = error.response) !== null && _error$response4 !== void 0 && (_error$response4$data = _error$response4.data) !== null && _error$response4$data !== void 0 && _error$response4$data.message) {\n        errorMessage = error.response.data.message;\n      } else if (error.code === 'NETWORK_ERROR' || !error.response) {\n        errorMessage = 'خطأ في الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت';\n      }\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_FAILURE,\n        payload: errorMessage\n      });\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // Load user function\n  const loadUser = async () => {\n    try {\n      dispatch({\n        type: AUTH_ACTIONS.LOAD_USER_START\n      });\n      const response = await api.get('/auth/me');\n      const {\n        data\n      } = response.data;\n      dispatch({\n        type: AUTH_ACTIONS.LOAD_USER_SUCCESS,\n        payload: data\n      });\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      dispatch({\n        type: AUTH_ACTIONS.LOAD_USER_FAILURE,\n        payload: ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.message) || 'فشل في تحميل بيانات المستخدم'\n      });\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      await api.post('/auth/logout');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      dispatch({\n        type: AUTH_ACTIONS.LOGOUT\n      });\n    }\n  };\n\n  // Update profile function\n  const updateProfile = async profileData => {\n    try {\n      const response = await api.put('/auth/profile', profileData);\n      const {\n        data\n      } = response.data;\n      dispatch({\n        type: AUTH_ACTIONS.UPDATE_PROFILE,\n        payload: data\n      });\n      return {\n        success: true,\n        data\n      };\n    } catch (error) {\n      var _error$response6, _error$response6$data;\n      const errorMessage = ((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.message) || 'فشل في تحديث الملف الشخصي';\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // Change password function\n  const changePassword = async (currentPassword, newPassword) => {\n    try {\n      await api.put('/auth/password', {\n        currentPassword,\n        newPassword\n      });\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response7, _error$response7$data;\n      const errorMessage = ((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.message) || 'فشل في تغيير كلمة المرور';\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // Clear error function\n  const clearError = () => {\n    dispatch({\n      type: AUTH_ACTIONS.CLEAR_ERROR\n    });\n  };\n\n  // Check if user is admin\n  const isAdmin = () => {\n    var _state$user;\n    return ((_state$user = state.user) === null || _state$user === void 0 ? void 0 : _state$user.role) === 'admin';\n  };\n\n  // Check if user is coach\n  const isCoach = () => {\n    var _state$user2;\n    return ((_state$user2 = state.user) === null || _state$user2 === void 0 ? void 0 : _state$user2.role) === 'coach';\n  };\n  const value = {\n    user: state.user,\n    token: state.token,\n    loading: state.loading,\n    error: state.error,\n    login,\n    logout,\n    loadUser,\n    updateProfile,\n    changePassword,\n    clearError,\n    isAdmin,\n    isCoach,\n    api // Export api instance for use in other components\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 279,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook to use auth context\n_s(AuthProvider, \"GUSXxL/WUElrtHc/X73NyHNRMdw=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "axios", "jsxDEV", "_jsxDEV", "initialState", "user", "token", "localStorage", "getItem", "loading", "error", "AUTH_ACTIONS", "LOGIN_START", "LOGIN_SUCCESS", "LOGIN_FAILURE", "LOGOUT", "LOAD_USER_START", "LOAD_USER_SUCCESS", "LOAD_USER_FAILURE", "UPDATE_PROFILE", "CLEAR_ERROR", "authReducer", "state", "action", "type", "setItem", "payload", "JSON", "stringify", "removeItem", "AuthContext", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "defaults", "headers", "common", "responseInterceptor", "interceptors", "response", "use", "_error$response", "status", "Promise", "reject", "eject", "loadUser", "login", "username", "password", "post", "data", "success", "_error$response2", "_error$response3", "_error$response4", "_error$response4$data", "console", "errorMessage", "message", "code", "get", "_error$response5", "_error$response5$data", "logout", "updateProfile", "profileData", "put", "_error$response6", "_error$response6$data", "changePassword", "currentPassword", "newPassword", "_error$response7", "_error$response7$data", "clearError", "isAdmin", "_state$user", "role", "isCoach", "_state$user2", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport axios from 'axios';\n\n// Initial state\nconst initialState = {\n  user: null,\n  token: localStorage.getItem('token'),\n  loading: true,\n  error: null,\n};\n\n// Action types\nconst AUTH_ACTIONS = {\n  LOGIN_START: 'LOGIN_START',\n  LOGIN_SUCCESS: 'LOGIN_SUCCESS',\n  LOGIN_FAILURE: 'LOGIN_FAILURE',\n  LOGOUT: 'LOGOUT',\n  LOAD_USER_START: 'LOAD_USER_START',\n  LOAD_USER_SUCCESS: 'LOAD_USER_SUCCESS',\n  LOAD_USER_FAILURE: 'LOAD_USER_FAILURE',\n  UPDATE_PROFILE: 'UPDATE_PROFILE',\n  CLEAR_ERROR: 'CLEAR_ERROR',\n};\n\n// Reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case AUTH_ACTIONS.LOGIN_START:\n    case AUTH_ACTIONS.LOAD_USER_START:\n      return {\n        ...state,\n        loading: true,\n        error: null,\n      };\n\n    case AUTH_ACTIONS.LOGIN_SUCCESS:\n      localStorage.setItem('token', action.payload.token);\n      localStorage.setItem('user', JSON.stringify(action.payload.user));\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        loading: false,\n        error: null,\n      };\n\n    case AUTH_ACTIONS.LOAD_USER_SUCCESS:\n      return {\n        ...state,\n        user: action.payload,\n        loading: false,\n        error: null,\n      };\n\n    case AUTH_ACTIONS.LOGIN_FAILURE:\n    case AUTH_ACTIONS.LOAD_USER_FAILURE:\n      localStorage.removeItem('token');\n      return {\n        ...state,\n        user: null,\n        token: null,\n        loading: false,\n        error: action.payload,\n      };\n\n    case AUTH_ACTIONS.LOGOUT:\n      localStorage.removeItem('token');\n      return {\n        ...state,\n        user: null,\n        token: null,\n        loading: false,\n        error: null,\n      };\n\n    case AUTH_ACTIONS.UPDATE_PROFILE:\n      return {\n        ...state,\n        user: { ...state.user, ...action.payload },\n      };\n\n    case AUTH_ACTIONS.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null,\n      };\n\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = createContext();\n\n// API base URL\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\n// Axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n});\n\n// Auth provider component\nexport const AuthProvider = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Set up axios interceptor for token\n  useEffect(() => {\n    const token = state.token;\n    \n    if (token) {\n      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete api.defaults.headers.common['Authorization'];\n    }\n\n    // Response interceptor for handling token expiration\n    const responseInterceptor = api.interceptors.response.use(\n      (response) => response,\n      (error) => {\n        if (error.response?.status === 401 && token) {\n          dispatch({ type: AUTH_ACTIONS.LOGOUT });\n        }\n        return Promise.reject(error);\n      }\n    );\n\n    return () => {\n      api.interceptors.response.eject(responseInterceptor);\n    };\n  }, [state.token]);\n\n  // Load user on app start\n  useEffect(() => {\n    if (state.token && !state.user) {\n      loadUser();\n    } else if (!state.token) {\n      dispatch({ type: AUTH_ACTIONS.LOAD_USER_FAILURE, payload: null });\n    }\n  }, [state.token, state.user]);\n\n  // Login function\n  const login = async (username, password) => {\n    try {\n      dispatch({ type: AUTH_ACTIONS.LOGIN_START });\n\n      const response = await api.post('/auth/login', {\n        username,\n        password,\n      });\n\n      const { token, data } = response.data;\n\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_SUCCESS,\n        payload: { token, user: data },\n      });\n\n      return { success: true, user: data };\n    } catch (error) {\n      console.error('Login error:', error);\n      let errorMessage = 'حدث خطأ في تسجيل الدخول';\n\n      if (error.response?.status === 401) {\n        errorMessage = 'اسم المستخدم أو كلمة المرور غير صحيحة';\n      } else if (error.response?.status === 400) {\n        errorMessage = 'يرجى إدخال اسم المستخدم وكلمة المرور';\n      } else if (error.response?.data?.message) {\n        errorMessage = error.response.data.message;\n      } else if (error.code === 'NETWORK_ERROR' || !error.response) {\n        errorMessage = 'خطأ في الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت';\n      }\n\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_FAILURE,\n        payload: errorMessage,\n      });\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // Load user function\n  const loadUser = async () => {\n    try {\n      dispatch({ type: AUTH_ACTIONS.LOAD_USER_START });\n\n      const response = await api.get('/auth/me');\n      const { data } = response.data;\n\n      dispatch({\n        type: AUTH_ACTIONS.LOAD_USER_SUCCESS,\n        payload: data,\n      });\n    } catch (error) {\n      dispatch({\n        type: AUTH_ACTIONS.LOAD_USER_FAILURE,\n        payload: error.response?.data?.message || 'فشل في تحميل بيانات المستخدم',\n      });\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      await api.post('/auth/logout');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      dispatch({ type: AUTH_ACTIONS.LOGOUT });\n    }\n  };\n\n  // Update profile function\n  const updateProfile = async (profileData) => {\n    try {\n      const response = await api.put('/auth/profile', profileData);\n      const { data } = response.data;\n\n      dispatch({\n        type: AUTH_ACTIONS.UPDATE_PROFILE,\n        payload: data,\n      });\n\n      return { success: true, data };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || 'فشل في تحديث الملف الشخصي';\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // Change password function\n  const changePassword = async (currentPassword, newPassword) => {\n    try {\n      await api.put('/auth/password', {\n        currentPassword,\n        newPassword,\n      });\n\n      return { success: true };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || 'فشل في تغيير كلمة المرور';\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // Clear error function\n  const clearError = () => {\n    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });\n  };\n\n  // Check if user is admin\n  const isAdmin = () => {\n    return state.user?.role === 'admin';\n  };\n\n  // Check if user is coach\n  const isCoach = () => {\n    return state.user?.role === 'coach';\n  };\n\n  const value = {\n    user: state.user,\n    token: state.token,\n    loading: state.loading,\n    error: state.error,\n    login,\n    logout,\n    loadUser,\n    updateProfile,\n    changePassword,\n    clearError,\n    isAdmin,\n    isCoach,\n    api, // Export api instance for use in other components\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Custom hook to use auth context\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC/E,OAAOC,KAAK,MAAM,OAAO;;AAEzB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAG;EACnBC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAEC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EACpCC,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,YAAY,GAAG;EACnBC,WAAW,EAAE,aAAa;EAC1BC,aAAa,EAAE,eAAe;EAC9BC,aAAa,EAAE,eAAe;EAC9BC,MAAM,EAAE,QAAQ;EAChBC,eAAe,EAAE,iBAAiB;EAClCC,iBAAiB,EAAE,mBAAmB;EACtCC,iBAAiB,EAAE,mBAAmB;EACtCC,cAAc,EAAE,gBAAgB;EAChCC,WAAW,EAAE;AACf,CAAC;;AAED;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACrC,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKb,YAAY,CAACC,WAAW;IAC7B,KAAKD,YAAY,CAACK,eAAe;MAC/B,OAAO;QACL,GAAGM,KAAK;QACRb,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKC,YAAY,CAACE,aAAa;MAC7BN,YAAY,CAACkB,OAAO,CAAC,OAAO,EAAEF,MAAM,CAACG,OAAO,CAACpB,KAAK,CAAC;MACnDC,YAAY,CAACkB,OAAO,CAAC,MAAM,EAAEE,IAAI,CAACC,SAAS,CAACL,MAAM,CAACG,OAAO,CAACrB,IAAI,CAAC,CAAC;MACjE,OAAO;QACL,GAAGiB,KAAK;QACRjB,IAAI,EAAEkB,MAAM,CAACG,OAAO,CAACrB,IAAI;QACzBC,KAAK,EAAEiB,MAAM,CAACG,OAAO,CAACpB,KAAK;QAC3BG,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKC,YAAY,CAACM,iBAAiB;MACjC,OAAO;QACL,GAAGK,KAAK;QACRjB,IAAI,EAAEkB,MAAM,CAACG,OAAO;QACpBjB,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKC,YAAY,CAACG,aAAa;IAC/B,KAAKH,YAAY,CAACO,iBAAiB;MACjCX,YAAY,CAACsB,UAAU,CAAC,OAAO,CAAC;MAChC,OAAO;QACL,GAAGP,KAAK;QACRjB,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXG,OAAO,EAAE,KAAK;QACdC,KAAK,EAAEa,MAAM,CAACG;MAChB,CAAC;IAEH,KAAKf,YAAY,CAACI,MAAM;MACtBR,YAAY,CAACsB,UAAU,CAAC,OAAO,CAAC;MAChC,OAAO;QACL,GAAGP,KAAK;QACRjB,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXG,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKC,YAAY,CAACQ,cAAc;MAC9B,OAAO;QACL,GAAGG,KAAK;QACRjB,IAAI,EAAE;UAAE,GAAGiB,KAAK,CAACjB,IAAI;UAAE,GAAGkB,MAAM,CAACG;QAAQ;MAC3C,CAAC;IAEH,KAAKf,YAAY,CAACS,WAAW;MAC3B,OAAO;QACL,GAAGE,KAAK;QACRZ,KAAK,EAAE;MACT,CAAC;IAEH;MACE,OAAOY,KAAK;EAChB;AACF,CAAC;;AAED;AACA,MAAMQ,WAAW,gBAAGjC,aAAa,CAAC,CAAC;;AAEnC;AACA,MAAMkC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;;AAEjF;AACA,MAAMC,GAAG,GAAGlC,KAAK,CAACmC,MAAM,CAAC;EACvBC,OAAO,EAAEN;AACX,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMO,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAAClB,KAAK,EAAEmB,QAAQ,CAAC,GAAG1C,UAAU,CAACsB,WAAW,EAAEjB,YAAY,CAAC;;EAE/D;EACAJ,SAAS,CAAC,MAAM;IACd,MAAMM,KAAK,GAAGgB,KAAK,CAAChB,KAAK;IAEzB,IAAIA,KAAK,EAAE;MACT6B,GAAG,CAACO,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUtC,KAAK,EAAE;IAClE,CAAC,MAAM;MACL,OAAO6B,GAAG,CAACO,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACrD;;IAEA;IACA,MAAMC,mBAAmB,GAAGV,GAAG,CAACW,YAAY,CAACC,QAAQ,CAACC,GAAG,CACtDD,QAAQ,IAAKA,QAAQ,EACrBrC,KAAK,IAAK;MAAA,IAAAuC,eAAA;MACT,IAAI,EAAAA,eAAA,GAAAvC,KAAK,CAACqC,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,IAAI5C,KAAK,EAAE;QAC3CmC,QAAQ,CAAC;UAAEjB,IAAI,EAAEb,YAAY,CAACI;QAAO,CAAC,CAAC;MACzC;MACA,OAAOoC,OAAO,CAACC,MAAM,CAAC1C,KAAK,CAAC;IAC9B,CACF,CAAC;IAED,OAAO,MAAM;MACXyB,GAAG,CAACW,YAAY,CAACC,QAAQ,CAACM,KAAK,CAACR,mBAAmB,CAAC;IACtD,CAAC;EACH,CAAC,EAAE,CAACvB,KAAK,CAAChB,KAAK,CAAC,CAAC;;EAEjB;EACAN,SAAS,CAAC,MAAM;IACd,IAAIsB,KAAK,CAAChB,KAAK,IAAI,CAACgB,KAAK,CAACjB,IAAI,EAAE;MAC9BiD,QAAQ,CAAC,CAAC;IACZ,CAAC,MAAM,IAAI,CAAChC,KAAK,CAAChB,KAAK,EAAE;MACvBmC,QAAQ,CAAC;QAAEjB,IAAI,EAAEb,YAAY,CAACO,iBAAiB;QAAEQ,OAAO,EAAE;MAAK,CAAC,CAAC;IACnE;EACF,CAAC,EAAE,CAACJ,KAAK,CAAChB,KAAK,EAAEgB,KAAK,CAACjB,IAAI,CAAC,CAAC;;EAE7B;EACA,MAAMkD,KAAK,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,QAAQ,KAAK;IAC1C,IAAI;MACFhB,QAAQ,CAAC;QAAEjB,IAAI,EAAEb,YAAY,CAACC;MAAY,CAAC,CAAC;MAE5C,MAAMmC,QAAQ,GAAG,MAAMZ,GAAG,CAACuB,IAAI,CAAC,aAAa,EAAE;QAC7CF,QAAQ;QACRC;MACF,CAAC,CAAC;MAEF,MAAM;QAAEnD,KAAK;QAAEqD;MAAK,CAAC,GAAGZ,QAAQ,CAACY,IAAI;MAErClB,QAAQ,CAAC;QACPjB,IAAI,EAAEb,YAAY,CAACE,aAAa;QAChCa,OAAO,EAAE;UAAEpB,KAAK;UAAED,IAAI,EAAEsD;QAAK;MAC/B,CAAC,CAAC;MAEF,OAAO;QAAEC,OAAO,EAAE,IAAI;QAAEvD,IAAI,EAAEsD;MAAK,CAAC;IACtC,CAAC,CAAC,OAAOjD,KAAK,EAAE;MAAA,IAAAmD,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdC,OAAO,CAACvD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,IAAIwD,YAAY,GAAG,yBAAyB;MAE5C,IAAI,EAAAL,gBAAA,GAAAnD,KAAK,CAACqC,QAAQ,cAAAc,gBAAA,uBAAdA,gBAAA,CAAgBX,MAAM,MAAK,GAAG,EAAE;QAClCgB,YAAY,GAAG,uCAAuC;MACxD,CAAC,MAAM,IAAI,EAAAJ,gBAAA,GAAApD,KAAK,CAACqC,QAAQ,cAAAe,gBAAA,uBAAdA,gBAAA,CAAgBZ,MAAM,MAAK,GAAG,EAAE;QACzCgB,YAAY,GAAG,sCAAsC;MACvD,CAAC,MAAM,KAAAH,gBAAA,GAAIrD,KAAK,CAACqC,QAAQ,cAAAgB,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBJ,IAAI,cAAAK,qBAAA,eAApBA,qBAAA,CAAsBG,OAAO,EAAE;QACxCD,YAAY,GAAGxD,KAAK,CAACqC,QAAQ,CAACY,IAAI,CAACQ,OAAO;MAC5C,CAAC,MAAM,IAAIzD,KAAK,CAAC0D,IAAI,KAAK,eAAe,IAAI,CAAC1D,KAAK,CAACqC,QAAQ,EAAE;QAC5DmB,YAAY,GAAG,uDAAuD;MACxE;MAEAzB,QAAQ,CAAC;QACPjB,IAAI,EAAEb,YAAY,CAACG,aAAa;QAChCY,OAAO,EAAEwC;MACX,CAAC,CAAC;MACF,OAAO;QAAEN,OAAO,EAAE,KAAK;QAAElD,KAAK,EAAEwD;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMZ,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFb,QAAQ,CAAC;QAAEjB,IAAI,EAAEb,YAAY,CAACK;MAAgB,CAAC,CAAC;MAEhD,MAAM+B,QAAQ,GAAG,MAAMZ,GAAG,CAACkC,GAAG,CAAC,UAAU,CAAC;MAC1C,MAAM;QAAEV;MAAK,CAAC,GAAGZ,QAAQ,CAACY,IAAI;MAE9BlB,QAAQ,CAAC;QACPjB,IAAI,EAAEb,YAAY,CAACM,iBAAiB;QACpCS,OAAO,EAAEiC;MACX,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOjD,KAAK,EAAE;MAAA,IAAA4D,gBAAA,EAAAC,qBAAA;MACd9B,QAAQ,CAAC;QACPjB,IAAI,EAAEb,YAAY,CAACO,iBAAiB;QACpCQ,OAAO,EAAE,EAAA4C,gBAAA,GAAA5D,KAAK,CAACqC,QAAQ,cAAAuB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBX,IAAI,cAAAY,qBAAA,uBAApBA,qBAAA,CAAsBJ,OAAO,KAAI;MAC5C,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMK,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMrC,GAAG,CAACuB,IAAI,CAAC,cAAc,CAAC;IAChC,CAAC,CAAC,OAAOhD,KAAK,EAAE;MACduD,OAAO,CAACvD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACR+B,QAAQ,CAAC;QAAEjB,IAAI,EAAEb,YAAY,CAACI;MAAO,CAAC,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAM0D,aAAa,GAAG,MAAOC,WAAW,IAAK;IAC3C,IAAI;MACF,MAAM3B,QAAQ,GAAG,MAAMZ,GAAG,CAACwC,GAAG,CAAC,eAAe,EAAED,WAAW,CAAC;MAC5D,MAAM;QAAEf;MAAK,CAAC,GAAGZ,QAAQ,CAACY,IAAI;MAE9BlB,QAAQ,CAAC;QACPjB,IAAI,EAAEb,YAAY,CAACQ,cAAc;QACjCO,OAAO,EAAEiC;MACX,CAAC,CAAC;MAEF,OAAO;QAAEC,OAAO,EAAE,IAAI;QAAED;MAAK,CAAC;IAChC,CAAC,CAAC,OAAOjD,KAAK,EAAE;MAAA,IAAAkE,gBAAA,EAAAC,qBAAA;MACd,MAAMX,YAAY,GAAG,EAAAU,gBAAA,GAAAlE,KAAK,CAACqC,QAAQ,cAAA6B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjB,IAAI,cAAAkB,qBAAA,uBAApBA,qBAAA,CAAsBV,OAAO,KAAI,2BAA2B;MACjF,OAAO;QAAEP,OAAO,EAAE,KAAK;QAAElD,KAAK,EAAEwD;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMY,cAAc,GAAG,MAAAA,CAAOC,eAAe,EAAEC,WAAW,KAAK;IAC7D,IAAI;MACF,MAAM7C,GAAG,CAACwC,GAAG,CAAC,gBAAgB,EAAE;QAC9BI,eAAe;QACfC;MACF,CAAC,CAAC;MAEF,OAAO;QAAEpB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOlD,KAAK,EAAE;MAAA,IAAAuE,gBAAA,EAAAC,qBAAA;MACd,MAAMhB,YAAY,GAAG,EAAAe,gBAAA,GAAAvE,KAAK,CAACqC,QAAQ,cAAAkC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBtB,IAAI,cAAAuB,qBAAA,uBAApBA,qBAAA,CAAsBf,OAAO,KAAI,0BAA0B;MAChF,OAAO;QAAEP,OAAO,EAAE,KAAK;QAAElD,KAAK,EAAEwD;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMiB,UAAU,GAAGA,CAAA,KAAM;IACvB1C,QAAQ,CAAC;MAAEjB,IAAI,EAAEb,YAAY,CAACS;IAAY,CAAC,CAAC;EAC9C,CAAC;;EAED;EACA,MAAMgE,OAAO,GAAGA,CAAA,KAAM;IAAA,IAAAC,WAAA;IACpB,OAAO,EAAAA,WAAA,GAAA/D,KAAK,CAACjB,IAAI,cAAAgF,WAAA,uBAAVA,WAAA,CAAYC,IAAI,MAAK,OAAO;EACrC,CAAC;;EAED;EACA,MAAMC,OAAO,GAAGA,CAAA,KAAM;IAAA,IAAAC,YAAA;IACpB,OAAO,EAAAA,YAAA,GAAAlE,KAAK,CAACjB,IAAI,cAAAmF,YAAA,uBAAVA,YAAA,CAAYF,IAAI,MAAK,OAAO;EACrC,CAAC;EAED,MAAMG,KAAK,GAAG;IACZpF,IAAI,EAAEiB,KAAK,CAACjB,IAAI;IAChBC,KAAK,EAAEgB,KAAK,CAAChB,KAAK;IAClBG,OAAO,EAAEa,KAAK,CAACb,OAAO;IACtBC,KAAK,EAAEY,KAAK,CAACZ,KAAK;IAClB6C,KAAK;IACLiB,MAAM;IACNlB,QAAQ;IACRmB,aAAa;IACbK,cAAc;IACdK,UAAU;IACVC,OAAO;IACPG,OAAO;IACPpD,GAAG,CAAE;EACP,CAAC;EAED,oBACEhC,OAAA,CAAC2B,WAAW,CAAC4D,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAlD,QAAA,EAChCA;EAAQ;IAAAoD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAAtD,EAAA,CApLaF,YAAY;AAAAyD,EAAA,GAAZzD,YAAY;AAqLzB,OAAO,MAAM0D,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAGpG,UAAU,CAACgC,WAAW,CAAC;EACvC,IAAI,CAACoE,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}