{"ast": null, "code": "import { createIsAfterIgnoreDatePart } from './time-utils';\nimport { mergeDateAndTime, getTodayDate } from './date-utils';\nexport const SECTION_TYPE_GRANULARITY = {\n  year: 1,\n  month: 2,\n  day: 3,\n  hours: 4,\n  minutes: 5,\n  seconds: 6,\n  milliseconds: 7\n};\nexport const getSectionTypeGranularity = sections => Math.max(...sections.map(section => {\n  var _SECTION_TYPE_GRANULA;\n  return (_SECTION_TYPE_GRANULA = SECTION_TYPE_GRANULARITY[section.type]) != null ? _SECTION_TYPE_GRANULA : 1;\n}));\nexport const getViewsGranularity = views => Math.max(...views.map(view => {\n  var _SECTION_TYPE_GRANULA2;\n  return (_SECTION_TYPE_GRANULA2 = SECTION_TYPE_GRANULARITY[view]) != null ? _SECTION_TYPE_GRANULA2 : 1;\n}));\nconst roundDate = (utils, granularity, date) => {\n  if (granularity === SECTION_TYPE_GRANULARITY.year) {\n    return utils.startOfYear(date);\n  }\n  if (granularity === SECTION_TYPE_GRANULARITY.month) {\n    return utils.startOfMonth(date);\n  }\n  if (granularity === SECTION_TYPE_GRANULARITY.day) {\n    return utils.startOfDay(date);\n  }\n\n  // We don't have startOfHour / startOfMinute / startOfSecond\n  let roundedDate = date;\n  if (granularity < SECTION_TYPE_GRANULARITY.minutes) {\n    roundedDate = utils.setMinutes(roundedDate, 0);\n  }\n  if (granularity < SECTION_TYPE_GRANULARITY.seconds) {\n    roundedDate = utils.setSeconds(roundedDate, 0);\n  }\n  if (granularity < SECTION_TYPE_GRANULARITY.milliseconds) {\n    roundedDate = utils.setMilliseconds(roundedDate, 0);\n  }\n  return roundedDate;\n};\nexport const getDefaultReferenceDate = ({\n  props,\n  utils,\n  granularity,\n  timezone,\n  getTodayDate: inGetTodayDate\n}) => {\n  var _props$disableIgnorin;\n  let referenceDate = inGetTodayDate ? inGetTodayDate() : roundDate(utils, granularity, getTodayDate(utils, timezone));\n  if (props.minDate != null && utils.isAfterDay(props.minDate, referenceDate)) {\n    referenceDate = roundDate(utils, granularity, props.minDate);\n  }\n  if (props.maxDate != null && utils.isBeforeDay(props.maxDate, referenceDate)) {\n    referenceDate = roundDate(utils, granularity, props.maxDate);\n  }\n  const isAfter = createIsAfterIgnoreDatePart((_props$disableIgnorin = props.disableIgnoringDatePartForTimeValidation) != null ? _props$disableIgnorin : false, utils);\n  if (props.minTime != null && isAfter(props.minTime, referenceDate)) {\n    referenceDate = roundDate(utils, granularity, props.disableIgnoringDatePartForTimeValidation ? props.minTime : mergeDateAndTime(utils, referenceDate, props.minTime));\n  }\n  if (props.maxTime != null && isAfter(referenceDate, props.maxTime)) {\n    referenceDate = roundDate(utils, granularity, props.disableIgnoringDatePartForTimeValidation ? props.maxTime : mergeDateAndTime(utils, referenceDate, props.maxTime));\n  }\n  return referenceDate;\n};", "map": {"version": 3, "names": ["createIsAfterIgnoreDatePart", "mergeDateAndTime", "getTodayDate", "SECTION_TYPE_GRANULARITY", "year", "month", "day", "hours", "minutes", "seconds", "milliseconds", "getSectionTypeGranularity", "sections", "Math", "max", "map", "section", "_SECTION_TYPE_GRANULA", "type", "getViewsGranularity", "views", "view", "_SECTION_TYPE_GRANULA2", "roundDate", "utils", "granularity", "date", "startOfYear", "startOfMonth", "startOfDay", "roundedDate", "setMinutes", "setSeconds", "setMilliseconds", "getDefaultReferenceDate", "props", "timezone", "inGetTodayDate", "_props$disableIgnorin", "referenceDate", "minDate", "isAfterDay", "maxDate", "isBeforeDay", "isAfter", "disableIgnoringDatePartForTimeValidation", "minTime", "maxTime"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/x-date-pickers/internals/utils/getDefaultReferenceDate.js"], "sourcesContent": ["import { createIsAfterIgnoreDatePart } from './time-utils';\nimport { mergeDateAndTime, getTodayDate } from './date-utils';\nexport const SECTION_TYPE_GRANULARITY = {\n  year: 1,\n  month: 2,\n  day: 3,\n  hours: 4,\n  minutes: 5,\n  seconds: 6,\n  milliseconds: 7\n};\nexport const getSectionTypeGranularity = sections => Math.max(...sections.map(section => {\n  var _SECTION_TYPE_GRANULA;\n  return (_SECTION_TYPE_GRANULA = SECTION_TYPE_GRANULARITY[section.type]) != null ? _SECTION_TYPE_GRANULA : 1;\n}));\nexport const getViewsGranularity = views => Math.max(...views.map(view => {\n  var _SECTION_TYPE_GRANULA2;\n  return (_SECTION_TYPE_GRANULA2 = SECTION_TYPE_GRANULARITY[view]) != null ? _SECTION_TYPE_GRANULA2 : 1;\n}));\nconst roundDate = (utils, granularity, date) => {\n  if (granularity === SECTION_TYPE_GRANULARITY.year) {\n    return utils.startOfYear(date);\n  }\n  if (granularity === SECTION_TYPE_GRANULARITY.month) {\n    return utils.startOfMonth(date);\n  }\n  if (granularity === SECTION_TYPE_GRANULARITY.day) {\n    return utils.startOfDay(date);\n  }\n\n  // We don't have startOfHour / startOfMinute / startOfSecond\n  let roundedDate = date;\n  if (granularity < SECTION_TYPE_GRANULARITY.minutes) {\n    roundedDate = utils.setMinutes(roundedDate, 0);\n  }\n  if (granularity < SECTION_TYPE_GRANULARITY.seconds) {\n    roundedDate = utils.setSeconds(roundedDate, 0);\n  }\n  if (granularity < SECTION_TYPE_GRANULARITY.milliseconds) {\n    roundedDate = utils.setMilliseconds(roundedDate, 0);\n  }\n  return roundedDate;\n};\nexport const getDefaultReferenceDate = ({\n  props,\n  utils,\n  granularity,\n  timezone,\n  getTodayDate: inGetTodayDate\n}) => {\n  var _props$disableIgnorin;\n  let referenceDate = inGetTodayDate ? inGetTodayDate() : roundDate(utils, granularity, getTodayDate(utils, timezone));\n  if (props.minDate != null && utils.isAfterDay(props.minDate, referenceDate)) {\n    referenceDate = roundDate(utils, granularity, props.minDate);\n  }\n  if (props.maxDate != null && utils.isBeforeDay(props.maxDate, referenceDate)) {\n    referenceDate = roundDate(utils, granularity, props.maxDate);\n  }\n  const isAfter = createIsAfterIgnoreDatePart((_props$disableIgnorin = props.disableIgnoringDatePartForTimeValidation) != null ? _props$disableIgnorin : false, utils);\n  if (props.minTime != null && isAfter(props.minTime, referenceDate)) {\n    referenceDate = roundDate(utils, granularity, props.disableIgnoringDatePartForTimeValidation ? props.minTime : mergeDateAndTime(utils, referenceDate, props.minTime));\n  }\n  if (props.maxTime != null && isAfter(referenceDate, props.maxTime)) {\n    referenceDate = roundDate(utils, granularity, props.disableIgnoringDatePartForTimeValidation ? props.maxTime : mergeDateAndTime(utils, referenceDate, props.maxTime));\n  }\n  return referenceDate;\n};"], "mappings": "AAAA,SAASA,2BAA2B,QAAQ,cAAc;AAC1D,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,cAAc;AAC7D,OAAO,MAAMC,wBAAwB,GAAG;EACtCC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,CAAC;EACRC,GAAG,EAAE,CAAC;EACNC,KAAK,EAAE,CAAC;EACRC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACVC,YAAY,EAAE;AAChB,CAAC;AACD,OAAO,MAAMC,yBAAyB,GAAGC,QAAQ,IAAIC,IAAI,CAACC,GAAG,CAAC,GAAGF,QAAQ,CAACG,GAAG,CAACC,OAAO,IAAI;EACvF,IAAIC,qBAAqB;EACzB,OAAO,CAACA,qBAAqB,GAAGd,wBAAwB,CAACa,OAAO,CAACE,IAAI,CAAC,KAAK,IAAI,GAAGD,qBAAqB,GAAG,CAAC;AAC7G,CAAC,CAAC,CAAC;AACH,OAAO,MAAME,mBAAmB,GAAGC,KAAK,IAAIP,IAAI,CAACC,GAAG,CAAC,GAAGM,KAAK,CAACL,GAAG,CAACM,IAAI,IAAI;EACxE,IAAIC,sBAAsB;EAC1B,OAAO,CAACA,sBAAsB,GAAGnB,wBAAwB,CAACkB,IAAI,CAAC,KAAK,IAAI,GAAGC,sBAAsB,GAAG,CAAC;AACvG,CAAC,CAAC,CAAC;AACH,MAAMC,SAAS,GAAGA,CAACC,KAAK,EAAEC,WAAW,EAAEC,IAAI,KAAK;EAC9C,IAAID,WAAW,KAAKtB,wBAAwB,CAACC,IAAI,EAAE;IACjD,OAAOoB,KAAK,CAACG,WAAW,CAACD,IAAI,CAAC;EAChC;EACA,IAAID,WAAW,KAAKtB,wBAAwB,CAACE,KAAK,EAAE;IAClD,OAAOmB,KAAK,CAACI,YAAY,CAACF,IAAI,CAAC;EACjC;EACA,IAAID,WAAW,KAAKtB,wBAAwB,CAACG,GAAG,EAAE;IAChD,OAAOkB,KAAK,CAACK,UAAU,CAACH,IAAI,CAAC;EAC/B;;EAEA;EACA,IAAII,WAAW,GAAGJ,IAAI;EACtB,IAAID,WAAW,GAAGtB,wBAAwB,CAACK,OAAO,EAAE;IAClDsB,WAAW,GAAGN,KAAK,CAACO,UAAU,CAACD,WAAW,EAAE,CAAC,CAAC;EAChD;EACA,IAAIL,WAAW,GAAGtB,wBAAwB,CAACM,OAAO,EAAE;IAClDqB,WAAW,GAAGN,KAAK,CAACQ,UAAU,CAACF,WAAW,EAAE,CAAC,CAAC;EAChD;EACA,IAAIL,WAAW,GAAGtB,wBAAwB,CAACO,YAAY,EAAE;IACvDoB,WAAW,GAAGN,KAAK,CAACS,eAAe,CAACH,WAAW,EAAE,CAAC,CAAC;EACrD;EACA,OAAOA,WAAW;AACpB,CAAC;AACD,OAAO,MAAMI,uBAAuB,GAAGA,CAAC;EACtCC,KAAK;EACLX,KAAK;EACLC,WAAW;EACXW,QAAQ;EACRlC,YAAY,EAAEmC;AAChB,CAAC,KAAK;EACJ,IAAIC,qBAAqB;EACzB,IAAIC,aAAa,GAAGF,cAAc,GAAGA,cAAc,CAAC,CAAC,GAAGd,SAAS,CAACC,KAAK,EAAEC,WAAW,EAAEvB,YAAY,CAACsB,KAAK,EAAEY,QAAQ,CAAC,CAAC;EACpH,IAAID,KAAK,CAACK,OAAO,IAAI,IAAI,IAAIhB,KAAK,CAACiB,UAAU,CAACN,KAAK,CAACK,OAAO,EAAED,aAAa,CAAC,EAAE;IAC3EA,aAAa,GAAGhB,SAAS,CAACC,KAAK,EAAEC,WAAW,EAAEU,KAAK,CAACK,OAAO,CAAC;EAC9D;EACA,IAAIL,KAAK,CAACO,OAAO,IAAI,IAAI,IAAIlB,KAAK,CAACmB,WAAW,CAACR,KAAK,CAACO,OAAO,EAAEH,aAAa,CAAC,EAAE;IAC5EA,aAAa,GAAGhB,SAAS,CAACC,KAAK,EAAEC,WAAW,EAAEU,KAAK,CAACO,OAAO,CAAC;EAC9D;EACA,MAAME,OAAO,GAAG5C,2BAA2B,CAAC,CAACsC,qBAAqB,GAAGH,KAAK,CAACU,wCAAwC,KAAK,IAAI,GAAGP,qBAAqB,GAAG,KAAK,EAAEd,KAAK,CAAC;EACpK,IAAIW,KAAK,CAACW,OAAO,IAAI,IAAI,IAAIF,OAAO,CAACT,KAAK,CAACW,OAAO,EAAEP,aAAa,CAAC,EAAE;IAClEA,aAAa,GAAGhB,SAAS,CAACC,KAAK,EAAEC,WAAW,EAAEU,KAAK,CAACU,wCAAwC,GAAGV,KAAK,CAACW,OAAO,GAAG7C,gBAAgB,CAACuB,KAAK,EAAEe,aAAa,EAAEJ,KAAK,CAACW,OAAO,CAAC,CAAC;EACvK;EACA,IAAIX,KAAK,CAACY,OAAO,IAAI,IAAI,IAAIH,OAAO,CAACL,aAAa,EAAEJ,KAAK,CAACY,OAAO,CAAC,EAAE;IAClER,aAAa,GAAGhB,SAAS,CAACC,KAAK,EAAEC,WAAW,EAAEU,KAAK,CAACU,wCAAwC,GAAGV,KAAK,CAACY,OAAO,GAAG9C,gBAAgB,CAACuB,KAAK,EAAEe,aAAa,EAAEJ,KAAK,CAACY,OAAO,CAAC,CAAC;EACvK;EACA,OAAOR,aAAa;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}