{"ast": null, "code": "import buildFormatLongFn from \"../../../_lib/buildFormatLongFn/index.js\"; //Source: https://www.unicode.org/cldr/charts/32/summary/gu.html\nvar dateFormats = {\n  full: 'EEEE, d MMMM, y',\n  // CLDR #1825\n  long: 'd MMMM, y',\n  // CLDR #1826\n  medium: 'd MMM, y',\n  // CLDR #1827\n  short: 'd/M/yy' // CLDR #1828\n};\nvar timeFormats = {\n  full: 'hh:mm:ss a zzzz',\n  // CLDR #1829\n  long: 'hh:mm:ss a z',\n  // CLDR #1830\n  medium: 'hh:mm:ss a',\n  // CLDR #1831\n  short: 'hh:mm a' // CLDR #1832\n};\nvar dateTimeFormats = {\n  full: '{{date}} {{time}}',\n  // CLDR #1833\n  long: '{{date}} {{time}}',\n  // CLDR #1834\n  medium: '{{date}} {{time}}',\n  // CLDR #1835\n  short: '{{date}} {{time}}' // CLDR #1836\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nexport default formatLong;", "map": {"version": 3, "names": ["buildFormatLongFn", "dateFormats", "full", "long", "medium", "short", "timeFormats", "dateTimeFormats", "formatLong", "date", "formats", "defaultWidth", "time", "dateTime"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/gu/_lib/formatLong/index.js"], "sourcesContent": ["import buildFormatLongFn from \"../../../_lib/buildFormatLongFn/index.js\"; //Source: https://www.unicode.org/cldr/charts/32/summary/gu.html\nvar dateFormats = {\n  full: 'EEEE, d MMMM, y',\n  // CLDR #1825\n  long: 'd MMMM, y',\n  // CLDR #1826\n  medium: 'd MMM, y',\n  // CLDR #1827\n  short: 'd/M/yy' // CLDR #1828\n};\n\nvar timeFormats = {\n  full: 'hh:mm:ss a zzzz',\n  // CLDR #1829\n  long: 'hh:mm:ss a z',\n  // CLDR #1830\n  medium: 'hh:mm:ss a',\n  // CLDR #1831\n  short: 'hh:mm a' // CLDR #1832\n};\n\nvar dateTimeFormats = {\n  full: '{{date}} {{time}}',\n  // CLDR #1833\n  long: '{{date}} {{time}}',\n  // CLDR #1834\n  medium: '{{date}} {{time}}',\n  // CLDR #1835\n  short: '{{date}} {{time}}' // CLDR #1836\n};\n\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nexport default formatLong;"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,0CAA0C,CAAC,CAAC;AAC1E,IAAIC,WAAW,GAAG;EAChBC,IAAI,EAAE,iBAAiB;EACvB;EACAC,IAAI,EAAE,WAAW;EACjB;EACAC,MAAM,EAAE,UAAU;EAClB;EACAC,KAAK,EAAE,QAAQ,CAAC;AAClB,CAAC;AAED,IAAIC,WAAW,GAAG;EAChBJ,IAAI,EAAE,iBAAiB;EACvB;EACAC,IAAI,EAAE,cAAc;EACpB;EACAC,MAAM,EAAE,YAAY;EACpB;EACAC,KAAK,EAAE,SAAS,CAAC;AACnB,CAAC;AAED,IAAIE,eAAe,GAAG;EACpBL,IAAI,EAAE,mBAAmB;EACzB;EACAC,IAAI,EAAE,mBAAmB;EACzB;EACAC,MAAM,EAAE,mBAAmB;EAC3B;EACAC,KAAK,EAAE,mBAAmB,CAAC;AAC7B,CAAC;AAED,IAAIG,UAAU,GAAG;EACfC,IAAI,EAAET,iBAAiB,CAAC;IACtBU,OAAO,EAAET,WAAW;IACpBU,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,IAAI,EAAEZ,iBAAiB,CAAC;IACtBU,OAAO,EAAEJ,WAAW;IACpBK,YAAY,EAAE;EAChB,CAAC,CAAC;EACFE,QAAQ,EAAEb,iBAAiB,CAAC;IAC1BU,OAAO,EAAEH,eAAe;IACxBI,YAAY,EAAE;EAChB,CAAC;AACH,CAAC;AACD,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}