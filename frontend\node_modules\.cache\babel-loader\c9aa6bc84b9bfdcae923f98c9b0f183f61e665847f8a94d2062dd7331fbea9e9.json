{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { secondsInMinute } from \"../constants/index.js\";\n/**\n * @name minutesToSeconds\n * @category Conversion Helpers\n * @summary Convert minutes to seconds.\n *\n * @description\n * Convert a number of minutes to a full number of seconds.\n *\n * @param { number } minutes - number of minutes to be converted\n *\n * @returns {number} the number of minutes converted in seconds\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 minutes to seconds\n * const result = minutesToSeconds(2)\n * //=> 120\n */\nexport default function minutesToSeconds(minutes) {\n  requiredArgs(1, arguments);\n  return Math.floor(minutes * secondsInMinute);\n}", "map": {"version": 3, "names": ["requiredArgs", "secondsInMinute", "minutesToSeconds", "minutes", "arguments", "Math", "floor"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/minutesToSeconds/index.js"], "sourcesContent": ["import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { secondsInMinute } from \"../constants/index.js\";\n/**\n * @name minutesToSeconds\n * @category Conversion Helpers\n * @summary Convert minutes to seconds.\n *\n * @description\n * Convert a number of minutes to a full number of seconds.\n *\n * @param { number } minutes - number of minutes to be converted\n *\n * @returns {number} the number of minutes converted in seconds\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 minutes to seconds\n * const result = minutesToSeconds(2)\n * //=> 120\n */\nexport default function minutesToSeconds(minutes) {\n  requiredArgs(1, arguments);\n  return Math.floor(minutes * secondsInMinute);\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,SAASC,eAAe,QAAQ,uBAAuB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,gBAAgBA,CAACC,OAAO,EAAE;EAChDH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,OAAOC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAGF,eAAe,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}