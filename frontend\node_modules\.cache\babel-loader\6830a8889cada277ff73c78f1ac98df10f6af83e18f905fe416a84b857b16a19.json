{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"ownerState\"];\nimport * as React from 'react';\nimport { useSlotProps } from '@mui/base/utils';\nimport MuiIconButton from '@mui/material/IconButton';\nimport InputAdornment from '@mui/material/InputAdornment';\nimport { ClearIcon } from '../icons';\nimport { useLocaleText } from '../internals';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const useClearableField = ({\n  clearable,\n  fieldProps: forwardedFieldProps,\n  InputProps: ForwardedInputProps,\n  onClear,\n  slots,\n  slotProps,\n  components,\n  componentsProps\n}) => {\n  var _ref, _slots$clearButton, _slotProps$clearButto, _ref2, _slots$clearIcon, _slotProps$clearIcon;\n  const localeText = useLocaleText();\n  const IconButton = (_ref = (_slots$clearButton = slots == null ? void 0 : slots.clearButton) != null ? _slots$clearButton : components == null ? void 0 : components.ClearButton) != null ? _ref : MuiIconButton;\n  // The spread is here to avoid this bug mui/material-ui#34056\n  const _useSlotProps = useSlotProps({\n      elementType: IconButton,\n      externalSlotProps: (_slotProps$clearButto = slotProps == null ? void 0 : slotProps.clearButton) != null ? _slotProps$clearButto : componentsProps == null ? void 0 : componentsProps.clearButton,\n      ownerState: {},\n      className: 'clearButton',\n      additionalProps: {\n        title: localeText.fieldClearLabel\n      }\n    }),\n    iconButtonProps = _objectWithoutPropertiesLoose(_useSlotProps, _excluded);\n  const EndClearIcon = (_ref2 = (_slots$clearIcon = slots == null ? void 0 : slots.clearIcon) != null ? _slots$clearIcon : components == null ? void 0 : components.ClearIcon) != null ? _ref2 : ClearIcon;\n  const endClearIconProps = useSlotProps({\n    elementType: EndClearIcon,\n    externalSlotProps: (_slotProps$clearIcon = slotProps == null ? void 0 : slotProps.clearIcon) != null ? _slotProps$clearIcon : componentsProps == null ? void 0 : componentsProps.clearIcon,\n    ownerState: {}\n  });\n  const InputProps = _extends({}, ForwardedInputProps, {\n    endAdornment: /*#__PURE__*/_jsxs(React.Fragment, {\n      children: [clearable && /*#__PURE__*/_jsx(InputAdornment, {\n        position: \"end\",\n        sx: {\n          marginRight: ForwardedInputProps != null && ForwardedInputProps.endAdornment ? -1 : -1.5\n        },\n        children: /*#__PURE__*/_jsx(IconButton, _extends({}, iconButtonProps, {\n          onClick: onClear,\n          children: /*#__PURE__*/_jsx(EndClearIcon, _extends({\n            fontSize: \"small\"\n          }, endClearIconProps))\n        }))\n      }), ForwardedInputProps == null ? void 0 : ForwardedInputProps.endAdornment]\n    })\n  });\n  const fieldProps = _extends({}, forwardedFieldProps, {\n    sx: [{\n      '& .clearButton': {\n        opacity: 1\n      },\n      '@media (pointer: fine)': {\n        '& .clearButton': {\n          opacity: 0\n        },\n        '&:hover, &:focus-within': {\n          '.clearButton': {\n            opacity: 1\n          }\n        }\n      }\n    }, ...(Array.isArray(forwardedFieldProps.sx) ? forwardedFieldProps.sx : [forwardedFieldProps.sx])]\n  });\n  return {\n    InputProps,\n    fieldProps\n  };\n};", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "useSlotProps", "MuiIconButton", "InputAdornment", "ClearIcon", "useLocaleText", "jsx", "_jsx", "jsxs", "_jsxs", "useClearableField", "clearable", "fieldProps", "forwardedFieldProps", "InputProps", "ForwardedInputProps", "onClear", "slots", "slotProps", "components", "componentsProps", "_ref", "_slots$clearButton", "_slotProps$clearButto", "_ref2", "_slots$clearIcon", "_slotProps$clearIcon", "localeText", "IconButton", "clearButton", "ClearButton", "_useSlotProps", "elementType", "externalSlotProps", "ownerState", "className", "additionalProps", "title", "fieldClearLabel", "iconButtonProps", "EndClearIcon", "clearIcon", "endClearIconProps", "endAdornment", "Fragment", "children", "position", "sx", "marginRight", "onClick", "fontSize", "opacity", "Array", "isArray"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/x-date-pickers/hooks/useClearableField.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"ownerState\"];\nimport * as React from 'react';\nimport { useSlotProps } from '@mui/base/utils';\nimport MuiIconButton from '@mui/material/IconButton';\nimport InputAdornment from '@mui/material/InputAdornment';\nimport { ClearIcon } from '../icons';\nimport { useLocaleText } from '../internals';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const useClearableField = ({\n  clearable,\n  fieldProps: forwardedFieldProps,\n  InputProps: ForwardedInputProps,\n  onClear,\n  slots,\n  slotProps,\n  components,\n  componentsProps\n}) => {\n  var _ref, _slots$clearButton, _slotProps$clearButto, _ref2, _slots$clearIcon, _slotProps$clearIcon;\n  const localeText = useLocaleText();\n  const IconButton = (_ref = (_slots$clearButton = slots == null ? void 0 : slots.clearButton) != null ? _slots$clearButton : components == null ? void 0 : components.ClearButton) != null ? _ref : MuiIconButton;\n  // The spread is here to avoid this bug mui/material-ui#34056\n  const _useSlotProps = useSlotProps({\n      elementType: IconButton,\n      externalSlotProps: (_slotProps$clearButto = slotProps == null ? void 0 : slotProps.clearButton) != null ? _slotProps$clearButto : componentsProps == null ? void 0 : componentsProps.clearButton,\n      ownerState: {},\n      className: 'clearButton',\n      additionalProps: {\n        title: localeText.fieldClearLabel\n      }\n    }),\n    iconButtonProps = _objectWithoutPropertiesLoose(_useSlotProps, _excluded);\n  const EndClearIcon = (_ref2 = (_slots$clearIcon = slots == null ? void 0 : slots.clearIcon) != null ? _slots$clearIcon : components == null ? void 0 : components.ClearIcon) != null ? _ref2 : ClearIcon;\n  const endClearIconProps = useSlotProps({\n    elementType: EndClearIcon,\n    externalSlotProps: (_slotProps$clearIcon = slotProps == null ? void 0 : slotProps.clearIcon) != null ? _slotProps$clearIcon : componentsProps == null ? void 0 : componentsProps.clearIcon,\n    ownerState: {}\n  });\n  const InputProps = _extends({}, ForwardedInputProps, {\n    endAdornment: /*#__PURE__*/_jsxs(React.Fragment, {\n      children: [clearable && /*#__PURE__*/_jsx(InputAdornment, {\n        position: \"end\",\n        sx: {\n          marginRight: ForwardedInputProps != null && ForwardedInputProps.endAdornment ? -1 : -1.5\n        },\n        children: /*#__PURE__*/_jsx(IconButton, _extends({}, iconButtonProps, {\n          onClick: onClear,\n          children: /*#__PURE__*/_jsx(EndClearIcon, _extends({\n            fontSize: \"small\"\n          }, endClearIconProps))\n        }))\n      }), ForwardedInputProps == null ? void 0 : ForwardedInputProps.endAdornment]\n    })\n  });\n  const fieldProps = _extends({}, forwardedFieldProps, {\n    sx: [{\n      '& .clearButton': {\n        opacity: 1\n      },\n      '@media (pointer: fine)': {\n        '& .clearButton': {\n          opacity: 0\n        },\n        '&:hover, &:focus-within': {\n          '.clearButton': {\n            opacity: 1\n          }\n        }\n      }\n    }, ...(Array.isArray(forwardedFieldProps.sx) ? forwardedFieldProps.sx : [forwardedFieldProps.sx])]\n  });\n  return {\n    InputProps,\n    fieldProps\n  };\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,YAAY,CAAC;AAChC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,SAASC,SAAS,QAAQ,UAAU;AACpC,SAASC,aAAa,QAAQ,cAAc;AAC5C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,OAAO,MAAMC,iBAAiB,GAAGA,CAAC;EAChCC,SAAS;EACTC,UAAU,EAAEC,mBAAmB;EAC/BC,UAAU,EAAEC,mBAAmB;EAC/BC,OAAO;EACPC,KAAK;EACLC,SAAS;EACTC,UAAU;EACVC;AACF,CAAC,KAAK;EACJ,IAAIC,IAAI,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,KAAK,EAAEC,gBAAgB,EAAEC,oBAAoB;EAClG,MAAMC,UAAU,GAAGtB,aAAa,CAAC,CAAC;EAClC,MAAMuB,UAAU,GAAG,CAACP,IAAI,GAAG,CAACC,kBAAkB,GAAGL,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACY,WAAW,KAAK,IAAI,GAAGP,kBAAkB,GAAGH,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACW,WAAW,KAAK,IAAI,GAAGT,IAAI,GAAGnB,aAAa;EAChN;EACA,MAAM6B,aAAa,GAAG9B,YAAY,CAAC;MAC/B+B,WAAW,EAAEJ,UAAU;MACvBK,iBAAiB,EAAE,CAACV,qBAAqB,GAAGL,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACW,WAAW,KAAK,IAAI,GAAGN,qBAAqB,GAAGH,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACS,WAAW;MAChMK,UAAU,EAAE,CAAC,CAAC;MACdC,SAAS,EAAE,aAAa;MACxBC,eAAe,EAAE;QACfC,KAAK,EAAEV,UAAU,CAACW;MACpB;IACF,CAAC,CAAC;IACFC,eAAe,GAAGzC,6BAA6B,CAACiC,aAAa,EAAEhC,SAAS,CAAC;EAC3E,MAAMyC,YAAY,GAAG,CAAChB,KAAK,GAAG,CAACC,gBAAgB,GAAGR,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACwB,SAAS,KAAK,IAAI,GAAGhB,gBAAgB,GAAGN,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACf,SAAS,KAAK,IAAI,GAAGoB,KAAK,GAAGpB,SAAS;EACxM,MAAMsC,iBAAiB,GAAGzC,YAAY,CAAC;IACrC+B,WAAW,EAAEQ,YAAY;IACzBP,iBAAiB,EAAE,CAACP,oBAAoB,GAAGR,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACuB,SAAS,KAAK,IAAI,GAAGf,oBAAoB,GAAGN,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACqB,SAAS;IAC1LP,UAAU,EAAE,CAAC;EACf,CAAC,CAAC;EACF,MAAMpB,UAAU,GAAGjB,QAAQ,CAAC,CAAC,CAAC,EAAEkB,mBAAmB,EAAE;IACnD4B,YAAY,EAAE,aAAalC,KAAK,CAACT,KAAK,CAAC4C,QAAQ,EAAE;MAC/CC,QAAQ,EAAE,CAAClC,SAAS,IAAI,aAAaJ,IAAI,CAACJ,cAAc,EAAE;QACxD2C,QAAQ,EAAE,KAAK;QACfC,EAAE,EAAE;UACFC,WAAW,EAAEjC,mBAAmB,IAAI,IAAI,IAAIA,mBAAmB,CAAC4B,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;QACvF,CAAC;QACDE,QAAQ,EAAE,aAAatC,IAAI,CAACqB,UAAU,EAAE/B,QAAQ,CAAC,CAAC,CAAC,EAAE0C,eAAe,EAAE;UACpEU,OAAO,EAAEjC,OAAO;UAChB6B,QAAQ,EAAE,aAAatC,IAAI,CAACiC,YAAY,EAAE3C,QAAQ,CAAC;YACjDqD,QAAQ,EAAE;UACZ,CAAC,EAAER,iBAAiB,CAAC;QACvB,CAAC,CAAC;MACJ,CAAC,CAAC,EAAE3B,mBAAmB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAAC4B,YAAY;IAC7E,CAAC;EACH,CAAC,CAAC;EACF,MAAM/B,UAAU,GAAGf,QAAQ,CAAC,CAAC,CAAC,EAAEgB,mBAAmB,EAAE;IACnDkC,EAAE,EAAE,CAAC;MACH,gBAAgB,EAAE;QAChBI,OAAO,EAAE;MACX,CAAC;MACD,wBAAwB,EAAE;QACxB,gBAAgB,EAAE;UAChBA,OAAO,EAAE;QACX,CAAC;QACD,yBAAyB,EAAE;UACzB,cAAc,EAAE;YACdA,OAAO,EAAE;UACX;QACF;MACF;IACF,CAAC,EAAE,IAAIC,KAAK,CAACC,OAAO,CAACxC,mBAAmB,CAACkC,EAAE,CAAC,GAAGlC,mBAAmB,CAACkC,EAAE,GAAG,CAAClC,mBAAmB,CAACkC,EAAE,CAAC,CAAC;EACnG,CAAC,CAAC;EACF,OAAO;IACLjC,UAAU;IACVF;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}