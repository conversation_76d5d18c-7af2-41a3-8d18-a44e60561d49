{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['R', 'A'],\n  abbreviated: ['RC', 'AD'],\n  wide: ['ro Chrìosta', 'anno domini']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['C1', 'C2', 'C3', 'C4'],\n  wide: [\"a' chiad chairteal\", 'an dàrna cairteal', 'an treas cairteal', 'an ceathramh cairteal']\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nvar monthValues = {\n  narrow: ['F', 'G', 'M', 'G', 'C', 'Ò', 'I', 'L', 'S', 'D', 'S', 'D'],\n  abbreviated: ['<PERSON>ao<PERSON>', '<PERSON>', '<PERSON>àrt', 'Gibl', '<PERSON>è<PERSON>', 'Ògmh', 'Iuch', 'Lùn', 'Sult', 'Dàmh', 'Samh', 'Dùbh'],\n  wide: ['Am Faoilleach', 'An Gearran', 'Am Màrt', 'An Giblean', 'An Cèitean', 'An t-Ògmhios', 'An t-Iuchar', 'An Lùnastal', 'An t-Sultain', 'An Dàmhair', 'An t-Samhain', 'An Dùbhlachd']\n};\nvar dayValues = {\n  narrow: ['D', 'L', 'M', 'C', 'A', 'H', 'S'],\n  short: ['Dò', 'Lu', 'Mà', 'Ci', 'Ar', 'Ha', 'Sa'],\n  abbreviated: ['Did', 'Dil', 'Dim', 'Dic', 'Dia', 'Dih', 'Dis'],\n  wide: ['Didòmhnaich', 'Diluain', 'Dimàirt', 'Diciadain', 'Diardaoin', 'Dihaoine', 'Disathairne']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'm',\n    pm: 'f',\n    midnight: 'm.o.',\n    noon: 'm.l.',\n    morning: 'madainn',\n    afternoon: 'feasgar',\n    evening: 'feasgar',\n    night: 'oidhche'\n  },\n  abbreviated: {\n    am: 'M.',\n    pm: 'F.',\n    midnight: 'meadhan oidhche',\n    noon: 'meadhan là',\n    morning: 'madainn',\n    afternoon: 'feasgar',\n    evening: 'feasgar',\n    night: 'oidhche'\n  },\n  wide: {\n    am: 'm.',\n    pm: 'f.',\n    midnight: 'meadhan oidhche',\n    noon: 'meadhan là',\n    morning: 'madainn',\n    afternoon: 'feasgar',\n    evening: 'feasgar',\n    night: 'oidhche'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'm',\n    pm: 'f',\n    midnight: 'm.o.',\n    noon: 'm.l.',\n    morning: 'sa mhadainn',\n    afternoon: 'feasgar',\n    evening: 'feasgar',\n    night: 'air an oidhche'\n  },\n  abbreviated: {\n    am: 'M.',\n    pm: 'F.',\n    midnight: 'meadhan oidhche',\n    noon: 'meadhan là',\n    morning: 'sa mhadainn',\n    afternoon: 'feasgar',\n    evening: 'feasgar',\n    night: 'air an oidhche'\n  },\n  wide: {\n    am: 'm.',\n    pm: 'f.',\n    midnight: 'meadhan oidhche',\n    noon: 'meadhan là',\n    morning: 'sa mhadainn',\n    afternoon: 'feasgar',\n    evening: 'feasgar',\n    night: 'air an oidhche'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber) {\n  var number = Number(dirtyNumber);\n  var rem100 = number % 100;\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n        return number + 'd';\n      case 2:\n        return number + 'na';\n    }\n  }\n  if (rem100 === 12) {\n    return number + 'na';\n  }\n  return number + 'mh';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "number", "Number", "rem100", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/gd/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['R', 'A'],\n  abbreviated: ['RC', 'AD'],\n  wide: ['ro Chrìosta', 'anno domini']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['C1', 'C2', 'C3', 'C4'],\n  wide: [\"a' chiad chairteal\", 'an dàrna cairteal', 'an treas cairteal', 'an ceathramh cairteal']\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nvar monthValues = {\n  narrow: ['F', 'G', 'M', 'G', 'C', 'Ò', 'I', 'L', 'S', 'D', 'S', 'D'],\n  abbreviated: ['<PERSON>ao<PERSON>', '<PERSON>', '<PERSON>àrt', 'Gibl', '<PERSON>è<PERSON>', 'Ògmh', 'Iuch', 'Lùn', 'Sult', 'Dàmh', 'Samh', 'Dùbh'],\n  wide: ['Am Faoilleach', 'An Gearran', 'Am Màrt', 'An Giblean', 'An Cèitean', 'An t-Ògmhios', 'An t-Iuchar', 'An Lùnastal', 'An t-Sultain', 'An Dàmhair', 'An t-Samhain', 'An Dùbhlachd']\n};\nvar dayValues = {\n  narrow: ['D', 'L', 'M', 'C', 'A', 'H', 'S'],\n  short: ['Dò', 'Lu', 'Mà', 'Ci', 'Ar', 'Ha', 'Sa'],\n  abbreviated: ['Did', 'Dil', 'Dim', 'Dic', 'Dia', 'Dih', 'Dis'],\n  wide: ['Didòmhnaich', 'Diluain', 'Dimàirt', 'Diciadain', 'Diardaoin', 'Dihaoine', 'Disathairne']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'm',\n    pm: 'f',\n    midnight: 'm.o.',\n    noon: 'm.l.',\n    morning: 'madainn',\n    afternoon: 'feasgar',\n    evening: 'feasgar',\n    night: 'oidhche'\n  },\n  abbreviated: {\n    am: 'M.',\n    pm: 'F.',\n    midnight: 'meadhan oidhche',\n    noon: 'meadhan là',\n    morning: 'madainn',\n    afternoon: 'feasgar',\n    evening: 'feasgar',\n    night: 'oidhche'\n  },\n  wide: {\n    am: 'm.',\n    pm: 'f.',\n    midnight: 'meadhan oidhche',\n    noon: 'meadhan là',\n    morning: 'madainn',\n    afternoon: 'feasgar',\n    evening: 'feasgar',\n    night: 'oidhche'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'm',\n    pm: 'f',\n    midnight: 'm.o.',\n    noon: 'm.l.',\n    morning: 'sa mhadainn',\n    afternoon: 'feasgar',\n    evening: 'feasgar',\n    night: 'air an oidhche'\n  },\n  abbreviated: {\n    am: 'M.',\n    pm: 'F.',\n    midnight: 'meadhan oidhche',\n    noon: 'meadhan là',\n    morning: 'sa mhadainn',\n    afternoon: 'feasgar',\n    evening: 'feasgar',\n    night: 'air an oidhche'\n  },\n  wide: {\n    am: 'm.',\n    pm: 'f.',\n    midnight: 'meadhan oidhche',\n    noon: 'meadhan là',\n    morning: 'sa mhadainn',\n    afternoon: 'feasgar',\n    evening: 'feasgar',\n    night: 'air an oidhche'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber) {\n  var number = Number(dirtyNumber);\n  var rem100 = number % 100;\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n        return number + 'd';\n      case 2:\n        return number + 'na';\n    }\n  }\n  if (rem100 === 12) {\n    return number + 'na';\n  }\n  return number + 'mh';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EAClBC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EACzBC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa;AACrC,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,oBAAoB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,uBAAuB;AAChG,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EAC5GC,IAAI,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,EAAE,aAAa,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc;AACzL,CAAC;AACD,IAAIG,SAAS,GAAG;EACdL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CAAC,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa;AACjG,CAAC;AACD,IAAIK,eAAe,GAAG;EACpBP,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,MAAM;IAChBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,iBAAiB;IAC3BC,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,iBAAiB;IAC3BC,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BhB,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,MAAM;IAChBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,aAAa;IACtBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,iBAAiB;IAC3BC,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE,aAAa;IACtBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,iBAAiB;IAC3BC,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE,aAAa;IACtBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAE;EACtD,IAAIC,MAAM,GAAGC,MAAM,CAACF,WAAW,CAAC;EAChC,IAAIG,MAAM,GAAGF,MAAM,GAAG,GAAG;EACzB,IAAIE,MAAM,GAAG,EAAE,IAAIA,MAAM,GAAG,EAAE,EAAE;IAC9B,QAAQA,MAAM,GAAG,EAAE;MACjB,KAAK,CAAC;QACJ,OAAOF,MAAM,GAAG,GAAG;MACrB,KAAK,CAAC;QACJ,OAAOA,MAAM,GAAG,IAAI;IACxB;EACF;EACA,IAAIE,MAAM,KAAK,EAAE,EAAE;IACjB,OAAOF,MAAM,GAAG,IAAI;EACtB;EACA,OAAOA,MAAM,GAAG,IAAI;AACtB,CAAC;AACD,IAAIG,QAAQ,GAAG;EACbL,aAAa,EAAEA,aAAa;EAC5BM,GAAG,EAAEzB,eAAe,CAAC;IACnB0B,MAAM,EAAEzB,SAAS;IACjB0B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE5B,eAAe,CAAC;IACvB0B,MAAM,EAAErB,aAAa;IACrBsB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAE9B,eAAe,CAAC;IACrB0B,MAAM,EAAEpB,WAAW;IACnBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFI,GAAG,EAAE/B,eAAe,CAAC;IACnB0B,MAAM,EAAEnB,SAAS;IACjBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,SAAS,EAAEhC,eAAe,CAAC;IACzB0B,MAAM,EAAEjB,eAAe;IACvBkB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEf,yBAAyB;IAC3CgB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}