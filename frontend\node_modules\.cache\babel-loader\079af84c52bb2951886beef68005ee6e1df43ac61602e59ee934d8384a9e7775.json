{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\"; // Source: https://www.unicode.org/cldr/charts/32/summary/te.html\n// Source: https://dsal.uchicago.edu/dictionaries/brown/\n// CLDR #1605 - #1608\nvar eraValues = {\n  narrow: ['క్రీ.పూ.', 'క్రీ.శ.'],\n  abbreviated: ['క్రీ.పూ.', 'క్రీ.శ.'],\n  wide: ['క్రీస్తు పూర్వం', 'క్రీస్తుశకం']\n};\n\n// CLDR #1613 - #1628\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['త్రై1', 'త్రై2', 'త్రై3', 'త్రై4'],\n  wide: ['1వ త్రైమాసికం', '2వ త్రైమాసికం', '3వ త్రైమాసికం', '4వ త్రైమాసికం']\n};\n\n// CLDR #1637 - #1708\nvar monthValues = {\n  narrow: ['జ', 'ఫి', 'మా', 'ఏ', 'మే', 'జూ', 'జు', 'ఆ', 'సె', 'అ', 'న', 'డి'],\n  abbreviated: ['జన', 'ఫిబ్ర', 'మార్చి', 'ఏప్రి', 'మే', 'జూన్', 'జులై', 'ఆగ', 'సెప్టెం', 'అక్టో', 'నవం', 'డిసెం'],\n  wide: ['జనవరి', 'ఫిబ్రవరి', 'మార్చి', 'ఏప్రిల్', 'మే', 'జూన్', 'జులై', 'ఆగస్టు', 'సెప్టెంబర్', 'అక్టోబర్', 'నవంబర్', 'డిసెంబర్']\n};\n\n// CLDR #1709 - #1764\nvar dayValues = {\n  narrow: ['ఆ', 'సో', 'మ', 'బు', 'గు', 'శు', 'శ'],\n  short: ['ఆది', 'సోమ', 'మంగళ', 'బుధ', 'గురు', 'శుక్ర', 'శని'],\n  abbreviated: ['ఆది', 'సోమ', 'మంగళ', 'బుధ', 'గురు', 'శుక్ర', 'శని'],\n  wide: ['ఆదివారం', 'సోమవారం', 'మంగళవారం', 'బుధవారం', 'గురువారం', 'శుక్రవారం', 'శనివారం']\n};\n\n// CLDR #1767 - #1806\nvar dayPeriodValues = {\n  narrow: {\n    am: 'పూర్వాహ్నం',\n    pm: 'అపరాహ్నం',\n    midnight: 'అర్ధరాత్రి',\n    noon: 'మిట్టమధ్యాహ్నం',\n    morning: 'ఉదయం',\n    afternoon: 'మధ్యాహ్నం',\n    evening: 'సాయంత్రం',\n    night: 'రాత్రి'\n  },\n  abbreviated: {\n    am: 'పూర్వాహ్నం',\n    pm: 'అపరాహ్నం',\n    midnight: 'అర్ధరాత్రి',\n    noon: 'మిట్టమధ్యాహ్నం',\n    morning: 'ఉదయం',\n    afternoon: 'మధ్యాహ్నం',\n    evening: 'సాయంత్రం',\n    night: 'రాత్రి'\n  },\n  wide: {\n    am: 'పూర్వాహ్నం',\n    pm: 'అపరాహ్నం',\n    midnight: 'అర్ధరాత్రి',\n    noon: 'మిట్టమధ్యాహ్నం',\n    morning: 'ఉదయం',\n    afternoon: 'మధ్యాహ్నం',\n    evening: 'సాయంత్రం',\n    night: 'రాత్రి'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'పూర్వాహ్నం',\n    pm: 'అపరాహ్నం',\n    midnight: 'అర్ధరాత్రి',\n    noon: 'మిట్టమధ్యాహ్నం',\n    morning: 'ఉదయం',\n    afternoon: 'మధ్యాహ్నం',\n    evening: 'సాయంత్రం',\n    night: 'రాత్రి'\n  },\n  abbreviated: {\n    am: 'పూర్వాహ్నం',\n    pm: 'అపరాహ్నం',\n    midnight: 'అర్ధరాత్రి',\n    noon: 'మిట్టమధ్యాహ్నం',\n    morning: 'ఉదయం',\n    afternoon: 'మధ్యాహ్నం',\n    evening: 'సాయంత్రం',\n    night: 'రాత్రి'\n  },\n  wide: {\n    am: 'పూర్వాహ్నం',\n    pm: 'అపరాహ్నం',\n    midnight: 'అర్ధరాత్రి',\n    noon: 'మిట్టమధ్యాహ్నం',\n    morning: 'ఉదయం',\n    afternoon: 'మధ్యాహ్నం',\n    evening: 'సాయంత్రం',\n    night: 'రాత్రి'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + 'వ';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "number", "Number", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/te/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\"; // Source: https://www.unicode.org/cldr/charts/32/summary/te.html\n// Source: https://dsal.uchicago.edu/dictionaries/brown/\n// CLDR #1605 - #1608\nvar eraValues = {\n  narrow: ['క్రీ.పూ.', 'క్రీ.శ.'],\n  abbreviated: ['క్రీ.పూ.', 'క్రీ.శ.'],\n  wide: ['క్రీస్తు పూర్వం', 'క్రీస్తుశకం']\n};\n\n// CLDR #1613 - #1628\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['త్రై1', 'త్రై2', 'త్రై3', 'త్రై4'],\n  wide: ['1వ త్రైమాసికం', '2వ త్రైమాసికం', '3వ త్రైమాసికం', '4వ త్రైమాసికం']\n};\n\n// CLDR #1637 - #1708\nvar monthValues = {\n  narrow: ['జ', 'ఫి', 'మా', 'ఏ', 'మే', 'జూ', 'జు', 'ఆ', 'సె', 'అ', 'న', 'డి'],\n  abbreviated: ['జన', 'ఫిబ్ర', 'మార్చి', 'ఏప్రి', 'మే', 'జూన్', 'జులై', 'ఆగ', 'సెప్టెం', 'అక్టో', 'నవం', 'డిసెం'],\n  wide: ['జనవరి', 'ఫిబ్రవరి', 'మార్చి', 'ఏప్రిల్', 'మే', 'జూన్', 'జులై', 'ఆగస్టు', 'సెప్టెంబర్', 'అక్టోబర్', 'నవంబర్', 'డిసెంబర్']\n};\n\n// CLDR #1709 - #1764\nvar dayValues = {\n  narrow: ['ఆ', 'సో', 'మ', 'బు', 'గు', 'శు', 'శ'],\n  short: ['ఆది', 'సోమ', 'మంగళ', 'బుధ', 'గురు', 'శుక్ర', 'శని'],\n  abbreviated: ['ఆది', 'సోమ', 'మంగళ', 'బుధ', 'గురు', 'శుక్ర', 'శని'],\n  wide: ['ఆదివారం', 'సోమవారం', 'మంగళవారం', 'బుధవారం', 'గురువారం', 'శుక్రవారం', 'శనివారం']\n};\n\n// CLDR #1767 - #1806\nvar dayPeriodValues = {\n  narrow: {\n    am: 'పూర్వాహ్నం',\n    pm: 'అపరాహ్నం',\n    midnight: 'అర్ధరాత్రి',\n    noon: 'మిట్టమధ్యాహ్నం',\n    morning: 'ఉదయం',\n    afternoon: 'మధ్యాహ్నం',\n    evening: 'సాయంత్రం',\n    night: 'రాత్రి'\n  },\n  abbreviated: {\n    am: 'పూర్వాహ్నం',\n    pm: 'అపరాహ్నం',\n    midnight: 'అర్ధరాత్రి',\n    noon: 'మిట్టమధ్యాహ్నం',\n    morning: 'ఉదయం',\n    afternoon: 'మధ్యాహ్నం',\n    evening: 'సాయంత్రం',\n    night: 'రాత్రి'\n  },\n  wide: {\n    am: 'పూర్వాహ్నం',\n    pm: 'అపరాహ్నం',\n    midnight: 'అర్ధరాత్రి',\n    noon: 'మిట్టమధ్యాహ్నం',\n    morning: 'ఉదయం',\n    afternoon: 'మధ్యాహ్నం',\n    evening: 'సాయంత్రం',\n    night: 'రాత్రి'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'పూర్వాహ్నం',\n    pm: 'అపరాహ్నం',\n    midnight: 'అర్ధరాత్రి',\n    noon: 'మిట్టమధ్యాహ్నం',\n    morning: 'ఉదయం',\n    afternoon: 'మధ్యాహ్నం',\n    evening: 'సాయంత్రం',\n    night: 'రాత్రి'\n  },\n  abbreviated: {\n    am: 'పూర్వాహ్నం',\n    pm: 'అపరాహ్నం',\n    midnight: 'అర్ధరాత్రి',\n    noon: 'మిట్టమధ్యాహ్నం',\n    morning: 'ఉదయం',\n    afternoon: 'మధ్యాహ్నం',\n    evening: 'సాయంత్రం',\n    night: 'రాత్రి'\n  },\n  wide: {\n    am: 'పూర్వాహ్నం',\n    pm: 'అపరాహ్నం',\n    midnight: 'అర్ధరాత్రి',\n    noon: 'మిట్టమధ్యాహ్నం',\n    morning: 'ఉదయం',\n    afternoon: 'మధ్యాహ్నం',\n    evening: 'సాయంత్రం',\n    night: 'రాత్రి'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + 'వ';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC,CAAC,CAAC;AACtE;AACA;AACA,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC;EAC/BC,WAAW,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC;EACpCC,IAAI,EAAE,CAAC,iBAAiB,EAAE,aAAa;AACzC,CAAC;;AAED;AACA,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACjDC,IAAI,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe;AAC3E,CAAC;;AAED;AACA,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;EAC3EC,WAAW,EAAE,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC;EAC/GC,IAAI,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU;AACjI,CAAC;;AAED;AACA,IAAIG,SAAS,GAAG;EACdL,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;EAC/CM,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC;EAC5DL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC;EAClEC,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS;AACxF,CAAC;;AAED;AACA,IAAIK,eAAe,GAAG;EACpBP,MAAM,EAAE;IACNQ,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,gBAAgB;IACtBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,gBAAgB;IACtBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,gBAAgB;IACtBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BhB,MAAM,EAAE;IACNQ,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,gBAAgB;IACtBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,gBAAgB;IACtBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,gBAAgB;IACtBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,QAAQ,EAAE;EAChE,IAAIC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAChC,OAAOE,MAAM,GAAG,GAAG;AACrB,CAAC;AACD,IAAIE,QAAQ,GAAG;EACbL,aAAa,EAAEA,aAAa;EAC5BM,GAAG,EAAEzB,eAAe,CAAC;IACnB0B,MAAM,EAAEzB,SAAS;IACjB0B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE5B,eAAe,CAAC;IACvB0B,MAAM,EAAErB,aAAa;IACrBsB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAE9B,eAAe,CAAC;IACrB0B,MAAM,EAAEpB,WAAW;IACnBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFI,GAAG,EAAE/B,eAAe,CAAC;IACnB0B,MAAM,EAAEnB,SAAS;IACjBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,SAAS,EAAEhC,eAAe,CAAC;IACzB0B,MAAM,EAAEjB,eAAe;IACvBkB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEf,yBAAyB;IAC3CgB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}