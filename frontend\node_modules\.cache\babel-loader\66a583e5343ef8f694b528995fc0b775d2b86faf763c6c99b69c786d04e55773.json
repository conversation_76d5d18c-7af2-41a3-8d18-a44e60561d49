{"ast": null, "code": "import addMinutes from \"../addMinutes/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport startOfMinute from \"../startOfMinute/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachMinuteOfInterval\n * @category Interval Helpers\n * @summary Return the array of minutes within the specified time interval.\n *\n * @description\n * Returns the array of minutes within the specified time interval.\n *\n * @param {Interval} interval - the interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @param {Object} [options] - an object with options.\n * @param {Number} [options.step=1] - the step to increment by. The step must be equal to or greater than 1\n * @throws {TypeError} 1 argument required\n * @returns {Date[]} the array with starts of minutes from the minute of the interval start to the minute of the interval end\n * @throws {RangeError} `options.step` must be a number equal to or greater than 1\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Each minute between 14 October 2020, 13:00 and 14 October 2020, 13:03\n * const result = eachMinuteOfInterval({\n *   start: new Date(2014, 9, 14, 13),\n *   end: new Date(2014, 9, 14, 13, 3)\n * })\n * //=> [\n * //   Wed Oct 14 2014 13:00:00,\n * //   Wed Oct 14 2014 13:01:00,\n * //   Wed Oct 14 2014 13:02:00,\n * //   Wed Oct 14 2014 13:03:00\n * // ]\n */\nexport default function eachMinuteOfInterval(interval, options) {\n  var _options$step;\n  requiredArgs(1, arguments);\n  var startDate = startOfMinute(toDate(interval.start));\n  var endDate = toDate(interval.end);\n  var startTime = startDate.getTime();\n  var endTime = endDate.getTime();\n  if (startTime >= endTime) {\n    throw new RangeError('Invalid interval');\n  }\n  var dates = [];\n  var currentDate = startDate;\n  var step = Number((_options$step = options === null || options === void 0 ? void 0 : options.step) !== null && _options$step !== void 0 ? _options$step : 1);\n  if (step < 1 || isNaN(step)) throw new RangeError('`options.step` must be a number equal to or greater than 1');\n  while (currentDate.getTime() <= endTime) {\n    dates.push(toDate(currentDate));\n    currentDate = addMinutes(currentDate, step);\n  }\n  return dates;\n}", "map": {"version": 3, "names": ["addMinutes", "toDate", "startOfMinute", "requiredArgs", "eachMinuteOfInterval", "interval", "options", "_options$step", "arguments", "startDate", "start", "endDate", "end", "startTime", "getTime", "endTime", "RangeError", "dates", "currentDate", "step", "Number", "isNaN", "push"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/eachMinuteOfInterval/index.js"], "sourcesContent": ["import addMinutes from \"../addMinutes/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport startOfMinute from \"../startOfMinute/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachMinuteOfInterval\n * @category Interval Helpers\n * @summary Return the array of minutes within the specified time interval.\n *\n * @description\n * Returns the array of minutes within the specified time interval.\n *\n * @param {Interval} interval - the interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @param {Object} [options] - an object with options.\n * @param {Number} [options.step=1] - the step to increment by. The step must be equal to or greater than 1\n * @throws {TypeError} 1 argument required\n * @returns {Date[]} the array with starts of minutes from the minute of the interval start to the minute of the interval end\n * @throws {RangeError} `options.step` must be a number equal to or greater than 1\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Each minute between 14 October 2020, 13:00 and 14 October 2020, 13:03\n * const result = eachMinuteOfInterval({\n *   start: new Date(2014, 9, 14, 13),\n *   end: new Date(2014, 9, 14, 13, 3)\n * })\n * //=> [\n * //   Wed Oct 14 2014 13:00:00,\n * //   Wed Oct 14 2014 13:01:00,\n * //   Wed Oct 14 2014 13:02:00,\n * //   Wed Oct 14 2014 13:03:00\n * // ]\n */\nexport default function eachMinuteOfInterval(interval, options) {\n  var _options$step;\n  requiredArgs(1, arguments);\n  var startDate = startOfMinute(toDate(interval.start));\n  var endDate = toDate(interval.end);\n  var startTime = startDate.getTime();\n  var endTime = endDate.getTime();\n  if (startTime >= endTime) {\n    throw new RangeError('Invalid interval');\n  }\n  var dates = [];\n  var currentDate = startDate;\n  var step = Number((_options$step = options === null || options === void 0 ? void 0 : options.step) !== null && _options$step !== void 0 ? _options$step : 1);\n  if (step < 1 || isNaN(step)) throw new RangeError('`options.step` must be a number equal to or greater than 1');\n  while (currentDate.getTime() <= endTime) {\n    dates.push(toDate(currentDate));\n    currentDate = addMinutes(currentDate, step);\n  }\n  return dates;\n}"], "mappings": "AAAA,OAAOA,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,oBAAoBA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EAC9D,IAAIC,aAAa;EACjBJ,YAAY,CAAC,CAAC,EAAEK,SAAS,CAAC;EAC1B,IAAIC,SAAS,GAAGP,aAAa,CAACD,MAAM,CAACI,QAAQ,CAACK,KAAK,CAAC,CAAC;EACrD,IAAIC,OAAO,GAAGV,MAAM,CAACI,QAAQ,CAACO,GAAG,CAAC;EAClC,IAAIC,SAAS,GAAGJ,SAAS,CAACK,OAAO,CAAC,CAAC;EACnC,IAAIC,OAAO,GAAGJ,OAAO,CAACG,OAAO,CAAC,CAAC;EAC/B,IAAID,SAAS,IAAIE,OAAO,EAAE;IACxB,MAAM,IAAIC,UAAU,CAAC,kBAAkB,CAAC;EAC1C;EACA,IAAIC,KAAK,GAAG,EAAE;EACd,IAAIC,WAAW,GAAGT,SAAS;EAC3B,IAAIU,IAAI,GAAGC,MAAM,CAAC,CAACb,aAAa,GAAGD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACa,IAAI,MAAM,IAAI,IAAIZ,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,CAAC,CAAC;EAC5J,IAAIY,IAAI,GAAG,CAAC,IAAIE,KAAK,CAACF,IAAI,CAAC,EAAE,MAAM,IAAIH,UAAU,CAAC,4DAA4D,CAAC;EAC/G,OAAOE,WAAW,CAACJ,OAAO,CAAC,CAAC,IAAIC,OAAO,EAAE;IACvCE,KAAK,CAACK,IAAI,CAACrB,MAAM,CAACiB,WAAW,CAAC,CAAC;IAC/BA,WAAW,GAAGlB,UAAU,CAACkB,WAAW,EAAEC,IAAI,CAAC;EAC7C;EACA,OAAOF,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}