{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useIsDateDisabled } from './useIsDateDisabled';\nimport { useUtils } from '../internals/hooks/useUtils';\nimport { singleItemValueManager } from '../internals/utils/valueManagers';\nimport { SECTION_TYPE_GRANULARITY } from '../internals/utils/getDefaultReferenceDate';\nexport const createCalendarStateReducer = (reduceAnimations, disableSwitchToMonthOnDayFocus, utils) => (state, action) => {\n  switch (action.type) {\n    case 'changeMonth':\n      return _extends({}, state, {\n        slideDirection: action.direction,\n        currentMonth: action.newMonth,\n        isMonthSwitchingAnimating: !reduceAnimations\n      });\n    case 'finishMonthSwitchingAnimation':\n      return _extends({}, state, {\n        isMonthSwitchingAnimating: false\n      });\n    case 'changeFocusedDay':\n      {\n        if (state.focusedDay != null && action.focusedDay != null && utils.isSameDay(action.focusedDay, state.focusedDay)) {\n          return state;\n        }\n        const needMonthSwitch = action.focusedDay != null && !disableSwitchToMonthOnDayFocus && !utils.isSameMonth(state.currentMonth, action.focusedDay);\n        return _extends({}, state, {\n          focusedDay: action.focusedDay,\n          isMonthSwitchingAnimating: needMonthSwitch && !reduceAnimations && !action.withoutMonthSwitchingAnimation,\n          currentMonth: needMonthSwitch ? utils.startOfMonth(action.focusedDay) : state.currentMonth,\n          slideDirection: action.focusedDay != null && utils.isAfterDay(action.focusedDay, state.currentMonth) ? 'left' : 'right'\n        });\n      }\n    default:\n      throw new Error('missing support');\n  }\n};\nexport const useCalendarState = params => {\n  const {\n    value,\n    referenceDate: referenceDateProp,\n    defaultCalendarMonth,\n    disableFuture,\n    disablePast,\n    disableSwitchToMonthOnDayFocus = false,\n    maxDate,\n    minDate,\n    onMonthChange,\n    reduceAnimations,\n    shouldDisableDate,\n    timezone\n  } = params;\n  const utils = useUtils();\n  const reducerFn = React.useRef(createCalendarStateReducer(Boolean(reduceAnimations), disableSwitchToMonthOnDayFocus, utils)).current;\n  const referenceDate = React.useMemo(() => {\n    let externalReferenceDate = null;\n    if (referenceDateProp) {\n      externalReferenceDate = referenceDateProp;\n    } else if (defaultCalendarMonth) {\n      // For `defaultCalendarMonth`, we just want to keep the month and the year to avoid a behavior change.\n      externalReferenceDate = utils.startOfMonth(defaultCalendarMonth);\n    }\n    return singleItemValueManager.getInitialReferenceValue({\n      value,\n      utils,\n      timezone,\n      props: params,\n      referenceDate: externalReferenceDate,\n      granularity: SECTION_TYPE_GRANULARITY.day\n    });\n  }, [] // eslint-disable-line react-hooks/exhaustive-deps\n  );\n  const [calendarState, dispatch] = React.useReducer(reducerFn, {\n    isMonthSwitchingAnimating: false,\n    focusedDay: referenceDate,\n    currentMonth: utils.startOfMonth(referenceDate),\n    slideDirection: 'left'\n  });\n  const handleChangeMonth = React.useCallback(payload => {\n    dispatch(_extends({\n      type: 'changeMonth'\n    }, payload));\n    if (onMonthChange) {\n      onMonthChange(payload.newMonth);\n    }\n  }, [onMonthChange]);\n  const changeMonth = React.useCallback(newDate => {\n    const newDateRequested = newDate;\n    if (utils.isSameMonth(newDateRequested, calendarState.currentMonth)) {\n      return;\n    }\n    handleChangeMonth({\n      newMonth: utils.startOfMonth(newDateRequested),\n      direction: utils.isAfterDay(newDateRequested, calendarState.currentMonth) ? 'left' : 'right'\n    });\n  }, [calendarState.currentMonth, handleChangeMonth, utils]);\n  const isDateDisabled = useIsDateDisabled({\n    shouldDisableDate,\n    minDate,\n    maxDate,\n    disableFuture,\n    disablePast,\n    timezone\n  });\n  const onMonthSwitchingAnimationEnd = React.useCallback(() => {\n    dispatch({\n      type: 'finishMonthSwitchingAnimation'\n    });\n  }, []);\n  const changeFocusedDay = useEventCallback((newFocusedDate, withoutMonthSwitchingAnimation) => {\n    if (!isDateDisabled(newFocusedDate)) {\n      dispatch({\n        type: 'changeFocusedDay',\n        focusedDay: newFocusedDate,\n        withoutMonthSwitchingAnimation\n      });\n    }\n  });\n  return {\n    referenceDate,\n    calendarState,\n    changeMonth,\n    changeFocusedDay,\n    isDateDisabled,\n    onMonthSwitchingAnimationEnd,\n    handleChangeMonth\n  };\n};", "map": {"version": 3, "names": ["_extends", "React", "useEventCallback", "useIsDateDisabled", "useUtils", "singleItemValueManager", "SECTION_TYPE_GRANULARITY", "createCalendarStateReducer", "reduceAnimations", "disableSwitchToMonthOnDayFocus", "utils", "state", "action", "type", "slideDirection", "direction", "currentMonth", "newMonth", "isMonthSwitchingAnimating", "focusedDay", "isSameDay", "needMonthSwitch", "isSameMonth", "withoutMonthSwitchingAnimation", "startOfMonth", "isAfterDay", "Error", "useCalendarState", "params", "value", "referenceDate", "referenceDateProp", "defaultCalendarMonth", "disableFuture", "disablePast", "maxDate", "minDate", "onMonthChange", "shouldDisableDate", "timezone", "reducerFn", "useRef", "Boolean", "current", "useMemo", "externalReferenceDate", "getInitialReferenceValue", "props", "granularity", "day", "calendarState", "dispatch", "useReducer", "handleChangeMonth", "useCallback", "payload", "changeMonth", "newDate", "newDateRequested", "isDateDisabled", "onMonthSwitchingAnimationEnd", "changeFocusedDay", "newFocusedDate"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/x-date-pickers/DateCalendar/useCalendarState.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useIsDateDisabled } from './useIsDateDisabled';\nimport { useUtils } from '../internals/hooks/useUtils';\nimport { singleItemValueManager } from '../internals/utils/valueManagers';\nimport { SECTION_TYPE_GRANULARITY } from '../internals/utils/getDefaultReferenceDate';\nexport const createCalendarStateReducer = (reduceAnimations, disableSwitchToMonthOnDayFocus, utils) => (state, action) => {\n  switch (action.type) {\n    case 'changeMonth':\n      return _extends({}, state, {\n        slideDirection: action.direction,\n        currentMonth: action.newMonth,\n        isMonthSwitchingAnimating: !reduceAnimations\n      });\n    case 'finishMonthSwitchingAnimation':\n      return _extends({}, state, {\n        isMonthSwitchingAnimating: false\n      });\n    case 'changeFocusedDay':\n      {\n        if (state.focusedDay != null && action.focusedDay != null && utils.isSameDay(action.focusedDay, state.focusedDay)) {\n          return state;\n        }\n        const needMonthSwitch = action.focusedDay != null && !disableSwitchToMonthOnDayFocus && !utils.isSameMonth(state.currentMonth, action.focusedDay);\n        return _extends({}, state, {\n          focusedDay: action.focusedDay,\n          isMonthSwitchingAnimating: needMonthSwitch && !reduceAnimations && !action.withoutMonthSwitchingAnimation,\n          currentMonth: needMonthSwitch ? utils.startOfMonth(action.focusedDay) : state.currentMonth,\n          slideDirection: action.focusedDay != null && utils.isAfterDay(action.focusedDay, state.currentMonth) ? 'left' : 'right'\n        });\n      }\n    default:\n      throw new Error('missing support');\n  }\n};\nexport const useCalendarState = params => {\n  const {\n    value,\n    referenceDate: referenceDateProp,\n    defaultCalendarMonth,\n    disableFuture,\n    disablePast,\n    disableSwitchToMonthOnDayFocus = false,\n    maxDate,\n    minDate,\n    onMonthChange,\n    reduceAnimations,\n    shouldDisableDate,\n    timezone\n  } = params;\n  const utils = useUtils();\n  const reducerFn = React.useRef(createCalendarStateReducer(Boolean(reduceAnimations), disableSwitchToMonthOnDayFocus, utils)).current;\n  const referenceDate = React.useMemo(() => {\n    let externalReferenceDate = null;\n    if (referenceDateProp) {\n      externalReferenceDate = referenceDateProp;\n    } else if (defaultCalendarMonth) {\n      // For `defaultCalendarMonth`, we just want to keep the month and the year to avoid a behavior change.\n      externalReferenceDate = utils.startOfMonth(defaultCalendarMonth);\n    }\n    return singleItemValueManager.getInitialReferenceValue({\n      value,\n      utils,\n      timezone,\n      props: params,\n      referenceDate: externalReferenceDate,\n      granularity: SECTION_TYPE_GRANULARITY.day\n    });\n  }, [] // eslint-disable-line react-hooks/exhaustive-deps\n  );\n  const [calendarState, dispatch] = React.useReducer(reducerFn, {\n    isMonthSwitchingAnimating: false,\n    focusedDay: referenceDate,\n    currentMonth: utils.startOfMonth(referenceDate),\n    slideDirection: 'left'\n  });\n  const handleChangeMonth = React.useCallback(payload => {\n    dispatch(_extends({\n      type: 'changeMonth'\n    }, payload));\n    if (onMonthChange) {\n      onMonthChange(payload.newMonth);\n    }\n  }, [onMonthChange]);\n  const changeMonth = React.useCallback(newDate => {\n    const newDateRequested = newDate;\n    if (utils.isSameMonth(newDateRequested, calendarState.currentMonth)) {\n      return;\n    }\n    handleChangeMonth({\n      newMonth: utils.startOfMonth(newDateRequested),\n      direction: utils.isAfterDay(newDateRequested, calendarState.currentMonth) ? 'left' : 'right'\n    });\n  }, [calendarState.currentMonth, handleChangeMonth, utils]);\n  const isDateDisabled = useIsDateDisabled({\n    shouldDisableDate,\n    minDate,\n    maxDate,\n    disableFuture,\n    disablePast,\n    timezone\n  });\n  const onMonthSwitchingAnimationEnd = React.useCallback(() => {\n    dispatch({\n      type: 'finishMonthSwitchingAnimation'\n    });\n  }, []);\n  const changeFocusedDay = useEventCallback((newFocusedDate, withoutMonthSwitchingAnimation) => {\n    if (!isDateDisabled(newFocusedDate)) {\n      dispatch({\n        type: 'changeFocusedDay',\n        focusedDay: newFocusedDate,\n        withoutMonthSwitchingAnimation\n      });\n    }\n  });\n  return {\n    referenceDate,\n    calendarState,\n    changeMonth,\n    changeFocusedDay,\n    isDateDisabled,\n    onMonthSwitchingAnimationEnd,\n    handleChangeMonth\n  };\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,QAAQ,QAAQ,6BAA6B;AACtD,SAASC,sBAAsB,QAAQ,kCAAkC;AACzE,SAASC,wBAAwB,QAAQ,4CAA4C;AACrF,OAAO,MAAMC,0BAA0B,GAAGA,CAACC,gBAAgB,EAAEC,8BAA8B,EAAEC,KAAK,KAAK,CAACC,KAAK,EAAEC,MAAM,KAAK;EACxH,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,aAAa;MAChB,OAAOb,QAAQ,CAAC,CAAC,CAAC,EAAEW,KAAK,EAAE;QACzBG,cAAc,EAAEF,MAAM,CAACG,SAAS;QAChCC,YAAY,EAAEJ,MAAM,CAACK,QAAQ;QAC7BC,yBAAyB,EAAE,CAACV;MAC9B,CAAC,CAAC;IACJ,KAAK,+BAA+B;MAClC,OAAOR,QAAQ,CAAC,CAAC,CAAC,EAAEW,KAAK,EAAE;QACzBO,yBAAyB,EAAE;MAC7B,CAAC,CAAC;IACJ,KAAK,kBAAkB;MACrB;QACE,IAAIP,KAAK,CAACQ,UAAU,IAAI,IAAI,IAAIP,MAAM,CAACO,UAAU,IAAI,IAAI,IAAIT,KAAK,CAACU,SAAS,CAACR,MAAM,CAACO,UAAU,EAAER,KAAK,CAACQ,UAAU,CAAC,EAAE;UACjH,OAAOR,KAAK;QACd;QACA,MAAMU,eAAe,GAAGT,MAAM,CAACO,UAAU,IAAI,IAAI,IAAI,CAACV,8BAA8B,IAAI,CAACC,KAAK,CAACY,WAAW,CAACX,KAAK,CAACK,YAAY,EAAEJ,MAAM,CAACO,UAAU,CAAC;QACjJ,OAAOnB,QAAQ,CAAC,CAAC,CAAC,EAAEW,KAAK,EAAE;UACzBQ,UAAU,EAAEP,MAAM,CAACO,UAAU;UAC7BD,yBAAyB,EAAEG,eAAe,IAAI,CAACb,gBAAgB,IAAI,CAACI,MAAM,CAACW,8BAA8B;UACzGP,YAAY,EAAEK,eAAe,GAAGX,KAAK,CAACc,YAAY,CAACZ,MAAM,CAACO,UAAU,CAAC,GAAGR,KAAK,CAACK,YAAY;UAC1FF,cAAc,EAAEF,MAAM,CAACO,UAAU,IAAI,IAAI,IAAIT,KAAK,CAACe,UAAU,CAACb,MAAM,CAACO,UAAU,EAAER,KAAK,CAACK,YAAY,CAAC,GAAG,MAAM,GAAG;QAClH,CAAC,CAAC;MACJ;IACF;MACE,MAAM,IAAIU,KAAK,CAAC,iBAAiB,CAAC;EACtC;AACF,CAAC;AACD,OAAO,MAAMC,gBAAgB,GAAGC,MAAM,IAAI;EACxC,MAAM;IACJC,KAAK;IACLC,aAAa,EAAEC,iBAAiB;IAChCC,oBAAoB;IACpBC,aAAa;IACbC,WAAW;IACXzB,8BAA8B,GAAG,KAAK;IACtC0B,OAAO;IACPC,OAAO;IACPC,aAAa;IACb7B,gBAAgB;IAChB8B,iBAAiB;IACjBC;EACF,CAAC,GAAGX,MAAM;EACV,MAAMlB,KAAK,GAAGN,QAAQ,CAAC,CAAC;EACxB,MAAMoC,SAAS,GAAGvC,KAAK,CAACwC,MAAM,CAAClC,0BAA0B,CAACmC,OAAO,CAAClC,gBAAgB,CAAC,EAAEC,8BAA8B,EAAEC,KAAK,CAAC,CAAC,CAACiC,OAAO;EACpI,MAAMb,aAAa,GAAG7B,KAAK,CAAC2C,OAAO,CAAC,MAAM;IACxC,IAAIC,qBAAqB,GAAG,IAAI;IAChC,IAAId,iBAAiB,EAAE;MACrBc,qBAAqB,GAAGd,iBAAiB;IAC3C,CAAC,MAAM,IAAIC,oBAAoB,EAAE;MAC/B;MACAa,qBAAqB,GAAGnC,KAAK,CAACc,YAAY,CAACQ,oBAAoB,CAAC;IAClE;IACA,OAAO3B,sBAAsB,CAACyC,wBAAwB,CAAC;MACrDjB,KAAK;MACLnB,KAAK;MACL6B,QAAQ;MACRQ,KAAK,EAAEnB,MAAM;MACbE,aAAa,EAAEe,qBAAqB;MACpCG,WAAW,EAAE1C,wBAAwB,CAAC2C;IACxC,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,CAAC;EACD,MAAM,CAACC,aAAa,EAAEC,QAAQ,CAAC,GAAGlD,KAAK,CAACmD,UAAU,CAACZ,SAAS,EAAE;IAC5DtB,yBAAyB,EAAE,KAAK;IAChCC,UAAU,EAAEW,aAAa;IACzBd,YAAY,EAAEN,KAAK,CAACc,YAAY,CAACM,aAAa,CAAC;IAC/ChB,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAMuC,iBAAiB,GAAGpD,KAAK,CAACqD,WAAW,CAACC,OAAO,IAAI;IACrDJ,QAAQ,CAACnD,QAAQ,CAAC;MAChBa,IAAI,EAAE;IACR,CAAC,EAAE0C,OAAO,CAAC,CAAC;IACZ,IAAIlB,aAAa,EAAE;MACjBA,aAAa,CAACkB,OAAO,CAACtC,QAAQ,CAAC;IACjC;EACF,CAAC,EAAE,CAACoB,aAAa,CAAC,CAAC;EACnB,MAAMmB,WAAW,GAAGvD,KAAK,CAACqD,WAAW,CAACG,OAAO,IAAI;IAC/C,MAAMC,gBAAgB,GAAGD,OAAO;IAChC,IAAI/C,KAAK,CAACY,WAAW,CAACoC,gBAAgB,EAAER,aAAa,CAAClC,YAAY,CAAC,EAAE;MACnE;IACF;IACAqC,iBAAiB,CAAC;MAChBpC,QAAQ,EAAEP,KAAK,CAACc,YAAY,CAACkC,gBAAgB,CAAC;MAC9C3C,SAAS,EAAEL,KAAK,CAACe,UAAU,CAACiC,gBAAgB,EAAER,aAAa,CAAClC,YAAY,CAAC,GAAG,MAAM,GAAG;IACvF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACkC,aAAa,CAAClC,YAAY,EAAEqC,iBAAiB,EAAE3C,KAAK,CAAC,CAAC;EAC1D,MAAMiD,cAAc,GAAGxD,iBAAiB,CAAC;IACvCmC,iBAAiB;IACjBF,OAAO;IACPD,OAAO;IACPF,aAAa;IACbC,WAAW;IACXK;EACF,CAAC,CAAC;EACF,MAAMqB,4BAA4B,GAAG3D,KAAK,CAACqD,WAAW,CAAC,MAAM;IAC3DH,QAAQ,CAAC;MACPtC,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,MAAMgD,gBAAgB,GAAG3D,gBAAgB,CAAC,CAAC4D,cAAc,EAAEvC,8BAA8B,KAAK;IAC5F,IAAI,CAACoC,cAAc,CAACG,cAAc,CAAC,EAAE;MACnCX,QAAQ,CAAC;QACPtC,IAAI,EAAE,kBAAkB;QACxBM,UAAU,EAAE2C,cAAc;QAC1BvC;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,OAAO;IACLO,aAAa;IACboB,aAAa;IACbM,WAAW;IACXK,gBAAgB;IAChBF,cAAc;IACdC,4BAA4B;IAC5BP;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}