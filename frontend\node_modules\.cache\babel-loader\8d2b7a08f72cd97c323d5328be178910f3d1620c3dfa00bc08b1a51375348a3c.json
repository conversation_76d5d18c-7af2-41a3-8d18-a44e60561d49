{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['av. J.-C', 'ap. J.-C'],\n  abbreviated: ['av. J.-C', 'ap. J.-C'],\n  wide: ['avant Jésus-Christ', 'apr<PERSON>-Christ']\n};\nvar quarterValues = {\n  narrow: ['T1', 'T2', 'T3', 'T4'],\n  abbreviated: ['1er trim.', '2ème trim.', '3ème trim.', '4ème trim.'],\n  wide: ['1er trimestre', '2ème trimestre', '3ème trimestre', '4ème trimestre']\n};\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['janv.', 'févr.', 'mars', 'avr.', 'mai', 'juin', 'juil.', 'août', 'sept.', 'oct.', 'nov.', 'déc.'],\n  wide: ['janvier', 'février', 'mars', 'avril', 'mai', 'juin', 'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre']\n};\nvar dayValues = {\n  narrow: ['D', 'L', 'M', 'M', 'J', 'V', 'S'],\n  short: ['di', 'lu', 'ma', 'me', 'je', 've', 'sa'],\n  abbreviated: ['dim.', 'lun.', 'mar.', 'mer.', 'jeu.', 'ven.', 'sam.'],\n  wide: ['dimanche', 'lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'minuit',\n    noon: 'midi',\n    morning: 'mat.',\n    afternoon: 'ap.m.',\n    evening: 'soir',\n    night: 'mat.'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'minuit',\n    noon: 'midi',\n    morning: 'matin',\n    afternoon: 'après-midi',\n    evening: 'soir',\n    night: 'matin'\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'minuit',\n    noon: 'midi',\n    morning: 'du matin',\n    afternoon: 'de l’après-midi',\n    evening: 'du soir',\n    night: 'du matin'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var number = Number(dirtyNumber);\n  var unit = options === null || options === void 0 ? void 0 : options.unit;\n  if (number === 0) return '0';\n  var feminineUnits = ['year', 'week', 'hour', 'minute', 'second'];\n  var suffix;\n  if (number === 1) {\n    suffix = unit && feminineUnits.includes(unit) ? 'ère' : 'er';\n  } else {\n    suffix = 'ème';\n  }\n  return number + suffix;\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "ordinalNumber", "dirtyNumber", "options", "number", "Number", "unit", "feminineUnits", "suffix", "includes", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/fr/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['av. J.-C', 'ap. J.-C'],\n  abbreviated: ['av. J.-C', 'ap. J.-C'],\n  wide: ['avant Jésus-Christ', 'apr<PERSON>-Christ']\n};\nvar quarterValues = {\n  narrow: ['T1', 'T2', 'T3', 'T4'],\n  abbreviated: ['1er trim.', '2ème trim.', '3ème trim.', '4ème trim.'],\n  wide: ['1er trimestre', '2ème trimestre', '3ème trimestre', '4ème trimestre']\n};\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['janv.', 'févr.', 'mars', 'avr.', 'mai', 'juin', 'juil.', 'août', 'sept.', 'oct.', 'nov.', 'déc.'],\n  wide: ['janvier', 'février', 'mars', 'avril', 'mai', 'juin', 'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre']\n};\nvar dayValues = {\n  narrow: ['D', 'L', 'M', 'M', 'J', 'V', 'S'],\n  short: ['di', 'lu', 'ma', 'me', 'je', 've', 'sa'],\n  abbreviated: ['dim.', 'lun.', 'mar.', 'mer.', 'jeu.', 'ven.', 'sam.'],\n  wide: ['dimanche', 'lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'minuit',\n    noon: 'midi',\n    morning: 'mat.',\n    afternoon: 'ap.m.',\n    evening: 'soir',\n    night: 'mat.'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'minuit',\n    noon: 'midi',\n    morning: 'matin',\n    afternoon: 'après-midi',\n    evening: 'soir',\n    night: 'matin'\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'minuit',\n    noon: 'midi',\n    morning: 'du matin',\n    afternoon: 'de l’après-midi',\n    evening: 'du soir',\n    night: 'du matin'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var number = Number(dirtyNumber);\n  var unit = options === null || options === void 0 ? void 0 : options.unit;\n  if (number === 0) return '0';\n  var feminineUnits = ['year', 'week', 'hour', 'minute', 'second'];\n  var suffix;\n  if (number === 1) {\n    suffix = unit && feminineUnits.includes(unit) ? 'ère' : 'er';\n  } else {\n    suffix = 'ème';\n  }\n  return number + suffix;\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;EAChCC,WAAW,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;EACrCC,IAAI,EAAE,CAAC,oBAAoB,EAAE,oBAAoB;AACnD,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAChCC,WAAW,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,CAAC;EACpEC,IAAI,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,gBAAgB;AAC9E,CAAC;AACD,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EAChHC,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU;AAChI,CAAC;AACD,IAAIG,SAAS,GAAG;EACdL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDL,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EACrEC,IAAI,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ;AAChF,CAAC;AACD,IAAIK,eAAe,GAAG;EACpBP,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,iBAAiB;IAC5BC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,OAAO,EAAE;EAC/D,IAAIC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAChC,IAAII,IAAI,GAAGH,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,IAAI;EACzE,IAAIF,MAAM,KAAK,CAAC,EAAE,OAAO,GAAG;EAC5B,IAAIG,aAAa,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC;EAChE,IAAIC,MAAM;EACV,IAAIJ,MAAM,KAAK,CAAC,EAAE;IAChBI,MAAM,GAAGF,IAAI,IAAIC,aAAa,CAACE,QAAQ,CAACH,IAAI,CAAC,GAAG,KAAK,GAAG,IAAI;EAC9D,CAAC,MAAM;IACLE,MAAM,GAAG,KAAK;EAChB;EACA,OAAOJ,MAAM,GAAGI,MAAM;AACxB,CAAC;AACD,IAAIE,QAAQ,GAAG;EACbT,aAAa,EAAEA,aAAa;EAC5BU,GAAG,EAAE5B,eAAe,CAAC;IACnB6B,MAAM,EAAE5B,SAAS;IACjB6B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE/B,eAAe,CAAC;IACvB6B,MAAM,EAAExB,aAAa;IACrByB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAEjC,eAAe,CAAC;IACrB6B,MAAM,EAAEvB,WAAW;IACnBwB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFI,GAAG,EAAElC,eAAe,CAAC;IACnB6B,MAAM,EAAEtB,SAAS;IACjBuB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,SAAS,EAAEnC,eAAe,CAAC;IACzB6B,MAAM,EAAEpB,eAAe;IACvBqB,YAAY,EAAE;EAChB,CAAC;AACH,CAAC;AACD,eAAeH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}