{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['ق', 'ب'],\n  abbreviated: ['ق.م.', 'ب.م.'],\n  wide: ['قبل الميلاد', 'بعد الميلاد']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['ر1', 'ر2', 'ر3', 'ر4'],\n  wide: ['الربع الأول', 'الربع الثاني', 'الربع الثالث', 'الربع الرابع']\n};\nvar monthValues = {\n  narrow: ['ي', 'ف', 'م', 'أ', 'م', 'ي', 'ي', 'أ', 'س', 'أ', 'ن', 'د'],\n  abbreviated: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],\n  wide: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']\n};\nvar dayValues = {\n  narrow: ['ح', 'ن', 'ث', 'ر', 'خ', 'ج', 'س'],\n  short: ['أحد', 'اثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'],\n  abbreviated: ['أحد', 'اثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'],\n  wide: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ص',\n    pm: 'م',\n    morning: 'الصباح',\n    noon: 'الظهر',\n    afternoon: 'بعد الظهر',\n    evening: 'المساء',\n    night: 'الليل',\n    midnight: 'منتصف الليل'\n  },\n  abbreviated: {\n    am: 'ص',\n    pm: 'م',\n    morning: 'الصباح',\n    noon: 'الظهر',\n    afternoon: 'بعد الظهر',\n    evening: 'المساء',\n    night: 'الليل',\n    midnight: 'منتصف الليل'\n  },\n  wide: {\n    am: 'ص',\n    pm: 'م',\n    morning: 'الصباح',\n    noon: 'الظهر',\n    afternoon: 'بعد الظهر',\n    evening: 'المساء',\n    night: 'الليل',\n    midnight: 'منتصف الليل'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ص',\n    pm: 'م',\n    morning: 'في الصباح',\n    noon: 'الظهر',\n    afternoon: 'بعد الظهر',\n    evening: 'في المساء',\n    night: 'في الليل',\n    midnight: 'منتصف الليل'\n  },\n  abbreviated: {\n    am: 'ص',\n    pm: 'م',\n    morning: 'في الصباح',\n    noon: 'الظهر',\n    afternoon: 'بعد الظهر',\n    evening: 'في المساء',\n    night: 'في الليل',\n    midnight: 'منتصف الليل'\n  },\n  wide: {\n    am: 'ص',\n    pm: 'م',\n    morning: 'في الصباح',\n    noon: 'الظهر',\n    afternoon: 'بعد الظهر',\n    evening: 'في المساء',\n    night: 'في الليل',\n    midnight: 'منتصف الليل'\n  }\n};\nvar ordinalNumber = function ordinalNumber(num) {\n  return String(num);\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "morning", "noon", "afternoon", "evening", "night", "midnight", "formattingDayPeriodValues", "ordinalNumber", "num", "String", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/ar/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['ق', 'ب'],\n  abbreviated: ['ق.م.', 'ب.م.'],\n  wide: ['قبل الميلاد', 'بعد الميلاد']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['ر1', 'ر2', 'ر3', 'ر4'],\n  wide: ['الربع الأول', 'الربع الثاني', 'الربع الثالث', 'الربع الرابع']\n};\nvar monthValues = {\n  narrow: ['ي', 'ف', 'م', 'أ', 'م', 'ي', 'ي', 'أ', 'س', 'أ', 'ن', 'د'],\n  abbreviated: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],\n  wide: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']\n};\nvar dayValues = {\n  narrow: ['ح', 'ن', 'ث', 'ر', 'خ', 'ج', 'س'],\n  short: ['أحد', 'اثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'],\n  abbreviated: ['أحد', 'اثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'],\n  wide: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ص',\n    pm: 'م',\n    morning: 'الصباح',\n    noon: 'الظهر',\n    afternoon: 'بعد الظهر',\n    evening: 'المساء',\n    night: 'الليل',\n    midnight: 'منتصف الليل'\n  },\n  abbreviated: {\n    am: 'ص',\n    pm: 'م',\n    morning: 'الصباح',\n    noon: 'الظهر',\n    afternoon: 'بعد الظهر',\n    evening: 'المساء',\n    night: 'الليل',\n    midnight: 'منتصف الليل'\n  },\n  wide: {\n    am: 'ص',\n    pm: 'م',\n    morning: 'الصباح',\n    noon: 'الظهر',\n    afternoon: 'بعد الظهر',\n    evening: 'المساء',\n    night: 'الليل',\n    midnight: 'منتصف الليل'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ص',\n    pm: 'م',\n    morning: 'في الصباح',\n    noon: 'الظهر',\n    afternoon: 'بعد الظهر',\n    evening: 'في المساء',\n    night: 'في الليل',\n    midnight: 'منتصف الليل'\n  },\n  abbreviated: {\n    am: 'ص',\n    pm: 'م',\n    morning: 'في الصباح',\n    noon: 'الظهر',\n    afternoon: 'بعد الظهر',\n    evening: 'في المساء',\n    night: 'في الليل',\n    midnight: 'منتصف الليل'\n  },\n  wide: {\n    am: 'ص',\n    pm: 'م',\n    morning: 'في الصباح',\n    noon: 'الظهر',\n    afternoon: 'بعد الظهر',\n    evening: 'في المساء',\n    night: 'في الليل',\n    midnight: 'منتصف الليل'\n  }\n};\nvar ordinalNumber = function ordinalNumber(num) {\n  return String(num);\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EAClBC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EAC7BC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa;AACrC,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc;AACtE,CAAC;AACD,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EAC5HC,IAAI,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ;AACtH,CAAC;AACD,IAAIG,SAAS,GAAG;EACdL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC;EAClEL,WAAW,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC;EACxEC,IAAI,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO;AAChF,CAAC;AACD,IAAIK,eAAe,GAAG;EACpBP,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,OAAO,EAAE,QAAQ;IACjBC,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE;EACZ,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,OAAO,EAAE,QAAQ;IACjBC,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE;EACZ,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,OAAO,EAAE,QAAQ;IACjBC,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE;EACZ;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BhB,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,OAAO,EAAE,WAAW;IACpBC,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,UAAU;IACjBC,QAAQ,EAAE;EACZ,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,OAAO,EAAE,WAAW;IACpBC,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,UAAU;IACjBC,QAAQ,EAAE;EACZ,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,OAAO,EAAE,WAAW;IACpBC,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,UAAU;IACjBC,QAAQ,EAAE;EACZ;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,GAAG,EAAE;EAC9C,OAAOC,MAAM,CAACD,GAAG,CAAC;AACpB,CAAC;AACD,IAAIE,QAAQ,GAAG;EACbH,aAAa,EAAEA,aAAa;EAC5BI,GAAG,EAAEvB,eAAe,CAAC;IACnBwB,MAAM,EAAEvB,SAAS;IACjBwB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE1B,eAAe,CAAC;IACvBwB,MAAM,EAAEnB,aAAa;IACrBoB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAE5B,eAAe,CAAC;IACrBwB,MAAM,EAAElB,WAAW;IACnBmB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFI,GAAG,EAAE7B,eAAe,CAAC;IACnBwB,MAAM,EAAEjB,SAAS;IACjBkB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,SAAS,EAAE9B,eAAe,CAAC;IACzBwB,MAAM,EAAEf,eAAe;IACvBgB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEb,yBAAyB;IAC3Cc,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}