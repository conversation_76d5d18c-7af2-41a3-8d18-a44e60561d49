{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport startOfYear from \"../startOfYear/index.js\";\nimport differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getDayOfYear\n * @category Day Helpers\n * @summary Get the day of the year of the given date.\n *\n * @description\n * Get the day of the year of the given date.\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the day of year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Which day of the year is 2 July 2014?\n * const result = getDayOfYear(new Date(2014, 6, 2))\n * //=> 183\n */\nexport default function getDayOfYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var diff = differenceInCalendarDays(date, startOfYear(date));\n  var dayOfYear = diff + 1;\n  return dayOfYear;\n}", "map": {"version": 3, "names": ["toDate", "startOfYear", "differenceInCalendarDays", "requiredArgs", "getDayOfYear", "dirtyDate", "arguments", "date", "diff", "dayOfYear"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/getDayOfYear/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport startOfYear from \"../startOfYear/index.js\";\nimport differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getDayOfYear\n * @category Day Helpers\n * @summary Get the day of the year of the given date.\n *\n * @description\n * Get the day of the year of the given date.\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the day of year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Which day of the year is 2 July 2014?\n * const result = getDayOfYear(new Date(2014, 6, 2))\n * //=> 183\n */\nexport default function getDayOfYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var diff = differenceInCalendarDays(date, startOfYear(date));\n  var dayOfYear = diff + 1;\n  return dayOfYear;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,wBAAwB,MAAM,sCAAsC;AAC3E,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,YAAYA,CAACC,SAAS,EAAE;EAC9CF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGP,MAAM,CAACK,SAAS,CAAC;EAC5B,IAAIG,IAAI,GAAGN,wBAAwB,CAACK,IAAI,EAAEN,WAAW,CAACM,IAAI,CAAC,CAAC;EAC5D,IAAIE,SAAS,GAAGD,IAAI,GAAG,CAAC;EACxB,OAAOC,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}