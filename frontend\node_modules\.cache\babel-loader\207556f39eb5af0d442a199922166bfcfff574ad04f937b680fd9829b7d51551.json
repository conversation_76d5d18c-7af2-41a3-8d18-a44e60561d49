{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { resolveComponentProps } from '@mui/base/utils';\nimport { refType } from '@mui/utils';\nimport { singleItemValueManager } from '../internals/utils/valueManagers';\nimport { useDatePickerDefaultizedProps } from '../DatePicker/shared';\nimport { useLocaleText, useUtils, validateDate } from '../internals';\nimport { useDesktopPicker } from '../internals/hooks/useDesktopPicker';\nimport { CalendarIcon } from '../icons';\nimport { DateField } from '../DateField';\nimport { extractValidationProps } from '../internals/utils/validation/extractValidationProps';\nimport { renderDateViewCalendar } from '../dateViewRenderers';\nimport { resolveDateFormat } from '../internals/utils/date-utils';\n/**\n * Demos:\n *\n * - [DatePicker](https://mui.com/x/react-date-pickers/date-picker/)\n * - [Validation](https://mui.com/x/react-date-pickers/validation/)\n *\n * API:\n *\n * - [DesktopDatePicker API](https://mui.com/x/api/date-pickers/desktop-date-picker/)\n */\nconst DesktopDatePicker = /*#__PURE__*/React.forwardRef(function DesktopDatePicker(inProps, ref) {\n  var _defaultizedProps$yea, _defaultizedProps$slo2, _props$localeText$ope, _props$localeText;\n  const localeText = useLocaleText();\n  const utils = useUtils();\n\n  // Props with the default values common to all date pickers\n  const defaultizedProps = useDatePickerDefaultizedProps(inProps, 'MuiDesktopDatePicker');\n  const viewRenderers = _extends({\n    day: renderDateViewCalendar,\n    month: renderDateViewCalendar,\n    year: renderDateViewCalendar\n  }, defaultizedProps.viewRenderers);\n\n  // Props with the default values specific to the desktop variant\n  const props = _extends({}, defaultizedProps, {\n    viewRenderers,\n    format: resolveDateFormat(utils, defaultizedProps, false),\n    yearsPerRow: (_defaultizedProps$yea = defaultizedProps.yearsPerRow) != null ? _defaultizedProps$yea : 4,\n    slots: _extends({\n      openPickerIcon: CalendarIcon,\n      field: DateField\n    }, defaultizedProps.slots),\n    slotProps: _extends({}, defaultizedProps.slotProps, {\n      field: ownerState => {\n        var _defaultizedProps$slo;\n        return _extends({}, resolveComponentProps((_defaultizedProps$slo = defaultizedProps.slotProps) == null ? void 0 : _defaultizedProps$slo.field, ownerState), extractValidationProps(defaultizedProps), {\n          ref\n        });\n      },\n      toolbar: _extends({\n        hidden: true\n      }, (_defaultizedProps$slo2 = defaultizedProps.slotProps) == null ? void 0 : _defaultizedProps$slo2.toolbar)\n    })\n  });\n  const {\n    renderPicker\n  } = useDesktopPicker({\n    props,\n    valueManager: singleItemValueManager,\n    valueType: 'date',\n    getOpenDialogAriaText: (_props$localeText$ope = (_props$localeText = props.localeText) == null ? void 0 : _props$localeText.openDatePickerDialogue) != null ? _props$localeText$ope : localeText.openDatePickerDialogue,\n    validator: validateDate\n  });\n  return renderPicker();\n});\nDesktopDatePicker.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * If `true`, the main element is focused during the first mount.\n   * This main element is:\n   * - the element chosen by the visible view if any (i.e: the selected day on the `day` view).\n   * - the `input` element if there is a field rendered.\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * Class name applied to the root element.\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the popover or modal will close after submitting the full date.\n   * @default `true` for desktop, `false` for mobile (based on the chosen wrapper and `desktopModeMediaQuery` prop).\n   */\n  closeOnSelect: PropTypes.bool,\n  /**\n   * Overridable components.\n   * @default {}\n   * @deprecated Please use `slots`.\n   */\n  components: PropTypes.object,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   * @deprecated Please use `slotProps`.\n   */\n  componentsProps: PropTypes.object,\n  /**\n   * Formats the day of week displayed in the calendar header.\n   * @param {string} day The day of week provided by the adapter.  Deprecated, will be removed in v7: Use `date` instead.\n   * @param {TDate} date The date of the day of week provided by the adapter.\n   * @returns {string} The name to display.\n   * @default (_day: string, date: TDate) => adapter.format(date, 'weekdayShort').charAt(0).toUpperCase()\n   */\n  dayOfWeekFormatter: PropTypes.func,\n  /**\n   * Default calendar month displayed when `value` and `defaultValue` are empty.\n   * @deprecated Consider using `referenceDate` instead.\n   */\n  defaultCalendarMonth: PropTypes.any,\n  /**\n   * The default value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: PropTypes.any,\n  /**\n   * If `true`, the picker and text field are disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * If `true`, today's date is rendering without highlighting with circle.\n   * @default false\n   */\n  disableHighlightToday: PropTypes.bool,\n  /**\n   * If `true`, the open picker button will not be rendered (renders only the field).\n   * @default false\n   */\n  disableOpenPicker: PropTypes.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  /**\n   * If `true`, the week number will be display in the calendar.\n   */\n  displayWeekNumber: PropTypes.bool,\n  /**\n   * Calendar will show more weeks in order to match this value.\n   * Put it to 6 for having fix number of week in Gregorian calendars\n   * @default undefined\n   */\n  fixedWeekNumber: PropTypes.number,\n  /**\n   * Format of the date when rendered in the input(s).\n   * Defaults to localized format based on the used `views`.\n   */\n  format: PropTypes.string,\n  /**\n   * Density of the format when rendered in the input.\n   * Setting `formatDensity` to `\"spacious\"` will add a space before and after each `/`, `-` and `.` character.\n   * @default \"dense\"\n   */\n  formatDensity: PropTypes.oneOf(['dense', 'spacious']),\n  /**\n   * Pass a ref to the `input` element.\n   */\n  inputRef: refType,\n  /**\n   * The label content.\n   */\n  label: PropTypes.node,\n  /**\n   * If `true`, calls `renderLoading` instead of rendering the day calendar.\n   * Can be used to preload information and show it in calendar.\n   * @default false\n   */\n  loading: PropTypes.bool,\n  /**\n   * Locale for components texts.\n   * Allows overriding texts coming from `LocalizationProvider` and `theme`.\n   */\n  localeText: PropTypes.object,\n  /**\n   * Maximal selectable date.\n   */\n  maxDate: PropTypes.any,\n  /**\n   * Minimal selectable date.\n   */\n  minDate: PropTypes.any,\n  /**\n   * Months rendered per row.\n   * @default 3\n   */\n  monthsPerRow: PropTypes.oneOf([3, 4]),\n  /**\n   * Name attribute used by the `input` element in the Field.\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when the value is accepted.\n   * @template TValue The value type. Will be either the same type as `value` or `null`. Can be in `[start, end]` format in case of range value.\n   * @param {TValue} value The value that was just accepted.\n   */\n  onAccept: PropTypes.func,\n  /**\n   * Callback fired when the value changes.\n   * @template TValue The value type. Will be either the same type as `value` or `null`. Can be in `[start, end]` format in case of range value.\n   * @template TError The validation error type. Will be either `string` or a `null`. Can be in `[start, end]` format in case of range value.\n   * @param {TValue} value The new value.\n   * @param {FieldChangeHandlerContext<TError>} context The context containing the validation result of the current value.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be closed.\n   * Use in controlled mode (see `open`).\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback fired when the error associated to the current value changes.\n   * If the error has a non-null value, then the `TextField` will be rendered in `error` state.\n   *\n   * @template TValue The value type. Will be either the same type as `value` or `null`. Can be in `[start, end]` format in case of range value.\n   * @template TError The validation error type. Will be either `string` or a `null`. Can be in `[start, end]` format in case of range value.\n   * @param {TError} error The new error describing why the current value is not valid.\n   * @param {TValue} value The value associated to the error.\n   */\n  onError: PropTypes.func,\n  /**\n   * Callback fired on month change.\n   * @template TDate\n   * @param {TDate} month The new month.\n   */\n  onMonthChange: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be opened.\n   * Use in controlled mode (see `open`).\n   */\n  onOpen: PropTypes.func,\n  /**\n   * Callback fired when the selected sections change.\n   * @param {FieldSelectedSections} newValue The new selected sections.\n   */\n  onSelectedSectionsChange: PropTypes.func,\n  /**\n   * Callback fired on view change.\n   * @template TView\n   * @param {TView} view The new view.\n   */\n  onViewChange: PropTypes.func,\n  /**\n   * Callback fired on year change.\n   * @template TDate\n   * @param {TDate} year The new year.\n   */\n  onYearChange: PropTypes.func,\n  /**\n   * Control the popup or dialog open state.\n   * @default false\n   */\n  open: PropTypes.bool,\n  /**\n   * The default visible view.\n   * Used when the component view is not controlled.\n   * Must be a valid option from `views` list.\n   */\n  openTo: PropTypes.oneOf(['day', 'month', 'year']),\n  /**\n   * Force rendering in particular orientation.\n   */\n  orientation: PropTypes.oneOf(['landscape', 'portrait']),\n  readOnly: PropTypes.bool,\n  /**\n   * If `true`, disable heavy animations.\n   * @default `@media(prefers-reduced-motion: reduce)` || `navigator.userAgent` matches Android <10 or iOS <13\n   */\n  reduceAnimations: PropTypes.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid date-time using the validation props, except callbacks like `shouldDisable<...>`.\n   */\n  referenceDate: PropTypes.any,\n  /**\n   * Component displaying when passed `loading` true.\n   * @returns {React.ReactNode} The node to render when loading.\n   * @default () => <span data-mui-test=\"loading-progress\">...</span>\n   */\n  renderLoading: PropTypes.func,\n  /**\n   * The currently selected sections.\n   * This prop accept four formats:\n   * 1. If a number is provided, the section at this index will be selected.\n   * 2. If an object with a `startIndex` and `endIndex` properties are provided, the sections between those two indexes will be selected.\n   * 3. If a string of type `FieldSectionType` is provided, the first section with that name will be selected.\n   * 4. If `null` is provided, no section will be selected\n   * If not provided, the selected sections will be handled internally.\n   */\n  selectedSections: PropTypes.oneOfType([PropTypes.oneOf(['all', 'day', 'hours', 'meridiem', 'minutes', 'month', 'seconds', 'weekDay', 'year']), PropTypes.number, PropTypes.shape({\n    endIndex: PropTypes.number.isRequired,\n    startIndex: PropTypes.number.isRequired\n  })]),\n  /**\n   * Disable specific date.\n   *\n   * Warning: This function can be called multiple times (e.g. when rendering date calendar, checking if focus can be moved to a certain date, etc.). Expensive computations can impact performance.\n   *\n   * @template TDate\n   * @param {TDate} day The date to test.\n   * @returns {boolean} If `true` the date will be disabled.\n   */\n  shouldDisableDate: PropTypes.func,\n  /**\n   * Disable specific month.\n   * @template TDate\n   * @param {TDate} month The month to test.\n   * @returns {boolean} If `true`, the month will be disabled.\n   */\n  shouldDisableMonth: PropTypes.func,\n  /**\n   * Disable specific year.\n   * @template TDate\n   * @param {TDate} year The year to test.\n   * @returns {boolean} If `true`, the year will be disabled.\n   */\n  shouldDisableYear: PropTypes.func,\n  /**\n   * If `true`, days outside the current month are rendered:\n   *\n   * - if `fixedWeekNumber` is defined, renders days to have the weeks requested.\n   *\n   * - if `fixedWeekNumber` is not defined, renders day to fill the first and last week of the current month.\n   *\n   * - ignored if `calendars` equals more than `1` on range pickers.\n   * @default false\n   */\n  showDaysOutsideCurrentMonth: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documention} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: PropTypes.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: PropTypes.any,\n  /**\n   * The visible view.\n   * Used when the component view is controlled.\n   * Must be a valid option from `views` list.\n   */\n  view: PropTypes.oneOf(['day', 'month', 'year']),\n  /**\n   * Define custom view renderers for each section.\n   * If `null`, the section will only have field editing.\n   * If `undefined`, internally defined view will be the used.\n   */\n  viewRenderers: PropTypes.shape({\n    day: PropTypes.func,\n    month: PropTypes.func,\n    year: PropTypes.func\n  }),\n  /**\n   * Available views.\n   */\n  views: PropTypes.arrayOf(PropTypes.oneOf(['day', 'month', 'year']).isRequired),\n  /**\n   * Years rendered per row.\n   * @default 4\n   */\n  yearsPerRow: PropTypes.oneOf([3, 4])\n};\nexport { DesktopDatePicker };", "map": {"version": 3, "names": ["_extends", "React", "PropTypes", "resolveComponentProps", "refType", "singleItemValueManager", "useDatePickerDefaultizedProps", "useLocaleText", "useUtils", "validateDate", "useDesktopPicker", "CalendarIcon", "DateField", "extractValidationProps", "renderDateViewCalendar", "resolveDateFormat", "DesktopDatePicker", "forwardRef", "inProps", "ref", "_defaultizedProps$yea", "_defaultizedProps$slo2", "_props$localeText$ope", "_props$localeText", "localeText", "utils", "defaultizedProps", "viewRenderers", "day", "month", "year", "props", "format", "yearsPerRow", "slots", "openPickerIcon", "field", "slotProps", "ownerState", "_defaultizedProps$slo", "toolbar", "hidden", "renderPicker", "valueManager", "valueType", "getOpenDialogAriaText", "openDatePickerDialogue", "validator", "propTypes", "autoFocus", "bool", "className", "string", "closeOnSelect", "components", "object", "componentsProps", "dayOfWeekFormatter", "func", "defaultCalendarMonth", "any", "defaultValue", "disabled", "disableFuture", "disableHighlightToday", "disableOpenPicker", "disablePast", "displayWeekNumber", "fixedWeekNumber", "number", "formatDensity", "oneOf", "inputRef", "label", "node", "loading", "maxDate", "minDate", "monthsPerRow", "name", "onAccept", "onChange", "onClose", "onError", "onMonthChange", "onOpen", "onSelectedSectionsChange", "onViewChange", "onYearChange", "open", "openTo", "orientation", "readOnly", "reduceAnimations", "referenceDate", "renderLoading", "selectedSections", "oneOfType", "shape", "endIndex", "isRequired", "startIndex", "shouldDisableDate", "shouldDisableMonth", "shouldDisableYear", "showDaysOutsideCurrentMonth", "sx", "arrayOf", "timezone", "value", "view", "views"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/x-date-pickers/DesktopDatePicker/DesktopDatePicker.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { resolveComponentProps } from '@mui/base/utils';\nimport { refType } from '@mui/utils';\nimport { singleItemValueManager } from '../internals/utils/valueManagers';\nimport { useDatePickerDefaultizedProps } from '../DatePicker/shared';\nimport { useLocaleText, useUtils, validateDate } from '../internals';\nimport { useDesktopPicker } from '../internals/hooks/useDesktopPicker';\nimport { CalendarIcon } from '../icons';\nimport { DateField } from '../DateField';\nimport { extractValidationProps } from '../internals/utils/validation/extractValidationProps';\nimport { renderDateViewCalendar } from '../dateViewRenderers';\nimport { resolveDateFormat } from '../internals/utils/date-utils';\n/**\n * Demos:\n *\n * - [DatePicker](https://mui.com/x/react-date-pickers/date-picker/)\n * - [Validation](https://mui.com/x/react-date-pickers/validation/)\n *\n * API:\n *\n * - [DesktopDatePicker API](https://mui.com/x/api/date-pickers/desktop-date-picker/)\n */\nconst DesktopDatePicker = /*#__PURE__*/React.forwardRef(function DesktopDatePicker(inProps, ref) {\n  var _defaultizedProps$yea, _defaultizedProps$slo2, _props$localeText$ope, _props$localeText;\n  const localeText = useLocaleText();\n  const utils = useUtils();\n\n  // Props with the default values common to all date pickers\n  const defaultizedProps = useDatePickerDefaultizedProps(inProps, 'MuiDesktopDatePicker');\n  const viewRenderers = _extends({\n    day: renderDateViewCalendar,\n    month: renderDateViewCalendar,\n    year: renderDateViewCalendar\n  }, defaultizedProps.viewRenderers);\n\n  // Props with the default values specific to the desktop variant\n  const props = _extends({}, defaultizedProps, {\n    viewRenderers,\n    format: resolveDateFormat(utils, defaultizedProps, false),\n    yearsPerRow: (_defaultizedProps$yea = defaultizedProps.yearsPerRow) != null ? _defaultizedProps$yea : 4,\n    slots: _extends({\n      openPickerIcon: CalendarIcon,\n      field: DateField\n    }, defaultizedProps.slots),\n    slotProps: _extends({}, defaultizedProps.slotProps, {\n      field: ownerState => {\n        var _defaultizedProps$slo;\n        return _extends({}, resolveComponentProps((_defaultizedProps$slo = defaultizedProps.slotProps) == null ? void 0 : _defaultizedProps$slo.field, ownerState), extractValidationProps(defaultizedProps), {\n          ref\n        });\n      },\n      toolbar: _extends({\n        hidden: true\n      }, (_defaultizedProps$slo2 = defaultizedProps.slotProps) == null ? void 0 : _defaultizedProps$slo2.toolbar)\n    })\n  });\n  const {\n    renderPicker\n  } = useDesktopPicker({\n    props,\n    valueManager: singleItemValueManager,\n    valueType: 'date',\n    getOpenDialogAriaText: (_props$localeText$ope = (_props$localeText = props.localeText) == null ? void 0 : _props$localeText.openDatePickerDialogue) != null ? _props$localeText$ope : localeText.openDatePickerDialogue,\n    validator: validateDate\n  });\n  return renderPicker();\n});\nDesktopDatePicker.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * If `true`, the main element is focused during the first mount.\n   * This main element is:\n   * - the element chosen by the visible view if any (i.e: the selected day on the `day` view).\n   * - the `input` element if there is a field rendered.\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * Class name applied to the root element.\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the popover or modal will close after submitting the full date.\n   * @default `true` for desktop, `false` for mobile (based on the chosen wrapper and `desktopModeMediaQuery` prop).\n   */\n  closeOnSelect: PropTypes.bool,\n  /**\n   * Overridable components.\n   * @default {}\n   * @deprecated Please use `slots`.\n   */\n  components: PropTypes.object,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   * @deprecated Please use `slotProps`.\n   */\n  componentsProps: PropTypes.object,\n  /**\n   * Formats the day of week displayed in the calendar header.\n   * @param {string} day The day of week provided by the adapter.  Deprecated, will be removed in v7: Use `date` instead.\n   * @param {TDate} date The date of the day of week provided by the adapter.\n   * @returns {string} The name to display.\n   * @default (_day: string, date: TDate) => adapter.format(date, 'weekdayShort').charAt(0).toUpperCase()\n   */\n  dayOfWeekFormatter: PropTypes.func,\n  /**\n   * Default calendar month displayed when `value` and `defaultValue` are empty.\n   * @deprecated Consider using `referenceDate` instead.\n   */\n  defaultCalendarMonth: PropTypes.any,\n  /**\n   * The default value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: PropTypes.any,\n  /**\n   * If `true`, the picker and text field are disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * If `true`, today's date is rendering without highlighting with circle.\n   * @default false\n   */\n  disableHighlightToday: PropTypes.bool,\n  /**\n   * If `true`, the open picker button will not be rendered (renders only the field).\n   * @default false\n   */\n  disableOpenPicker: PropTypes.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  /**\n   * If `true`, the week number will be display in the calendar.\n   */\n  displayWeekNumber: PropTypes.bool,\n  /**\n   * Calendar will show more weeks in order to match this value.\n   * Put it to 6 for having fix number of week in Gregorian calendars\n   * @default undefined\n   */\n  fixedWeekNumber: PropTypes.number,\n  /**\n   * Format of the date when rendered in the input(s).\n   * Defaults to localized format based on the used `views`.\n   */\n  format: PropTypes.string,\n  /**\n   * Density of the format when rendered in the input.\n   * Setting `formatDensity` to `\"spacious\"` will add a space before and after each `/`, `-` and `.` character.\n   * @default \"dense\"\n   */\n  formatDensity: PropTypes.oneOf(['dense', 'spacious']),\n  /**\n   * Pass a ref to the `input` element.\n   */\n  inputRef: refType,\n  /**\n   * The label content.\n   */\n  label: PropTypes.node,\n  /**\n   * If `true`, calls `renderLoading` instead of rendering the day calendar.\n   * Can be used to preload information and show it in calendar.\n   * @default false\n   */\n  loading: PropTypes.bool,\n  /**\n   * Locale for components texts.\n   * Allows overriding texts coming from `LocalizationProvider` and `theme`.\n   */\n  localeText: PropTypes.object,\n  /**\n   * Maximal selectable date.\n   */\n  maxDate: PropTypes.any,\n  /**\n   * Minimal selectable date.\n   */\n  minDate: PropTypes.any,\n  /**\n   * Months rendered per row.\n   * @default 3\n   */\n  monthsPerRow: PropTypes.oneOf([3, 4]),\n  /**\n   * Name attribute used by the `input` element in the Field.\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when the value is accepted.\n   * @template TValue The value type. Will be either the same type as `value` or `null`. Can be in `[start, end]` format in case of range value.\n   * @param {TValue} value The value that was just accepted.\n   */\n  onAccept: PropTypes.func,\n  /**\n   * Callback fired when the value changes.\n   * @template TValue The value type. Will be either the same type as `value` or `null`. Can be in `[start, end]` format in case of range value.\n   * @template TError The validation error type. Will be either `string` or a `null`. Can be in `[start, end]` format in case of range value.\n   * @param {TValue} value The new value.\n   * @param {FieldChangeHandlerContext<TError>} context The context containing the validation result of the current value.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be closed.\n   * Use in controlled mode (see `open`).\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback fired when the error associated to the current value changes.\n   * If the error has a non-null value, then the `TextField` will be rendered in `error` state.\n   *\n   * @template TValue The value type. Will be either the same type as `value` or `null`. Can be in `[start, end]` format in case of range value.\n   * @template TError The validation error type. Will be either `string` or a `null`. Can be in `[start, end]` format in case of range value.\n   * @param {TError} error The new error describing why the current value is not valid.\n   * @param {TValue} value The value associated to the error.\n   */\n  onError: PropTypes.func,\n  /**\n   * Callback fired on month change.\n   * @template TDate\n   * @param {TDate} month The new month.\n   */\n  onMonthChange: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be opened.\n   * Use in controlled mode (see `open`).\n   */\n  onOpen: PropTypes.func,\n  /**\n   * Callback fired when the selected sections change.\n   * @param {FieldSelectedSections} newValue The new selected sections.\n   */\n  onSelectedSectionsChange: PropTypes.func,\n  /**\n   * Callback fired on view change.\n   * @template TView\n   * @param {TView} view The new view.\n   */\n  onViewChange: PropTypes.func,\n  /**\n   * Callback fired on year change.\n   * @template TDate\n   * @param {TDate} year The new year.\n   */\n  onYearChange: PropTypes.func,\n  /**\n   * Control the popup or dialog open state.\n   * @default false\n   */\n  open: PropTypes.bool,\n  /**\n   * The default visible view.\n   * Used when the component view is not controlled.\n   * Must be a valid option from `views` list.\n   */\n  openTo: PropTypes.oneOf(['day', 'month', 'year']),\n  /**\n   * Force rendering in particular orientation.\n   */\n  orientation: PropTypes.oneOf(['landscape', 'portrait']),\n  readOnly: PropTypes.bool,\n  /**\n   * If `true`, disable heavy animations.\n   * @default `@media(prefers-reduced-motion: reduce)` || `navigator.userAgent` matches Android <10 or iOS <13\n   */\n  reduceAnimations: PropTypes.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid date-time using the validation props, except callbacks like `shouldDisable<...>`.\n   */\n  referenceDate: PropTypes.any,\n  /**\n   * Component displaying when passed `loading` true.\n   * @returns {React.ReactNode} The node to render when loading.\n   * @default () => <span data-mui-test=\"loading-progress\">...</span>\n   */\n  renderLoading: PropTypes.func,\n  /**\n   * The currently selected sections.\n   * This prop accept four formats:\n   * 1. If a number is provided, the section at this index will be selected.\n   * 2. If an object with a `startIndex` and `endIndex` properties are provided, the sections between those two indexes will be selected.\n   * 3. If a string of type `FieldSectionType` is provided, the first section with that name will be selected.\n   * 4. If `null` is provided, no section will be selected\n   * If not provided, the selected sections will be handled internally.\n   */\n  selectedSections: PropTypes.oneOfType([PropTypes.oneOf(['all', 'day', 'hours', 'meridiem', 'minutes', 'month', 'seconds', 'weekDay', 'year']), PropTypes.number, PropTypes.shape({\n    endIndex: PropTypes.number.isRequired,\n    startIndex: PropTypes.number.isRequired\n  })]),\n  /**\n   * Disable specific date.\n   *\n   * Warning: This function can be called multiple times (e.g. when rendering date calendar, checking if focus can be moved to a certain date, etc.). Expensive computations can impact performance.\n   *\n   * @template TDate\n   * @param {TDate} day The date to test.\n   * @returns {boolean} If `true` the date will be disabled.\n   */\n  shouldDisableDate: PropTypes.func,\n  /**\n   * Disable specific month.\n   * @template TDate\n   * @param {TDate} month The month to test.\n   * @returns {boolean} If `true`, the month will be disabled.\n   */\n  shouldDisableMonth: PropTypes.func,\n  /**\n   * Disable specific year.\n   * @template TDate\n   * @param {TDate} year The year to test.\n   * @returns {boolean} If `true`, the year will be disabled.\n   */\n  shouldDisableYear: PropTypes.func,\n  /**\n   * If `true`, days outside the current month are rendered:\n   *\n   * - if `fixedWeekNumber` is defined, renders days to have the weeks requested.\n   *\n   * - if `fixedWeekNumber` is not defined, renders day to fill the first and last week of the current month.\n   *\n   * - ignored if `calendars` equals more than `1` on range pickers.\n   * @default false\n   */\n  showDaysOutsideCurrentMonth: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documention} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: PropTypes.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: PropTypes.any,\n  /**\n   * The visible view.\n   * Used when the component view is controlled.\n   * Must be a valid option from `views` list.\n   */\n  view: PropTypes.oneOf(['day', 'month', 'year']),\n  /**\n   * Define custom view renderers for each section.\n   * If `null`, the section will only have field editing.\n   * If `undefined`, internally defined view will be the used.\n   */\n  viewRenderers: PropTypes.shape({\n    day: PropTypes.func,\n    month: PropTypes.func,\n    year: PropTypes.func\n  }),\n  /**\n   * Available views.\n   */\n  views: PropTypes.arrayOf(PropTypes.oneOf(['day', 'month', 'year']).isRequired),\n  /**\n   * Years rendered per row.\n   * @default 4\n   */\n  yearsPerRow: PropTypes.oneOf([3, 4])\n};\nexport { DesktopDatePicker };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,qBAAqB,QAAQ,iBAAiB;AACvD,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASC,sBAAsB,QAAQ,kCAAkC;AACzE,SAASC,6BAA6B,QAAQ,sBAAsB;AACpE,SAASC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,cAAc;AACpE,SAASC,gBAAgB,QAAQ,qCAAqC;AACtE,SAASC,YAAY,QAAQ,UAAU;AACvC,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,sBAAsB,QAAQ,sDAAsD;AAC7F,SAASC,sBAAsB,QAAQ,sBAAsB;AAC7D,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,aAAaf,KAAK,CAACgB,UAAU,CAAC,SAASD,iBAAiBA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC/F,IAAIC,qBAAqB,EAAEC,sBAAsB,EAAEC,qBAAqB,EAAEC,iBAAiB;EAC3F,MAAMC,UAAU,GAAGjB,aAAa,CAAC,CAAC;EAClC,MAAMkB,KAAK,GAAGjB,QAAQ,CAAC,CAAC;;EAExB;EACA,MAAMkB,gBAAgB,GAAGpB,6BAA6B,CAACY,OAAO,EAAE,sBAAsB,CAAC;EACvF,MAAMS,aAAa,GAAG3B,QAAQ,CAAC;IAC7B4B,GAAG,EAAEd,sBAAsB;IAC3Be,KAAK,EAAEf,sBAAsB;IAC7BgB,IAAI,EAAEhB;EACR,CAAC,EAAEY,gBAAgB,CAACC,aAAa,CAAC;;EAElC;EACA,MAAMI,KAAK,GAAG/B,QAAQ,CAAC,CAAC,CAAC,EAAE0B,gBAAgB,EAAE;IAC3CC,aAAa;IACbK,MAAM,EAAEjB,iBAAiB,CAACU,KAAK,EAAEC,gBAAgB,EAAE,KAAK,CAAC;IACzDO,WAAW,EAAE,CAACb,qBAAqB,GAAGM,gBAAgB,CAACO,WAAW,KAAK,IAAI,GAAGb,qBAAqB,GAAG,CAAC;IACvGc,KAAK,EAAElC,QAAQ,CAAC;MACdmC,cAAc,EAAExB,YAAY;MAC5ByB,KAAK,EAAExB;IACT,CAAC,EAAEc,gBAAgB,CAACQ,KAAK,CAAC;IAC1BG,SAAS,EAAErC,QAAQ,CAAC,CAAC,CAAC,EAAE0B,gBAAgB,CAACW,SAAS,EAAE;MAClDD,KAAK,EAAEE,UAAU,IAAI;QACnB,IAAIC,qBAAqB;QACzB,OAAOvC,QAAQ,CAAC,CAAC,CAAC,EAAEG,qBAAqB,CAAC,CAACoC,qBAAqB,GAAGb,gBAAgB,CAACW,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,qBAAqB,CAACH,KAAK,EAAEE,UAAU,CAAC,EAAEzB,sBAAsB,CAACa,gBAAgB,CAAC,EAAE;UACpMP;QACF,CAAC,CAAC;MACJ,CAAC;MACDqB,OAAO,EAAExC,QAAQ,CAAC;QAChByC,MAAM,EAAE;MACV,CAAC,EAAE,CAACpB,sBAAsB,GAAGK,gBAAgB,CAACW,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGhB,sBAAsB,CAACmB,OAAO;IAC5G,CAAC;EACH,CAAC,CAAC;EACF,MAAM;IACJE;EACF,CAAC,GAAGhC,gBAAgB,CAAC;IACnBqB,KAAK;IACLY,YAAY,EAAEtC,sBAAsB;IACpCuC,SAAS,EAAE,MAAM;IACjBC,qBAAqB,EAAE,CAACvB,qBAAqB,GAAG,CAACC,iBAAiB,GAAGQ,KAAK,CAACP,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,iBAAiB,CAACuB,sBAAsB,KAAK,IAAI,GAAGxB,qBAAqB,GAAGE,UAAU,CAACsB,sBAAsB;IACvNC,SAAS,EAAEtC;EACb,CAAC,CAAC;EACF,OAAOiC,YAAY,CAAC,CAAC;AACvB,CAAC,CAAC;AACF1B,iBAAiB,CAACgC,SAAS,GAAG;EAC5B;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;EACEC,SAAS,EAAE/C,SAAS,CAACgD,IAAI;EACzB;AACF;AACA;EACEC,SAAS,EAAEjD,SAAS,CAACkD,MAAM;EAC3B;AACF;AACA;AACA;EACEC,aAAa,EAAEnD,SAAS,CAACgD,IAAI;EAC7B;AACF;AACA;AACA;AACA;EACEI,UAAU,EAAEpD,SAAS,CAACqD,MAAM;EAC5B;AACF;AACA;AACA;AACA;EACEC,eAAe,EAAEtD,SAAS,CAACqD,MAAM;EACjC;AACF;AACA;AACA;AACA;AACA;AACA;EACEE,kBAAkB,EAAEvD,SAAS,CAACwD,IAAI;EAClC;AACF;AACA;AACA;EACEC,oBAAoB,EAAEzD,SAAS,CAAC0D,GAAG;EACnC;AACF;AACA;AACA;EACEC,YAAY,EAAE3D,SAAS,CAAC0D,GAAG;EAC3B;AACF;AACA;AACA;EACEE,QAAQ,EAAE5D,SAAS,CAACgD,IAAI;EACxB;AACF;AACA;AACA;EACEa,aAAa,EAAE7D,SAAS,CAACgD,IAAI;EAC7B;AACF;AACA;AACA;EACEc,qBAAqB,EAAE9D,SAAS,CAACgD,IAAI;EACrC;AACF;AACA;AACA;EACEe,iBAAiB,EAAE/D,SAAS,CAACgD,IAAI;EACjC;AACF;AACA;AACA;EACEgB,WAAW,EAAEhE,SAAS,CAACgD,IAAI;EAC3B;AACF;AACA;EACEiB,iBAAiB,EAAEjE,SAAS,CAACgD,IAAI;EACjC;AACF;AACA;AACA;AACA;EACEkB,eAAe,EAAElE,SAAS,CAACmE,MAAM;EACjC;AACF;AACA;AACA;EACErC,MAAM,EAAE9B,SAAS,CAACkD,MAAM;EACxB;AACF;AACA;AACA;AACA;EACEkB,aAAa,EAAEpE,SAAS,CAACqE,KAAK,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;EACrD;AACF;AACA;EACEC,QAAQ,EAAEpE,OAAO;EACjB;AACF;AACA;EACEqE,KAAK,EAAEvE,SAAS,CAACwE,IAAI;EACrB;AACF;AACA;AACA;AACA;EACEC,OAAO,EAAEzE,SAAS,CAACgD,IAAI;EACvB;AACF;AACA;AACA;EACE1B,UAAU,EAAEtB,SAAS,CAACqD,MAAM;EAC5B;AACF;AACA;EACEqB,OAAO,EAAE1E,SAAS,CAAC0D,GAAG;EACtB;AACF;AACA;EACEiB,OAAO,EAAE3E,SAAS,CAAC0D,GAAG;EACtB;AACF;AACA;AACA;EACEkB,YAAY,EAAE5E,SAAS,CAACqE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACrC;AACF;AACA;EACEQ,IAAI,EAAE7E,SAAS,CAACkD,MAAM;EACtB;AACF;AACA;AACA;AACA;EACE4B,QAAQ,EAAE9E,SAAS,CAACwD,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;AACA;EACEuB,QAAQ,EAAE/E,SAAS,CAACwD,IAAI;EACxB;AACF;AACA;AACA;EACEwB,OAAO,EAAEhF,SAAS,CAACwD,IAAI;EACvB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEyB,OAAO,EAAEjF,SAAS,CAACwD,IAAI;EACvB;AACF;AACA;AACA;AACA;EACE0B,aAAa,EAAElF,SAAS,CAACwD,IAAI;EAC7B;AACF;AACA;AACA;EACE2B,MAAM,EAAEnF,SAAS,CAACwD,IAAI;EACtB;AACF;AACA;AACA;EACE4B,wBAAwB,EAAEpF,SAAS,CAACwD,IAAI;EACxC;AACF;AACA;AACA;AACA;EACE6B,YAAY,EAAErF,SAAS,CAACwD,IAAI;EAC5B;AACF;AACA;AACA;AACA;EACE8B,YAAY,EAAEtF,SAAS,CAACwD,IAAI;EAC5B;AACF;AACA;AACA;EACE+B,IAAI,EAAEvF,SAAS,CAACgD,IAAI;EACpB;AACF;AACA;AACA;AACA;EACEwC,MAAM,EAAExF,SAAS,CAACqE,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;EACjD;AACF;AACA;EACEoB,WAAW,EAAEzF,SAAS,CAACqE,KAAK,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;EACvDqB,QAAQ,EAAE1F,SAAS,CAACgD,IAAI;EACxB;AACF;AACA;AACA;EACE2C,gBAAgB,EAAE3F,SAAS,CAACgD,IAAI;EAChC;AACF;AACA;AACA;EACE4C,aAAa,EAAE5F,SAAS,CAAC0D,GAAG;EAC5B;AACF;AACA;AACA;AACA;EACEmC,aAAa,EAAE7F,SAAS,CAACwD,IAAI;EAC7B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEsC,gBAAgB,EAAE9F,SAAS,CAAC+F,SAAS,CAAC,CAAC/F,SAAS,CAACqE,KAAK,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,EAAErE,SAAS,CAACmE,MAAM,EAAEnE,SAAS,CAACgG,KAAK,CAAC;IAC/KC,QAAQ,EAAEjG,SAAS,CAACmE,MAAM,CAAC+B,UAAU;IACrCC,UAAU,EAAEnG,SAAS,CAACmE,MAAM,CAAC+B;EAC/B,CAAC,CAAC,CAAC,CAAC;EACJ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEE,iBAAiB,EAAEpG,SAAS,CAACwD,IAAI;EACjC;AACF;AACA;AACA;AACA;AACA;EACE6C,kBAAkB,EAAErG,SAAS,CAACwD,IAAI;EAClC;AACF;AACA;AACA;AACA;AACA;EACE8C,iBAAiB,EAAEtG,SAAS,CAACwD,IAAI;EACjC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE+C,2BAA2B,EAAEvG,SAAS,CAACgD,IAAI;EAC3C;AACF;AACA;AACA;EACEb,SAAS,EAAEnC,SAAS,CAACqD,MAAM;EAC3B;AACF;AACA;AACA;EACErB,KAAK,EAAEhC,SAAS,CAACqD,MAAM;EACvB;AACF;AACA;EACEmD,EAAE,EAAExG,SAAS,CAAC+F,SAAS,CAAC,CAAC/F,SAAS,CAACyG,OAAO,CAACzG,SAAS,CAAC+F,SAAS,CAAC,CAAC/F,SAAS,CAACwD,IAAI,EAAExD,SAAS,CAACqD,MAAM,EAAErD,SAAS,CAACgD,IAAI,CAAC,CAAC,CAAC,EAAEhD,SAAS,CAACwD,IAAI,EAAExD,SAAS,CAACqD,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;AACA;AACA;EACEqD,QAAQ,EAAE1G,SAAS,CAACkD,MAAM;EAC1B;AACF;AACA;AACA;EACEyD,KAAK,EAAE3G,SAAS,CAAC0D,GAAG;EACpB;AACF;AACA;AACA;AACA;EACEkD,IAAI,EAAE5G,SAAS,CAACqE,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;EAC/C;AACF;AACA;AACA;AACA;EACE5C,aAAa,EAAEzB,SAAS,CAACgG,KAAK,CAAC;IAC7BtE,GAAG,EAAE1B,SAAS,CAACwD,IAAI;IACnB7B,KAAK,EAAE3B,SAAS,CAACwD,IAAI;IACrB5B,IAAI,EAAE5B,SAAS,CAACwD;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEqD,KAAK,EAAE7G,SAAS,CAACyG,OAAO,CAACzG,SAAS,CAACqE,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC6B,UAAU,CAAC;EAC9E;AACF;AACA;AACA;EACEnE,WAAW,EAAE/B,SAAS,CAACqE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AACrC,CAAC;AACD,SAASvD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}