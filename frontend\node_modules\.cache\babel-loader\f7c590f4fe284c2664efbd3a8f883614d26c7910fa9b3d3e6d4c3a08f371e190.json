{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    past: '{{count}} წამზე ნაკლები ხნის წინ',\n    present: '{{count}} წამზე ნაკლები',\n    future: '{{count}} წამზე ნაკლებში'\n  },\n  xSeconds: {\n    past: '{{count}} წამის წინ',\n    present: '{{count}} წამი',\n    future: '{{count}} წამში'\n  },\n  halfAMinute: {\n    past: 'ნახევარი წუთის წინ',\n    present: 'ნახევარი წუთი',\n    future: 'ნახევარი წუთში'\n  },\n  lessThanXMinutes: {\n    past: '{{count}} წუთზე ნაკლები ხნის წინ',\n    present: '{{count}} წუთზე ნაკლები',\n    future: '{{count}} წუთზე ნაკლებში'\n  },\n  xMinutes: {\n    past: '{{count}} წუთის წინ',\n    present: '{{count}} წუთი',\n    future: '{{count}} წუთში'\n  },\n  aboutXHours: {\n    past: 'დაახლოებით {{count}} საათის წინ',\n    present: 'დაახლოებით {{count}} საათი',\n    future: 'დაახლოებით {{count}} საათში'\n  },\n  xHours: {\n    past: '{{count}} საათის წინ',\n    present: '{{count}} საათი',\n    future: '{{count}} საათში'\n  },\n  xDays: {\n    past: '{{count}} დღის წინ',\n    present: '{{count}} დღე',\n    future: '{{count}} დღეში'\n  },\n  aboutXWeeks: {\n    past: 'დაახლოებით {{count}} კვირას წინ',\n    present: 'დაახლოებით {{count}} კვირა',\n    future: 'დაახლოებით {{count}} კვირაში'\n  },\n  xWeeks: {\n    past: '{{count}} კვირას კვირა',\n    present: '{{count}} კვირა',\n    future: '{{count}} კვირაში'\n  },\n  aboutXMonths: {\n    past: 'დაახლოებით {{count}} თვის წინ',\n    present: 'დაახლოებით {{count}} თვე',\n    future: 'დაახლოებით {{count}} თვეში'\n  },\n  xMonths: {\n    past: '{{count}} თვის წინ',\n    present: '{{count}} თვე',\n    future: '{{count}} თვეში'\n  },\n  aboutXYears: {\n    past: 'დაახლოებით {{count}} წლის წინ',\n    present: 'დაახლოებით {{count}} წელი',\n    future: 'დაახლოებით {{count}} წელში'\n  },\n  xYears: {\n    past: '{{count}} წლის წინ',\n    present: '{{count}} წელი',\n    future: '{{count}} წელში'\n  },\n  overXYears: {\n    past: '{{count}} წელზე მეტი ხნის წინ',\n    present: '{{count}} წელზე მეტი',\n    future: '{{count}} წელზე მეტი ხნის შემდეგ'\n  },\n  almostXYears: {\n    past: 'თითქმის {{count}} წლის წინ',\n    present: 'თითქმის {{count}} წელი',\n    future: 'თითქმის {{count}} წელში'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (options !== null && options !== void 0 && options.addSuffix && options.comparison && options.comparison > 0) {\n    result = tokenValue.future.replace('{{count}}', String(count));\n  } else if (options !== null && options !== void 0 && options.addSuffix) {\n    result = tokenValue.past.replace('{{count}}', String(count));\n  } else {\n    result = tokenValue.present.replace('{{count}}', String(count));\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "past", "present", "future", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "addSuffix", "comparison", "replace", "String"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/ka/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    past: '{{count}} წამზე ნაკლები ხნის წინ',\n    present: '{{count}} წამზე ნაკლები',\n    future: '{{count}} წამზე ნაკლებში'\n  },\n  xSeconds: {\n    past: '{{count}} წამის წინ',\n    present: '{{count}} წამი',\n    future: '{{count}} წამში'\n  },\n  halfAMinute: {\n    past: 'ნახევარი წუთის წინ',\n    present: 'ნახევარი წუთი',\n    future: 'ნახევარი წუთში'\n  },\n  lessThanXMinutes: {\n    past: '{{count}} წუთზე ნაკლები ხნის წინ',\n    present: '{{count}} წუთზე ნაკლები',\n    future: '{{count}} წუთზე ნაკლებში'\n  },\n  xMinutes: {\n    past: '{{count}} წუთის წინ',\n    present: '{{count}} წუთი',\n    future: '{{count}} წუთში'\n  },\n  aboutXHours: {\n    past: 'დაახლოებით {{count}} საათის წინ',\n    present: 'დაახლოებით {{count}} საათი',\n    future: 'დაახლოებით {{count}} საათში'\n  },\n  xHours: {\n    past: '{{count}} საათის წინ',\n    present: '{{count}} საათი',\n    future: '{{count}} საათში'\n  },\n  xDays: {\n    past: '{{count}} დღის წინ',\n    present: '{{count}} დღე',\n    future: '{{count}} დღეში'\n  },\n  aboutXWeeks: {\n    past: 'დაახლოებით {{count}} კვირას წინ',\n    present: 'დაახლოებით {{count}} კვირა',\n    future: 'დაახლოებით {{count}} კვირაში'\n  },\n  xWeeks: {\n    past: '{{count}} კვირას კვირა',\n    present: '{{count}} კვირა',\n    future: '{{count}} კვირაში'\n  },\n  aboutXMonths: {\n    past: 'დაახლოებით {{count}} თვის წინ',\n    present: 'დაახლოებით {{count}} თვე',\n    future: 'დაახლოებით {{count}} თვეში'\n  },\n  xMonths: {\n    past: '{{count}} თვის წინ',\n    present: '{{count}} თვე',\n    future: '{{count}} თვეში'\n  },\n  aboutXYears: {\n    past: 'დაახლოებით {{count}} წლის წინ',\n    present: 'დაახლოებით {{count}} წელი',\n    future: 'დაახლოებით {{count}} წელში'\n  },\n  xYears: {\n    past: '{{count}} წლის წინ',\n    present: '{{count}} წელი',\n    future: '{{count}} წელში'\n  },\n  overXYears: {\n    past: '{{count}} წელზე მეტი ხნის წინ',\n    present: '{{count}} წელზე მეტი',\n    future: '{{count}} წელზე მეტი ხნის შემდეგ'\n  },\n  almostXYears: {\n    past: 'თითქმის {{count}} წლის წინ',\n    present: 'თითქმის {{count}} წელი',\n    future: 'თითქმის {{count}} წელში'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (options !== null && options !== void 0 && options.addSuffix && options.comparison && options.comparison > 0) {\n    result = tokenValue.future.replace('{{count}}', String(count));\n  } else if (options !== null && options !== void 0 && options.addSuffix) {\n    result = tokenValue.past.replace('{{count}}', String(count));\n  } else {\n    result = tokenValue.present.replace('{{count}}', String(count));\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,IAAI,EAAE,kCAAkC;IACxCC,OAAO,EAAE,yBAAyB;IAClCC,MAAM,EAAE;EACV,CAAC;EACDC,QAAQ,EAAE;IACRH,IAAI,EAAE,qBAAqB;IAC3BC,OAAO,EAAE,gBAAgB;IACzBC,MAAM,EAAE;EACV,CAAC;EACDE,WAAW,EAAE;IACXJ,IAAI,EAAE,oBAAoB;IAC1BC,OAAO,EAAE,eAAe;IACxBC,MAAM,EAAE;EACV,CAAC;EACDG,gBAAgB,EAAE;IAChBL,IAAI,EAAE,kCAAkC;IACxCC,OAAO,EAAE,yBAAyB;IAClCC,MAAM,EAAE;EACV,CAAC;EACDI,QAAQ,EAAE;IACRN,IAAI,EAAE,qBAAqB;IAC3BC,OAAO,EAAE,gBAAgB;IACzBC,MAAM,EAAE;EACV,CAAC;EACDK,WAAW,EAAE;IACXP,IAAI,EAAE,iCAAiC;IACvCC,OAAO,EAAE,4BAA4B;IACrCC,MAAM,EAAE;EACV,CAAC;EACDM,MAAM,EAAE;IACNR,IAAI,EAAE,sBAAsB;IAC5BC,OAAO,EAAE,iBAAiB;IAC1BC,MAAM,EAAE;EACV,CAAC;EACDO,KAAK,EAAE;IACLT,IAAI,EAAE,oBAAoB;IAC1BC,OAAO,EAAE,eAAe;IACxBC,MAAM,EAAE;EACV,CAAC;EACDQ,WAAW,EAAE;IACXV,IAAI,EAAE,iCAAiC;IACvCC,OAAO,EAAE,4BAA4B;IACrCC,MAAM,EAAE;EACV,CAAC;EACDS,MAAM,EAAE;IACNX,IAAI,EAAE,wBAAwB;IAC9BC,OAAO,EAAE,iBAAiB;IAC1BC,MAAM,EAAE;EACV,CAAC;EACDU,YAAY,EAAE;IACZZ,IAAI,EAAE,+BAA+B;IACrCC,OAAO,EAAE,0BAA0B;IACnCC,MAAM,EAAE;EACV,CAAC;EACDW,OAAO,EAAE;IACPb,IAAI,EAAE,oBAAoB;IAC1BC,OAAO,EAAE,eAAe;IACxBC,MAAM,EAAE;EACV,CAAC;EACDY,WAAW,EAAE;IACXd,IAAI,EAAE,+BAA+B;IACrCC,OAAO,EAAE,2BAA2B;IACpCC,MAAM,EAAE;EACV,CAAC;EACDa,MAAM,EAAE;IACNf,IAAI,EAAE,oBAAoB;IAC1BC,OAAO,EAAE,gBAAgB;IACzBC,MAAM,EAAE;EACV,CAAC;EACDc,UAAU,EAAE;IACVhB,IAAI,EAAE,+BAA+B;IACrCC,OAAO,EAAE,sBAAsB;IAC/BC,MAAM,EAAE;EACV,CAAC;EACDe,YAAY,EAAE;IACZjB,IAAI,EAAE,4BAA4B;IAClCC,OAAO,EAAE,wBAAwB;IACjCC,MAAM,EAAE;EACV;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGzB,oBAAoB,CAACqB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACI,UAAU,IAAIJ,OAAO,CAACI,UAAU,GAAG,CAAC,EAAE;IACtHH,MAAM,GAAGC,UAAU,CAACrB,MAAM,CAACwB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACP,KAAK,CAAC,CAAC;EAChE,CAAC,MAAM,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACG,SAAS,EAAE;IACtEF,MAAM,GAAGC,UAAU,CAACvB,IAAI,CAAC0B,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACP,KAAK,CAAC,CAAC;EAC9D,CAAC,MAAM;IACLE,MAAM,GAAGC,UAAU,CAACtB,OAAO,CAACyB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACP,KAAK,CAAC,CAAC;EACjE;EACA,OAAOE,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}