import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate, useLocation } from 'react-router-dom';

const LoginSimple = () => {
  console.log('🚀 LoginSimple component rendered');
  
  const navigate = useNavigate();
  const location = useLocation();
  const { login, error, loading } = useAuth();

  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get redirect path from location state
  const from = location.state?.from?.pathname || '/dashboard';

  const handleLogin = async () => {
    console.log('🔥 handleLogin called!');
    console.log('Username:', username, 'Password:', password);

    if (!username || !password) {
      alert('يرجى إدخال اسم المستخدم وكلمة المرور');
      return;
    }

    setIsSubmitting(true);

    try {
      console.log('Attempting login...');
      const result = await login(username, password);
      console.log('Login result:', result);

      if (result.success) {
        console.log('Login successful, navigating to:', from);
        navigate(from, { replace: true });
      } else {
        console.log('Login failed:', result.error);
        alert(result.error || 'فشل تسجيل الدخول');
      }
    } catch (error) {
      console.error('Login error:', error);
      alert('حدث خطأ في تسجيل الدخول');
    } finally {
      setIsSubmitting(false);
    }
  };

  const fillAdmin = () => {
    console.log('🎯 Fill Admin clicked');
    setUsername('admin');
    setPassword('123456');
  };

  const fillCoach = () => {
    console.log('🎯 Fill Coach clicked');
    setUsername('coach');
    setPassword('123456');
  };

  return (
    <div style={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      backgroundColor: '#f5f5f5',
      fontFamily: 'Arial, sans-serif',
      direction: 'rtl'
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '40px',
        borderRadius: '10px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        width: '400px',
        maxWidth: '90%'
      }}>
        <h1 style={{ textAlign: 'center', marginBottom: '30px', color: '#333' }}>
          🥋 أكاديمية التايكوندو
        </h1>
        
        <h2 style={{ textAlign: 'center', marginBottom: '30px', color: '#666' }}>
          تسجيل الدخول
        </h2>

        {error && (
          <div style={{
            backgroundColor: '#ffebee',
            color: '#c62828',
            padding: '10px',
            borderRadius: '5px',
            marginBottom: '20px',
            textAlign: 'center'
          }}>
            {error}
          </div>
        )}

        <div style={{ marginBottom: '20px' }}>
          <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
            اسم المستخدم:
          </label>
          <input
            type="text"
            value={username}
            onChange={(e) => {
              console.log('Username changed:', e.target.value);
              setUsername(e.target.value);
            }}
            style={{
              width: '100%',
              padding: '12px',
              border: '1px solid #ddd',
              borderRadius: '5px',
              fontSize: '16px',
              boxSizing: 'border-box'
            }}
            placeholder="أدخل اسم المستخدم"
          />
        </div>

        <div style={{ marginBottom: '20px' }}>
          <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
            كلمة المرور:
          </label>
          <input
            type="password"
            value={password}
            onChange={(e) => {
              console.log('Password changed:', e.target.value);
              setPassword(e.target.value);
            }}
            style={{
              width: '100%',
              padding: '12px',
              border: '1px solid #ddd',
              borderRadius: '5px',
              fontSize: '16px',
              boxSizing: 'border-box'
            }}
            placeholder="أدخل كلمة المرور"
          />
        </div>

        <div style={{ marginBottom: '20px' }}>
          <button
            onClick={() => {
              console.log('🎯 Login button clicked!');
              handleLogin();
            }}
            disabled={isSubmitting || loading}
            style={{
              width: '100%',
              padding: '12px',
              backgroundColor: isSubmitting ? '#ccc' : '#1976d2',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              fontSize: '16px',
              fontWeight: 'bold',
              cursor: isSubmitting ? 'not-allowed' : 'pointer'
            }}
          >
            {isSubmitting ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
          </button>
        </div>

        <div style={{ textAlign: 'center', marginTop: '20px' }}>
          <p style={{ marginBottom: '10px', color: '#666' }}>حسابات تجريبية:</p>
          <button
            onClick={fillAdmin}
            style={{
              margin: '5px',
              padding: '8px 16px',
              backgroundColor: '#4caf50',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            المدير: admin / 123456
          </button>
          <button
            onClick={fillCoach}
            style={{
              margin: '5px',
              padding: '8px 16px',
              backgroundColor: '#ff9800',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            المدرب: coach / 123456
          </button>
        </div>
      </div>
    </div>
  );
};

export default LoginSimple;
