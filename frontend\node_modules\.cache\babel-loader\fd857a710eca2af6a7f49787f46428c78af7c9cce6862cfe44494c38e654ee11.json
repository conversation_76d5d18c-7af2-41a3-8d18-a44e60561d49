{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: function lastWeek(date) {\n    var day = date.getUTCDay();\n    switch (day) {\n      case 0:\n        return \"'prejšnjo nedeljo ob' p\";\n      case 3:\n        return \"'prejšnjo sredo ob' p\";\n      case 6:\n        return \"'prejš<PERSON>jo soboto ob' p\";\n      default:\n        return \"'prejšnji' EEEE 'ob' p\";\n    }\n  },\n  yesterday: \"'včeraj ob' p\",\n  today: \"'danes ob' p\",\n  tomorrow: \"'jutri ob' p\",\n  nextWeek: function nextWeek(date) {\n    var day = date.getUTCDay();\n    switch (day) {\n      case 0:\n        return \"'naslednjo nedeljo ob' p\";\n      case 3:\n        return \"'naslednjo sredo ob' p\";\n      case 6:\n        return \"'naslednjo soboto ob' p\";\n      default:\n        return \"'naslednji' EEEE 'ob' p\";\n    }\n  },\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, _baseDate, _options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date);\n  }\n  return format;\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "date", "day", "getUTCDay", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_baseDate", "_options", "format"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/sl/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: function lastWeek(date) {\n    var day = date.getUTCDay();\n    switch (day) {\n      case 0:\n        return \"'prejšnjo nedeljo ob' p\";\n      case 3:\n        return \"'prejšnjo sredo ob' p\";\n      case 6:\n        return \"'prejš<PERSON>jo soboto ob' p\";\n      default:\n        return \"'prejšnji' EEEE 'ob' p\";\n    }\n  },\n  yesterday: \"'včeraj ob' p\",\n  today: \"'danes ob' p\",\n  tomorrow: \"'jutri ob' p\",\n  nextWeek: function nextWeek(date) {\n    var day = date.getUTCDay();\n    switch (day) {\n      case 0:\n        return \"'naslednjo nedeljo ob' p\";\n      case 3:\n        return \"'naslednjo sredo ob' p\";\n      case 6:\n        return \"'naslednjo soboto ob' p\";\n      default:\n        return \"'naslednji' EEEE 'ob' p\";\n    }\n  },\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, _baseDate, _options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date);\n  }\n  return format;\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,SAASA,QAAQA,CAACC,IAAI,EAAE;IAChC,IAAIC,GAAG,GAAGD,IAAI,CAACE,SAAS,CAAC,CAAC;IAC1B,QAAQD,GAAG;MACT,KAAK,CAAC;QACJ,OAAO,yBAAyB;MAClC,KAAK,CAAC;QACJ,OAAO,uBAAuB;MAChC,KAAK,CAAC;QACJ,OAAO,wBAAwB;MACjC;QACE,OAAO,wBAAwB;IACnC;EACF,CAAC;EACDE,SAAS,EAAE,eAAe;EAC1BC,KAAK,EAAE,cAAc;EACrBC,QAAQ,EAAE,cAAc;EACxBC,QAAQ,EAAE,SAASA,QAAQA,CAACN,IAAI,EAAE;IAChC,IAAIC,GAAG,GAAGD,IAAI,CAACE,SAAS,CAAC,CAAC;IAC1B,QAAQD,GAAG;MACT,KAAK,CAAC;QACJ,OAAO,0BAA0B;MACnC,KAAK,CAAC;QACJ,OAAO,wBAAwB;MACjC,KAAK,CAAC;QACJ,OAAO,yBAAyB;MAClC;QACE,OAAO,yBAAyB;IACpC;EACF,CAAC;EACDM,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAET,IAAI,EAAEU,SAAS,EAAEC,QAAQ,EAAE;EAC7E,IAAIC,MAAM,GAAGd,oBAAoB,CAACW,KAAK,CAAC;EACxC,IAAI,OAAOG,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACZ,IAAI,CAAC;EACrB;EACA,OAAOY,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}