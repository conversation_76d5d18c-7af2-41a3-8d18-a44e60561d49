import React from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  Avatar,
} from '@mui/material';
import {
  People,
  Payment,
  EventAvailable,
  TrendingUp,
} from '@mui/icons-material';

const Dashboard = () => {
  // Mock data - will be replaced with real API calls
  const stats = [
    {
      title: 'إجمالي الطلاب',
      value: '156',
      icon: <People />,
      color: '#1976d2',
      change: '+12%',
    },
    {
      title: 'الاشتراكات النشطة',
      value: '142',
      icon: <Payment />,
      color: '#2e7d32',
      change: '+8%',
    },
    {
      title: 'حضور اليوم',
      value: '89',
      icon: <EventAvailable />,
      color: '#ed6c02',
      change: '+15%',
    },
    {
      title: 'الإيرادات الشهرية',
      value: '45,000 ر.س',
      icon: <TrendingUp />,
      color: '#9c27b0',
      change: '+22%',
    },
  ];

  return (
    <Box>
      {/* Page Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" fontWeight="bold" gutterBottom>
          لوحة التحكم
        </Typography>
        <Typography variant="body1" color="text.secondary">
          مرحباً بك في نظام إدارة أكاديمية التايكوندو
        </Typography>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card
              sx={{
                height: '100%',
                transition: 'transform 0.2s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                },
              }}
            >
              <CardContent>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    mb: 2,
                  }}
                >
                  <Avatar
                    sx={{
                      bgcolor: stat.color,
                      width: 56,
                      height: 56,
                    }}
                  >
                    {stat.icon}
                  </Avatar>
                  <Box sx={{ textAlign: 'right' }}>
                    <Typography
                      variant="h4"
                      fontWeight="bold"
                      color={stat.color}
                    >
                      {stat.value}
                    </Typography>
                    <Typography
                      variant="caption"
                      color="success.main"
                      fontWeight="bold"
                    >
                      {stat.change}
                    </Typography>
                  </Box>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  {stat.title}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Content Grid */}
      <Grid container spacing={3}>
        {/* Recent Activities */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              الأنشطة الأخيرة
            </Typography>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '80%',
                color: 'text.secondary',
              }}
            >
              <Typography>
                سيتم عرض الأنشطة الأخيرة هنا
              </Typography>
            </Box>
          </Paper>
        </Grid>

        {/* Quick Stats */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              إحصائيات سريعة
            </Typography>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '80%',
                color: 'text.secondary',
              }}
            >
              <Typography>
                سيتم عرض الإحصائيات هنا
              </Typography>
            </Box>
          </Paper>
        </Grid>

        {/* Attendance Chart */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: 300 }}>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              مخطط الحضور
            </Typography>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '80%',
                color: 'text.secondary',
              }}
            >
              <Typography>
                سيتم عرض مخطط الحضور هنا
              </Typography>
            </Box>
          </Paper>
        </Grid>

        {/* Revenue Chart */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: 300 }}>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              مخطط الإيرادات
            </Typography>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '80%',
                color: 'text.secondary',
              }}
            >
              <Typography>
                سيتم عرض مخطط الإيرادات هنا
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
