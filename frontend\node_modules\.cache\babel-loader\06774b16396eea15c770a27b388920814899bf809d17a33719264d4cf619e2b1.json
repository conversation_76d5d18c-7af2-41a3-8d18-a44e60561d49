{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'کمتر از یک ثانیه',\n    other: 'کمتر از {{count}} ثانیه'\n  },\n  xSeconds: {\n    one: '1 ثانیه',\n    other: '{{count}} ثانیه'\n  },\n  halfAMinute: 'نیم دقیقه',\n  lessThanXMinutes: {\n    one: 'کمتر از یک دقیقه',\n    other: 'کمتر از {{count}} دقیقه'\n  },\n  xMinutes: {\n    one: '1 دقیقه',\n    other: '{{count}} دقیقه'\n  },\n  aboutXHours: {\n    one: 'حدود 1 ساعت',\n    other: 'حدود {{count}} ساعت'\n  },\n  xHours: {\n    one: '1 ساعت',\n    other: '{{count}} ساعت'\n  },\n  xDays: {\n    one: '1 روز',\n    other: '{{count}} روز'\n  },\n  aboutXWeeks: {\n    one: 'حدود 1 هفته',\n    other: 'حدود {{count}} هفته'\n  },\n  xWeeks: {\n    one: '1 هفته',\n    other: '{{count}} هفته'\n  },\n  aboutXMonths: {\n    one: 'حدود 1 ماه',\n    other: 'حدود {{count}} ماه'\n  },\n  xMonths: {\n    one: '1 ماه',\n    other: '{{count}} ماه'\n  },\n  aboutXYears: {\n    one: 'حدود 1 سال',\n    other: 'حدود {{count}} سال'\n  },\n  xYears: {\n    one: '1 سال',\n    other: '{{count}} سال'\n  },\n  overXYears: {\n    one: 'بیشتر از 1 سال',\n    other: 'بیشتر از {{count}} سال'\n  },\n  almostXYears: {\n    one: 'نزدیک 1 سال',\n    other: 'نزدیک {{count}} سال'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'در ' + result;\n    } else {\n      return result + ' قبل';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/fa-IR/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'کمتر از یک ثانیه',\n    other: 'کمتر از {{count}} ثانیه'\n  },\n  xSeconds: {\n    one: '1 ثانیه',\n    other: '{{count}} ثانیه'\n  },\n  halfAMinute: 'نیم دقیقه',\n  lessThanXMinutes: {\n    one: 'کمتر از یک دقیقه',\n    other: 'کمتر از {{count}} دقیقه'\n  },\n  xMinutes: {\n    one: '1 دقیقه',\n    other: '{{count}} دقیقه'\n  },\n  aboutXHours: {\n    one: 'حدود 1 ساعت',\n    other: 'حدود {{count}} ساعت'\n  },\n  xHours: {\n    one: '1 ساعت',\n    other: '{{count}} ساعت'\n  },\n  xDays: {\n    one: '1 روز',\n    other: '{{count}} روز'\n  },\n  aboutXWeeks: {\n    one: 'حدود 1 هفته',\n    other: 'حدود {{count}} هفته'\n  },\n  xWeeks: {\n    one: '1 هفته',\n    other: '{{count}} هفته'\n  },\n  aboutXMonths: {\n    one: 'حدود 1 ماه',\n    other: 'حدود {{count}} ماه'\n  },\n  xMonths: {\n    one: '1 ماه',\n    other: '{{count}} ماه'\n  },\n  aboutXYears: {\n    one: 'حدود 1 سال',\n    other: 'حدود {{count}} سال'\n  },\n  xYears: {\n    one: '1 سال',\n    other: '{{count}} سال'\n  },\n  overXYears: {\n    one: 'بیشتر از 1 سال',\n    other: 'بیشتر از {{count}} سال'\n  },\n  almostXYears: {\n    one: 'نزدیک 1 سال',\n    other: 'نزدیک {{count}} سال'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'در ' + result;\n    } else {\n      return result + ' قبل';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,WAAW;EACxBC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAGL,MAAM;IACvB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,MAAM;IACxB;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}