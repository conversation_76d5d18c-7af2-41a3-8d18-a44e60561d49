const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Protect routes - verify JWT token
const protect = async (req, res, next) => {
  let token;

  // Check for token in headers
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    try {
      // Get token from header
      token = req.headers.authorization.split(' ')[1];

      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);

      // Get user from token
      req.user = await User.findById(decoded.id).select('-password');

      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'المستخدم غير موجود، يرجى تسجيل الدخول مرة أخرى'
        });
      }

      next();
    } catch (error) {
      console.error('Token verification error:', error);
      return res.status(401).json({
        success: false,
        message: 'غير مصرح، رمز الوصول غير صالح'
      });
    }
  }

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'غير مصرح، لا يوجد رمز وصول'
    });
  }
};

// Grant access to specific roles
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: `دور المستخدم ${req.user.role} غير مصرح له بالوصول لهذا المسار`
      });
    }
    next();
  };
};

// Check if user is admin
const adminOnly = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: 'هذا المسار مخصص للمدراء فقط'
    });
  }
  next();
};

// Check if user is admin or coach
const adminOrCoach = (req, res, next) => {
  if (!['admin', 'coach'].includes(req.user.role)) {
    return res.status(403).json({
      success: false,
      message: 'هذا المسار مخصص للمدراء والمدربين فقط'
    });
  }
  next();
};

module.exports = {
  protect,
  authorize,
  adminOnly,
  adminOrCoach
};
