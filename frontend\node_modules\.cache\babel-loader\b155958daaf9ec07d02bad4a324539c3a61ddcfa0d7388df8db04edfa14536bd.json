{"ast": null, "code": "import { millisecondsInHour } from \"../constants/index.js\";\nimport differenceInMilliseconds from \"../differenceInMilliseconds/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { getRoundingMethod } from \"../_lib/roundingMethods/index.js\";\n/**\n * @name differenceInHours\n * @category Hour Helpers\n * @summary Get the number of hours between the given dates.\n *\n * @description\n * Get the number of hours between the given dates.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @param {Object} [options] - an object with options.\n * @param {String} [options.roundingMethod='trunc'] - a rounding method (`ceil`, `floor`, `round` or `trunc`)\n * @returns {Number} the number of hours\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many hours are between 2 July 2014 06:50:00 and 2 July 2014 19:00:00?\n * const result = differenceInHours(\n *   new Date(2014, 6, 2, 19, 0),\n *   new Date(2014, 6, 2, 6, 50)\n * )\n * //=> 12\n */\nexport default function differenceInHours(dateLeft, dateRight, options) {\n  requiredArgs(2, arguments);\n  var diff = differenceInMilliseconds(dateLeft, dateRight) / millisecondsInHour;\n  return getRoundingMethod(options === null || options === void 0 ? void 0 : options.roundingMethod)(diff);\n}", "map": {"version": 3, "names": ["millisecondsInHour", "differenceInMilliseconds", "requiredArgs", "getRoundingMethod", "differenceInHours", "dateLeft", "dateRight", "options", "arguments", "diff", "roundingMethod"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/differenceInHours/index.js"], "sourcesContent": ["import { millisecondsInHour } from \"../constants/index.js\";\nimport differenceInMilliseconds from \"../differenceInMilliseconds/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { getRoundingMethod } from \"../_lib/roundingMethods/index.js\";\n/**\n * @name differenceInHours\n * @category Hour Helpers\n * @summary Get the number of hours between the given dates.\n *\n * @description\n * Get the number of hours between the given dates.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @param {Object} [options] - an object with options.\n * @param {String} [options.roundingMethod='trunc'] - a rounding method (`ceil`, `floor`, `round` or `trunc`)\n * @returns {Number} the number of hours\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many hours are between 2 July 2014 06:50:00 and 2 July 2014 19:00:00?\n * const result = differenceInHours(\n *   new Date(2014, 6, 2, 19, 0),\n *   new Date(2014, 6, 2, 6, 50)\n * )\n * //=> 12\n */\nexport default function differenceInHours(dateLeft, dateRight, options) {\n  requiredArgs(2, arguments);\n  var diff = differenceInMilliseconds(dateLeft, dateRight) / millisecondsInHour;\n  return getRoundingMethod(options === null || options === void 0 ? void 0 : options.roundingMethod)(diff);\n}"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,uBAAuB;AAC1D,OAAOC,wBAAwB,MAAM,sCAAsC;AAC3E,OAAOC,YAAY,MAAM,+BAA+B;AACxD,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,iBAAiBA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAE;EACtEL,YAAY,CAAC,CAAC,EAAEM,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGR,wBAAwB,CAACI,QAAQ,EAAEC,SAAS,CAAC,GAAGN,kBAAkB;EAC7E,OAAOG,iBAAiB,CAACI,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,cAAc,CAAC,CAACD,IAAI,CAAC;AAC1G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}