{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'segundo bat baino gutxiago',\n    other: '{{count}} segundo baino gutxiago'\n  },\n  xSeconds: {\n    one: '1 segundo',\n    other: '{{count}} segundo'\n  },\n  halfAMinute: 'minutu erdi',\n  lessThanXMinutes: {\n    one: 'minutu bat baino gutxiago',\n    other: '{{count}} minutu baino gutxiago'\n  },\n  xMinutes: {\n    one: '1 minutu',\n    other: '{{count}} minutu'\n  },\n  aboutXHours: {\n    one: '1 ordu gutxi gorabehera',\n    other: '{{count}} ordu gutxi gorabehera'\n  },\n  xHours: {\n    one: '1 ordu',\n    other: '{{count}} ordu'\n  },\n  xDays: {\n    one: '1 egun',\n    other: '{{count}} egun'\n  },\n  aboutXWeeks: {\n    one: 'aste 1 inguru',\n    other: '{{count}} aste inguru'\n  },\n  xWeeks: {\n    one: '1 aste',\n    other: '{{count}} astean'\n  },\n  aboutXMonths: {\n    one: '1 hilabete gutxi gorabehera',\n    other: '{{count}} hilabete gutxi gorabehera'\n  },\n  xMonths: {\n    one: '1 hilabete',\n    other: '{{count}} hilabete'\n  },\n  aboutXYears: {\n    one: '1 urte gutxi gorabehera',\n    other: '{{count}} urte gutxi gorabehera'\n  },\n  xYears: {\n    one: '1 urte',\n    other: '{{count}} urte'\n  },\n  overXYears: {\n    one: '1 urte baino gehiago',\n    other: '{{count}} urte baino gehiago'\n  },\n  almostXYears: {\n    one: 'ia 1 urte',\n    other: 'ia {{count}} urte'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'en ' + result;\n    } else {\n      return 'duela ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/eu/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'segundo bat baino gutxiago',\n    other: '{{count}} segundo baino gutxiago'\n  },\n  xSeconds: {\n    one: '1 segundo',\n    other: '{{count}} segundo'\n  },\n  halfAMinute: 'minutu erdi',\n  lessThanXMinutes: {\n    one: 'minutu bat baino gutxiago',\n    other: '{{count}} minutu baino gutxiago'\n  },\n  xMinutes: {\n    one: '1 minutu',\n    other: '{{count}} minutu'\n  },\n  aboutXHours: {\n    one: '1 ordu gutxi gorabehera',\n    other: '{{count}} ordu gutxi gorabehera'\n  },\n  xHours: {\n    one: '1 ordu',\n    other: '{{count}} ordu'\n  },\n  xDays: {\n    one: '1 egun',\n    other: '{{count}} egun'\n  },\n  aboutXWeeks: {\n    one: 'aste 1 inguru',\n    other: '{{count}} aste inguru'\n  },\n  xWeeks: {\n    one: '1 aste',\n    other: '{{count}} astean'\n  },\n  aboutXMonths: {\n    one: '1 hilabete gutxi gorabehera',\n    other: '{{count}} hilabete gutxi gorabehera'\n  },\n  xMonths: {\n    one: '1 hilabete',\n    other: '{{count}} hilabete'\n  },\n  aboutXYears: {\n    one: '1 urte gutxi gorabehera',\n    other: '{{count}} urte gutxi gorabehera'\n  },\n  xYears: {\n    one: '1 urte',\n    other: '{{count}} urte'\n  },\n  overXYears: {\n    one: '1 urte baino gehiago',\n    other: '{{count}} urte baino gehiago'\n  },\n  almostXYears: {\n    one: 'ia 1 urte',\n    other: 'ia {{count}} urte'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'en ' + result;\n    } else {\n      return 'duela ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,4BAA4B;IACjCC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,aAAa;EAC1BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,2BAA2B;IAChCC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,yBAAyB;IAC9BC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,6BAA6B;IAClCC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,yBAAyB;IAC9BC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAGL,MAAM;IACvB,CAAC,MAAM;MACL,OAAO,QAAQ,GAAGA,MAAM;IAC1B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}