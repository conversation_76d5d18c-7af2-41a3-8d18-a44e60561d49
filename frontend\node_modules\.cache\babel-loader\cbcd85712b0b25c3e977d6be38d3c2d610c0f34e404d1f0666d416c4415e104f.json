{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"PaperComponent\", \"popperPlacement\", \"ownerState\", \"children\", \"paperSlotProps\", \"paperClasses\", \"onPaperClick\", \"onPaperTouchStart\"];\nimport * as React from 'react';\nimport { useSlotProps } from '@mui/base/utils';\nimport Grow from '@mui/material/Grow';\nimport Fade from '@mui/material/Fade';\nimport MuiPaper from '@mui/material/Paper';\nimport MuiPopper from '@mui/material/Popper';\nimport BaseFocusTrap from '@mui/material/Unstable_TrapFocus';\nimport { unstable_useForkRef as useForkRef, unstable_useEventCallback as useEventCallback, unstable_ownerDocument as ownerDocument, unstable_composeClasses as composeClasses } from '@mui/utils';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { getPickersPopperUtilityClass } from './pickersPopperClasses';\nimport { getActiveElement } from '../utils/utils';\nimport { useDefaultReduceAnimations } from '../hooks/useDefaultReduceAnimations';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    paper: ['paper']\n  };\n  return composeClasses(slots, getPickersPopperUtilityClass, classes);\n};\nconst PickersPopperRoot = styled(MuiPopper, {\n  name: 'MuiPickersPopper',\n  slot: 'Root',\n  overridesResolver: (_, styles) => styles.root\n})(({\n  theme\n}) => ({\n  zIndex: theme.zIndex.modal\n}));\nconst PickersPopperPaper = styled(MuiPaper, {\n  name: 'MuiPickersPopper',\n  slot: 'Paper',\n  overridesResolver: (_, styles) => styles.paper\n})(({\n  ownerState\n}) => _extends({\n  outline: 0,\n  transformOrigin: 'top center'\n}, ownerState.placement.includes('top') && {\n  transformOrigin: 'bottom center'\n}));\nfunction clickedRootScrollbar(event, doc) {\n  return doc.documentElement.clientWidth < event.clientX || doc.documentElement.clientHeight < event.clientY;\n}\n/**\n * Based on @mui/material/ClickAwayListener without the customization.\n * We can probably strip away even more since children won't be portaled.\n * @param {boolean} active Only listen to clicks when the popper is opened.\n * @param {(event: MouseEvent | TouchEvent) => void} onClickAway The callback to call when clicking outside the popper.\n * @returns {Array} The ref and event handler to listen to the outside clicks.\n */\nfunction useClickAwayListener(active, onClickAway) {\n  const movedRef = React.useRef(false);\n  const syntheticEventRef = React.useRef(false);\n  const nodeRef = React.useRef(null);\n  const activatedRef = React.useRef(false);\n  React.useEffect(() => {\n    if (!active) {\n      return undefined;\n    }\n\n    // Ensure that this hook is not \"activated\" synchronously.\n    // https://github.com/facebook/react/issues/20074\n    function armClickAwayListener() {\n      activatedRef.current = true;\n    }\n    document.addEventListener('mousedown', armClickAwayListener, true);\n    document.addEventListener('touchstart', armClickAwayListener, true);\n    return () => {\n      document.removeEventListener('mousedown', armClickAwayListener, true);\n      document.removeEventListener('touchstart', armClickAwayListener, true);\n      activatedRef.current = false;\n    };\n  }, [active]);\n\n  // The handler doesn't take event.defaultPrevented into account:\n  //\n  // event.preventDefault() is meant to stop default behaviors like\n  // clicking a checkbox to check it, hitting a button to submit a form,\n  // and hitting left arrow to move the cursor in a text input etc.\n  // Only special HTML elements have these default behaviors.\n  const handleClickAway = useEventCallback(event => {\n    if (!activatedRef.current) {\n      return;\n    }\n\n    // Given developers can stop the propagation of the synthetic event,\n    // we can only be confident with a positive value.\n    const insideReactTree = syntheticEventRef.current;\n    syntheticEventRef.current = false;\n    const doc = ownerDocument(nodeRef.current);\n\n    // 1. IE11 support, which trigger the handleClickAway even after the unbind\n    // 2. The child might render null.\n    // 3. Behave like a blur listener.\n    if (!nodeRef.current ||\n    // is a TouchEvent?\n    'clientX' in event && clickedRootScrollbar(event, doc)) {\n      return;\n    }\n\n    // Do not act if user performed touchmove\n    if (movedRef.current) {\n      movedRef.current = false;\n      return;\n    }\n    let insideDOM;\n\n    // If not enough, can use https://github.com/DieterHolvoet/event-propagation-path/blob/master/propagationPath.js\n    if (event.composedPath) {\n      insideDOM = event.composedPath().indexOf(nodeRef.current) > -1;\n    } else {\n      insideDOM = !doc.documentElement.contains(event.target) || nodeRef.current.contains(event.target);\n    }\n    if (!insideDOM && !insideReactTree) {\n      onClickAway(event);\n    }\n  });\n\n  // Keep track of mouse/touch events that bubbled up through the portal.\n  const handleSynthetic = () => {\n    syntheticEventRef.current = true;\n  };\n  React.useEffect(() => {\n    if (active) {\n      const doc = ownerDocument(nodeRef.current);\n      const handleTouchMove = () => {\n        movedRef.current = true;\n      };\n      doc.addEventListener('touchstart', handleClickAway);\n      doc.addEventListener('touchmove', handleTouchMove);\n      return () => {\n        doc.removeEventListener('touchstart', handleClickAway);\n        doc.removeEventListener('touchmove', handleTouchMove);\n      };\n    }\n    return undefined;\n  }, [active, handleClickAway]);\n  React.useEffect(() => {\n    // TODO This behavior is not tested automatically\n    // It's unclear whether this is due to different update semantics in test (batched in act() vs discrete on click).\n    // Or if this is a timing related issues due to different Transition components\n    // Once we get rid of all the manual scheduling (e.g. setTimeout(update, 0)) we can revisit this code+test.\n    if (active) {\n      const doc = ownerDocument(nodeRef.current);\n      doc.addEventListener('click', handleClickAway);\n      return () => {\n        doc.removeEventListener('click', handleClickAway);\n        // cleanup `handleClickAway`\n        syntheticEventRef.current = false;\n      };\n    }\n    return undefined;\n  }, [active, handleClickAway]);\n  return [nodeRef, handleSynthetic, handleSynthetic];\n}\nconst PickersPopperPaperWrapper = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      PaperComponent,\n      popperPlacement,\n      ownerState: inOwnerState,\n      children,\n      paperSlotProps,\n      paperClasses,\n      onPaperClick,\n      onPaperTouchStart\n      // picks up the style props provided by `Transition`\n      // https://mui.com/material-ui/transitions/#child-requirement\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, inOwnerState, {\n    placement: popperPlacement\n  });\n  const paperProps = useSlotProps({\n    elementType: PaperComponent,\n    externalSlotProps: paperSlotProps,\n    additionalProps: {\n      tabIndex: -1,\n      elevation: 8,\n      ref\n    },\n    className: paperClasses,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(PaperComponent, _extends({}, other, paperProps, {\n    onClick: event => {\n      var _paperProps$onClick;\n      onPaperClick(event);\n      (_paperProps$onClick = paperProps.onClick) == null || _paperProps$onClick.call(paperProps, event);\n    },\n    onTouchStart: event => {\n      var _paperProps$onTouchSt;\n      onPaperTouchStart(event);\n      (_paperProps$onTouchSt = paperProps.onTouchStart) == null || _paperProps$onTouchSt.call(paperProps, event);\n    },\n    ownerState: ownerState,\n    children: children\n  }));\n});\nexport function PickersPopper(inProps) {\n  var _slots$desktopTransit, _slots$desktopTrapFoc, _slots$desktopPaper, _slots$popper;\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiPickersPopper'\n  });\n  const {\n    anchorEl,\n    children,\n    containerRef = null,\n    shouldRestoreFocus,\n    onBlur,\n    onDismiss,\n    open,\n    role,\n    placement,\n    slots,\n    slotProps,\n    reduceAnimations: inReduceAnimations\n  } = props;\n  React.useEffect(() => {\n    function handleKeyDown(nativeEvent) {\n      // IE11, Edge (prior to using Blink?) use 'Esc'\n      if (open && (nativeEvent.key === 'Escape' || nativeEvent.key === 'Esc')) {\n        onDismiss();\n      }\n    }\n    document.addEventListener('keydown', handleKeyDown);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [onDismiss, open]);\n  const lastFocusedElementRef = React.useRef(null);\n  React.useEffect(() => {\n    if (role === 'tooltip' || shouldRestoreFocus && !shouldRestoreFocus()) {\n      return;\n    }\n    if (open) {\n      lastFocusedElementRef.current = getActiveElement(document);\n    } else if (lastFocusedElementRef.current && lastFocusedElementRef.current instanceof HTMLElement) {\n      // make sure the button is flushed with updated label, before returning focus to it\n      // avoids issue, where screen reader could fail to announce selected date after selection\n      setTimeout(() => {\n        if (lastFocusedElementRef.current instanceof HTMLElement) {\n          lastFocusedElementRef.current.focus();\n        }\n      });\n    }\n  }, [open, role, shouldRestoreFocus]);\n  const [clickAwayRef, onPaperClick, onPaperTouchStart] = useClickAwayListener(open, onBlur != null ? onBlur : onDismiss);\n  const paperRef = React.useRef(null);\n  const handleRef = useForkRef(paperRef, containerRef);\n  const handlePaperRef = useForkRef(handleRef, clickAwayRef);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  const defaultReduceAnimations = useDefaultReduceAnimations();\n  const reduceAnimations = inReduceAnimations != null ? inReduceAnimations : defaultReduceAnimations;\n  const handleKeyDown = event => {\n    if (event.key === 'Escape') {\n      // stop the propagation to avoid closing parent modal\n      event.stopPropagation();\n      onDismiss();\n    }\n  };\n  const Transition = ((_slots$desktopTransit = slots == null ? void 0 : slots.desktopTransition) != null ? _slots$desktopTransit : reduceAnimations) ? Fade : Grow;\n  const FocusTrap = (_slots$desktopTrapFoc = slots == null ? void 0 : slots.desktopTrapFocus) != null ? _slots$desktopTrapFoc : BaseFocusTrap;\n  const Paper = (_slots$desktopPaper = slots == null ? void 0 : slots.desktopPaper) != null ? _slots$desktopPaper : PickersPopperPaper;\n  const Popper = (_slots$popper = slots == null ? void 0 : slots.popper) != null ? _slots$popper : PickersPopperRoot;\n  const popperProps = useSlotProps({\n    elementType: Popper,\n    externalSlotProps: slotProps == null ? void 0 : slotProps.popper,\n    additionalProps: {\n      transition: true,\n      role,\n      open,\n      anchorEl,\n      placement,\n      onKeyDown: handleKeyDown\n    },\n    className: classes.root,\n    ownerState: props\n  });\n  return /*#__PURE__*/_jsx(Popper, _extends({}, popperProps, {\n    children: ({\n      TransitionProps,\n      placement: popperPlacement\n    }) => /*#__PURE__*/_jsx(FocusTrap, _extends({\n      open: open,\n      disableAutoFocus: true\n      // pickers are managing focus position manually\n      // without this prop the focus is returned to the button before `aria-label` is updated\n      // which would force screen readers to read too old label\n      ,\n\n      disableRestoreFocus: true,\n      disableEnforceFocus: role === 'tooltip',\n      isEnabled: () => true\n    }, slotProps == null ? void 0 : slotProps.desktopTrapFocus, {\n      children: /*#__PURE__*/_jsx(Transition, _extends({}, TransitionProps, slotProps == null ? void 0 : slotProps.desktopTransition, {\n        children: /*#__PURE__*/_jsx(PickersPopperPaperWrapper, {\n          PaperComponent: Paper,\n          ownerState: ownerState,\n          popperPlacement: popperPlacement,\n          ref: handlePaperRef,\n          onPaperClick: onPaperClick,\n          onPaperTouchStart: onPaperTouchStart,\n          paperClasses: classes.paper,\n          paperSlotProps: slotProps == null ? void 0 : slotProps.desktopPaper,\n          children: children\n        })\n      }))\n    }))\n  }));\n}", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "useSlotProps", "Grow", "Fade", "MuiPaper", "MuiPopper", "BaseFocusTrap", "unstable_useForkRef", "useForkRef", "unstable_useEventCallback", "useEventCallback", "unstable_ownerDocument", "ownerDocument", "unstable_composeClasses", "composeClasses", "styled", "useThemeProps", "getPickersPopperUtilityClass", "getActiveElement", "useDefaultReduceAnimations", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "paper", "PickersPopperRoot", "name", "slot", "overridesResolver", "_", "styles", "theme", "zIndex", "modal", "PickersPopperPaper", "outline", "transform<PERSON><PERSON>in", "placement", "includes", "clickedRootScrollbar", "event", "doc", "documentElement", "clientWidth", "clientX", "clientHeight", "clientY", "useClickAwayListener", "active", "onClickAway", "movedRef", "useRef", "syntheticEventRef", "nodeRef", "activatedRef", "useEffect", "undefined", "armClickAwayListener", "current", "document", "addEventListener", "removeEventListener", "handleClickAway", "insideReactTree", "insideDOM", "<PERSON><PERSON><PERSON>", "indexOf", "contains", "target", "handleSynthetic", "handleTouchMove", "PickersPopperPaperWrapper", "forwardRef", "props", "ref", "PaperComponent", "popperPlacement", "inOwnerState", "children", "paperSlotProps", "paperClasses", "onPaperClick", "onPaperTouchStart", "other", "paperProps", "elementType", "externalSlotProps", "additionalProps", "tabIndex", "elevation", "className", "onClick", "_paperProps$onClick", "call", "onTouchStart", "_paperProps$onTouchSt", "PickersPopper", "inProps", "_slots$desktopTransit", "_slots$desktopTrapFoc", "_slots$desktopPaper", "_slots$popper", "anchorEl", "containerRef", "shouldRestoreFocus", "onBlur", "on<PERSON><PERSON><PERSON>", "open", "role", "slotProps", "reduceAnimations", "inReduceAnimations", "handleKeyDown", "nativeEvent", "key", "lastFocusedElementRef", "HTMLElement", "setTimeout", "focus", "clickAwayRef", "paperRef", "handleRef", "handlePaperRef", "defaultReduceAnimations", "stopPropagation", "Transition", "desktopTransition", "FocusTrap", "desktopTrapFocus", "Paper", "desktopPaper", "<PERSON><PERSON>", "popper", "popperProps", "transition", "onKeyDown", "TransitionProps", "disableAutoFocus", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disableEnforceFocus", "isEnabled"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/x-date-pickers/internals/components/PickersPopper.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"PaperComponent\", \"popperPlacement\", \"ownerState\", \"children\", \"paperSlotProps\", \"paperClasses\", \"onPaperClick\", \"onPaperTouchStart\"];\nimport * as React from 'react';\nimport { useSlotProps } from '@mui/base/utils';\nimport Grow from '@mui/material/Grow';\nimport Fade from '@mui/material/Fade';\nimport MuiPaper from '@mui/material/Paper';\nimport MuiPopper from '@mui/material/Popper';\nimport BaseFocusTrap from '@mui/material/Unstable_TrapFocus';\nimport { unstable_useForkRef as useForkRef, unstable_useEventCallback as useEventCallback, unstable_ownerDocument as ownerDocument, unstable_composeClasses as composeClasses } from '@mui/utils';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { getPickersPopperUtilityClass } from './pickersPopperClasses';\nimport { getActiveElement } from '../utils/utils';\nimport { useDefaultReduceAnimations } from '../hooks/useDefaultReduceAnimations';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    paper: ['paper']\n  };\n  return composeClasses(slots, getPickersPopperUtilityClass, classes);\n};\nconst PickersPopperRoot = styled(MuiPopper, {\n  name: 'MuiPickersPopper',\n  slot: 'Root',\n  overridesResolver: (_, styles) => styles.root\n})(({\n  theme\n}) => ({\n  zIndex: theme.zIndex.modal\n}));\nconst PickersPopperPaper = styled(MuiPaper, {\n  name: 'MuiPickersPopper',\n  slot: 'Paper',\n  overridesResolver: (_, styles) => styles.paper\n})(({\n  ownerState\n}) => _extends({\n  outline: 0,\n  transformOrigin: 'top center'\n}, ownerState.placement.includes('top') && {\n  transformOrigin: 'bottom center'\n}));\nfunction clickedRootScrollbar(event, doc) {\n  return doc.documentElement.clientWidth < event.clientX || doc.documentElement.clientHeight < event.clientY;\n}\n/**\n * Based on @mui/material/ClickAwayListener without the customization.\n * We can probably strip away even more since children won't be portaled.\n * @param {boolean} active Only listen to clicks when the popper is opened.\n * @param {(event: MouseEvent | TouchEvent) => void} onClickAway The callback to call when clicking outside the popper.\n * @returns {Array} The ref and event handler to listen to the outside clicks.\n */\nfunction useClickAwayListener(active, onClickAway) {\n  const movedRef = React.useRef(false);\n  const syntheticEventRef = React.useRef(false);\n  const nodeRef = React.useRef(null);\n  const activatedRef = React.useRef(false);\n  React.useEffect(() => {\n    if (!active) {\n      return undefined;\n    }\n\n    // Ensure that this hook is not \"activated\" synchronously.\n    // https://github.com/facebook/react/issues/20074\n    function armClickAwayListener() {\n      activatedRef.current = true;\n    }\n    document.addEventListener('mousedown', armClickAwayListener, true);\n    document.addEventListener('touchstart', armClickAwayListener, true);\n    return () => {\n      document.removeEventListener('mousedown', armClickAwayListener, true);\n      document.removeEventListener('touchstart', armClickAwayListener, true);\n      activatedRef.current = false;\n    };\n  }, [active]);\n\n  // The handler doesn't take event.defaultPrevented into account:\n  //\n  // event.preventDefault() is meant to stop default behaviors like\n  // clicking a checkbox to check it, hitting a button to submit a form,\n  // and hitting left arrow to move the cursor in a text input etc.\n  // Only special HTML elements have these default behaviors.\n  const handleClickAway = useEventCallback(event => {\n    if (!activatedRef.current) {\n      return;\n    }\n\n    // Given developers can stop the propagation of the synthetic event,\n    // we can only be confident with a positive value.\n    const insideReactTree = syntheticEventRef.current;\n    syntheticEventRef.current = false;\n    const doc = ownerDocument(nodeRef.current);\n\n    // 1. IE11 support, which trigger the handleClickAway even after the unbind\n    // 2. The child might render null.\n    // 3. Behave like a blur listener.\n    if (!nodeRef.current ||\n    // is a TouchEvent?\n    'clientX' in event && clickedRootScrollbar(event, doc)) {\n      return;\n    }\n\n    // Do not act if user performed touchmove\n    if (movedRef.current) {\n      movedRef.current = false;\n      return;\n    }\n    let insideDOM;\n\n    // If not enough, can use https://github.com/DieterHolvoet/event-propagation-path/blob/master/propagationPath.js\n    if (event.composedPath) {\n      insideDOM = event.composedPath().indexOf(nodeRef.current) > -1;\n    } else {\n      insideDOM = !doc.documentElement.contains(event.target) || nodeRef.current.contains(event.target);\n    }\n    if (!insideDOM && !insideReactTree) {\n      onClickAway(event);\n    }\n  });\n\n  // Keep track of mouse/touch events that bubbled up through the portal.\n  const handleSynthetic = () => {\n    syntheticEventRef.current = true;\n  };\n  React.useEffect(() => {\n    if (active) {\n      const doc = ownerDocument(nodeRef.current);\n      const handleTouchMove = () => {\n        movedRef.current = true;\n      };\n      doc.addEventListener('touchstart', handleClickAway);\n      doc.addEventListener('touchmove', handleTouchMove);\n      return () => {\n        doc.removeEventListener('touchstart', handleClickAway);\n        doc.removeEventListener('touchmove', handleTouchMove);\n      };\n    }\n    return undefined;\n  }, [active, handleClickAway]);\n  React.useEffect(() => {\n    // TODO This behavior is not tested automatically\n    // It's unclear whether this is due to different update semantics in test (batched in act() vs discrete on click).\n    // Or if this is a timing related issues due to different Transition components\n    // Once we get rid of all the manual scheduling (e.g. setTimeout(update, 0)) we can revisit this code+test.\n    if (active) {\n      const doc = ownerDocument(nodeRef.current);\n      doc.addEventListener('click', handleClickAway);\n      return () => {\n        doc.removeEventListener('click', handleClickAway);\n        // cleanup `handleClickAway`\n        syntheticEventRef.current = false;\n      };\n    }\n    return undefined;\n  }, [active, handleClickAway]);\n  return [nodeRef, handleSynthetic, handleSynthetic];\n}\nconst PickersPopperPaperWrapper = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      PaperComponent,\n      popperPlacement,\n      ownerState: inOwnerState,\n      children,\n      paperSlotProps,\n      paperClasses,\n      onPaperClick,\n      onPaperTouchStart\n      // picks up the style props provided by `Transition`\n      // https://mui.com/material-ui/transitions/#child-requirement\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, inOwnerState, {\n    placement: popperPlacement\n  });\n  const paperProps = useSlotProps({\n    elementType: PaperComponent,\n    externalSlotProps: paperSlotProps,\n    additionalProps: {\n      tabIndex: -1,\n      elevation: 8,\n      ref\n    },\n    className: paperClasses,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(PaperComponent, _extends({}, other, paperProps, {\n    onClick: event => {\n      var _paperProps$onClick;\n      onPaperClick(event);\n      (_paperProps$onClick = paperProps.onClick) == null || _paperProps$onClick.call(paperProps, event);\n    },\n    onTouchStart: event => {\n      var _paperProps$onTouchSt;\n      onPaperTouchStart(event);\n      (_paperProps$onTouchSt = paperProps.onTouchStart) == null || _paperProps$onTouchSt.call(paperProps, event);\n    },\n    ownerState: ownerState,\n    children: children\n  }));\n});\nexport function PickersPopper(inProps) {\n  var _slots$desktopTransit, _slots$desktopTrapFoc, _slots$desktopPaper, _slots$popper;\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiPickersPopper'\n  });\n  const {\n    anchorEl,\n    children,\n    containerRef = null,\n    shouldRestoreFocus,\n    onBlur,\n    onDismiss,\n    open,\n    role,\n    placement,\n    slots,\n    slotProps,\n    reduceAnimations: inReduceAnimations\n  } = props;\n  React.useEffect(() => {\n    function handleKeyDown(nativeEvent) {\n      // IE11, Edge (prior to using Blink?) use 'Esc'\n      if (open && (nativeEvent.key === 'Escape' || nativeEvent.key === 'Esc')) {\n        onDismiss();\n      }\n    }\n    document.addEventListener('keydown', handleKeyDown);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [onDismiss, open]);\n  const lastFocusedElementRef = React.useRef(null);\n  React.useEffect(() => {\n    if (role === 'tooltip' || shouldRestoreFocus && !shouldRestoreFocus()) {\n      return;\n    }\n    if (open) {\n      lastFocusedElementRef.current = getActiveElement(document);\n    } else if (lastFocusedElementRef.current && lastFocusedElementRef.current instanceof HTMLElement) {\n      // make sure the button is flushed with updated label, before returning focus to it\n      // avoids issue, where screen reader could fail to announce selected date after selection\n      setTimeout(() => {\n        if (lastFocusedElementRef.current instanceof HTMLElement) {\n          lastFocusedElementRef.current.focus();\n        }\n      });\n    }\n  }, [open, role, shouldRestoreFocus]);\n  const [clickAwayRef, onPaperClick, onPaperTouchStart] = useClickAwayListener(open, onBlur != null ? onBlur : onDismiss);\n  const paperRef = React.useRef(null);\n  const handleRef = useForkRef(paperRef, containerRef);\n  const handlePaperRef = useForkRef(handleRef, clickAwayRef);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  const defaultReduceAnimations = useDefaultReduceAnimations();\n  const reduceAnimations = inReduceAnimations != null ? inReduceAnimations : defaultReduceAnimations;\n  const handleKeyDown = event => {\n    if (event.key === 'Escape') {\n      // stop the propagation to avoid closing parent modal\n      event.stopPropagation();\n      onDismiss();\n    }\n  };\n  const Transition = ((_slots$desktopTransit = slots == null ? void 0 : slots.desktopTransition) != null ? _slots$desktopTransit : reduceAnimations) ? Fade : Grow;\n  const FocusTrap = (_slots$desktopTrapFoc = slots == null ? void 0 : slots.desktopTrapFocus) != null ? _slots$desktopTrapFoc : BaseFocusTrap;\n  const Paper = (_slots$desktopPaper = slots == null ? void 0 : slots.desktopPaper) != null ? _slots$desktopPaper : PickersPopperPaper;\n  const Popper = (_slots$popper = slots == null ? void 0 : slots.popper) != null ? _slots$popper : PickersPopperRoot;\n  const popperProps = useSlotProps({\n    elementType: Popper,\n    externalSlotProps: slotProps == null ? void 0 : slotProps.popper,\n    additionalProps: {\n      transition: true,\n      role,\n      open,\n      anchorEl,\n      placement,\n      onKeyDown: handleKeyDown\n    },\n    className: classes.root,\n    ownerState: props\n  });\n  return /*#__PURE__*/_jsx(Popper, _extends({}, popperProps, {\n    children: ({\n      TransitionProps,\n      placement: popperPlacement\n    }) => /*#__PURE__*/_jsx(FocusTrap, _extends({\n      open: open,\n      disableAutoFocus: true\n      // pickers are managing focus position manually\n      // without this prop the focus is returned to the button before `aria-label` is updated\n      // which would force screen readers to read too old label\n      ,\n      disableRestoreFocus: true,\n      disableEnforceFocus: role === 'tooltip',\n      isEnabled: () => true\n    }, slotProps == null ? void 0 : slotProps.desktopTrapFocus, {\n      children: /*#__PURE__*/_jsx(Transition, _extends({}, TransitionProps, slotProps == null ? void 0 : slotProps.desktopTransition, {\n        children: /*#__PURE__*/_jsx(PickersPopperPaperWrapper, {\n          PaperComponent: Paper,\n          ownerState: ownerState,\n          popperPlacement: popperPlacement,\n          ref: handlePaperRef,\n          onPaperClick: onPaperClick,\n          onPaperTouchStart: onPaperTouchStart,\n          paperClasses: classes.paper,\n          paperSlotProps: slotProps == null ? void 0 : slotProps.desktopPaper,\n          children: children\n        })\n      }))\n    }))\n  }));\n}"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,gBAAgB,EAAE,iBAAiB,EAAE,YAAY,EAAE,UAAU,EAAE,gBAAgB,EAAE,cAAc,EAAE,cAAc,EAAE,mBAAmB,CAAC;AACxJ,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,SAASC,mBAAmB,IAAIC,UAAU,EAAEC,yBAAyB,IAAIC,gBAAgB,EAAEC,sBAAsB,IAAIC,aAAa,EAAEC,uBAAuB,IAAIC,cAAc,QAAQ,YAAY;AACjM,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,SAASC,4BAA4B,QAAQ,wBAAwB;AACrE,SAASC,gBAAgB,QAAQ,gBAAgB;AACjD,SAASC,0BAA0B,QAAQ,qCAAqC;AAChF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,KAAK,EAAE,CAAC,OAAO;EACjB,CAAC;EACD,OAAOb,cAAc,CAACW,KAAK,EAAER,4BAA4B,EAAEO,OAAO,CAAC;AACrE,CAAC;AACD,MAAMI,iBAAiB,GAAGb,MAAM,CAACV,SAAS,EAAE;EAC1CwB,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACP;AAC3C,CAAC,CAAC,CAAC,CAAC;EACFQ;AACF,CAAC,MAAM;EACLC,MAAM,EAAED,KAAK,CAACC,MAAM,CAACC;AACvB,CAAC,CAAC,CAAC;AACH,MAAMC,kBAAkB,GAAGtB,MAAM,CAACX,QAAQ,EAAE;EAC1CyB,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC3C,CAAC,CAAC,CAAC,CAAC;EACFJ;AACF,CAAC,KAAKzB,QAAQ,CAAC;EACbwC,OAAO,EAAE,CAAC;EACVC,eAAe,EAAE;AACnB,CAAC,EAAEhB,UAAU,CAACiB,SAAS,CAACC,QAAQ,CAAC,KAAK,CAAC,IAAI;EACzCF,eAAe,EAAE;AACnB,CAAC,CAAC,CAAC;AACH,SAASG,oBAAoBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACxC,OAAOA,GAAG,CAACC,eAAe,CAACC,WAAW,GAAGH,KAAK,CAACI,OAAO,IAAIH,GAAG,CAACC,eAAe,CAACG,YAAY,GAAGL,KAAK,CAACM,OAAO;AAC5G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAACC,MAAM,EAAEC,WAAW,EAAE;EACjD,MAAMC,QAAQ,GAAGrD,KAAK,CAACsD,MAAM,CAAC,KAAK,CAAC;EACpC,MAAMC,iBAAiB,GAAGvD,KAAK,CAACsD,MAAM,CAAC,KAAK,CAAC;EAC7C,MAAME,OAAO,GAAGxD,KAAK,CAACsD,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMG,YAAY,GAAGzD,KAAK,CAACsD,MAAM,CAAC,KAAK,CAAC;EACxCtD,KAAK,CAAC0D,SAAS,CAAC,MAAM;IACpB,IAAI,CAACP,MAAM,EAAE;MACX,OAAOQ,SAAS;IAClB;;IAEA;IACA;IACA,SAASC,oBAAoBA,CAAA,EAAG;MAC9BH,YAAY,CAACI,OAAO,GAAG,IAAI;IAC7B;IACAC,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEH,oBAAoB,EAAE,IAAI,CAAC;IAClEE,QAAQ,CAACC,gBAAgB,CAAC,YAAY,EAAEH,oBAAoB,EAAE,IAAI,CAAC;IACnE,OAAO,MAAM;MACXE,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEJ,oBAAoB,EAAE,IAAI,CAAC;MACrEE,QAAQ,CAACE,mBAAmB,CAAC,YAAY,EAAEJ,oBAAoB,EAAE,IAAI,CAAC;MACtEH,YAAY,CAACI,OAAO,GAAG,KAAK;IAC9B,CAAC;EACH,CAAC,EAAE,CAACV,MAAM,CAAC,CAAC;;EAEZ;EACA;EACA;EACA;EACA;EACA;EACA,MAAMc,eAAe,GAAGvD,gBAAgB,CAACiC,KAAK,IAAI;IAChD,IAAI,CAACc,YAAY,CAACI,OAAO,EAAE;MACzB;IACF;;IAEA;IACA;IACA,MAAMK,eAAe,GAAGX,iBAAiB,CAACM,OAAO;IACjDN,iBAAiB,CAACM,OAAO,GAAG,KAAK;IACjC,MAAMjB,GAAG,GAAGhC,aAAa,CAAC4C,OAAO,CAACK,OAAO,CAAC;;IAE1C;IACA;IACA;IACA,IAAI,CAACL,OAAO,CAACK,OAAO;IACpB;IACA,SAAS,IAAIlB,KAAK,IAAID,oBAAoB,CAACC,KAAK,EAAEC,GAAG,CAAC,EAAE;MACtD;IACF;;IAEA;IACA,IAAIS,QAAQ,CAACQ,OAAO,EAAE;MACpBR,QAAQ,CAACQ,OAAO,GAAG,KAAK;MACxB;IACF;IACA,IAAIM,SAAS;;IAEb;IACA,IAAIxB,KAAK,CAACyB,YAAY,EAAE;MACtBD,SAAS,GAAGxB,KAAK,CAACyB,YAAY,CAAC,CAAC,CAACC,OAAO,CAACb,OAAO,CAACK,OAAO,CAAC,GAAG,CAAC,CAAC;IAChE,CAAC,MAAM;MACLM,SAAS,GAAG,CAACvB,GAAG,CAACC,eAAe,CAACyB,QAAQ,CAAC3B,KAAK,CAAC4B,MAAM,CAAC,IAAIf,OAAO,CAACK,OAAO,CAACS,QAAQ,CAAC3B,KAAK,CAAC4B,MAAM,CAAC;IACnG;IACA,IAAI,CAACJ,SAAS,IAAI,CAACD,eAAe,EAAE;MAClCd,WAAW,CAACT,KAAK,CAAC;IACpB;EACF,CAAC,CAAC;;EAEF;EACA,MAAM6B,eAAe,GAAGA,CAAA,KAAM;IAC5BjB,iBAAiB,CAACM,OAAO,GAAG,IAAI;EAClC,CAAC;EACD7D,KAAK,CAAC0D,SAAS,CAAC,MAAM;IACpB,IAAIP,MAAM,EAAE;MACV,MAAMP,GAAG,GAAGhC,aAAa,CAAC4C,OAAO,CAACK,OAAO,CAAC;MAC1C,MAAMY,eAAe,GAAGA,CAAA,KAAM;QAC5BpB,QAAQ,CAACQ,OAAO,GAAG,IAAI;MACzB,CAAC;MACDjB,GAAG,CAACmB,gBAAgB,CAAC,YAAY,EAAEE,eAAe,CAAC;MACnDrB,GAAG,CAACmB,gBAAgB,CAAC,WAAW,EAAEU,eAAe,CAAC;MAClD,OAAO,MAAM;QACX7B,GAAG,CAACoB,mBAAmB,CAAC,YAAY,EAAEC,eAAe,CAAC;QACtDrB,GAAG,CAACoB,mBAAmB,CAAC,WAAW,EAAES,eAAe,CAAC;MACvD,CAAC;IACH;IACA,OAAOd,SAAS;EAClB,CAAC,EAAE,CAACR,MAAM,EAAEc,eAAe,CAAC,CAAC;EAC7BjE,KAAK,CAAC0D,SAAS,CAAC,MAAM;IACpB;IACA;IACA;IACA;IACA,IAAIP,MAAM,EAAE;MACV,MAAMP,GAAG,GAAGhC,aAAa,CAAC4C,OAAO,CAACK,OAAO,CAAC;MAC1CjB,GAAG,CAACmB,gBAAgB,CAAC,OAAO,EAAEE,eAAe,CAAC;MAC9C,OAAO,MAAM;QACXrB,GAAG,CAACoB,mBAAmB,CAAC,OAAO,EAAEC,eAAe,CAAC;QACjD;QACAV,iBAAiB,CAACM,OAAO,GAAG,KAAK;MACnC,CAAC;IACH;IACA,OAAOF,SAAS;EAClB,CAAC,EAAE,CAACR,MAAM,EAAEc,eAAe,CAAC,CAAC;EAC7B,OAAO,CAACT,OAAO,EAAEgB,eAAe,EAAEA,eAAe,CAAC;AACpD;AACA,MAAME,yBAAyB,GAAG,aAAa1E,KAAK,CAAC2E,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAC9E,MAAM;MACFC,cAAc;MACdC,eAAe;MACfxD,UAAU,EAAEyD,YAAY;MACxBC,QAAQ;MACRC,cAAc;MACdC,YAAY;MACZC,YAAY;MACZC;MACA;MACA;IACF,CAAC,GAAGT,KAAK;IACTU,KAAK,GAAGzF,6BAA6B,CAAC+E,KAAK,EAAE7E,SAAS,CAAC;EACzD,MAAMwB,UAAU,GAAGzB,QAAQ,CAAC,CAAC,CAAC,EAAEkF,YAAY,EAAE;IAC5CxC,SAAS,EAAEuC;EACb,CAAC,CAAC;EACF,MAAMQ,UAAU,GAAGtF,YAAY,CAAC;IAC9BuF,WAAW,EAAEV,cAAc;IAC3BW,iBAAiB,EAAEP,cAAc;IACjCQ,eAAe,EAAE;MACfC,QAAQ,EAAE,CAAC,CAAC;MACZC,SAAS,EAAE,CAAC;MACZf;IACF,CAAC;IACDgB,SAAS,EAAEV,YAAY;IACvB5D;EACF,CAAC,CAAC;EACF,OAAO,aAAaF,IAAI,CAACyD,cAAc,EAAEhF,QAAQ,CAAC,CAAC,CAAC,EAAEwF,KAAK,EAAEC,UAAU,EAAE;IACvEO,OAAO,EAAEnD,KAAK,IAAI;MAChB,IAAIoD,mBAAmB;MACvBX,YAAY,CAACzC,KAAK,CAAC;MACnB,CAACoD,mBAAmB,GAAGR,UAAU,CAACO,OAAO,KAAK,IAAI,IAAIC,mBAAmB,CAACC,IAAI,CAACT,UAAU,EAAE5C,KAAK,CAAC;IACnG,CAAC;IACDsD,YAAY,EAAEtD,KAAK,IAAI;MACrB,IAAIuD,qBAAqB;MACzBb,iBAAiB,CAAC1C,KAAK,CAAC;MACxB,CAACuD,qBAAqB,GAAGX,UAAU,CAACU,YAAY,KAAK,IAAI,IAAIC,qBAAqB,CAACF,IAAI,CAACT,UAAU,EAAE5C,KAAK,CAAC;IAC5G,CAAC;IACDpB,UAAU,EAAEA,UAAU;IACtB0D,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,OAAO,SAASkB,aAAaA,CAACC,OAAO,EAAE;EACrC,IAAIC,qBAAqB,EAAEC,qBAAqB,EAAEC,mBAAmB,EAAEC,aAAa;EACpF,MAAM5B,KAAK,GAAG5D,aAAa,CAAC;IAC1B4D,KAAK,EAAEwB,OAAO;IACdvE,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJ4E,QAAQ;IACRxB,QAAQ;IACRyB,YAAY,GAAG,IAAI;IACnBC,kBAAkB;IAClBC,MAAM;IACNC,SAAS;IACTC,IAAI;IACJC,IAAI;IACJvE,SAAS;IACTf,KAAK;IACLuF,SAAS;IACTC,gBAAgB,EAAEC;EACpB,CAAC,GAAGtC,KAAK;EACT5E,KAAK,CAAC0D,SAAS,CAAC,MAAM;IACpB,SAASyD,aAAaA,CAACC,WAAW,EAAE;MAClC;MACA,IAAIN,IAAI,KAAKM,WAAW,CAACC,GAAG,KAAK,QAAQ,IAAID,WAAW,CAACC,GAAG,KAAK,KAAK,CAAC,EAAE;QACvER,SAAS,CAAC,CAAC;MACb;IACF;IACA/C,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEoD,aAAa,CAAC;IACnD,OAAO,MAAM;MACXrD,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEmD,aAAa,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,CAACN,SAAS,EAAEC,IAAI,CAAC,CAAC;EACrB,MAAMQ,qBAAqB,GAAGtH,KAAK,CAACsD,MAAM,CAAC,IAAI,CAAC;EAChDtD,KAAK,CAAC0D,SAAS,CAAC,MAAM;IACpB,IAAIqD,IAAI,KAAK,SAAS,IAAIJ,kBAAkB,IAAI,CAACA,kBAAkB,CAAC,CAAC,EAAE;MACrE;IACF;IACA,IAAIG,IAAI,EAAE;MACRQ,qBAAqB,CAACzD,OAAO,GAAG3C,gBAAgB,CAAC4C,QAAQ,CAAC;IAC5D,CAAC,MAAM,IAAIwD,qBAAqB,CAACzD,OAAO,IAAIyD,qBAAqB,CAACzD,OAAO,YAAY0D,WAAW,EAAE;MAChG;MACA;MACAC,UAAU,CAAC,MAAM;QACf,IAAIF,qBAAqB,CAACzD,OAAO,YAAY0D,WAAW,EAAE;UACxDD,qBAAqB,CAACzD,OAAO,CAAC4D,KAAK,CAAC,CAAC;QACvC;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACX,IAAI,EAAEC,IAAI,EAAEJ,kBAAkB,CAAC,CAAC;EACpC,MAAM,CAACe,YAAY,EAAEtC,YAAY,EAAEC,iBAAiB,CAAC,GAAGnC,oBAAoB,CAAC4D,IAAI,EAAEF,MAAM,IAAI,IAAI,GAAGA,MAAM,GAAGC,SAAS,CAAC;EACvH,MAAMc,QAAQ,GAAG3H,KAAK,CAACsD,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMsE,SAAS,GAAGpH,UAAU,CAACmH,QAAQ,EAAEjB,YAAY,CAAC;EACpD,MAAMmB,cAAc,GAAGrH,UAAU,CAACoH,SAAS,EAAEF,YAAY,CAAC;EAC1D,MAAMnG,UAAU,GAAGqD,KAAK;EACxB,MAAMpD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMuG,uBAAuB,GAAG3G,0BAA0B,CAAC,CAAC;EAC5D,MAAM8F,gBAAgB,GAAGC,kBAAkB,IAAI,IAAI,GAAGA,kBAAkB,GAAGY,uBAAuB;EAClG,MAAMX,aAAa,GAAGxE,KAAK,IAAI;IAC7B,IAAIA,KAAK,CAAC0E,GAAG,KAAK,QAAQ,EAAE;MAC1B;MACA1E,KAAK,CAACoF,eAAe,CAAC,CAAC;MACvBlB,SAAS,CAAC,CAAC;IACb;EACF,CAAC;EACD,MAAMmB,UAAU,GAAG,CAAC,CAAC3B,qBAAqB,GAAG5E,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACwG,iBAAiB,KAAK,IAAI,GAAG5B,qBAAqB,GAAGY,gBAAgB,IAAI9G,IAAI,GAAGD,IAAI;EAChK,MAAMgI,SAAS,GAAG,CAAC5B,qBAAqB,GAAG7E,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC0G,gBAAgB,KAAK,IAAI,GAAG7B,qBAAqB,GAAGhG,aAAa;EAC3I,MAAM8H,KAAK,GAAG,CAAC7B,mBAAmB,GAAG9E,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC4G,YAAY,KAAK,IAAI,GAAG9B,mBAAmB,GAAGlE,kBAAkB;EACpI,MAAMiG,MAAM,GAAG,CAAC9B,aAAa,GAAG/E,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC8G,MAAM,KAAK,IAAI,GAAG/B,aAAa,GAAG5E,iBAAiB;EAClH,MAAM4G,WAAW,GAAGvI,YAAY,CAAC;IAC/BuF,WAAW,EAAE8C,MAAM;IACnB7C,iBAAiB,EAAEuB,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACuB,MAAM;IAChE7C,eAAe,EAAE;MACf+C,UAAU,EAAE,IAAI;MAChB1B,IAAI;MACJD,IAAI;MACJL,QAAQ;MACRjE,SAAS;MACTkG,SAAS,EAAEvB;IACb,CAAC;IACDtB,SAAS,EAAErE,OAAO,CAACE,IAAI;IACvBH,UAAU,EAAEqD;EACd,CAAC,CAAC;EACF,OAAO,aAAavD,IAAI,CAACiH,MAAM,EAAExI,QAAQ,CAAC,CAAC,CAAC,EAAE0I,WAAW,EAAE;IACzDvD,QAAQ,EAAEA,CAAC;MACT0D,eAAe;MACfnG,SAAS,EAAEuC;IACb,CAAC,KAAK,aAAa1D,IAAI,CAAC6G,SAAS,EAAEpI,QAAQ,CAAC;MAC1CgH,IAAI,EAAEA,IAAI;MACV8B,gBAAgB,EAAE;MAClB;MACA;MACA;MAAA;;MAEAC,mBAAmB,EAAE,IAAI;MACzBC,mBAAmB,EAAE/B,IAAI,KAAK,SAAS;MACvCgC,SAAS,EAAEA,CAAA,KAAM;IACnB,CAAC,EAAE/B,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACmB,gBAAgB,EAAE;MAC1DlD,QAAQ,EAAE,aAAa5D,IAAI,CAAC2G,UAAU,EAAElI,QAAQ,CAAC,CAAC,CAAC,EAAE6I,eAAe,EAAE3B,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACiB,iBAAiB,EAAE;QAC9HhD,QAAQ,EAAE,aAAa5D,IAAI,CAACqD,yBAAyB,EAAE;UACrDI,cAAc,EAAEsD,KAAK;UACrB7G,UAAU,EAAEA,UAAU;UACtBwD,eAAe,EAAEA,eAAe;UAChCF,GAAG,EAAEgD,cAAc;UACnBzC,YAAY,EAAEA,YAAY;UAC1BC,iBAAiB,EAAEA,iBAAiB;UACpCF,YAAY,EAAE3D,OAAO,CAACG,KAAK;UAC3BuD,cAAc,EAAE8B,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACqB,YAAY;UACnEpD,QAAQ,EAAEA;QACZ,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}