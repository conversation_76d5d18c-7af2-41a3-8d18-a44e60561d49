{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { monthsInYear } from \"../constants/index.js\";\n/**\n * @name monthsToYears\n * @category Conversion Helpers\n * @summary Convert number of months to years.\n *\n * @description\n * Convert a number of months to a full number of years.\n *\n * @param {number} months - number of months to be converted\n *\n * @returns {number} the number of months converted in years\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 36 months to years:\n * const result = monthsToYears(36)\n * //=> 3\n *\n * // It uses floor rounding:\n * const result = monthsToYears(40)\n * //=> 3\n */\nexport default function monthsToYears(months) {\n  requiredArgs(1, arguments);\n  var years = months / monthsInYear;\n  return Math.floor(years);\n}", "map": {"version": 3, "names": ["requiredArgs", "monthsInYear", "monthsT<PERSON><PERSON><PERSON>s", "months", "arguments", "years", "Math", "floor"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/monthsToYears/index.js"], "sourcesContent": ["import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { monthsInYear } from \"../constants/index.js\";\n/**\n * @name monthsToYears\n * @category Conversion Helpers\n * @summary Convert number of months to years.\n *\n * @description\n * Convert a number of months to a full number of years.\n *\n * @param {number} months - number of months to be converted\n *\n * @returns {number} the number of months converted in years\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 36 months to years:\n * const result = monthsToYears(36)\n * //=> 3\n *\n * // It uses floor rounding:\n * const result = monthsToYears(40)\n * //=> 3\n */\nexport default function monthsToYears(months) {\n  requiredArgs(1, arguments);\n  var years = months / monthsInYear;\n  return Math.floor(years);\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,SAASC,YAAY,QAAQ,uBAAuB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,aAAaA,CAACC,MAAM,EAAE;EAC5CH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,KAAK,GAAGF,MAAM,GAAGF,YAAY;EACjC,OAAOK,IAAI,CAACC,KAAK,CAACF,KAAK,CAAC;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}