[{"C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\contexts\\AuthContext.js": "3", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Auth\\ProtectedRoute.js": "4", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Common\\LoadingSpinner.js": "5", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Auth\\AdminRoute.js": "6", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Layout\\Layout.js": "7", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Students\\Students.js": "8", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Students\\AddStudent.js": "9", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Students\\EditStudent.js": "10", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Subscriptions\\Subscriptions.js": "11", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Dashboard\\Dashboard.js": "12", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Auth\\Login.js": "13", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Subscriptions\\SubscriptionDetails.js": "14", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Attendance\\EditAttendance.js": "15", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Attendance\\Attendance.js": "16", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Attendance\\AttendanceDetails.js": "17", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Subscriptions\\AddSubscription.js": "18", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Subscriptions\\EditSubscription.js": "19", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Students\\StudentDetails.js": "20", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Reports\\Reports.js": "21", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Attendance\\AddAttendance.js": "22", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Settings\\Settings.js": "23", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Profile\\Profile.js": "24", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Layout\\Header.js": "25", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Layout\\Sidebar.js": "26", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Auth\\ChangePassword.js": "27", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Auth\\LogoutDialog.js": "28", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\services\\studentService.js": "29", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\services\\api.js": "30", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Auth\\LoginSimple.js": "31"}, {"size": 2749, "mtime": 1751039348760, "results": "32", "hashOfConfig": "33"}, {"size": 4830, "mtime": 1751047071874, "results": "34", "hashOfConfig": "33"}, {"size": 8472, "mtime": 1751044789721, "results": "35", "hashOfConfig": "33"}, {"size": 637, "mtime": 1751040339945, "results": "36", "hashOfConfig": "33"}, {"size": 578, "mtime": 1751039444680, "results": "37", "hashOfConfig": "33"}, {"size": 1411, "mtime": 1751039471900, "results": "38", "hashOfConfig": "33"}, {"size": 3296, "mtime": 1751039494352, "results": "39", "hashOfConfig": "33"}, {"size": 12387, "mtime": 1751043223123, "results": "40", "hashOfConfig": "33"}, {"size": 20749, "mtime": 1751043403241, "results": "41", "hashOfConfig": "33"}, {"size": 22788, "mtime": 1751043637771, "results": "42", "hashOfConfig": "33"}, {"size": 479, "mtime": 1751039716566, "results": "43", "hashOfConfig": "33"}, {"size": 5913, "mtime": 1751039657938, "results": "44", "hashOfConfig": "33"}, {"size": 9350, "mtime": 1751046972127, "results": "45", "hashOfConfig": "33"}, {"size": 487, "mtime": 1751039728071, "results": "46", "hashOfConfig": "33"}, {"size": 479, "mtime": 1751039783554, "results": "47", "hashOfConfig": "33"}, {"size": 457, "mtime": 1751039755068, "results": "48", "hashOfConfig": "33"}, {"size": 475, "mtime": 1751039763687, "results": "49", "hashOfConfig": "33"}, {"size": 485, "mtime": 1751039737425, "results": "50", "hashOfConfig": "33"}, {"size": 477, "mtime": 1751039746784, "results": "51", "hashOfConfig": "33"}, {"size": 20386, "mtime": 1751043508094, "results": "52", "hashOfConfig": "33"}, {"size": 437, "mtime": 1751039793610, "results": "53", "hashOfConfig": "33"}, {"size": 473, "mtime": 1751039773346, "results": "54", "hashOfConfig": "33"}, {"size": 443, "mtime": 1751039802405, "results": "55", "hashOfConfig": "33"}, {"size": 6019, "mtime": 1751041215403, "results": "56", "hashOfConfig": "33"}, {"size": 4801, "mtime": 1751041321352, "results": "57", "hashOfConfig": "33"}, {"size": 5090, "mtime": 1751039549121, "results": "58", "hashOfConfig": "33"}, {"size": 8121, "mtime": 1751040382686, "results": "59", "hashOfConfig": "33"}, {"size": 2892, "mtime": 1751041238913, "results": "60", "hashOfConfig": "33"}, {"size": 2656, "mtime": 1751043298816, "results": "61", "hashOfConfig": "33"}, {"size": 1097, "mtime": 1751043808848, "results": "62", "hashOfConfig": "33"}, {"size": 6137, "mtime": 1751047031412, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1wils2a", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 66, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\contexts\\AuthContext.js", [], ["157"], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Auth\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Common\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Auth\\AdminRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Layout\\Layout.js", ["158", "159", "160", "161", "162"], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Students\\Students.js", ["163"], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Students\\AddStudent.js", ["164", "165"], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Students\\EditStudent.js", ["166"], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Subscriptions\\Subscriptions.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Dashboard\\Dashboard.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Auth\\Login.js", ["167", "168", "169", "170", "171", "172", "173", "174", "175", "176", "177", "178", "179", "180", "181", "182", "183", "184", "185", "186", "187", "188", "189", "190", "191", "192", "193", "194", "195", "196", "197", "198", "199", "200", "201", "202", "203", "204", "205", "206", "207", "208", "209", "210", "211", "212", "213", "214", "215", "216", "217", "218", "219", "220", "221", "222", "223", "224", "225", "226", "227", "228", "229", "230", "231", "232", "233", "234", "235", "236", "237"], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Subscriptions\\SubscriptionDetails.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Attendance\\EditAttendance.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Attendance\\Attendance.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Attendance\\AttendanceDetails.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Subscriptions\\AddSubscription.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Subscriptions\\EditSubscription.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Students\\StudentDetails.js", ["238", "239"], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Reports\\Reports.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Attendance\\AddAttendance.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Settings\\Settings.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Profile\\Profile.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Layout\\Header.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Layout\\Sidebar.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Auth\\ChangePassword.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Auth\\LogoutDialog.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\services\\studentService.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Auth\\LoginSimple.js", [], [], {"ruleId": "240", "severity": 1, "message": "241", "line": 166, "column": 6, "nodeType": "242", "endLine": 166, "endColumn": 8, "suggestions": "243", "suppressions": "244"}, {"ruleId": "245", "severity": 1, "message": "246", "line": 6, "column": 3, "nodeType": "247", "messageId": "248", "endLine": 6, "endColumn": 10}, {"ruleId": "245", "severity": 1, "message": "249", "line": 7, "column": 3, "nodeType": "247", "messageId": "248", "endLine": 7, "endColumn": 13}, {"ruleId": "245", "severity": 1, "message": "250", "line": 8, "column": 3, "nodeType": "247", "messageId": "248", "endLine": 8, "endColumn": 13}, {"ruleId": "245", "severity": 1, "message": "251", "line": 13, "column": 11, "nodeType": "247", "messageId": "248", "endLine": 13, "endColumn": 19}, {"ruleId": "245", "severity": 1, "message": "252", "line": 14, "column": 18, "nodeType": "247", "messageId": "248", "endLine": 14, "endColumn": 33}, {"ruleId": "240", "severity": 1, "message": "253", "line": 62, "column": 6, "nodeType": "242", "endLine": 62, "endColumn": 50, "suggestions": "254"}, {"ruleId": "245", "severity": 1, "message": "255", "line": 15, "column": 3, "nodeType": "247", "messageId": "248", "endLine": 15, "endColumn": 10}, {"ruleId": "245", "severity": 1, "message": "256", "line": 28, "column": 20, "nodeType": "247", "messageId": "248", "endLine": 28, "endColumn": 32}, {"ruleId": "240", "severity": 1, "message": "257", "line": 102, "column": 6, "nodeType": "242", "endLine": 102, "endColumn": 10, "suggestions": "258"}, {"ruleId": "245", "severity": 1, "message": "259", "line": 12, "column": 10, "nodeType": "247", "messageId": "248", "endLine": 12, "endColumn": 18}, {"ruleId": "245", "severity": 1, "message": "260", "line": 12, "column": 20, "nodeType": "247", "messageId": "248", "endLine": 12, "endColumn": 31}, {"ruleId": "245", "severity": 1, "message": "261", "line": 13, "column": 10, "nodeType": "247", "messageId": "248", "endLine": 13, "endColumn": 18}, {"ruleId": "245", "severity": 1, "message": "262", "line": 13, "column": 20, "nodeType": "247", "messageId": "248", "endLine": 13, "endColumn": 31}, {"ruleId": "263", "severity": 2, "message": "264", "line": 21, "column": 5, "nodeType": "247", "messageId": "265", "endLine": 21, "endColumn": 16}, {"ruleId": "263", "severity": 2, "message": "266", "line": 27, "column": 9, "nodeType": "247", "messageId": "265", "endLine": 27, "endColumn": 19}, {"ruleId": "263", "severity": 2, "message": "267", "line": 28, "column": 7, "nodeType": "247", "messageId": "265", "endLine": 28, "endColumn": 20}, {"ruleId": "263", "severity": 2, "message": "268", "line": 36, "column": 7, "nodeType": "247", "messageId": "265", "endLine": 36, "endColumn": 17}, {"ruleId": "263", "severity": 2, "message": "269", "line": 41, "column": 5, "nodeType": "247", "messageId": "265", "endLine": 41, "endColumn": 20}, {"ruleId": "263", "severity": 2, "message": "270", "line": 41, "column": 22, "nodeType": "247", "messageId": "265", "endLine": 41, "endColumn": 34}, {"ruleId": "263", "severity": 2, "message": "271", "line": 54, "column": 10, "nodeType": "247", "messageId": "265", "endLine": 54, "endColumn": 18}, {"ruleId": "263", "severity": 2, "message": "271", "line": 58, "column": 10, "nodeType": "247", "messageId": "265", "endLine": 58, "endColumn": 18}, {"ruleId": "263", "severity": 2, "message": "267", "line": 62, "column": 5, "nodeType": "247", "messageId": "265", "endLine": 62, "endColumn": 18}, {"ruleId": "263", "severity": 2, "message": "271", "line": 74, "column": 52, "nodeType": "247", "messageId": "265", "endLine": 74, "endColumn": 60}, {"ruleId": "263", "severity": 2, "message": "271", "line": 85, "column": 34, "nodeType": "247", "messageId": "265", "endLine": 85, "endColumn": 42}, {"ruleId": "263", "severity": 2, "message": "271", "line": 85, "column": 53, "nodeType": "247", "messageId": "265", "endLine": 85, "endColumn": 61}, {"ruleId": "263", "severity": 2, "message": "272", "line": 90, "column": 9, "nodeType": "247", "messageId": "265", "endLine": 90, "endColumn": 24}, {"ruleId": "263", "severity": 2, "message": "264", "line": 109, "column": 7, "nodeType": "247", "messageId": "265", "endLine": 109, "endColumn": 18}, {"ruleId": "263", "severity": 2, "message": "264", "line": 111, "column": 7, "nodeType": "247", "messageId": "265", "endLine": 111, "endColumn": 18}, {"ruleId": "263", "severity": 2, "message": "268", "line": 113, "column": 5, "nodeType": "247", "messageId": "265", "endLine": 113, "endColumn": 15}, {"ruleId": "263", "severity": 2, "message": "267", "line": 114, "column": 5, "nodeType": "247", "messageId": "265", "endLine": 114, "endColumn": 18}, {"ruleId": "263", "severity": 2, "message": "273", "line": 118, "column": 3, "nodeType": "247", "messageId": "265", "endLine": 118, "endColumn": 12}, {"ruleId": "263", "severity": 2, "message": "268", "line": 119, "column": 5, "nodeType": "247", "messageId": "265", "endLine": 119, "endColumn": 15}, {"ruleId": "240", "severity": 1, "message": "274", "line": 120, "column": 6, "nodeType": "242", "endLine": 120, "endColumn": 18, "suggestions": "275"}, {"ruleId": "263", "severity": 2, "message": "268", "line": 120, "column": 7, "nodeType": "247", "messageId": "265", "endLine": 120, "endColumn": 17}, {"ruleId": "276", "severity": 2, "message": "277", "line": 123, "column": 6, "nodeType": "278", "messageId": "279", "endLine": 123, "endColumn": 15}, {"ruleId": "276", "severity": 2, "message": "280", "line": 124, "column": 8, "nodeType": "278", "messageId": "279", "endLine": 124, "endColumn": 11}, {"ruleId": "276", "severity": 2, "message": "281", "line": 134, "column": 10, "nodeType": "278", "messageId": "279", "endLine": 134, "endColumn": 15}, {"ruleId": "276", "severity": 2, "message": "280", "line": 144, "column": 12, "nodeType": "278", "messageId": "279", "endLine": 144, "endColumn": 15}, {"ruleId": "276", "severity": 2, "message": "282", "line": 152, "column": 14, "nodeType": "278", "messageId": "279", "endLine": 152, "endColumn": 20}, {"ruleId": "276", "severity": 2, "message": "283", "line": 160, "column": 16, "nodeType": "278", "messageId": "279", "endLine": 160, "endColumn": 33}, {"ruleId": "276", "severity": 2, "message": "284", "line": 162, "column": 14, "nodeType": "278", "messageId": "279", "endLine": 162, "endColumn": 24}, {"ruleId": "276", "severity": 2, "message": "284", "line": 165, "column": 14, "nodeType": "278", "messageId": "279", "endLine": 165, "endColumn": 24}, {"ruleId": "263", "severity": 2, "message": "285", "line": 171, "column": 12, "nodeType": "247", "messageId": "265", "endLine": 171, "endColumn": 24}, {"ruleId": "276", "severity": 2, "message": "286", "line": 172, "column": 14, "nodeType": "278", "messageId": "279", "endLine": 172, "endColumn": 18}, {"ruleId": "263", "severity": 2, "message": "285", "line": 172, "column": 23, "nodeType": "247", "messageId": "265", "endLine": 172, "endColumn": 35}, {"ruleId": "276", "severity": 2, "message": "287", "line": 173, "column": 16, "nodeType": "278", "messageId": "279", "endLine": 173, "endColumn": 21}, {"ruleId": "263", "severity": 2, "message": "285", "line": 180, "column": 22, "nodeType": "247", "messageId": "265", "endLine": 180, "endColumn": 34}, {"ruleId": "276", "severity": 2, "message": "286", "line": 181, "column": 14, "nodeType": "278", "messageId": "279", "endLine": 181, "endColumn": 18}, {"ruleId": "276", "severity": 2, "message": "287", "line": 182, "column": 16, "nodeType": "278", "messageId": "279", "endLine": 182, "endColumn": 21}, {"ruleId": "276", "severity": 2, "message": "280", "line": 189, "column": 12, "nodeType": "278", "messageId": "279", "endLine": 189, "endColumn": 15}, {"ruleId": "276", "severity": 2, "message": "288", "line": 190, "column": 14, "nodeType": "278", "messageId": "279", "endLine": 190, "endColumn": 23}, {"ruleId": "263", "severity": 2, "message": "271", "line": 199, "column": 22, "nodeType": "247", "messageId": "265", "endLine": 199, "endColumn": 30}, {"ruleId": "263", "severity": 2, "message": "266", "line": 202, "column": 24, "nodeType": "247", "messageId": "265", "endLine": 202, "endColumn": 34}, {"ruleId": "263", "severity": 2, "message": "266", "line": 203, "column": 27, "nodeType": "247", "messageId": "265", "endLine": 203, "endColumn": 37}, {"ruleId": "276", "severity": 2, "message": "289", "line": 206, "column": 20, "nodeType": "278", "messageId": "279", "endLine": 206, "endColumn": 34}, {"ruleId": "276", "severity": 2, "message": "290", "line": 207, "column": 22, "nodeType": "278", "messageId": "279", "endLine": 207, "endColumn": 28}, {"ruleId": "276", "severity": 2, "message": "288", "line": 213, "column": 14, "nodeType": "278", "messageId": "279", "endLine": 213, "endColumn": 23}, {"ruleId": "263", "severity": 2, "message": "270", "line": 219, "column": 21, "nodeType": "247", "messageId": "265", "endLine": 219, "endColumn": 33}, {"ruleId": "263", "severity": 2, "message": "271", "line": 222, "column": 22, "nodeType": "247", "messageId": "265", "endLine": 222, "endColumn": 30}, {"ruleId": "263", "severity": 2, "message": "266", "line": 225, "column": 24, "nodeType": "247", "messageId": "265", "endLine": 225, "endColumn": 34}, {"ruleId": "263", "severity": 2, "message": "266", "line": 226, "column": 27, "nodeType": "247", "messageId": "265", "endLine": 226, "endColumn": 37}, {"ruleId": "276", "severity": 2, "message": "289", "line": 229, "column": 20, "nodeType": "278", "messageId": "279", "endLine": 229, "endColumn": 34}, {"ruleId": "276", "severity": 2, "message": "291", "line": 230, "column": 22, "nodeType": "278", "messageId": "279", "endLine": 230, "endColumn": 26}, {"ruleId": "276", "severity": 2, "message": "289", "line": 234, "column": 20, "nodeType": "278", "messageId": "279", "endLine": 234, "endColumn": 34}, {"ruleId": "276", "severity": 2, "message": "292", "line": 235, "column": 22, "nodeType": "278", "messageId": "279", "endLine": 235, "endColumn": 32}, {"ruleId": "263", "severity": 2, "message": "270", "line": 240, "column": 24, "nodeType": "247", "messageId": "265", "endLine": 240, "endColumn": 36}, {"ruleId": "276", "severity": 2, "message": "293", "line": 240, "column": 40, "nodeType": "278", "messageId": "279", "endLine": 240, "endColumn": 53}, {"ruleId": "276", "severity": 2, "message": "294", "line": 240, "column": 60, "nodeType": "278", "messageId": "279", "endLine": 240, "endColumn": 70}, {"ruleId": "276", "severity": 2, "message": "295", "line": 247, "column": 14, "nodeType": "278", "messageId": "279", "endLine": 247, "endColumn": 20}, {"ruleId": "263", "severity": 2, "message": "285", "line": 251, "column": 52, "nodeType": "247", "messageId": "265", "endLine": 251, "endColumn": 64}, {"ruleId": "276", "severity": 2, "message": "296", "line": 266, "column": 18, "nodeType": "278", "messageId": "279", "endLine": 266, "endColumn": 34}, {"ruleId": "263", "severity": 2, "message": "285", "line": 278, "column": 16, "nodeType": "247", "messageId": "265", "endLine": 278, "endColumn": 28}, {"ruleId": "276", "severity": 2, "message": "280", "line": 288, "column": 12, "nodeType": "278", "messageId": "279", "endLine": 288, "endColumn": 15}, {"ruleId": "276", "severity": 2, "message": "284", "line": 289, "column": 14, "nodeType": "278", "messageId": "279", "endLine": 289, "endColumn": 24}, {"ruleId": "276", "severity": 2, "message": "280", "line": 292, "column": 14, "nodeType": "278", "messageId": "279", "endLine": 292, "endColumn": 17}, {"ruleId": "276", "severity": 2, "message": "295", "line": 293, "column": 16, "nodeType": "278", "messageId": "279", "endLine": 293, "endColumn": 22}, {"ruleId": "263", "severity": 2, "message": "285", "line": 297, "column": 43, "nodeType": "247", "messageId": "265", "endLine": 297, "endColumn": 55}, {"ruleId": "276", "severity": 2, "message": "295", "line": 302, "column": 16, "nodeType": "278", "messageId": "279", "endLine": 302, "endColumn": 22}, {"ruleId": "263", "severity": 2, "message": "285", "line": 306, "column": 43, "nodeType": "247", "messageId": "265", "endLine": 306, "endColumn": 55}, {"ruleId": "276", "severity": 2, "message": "284", "line": 312, "column": 14, "nodeType": "278", "messageId": "279", "endLine": 312, "endColumn": 24}, {"ruleId": "245", "severity": 1, "message": "297", "line": 35, "column": 14, "nodeType": "247", "messageId": "248", "endLine": 35, "endColumn": 25}, {"ruleId": "240", "severity": 1, "message": "257", "line": 54, "column": 6, "nodeType": "242", "endLine": 54, "endColumn": 10, "suggestions": "298"}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'state.token' and 'state.user'. Either include them or remove the dependency array.", "ArrayExpression", ["299"], ["300"], "no-unused-vars", "'Toolbar' is defined but never used.", "Identifier", "unusedVar", "'Typography' is defined but never used.", "'IconButton' is defined but never used.", "'MenuIcon' is defined but never used.", "'ChevronLeftIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchStudents'. Either include it or remove the dependency array.", ["301"], "'Divider' is defined but never used.", "'CalendarIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchStudent'. Either include it or remove the dependency array.", ["302"], "'username' is assigned a value but never used.", "'setUsername' is assigned a value but never used.", "'password' is assigned a value but never used.", "'setPassword' is assigned a value but never used.", "no-undef", "'setFormData' is not defined.", "undef", "'formErrors' is not defined.", "'setFormErrors' is not defined.", "'clearError' is not defined.", "'setShowPassword' is not defined.", "'showPassword' is not defined.", "'formData' is not defined.", "'setLoginSuccess' is not defined.", "'useEffect' is not defined.", "React Hook useEffect has an unnecessary dependency: 'clearError'. Either exclude it or remove the dependency array. Outer scope values like 'clearError' aren't valid dependencies because mutating them doesn't re-render the component.", ["303"], "react/jsx-no-undef", "'Container' is not defined.", "JSXIdentifier", "undefined", "'Box' is not defined.", "'Paper' is not defined.", "'Avatar' is not defined.", "'SportsMartialArts' is not defined.", "'Typography' is not defined.", "'loginSuccess' is not defined.", "'Fade' is not defined.", "'Alert' is not defined.", "'TextField' is not defined.", "'InputAdornment' is not defined.", "'Person' is not defined.", "'Lock' is not defined.", "'IconButton' is not defined.", "'VisibilityOff' is not defined.", "'Visibility' is not defined.", "'Button' is not defined.", "'CircularProgress' is not defined.", "'MessageIcon' is defined but never used.", ["304"], {"desc": "305", "fix": "306"}, {"kind": "307", "justification": "308"}, {"desc": "309", "fix": "310"}, {"desc": "311", "fix": "312"}, {"desc": "313", "fix": "314"}, {"desc": "311", "fix": "315"}, "Update the dependencies array to be: [state.token, state.user]", {"range": "316", "text": "317"}, "directive", "", "Update the dependencies array to be: [page, searchTerm, beltFilter, statusFilter, fetchStudents]", {"range": "318", "text": "319"}, "Update the dependencies array to be: [fetchStudent, id]", {"range": "320", "text": "321"}, "Update the dependencies array to be: []", {"range": "322", "text": "323"}, {"range": "324", "text": "321"}, [4266, 4268], "[state.token, state.user]", [1695, 1739], "[page, searchTerm, beltFilter, statusFilter, fetchStudents]", [2398, 2402], "[fetchStudent, id]", [2953, 2965], "[]", [1357, 1361]]