[{"C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\contexts\\AuthContext.js": "3", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Auth\\ProtectedRoute.js": "4", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Common\\LoadingSpinner.js": "5", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Auth\\AdminRoute.js": "6", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Layout\\Layout.js": "7", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Students\\Students.js": "8", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Students\\AddStudent.js": "9", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Students\\EditStudent.js": "10", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Subscriptions\\Subscriptions.js": "11", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Dashboard\\Dashboard.js": "12", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Auth\\Login.js": "13", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Subscriptions\\SubscriptionDetails.js": "14", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Attendance\\EditAttendance.js": "15", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Attendance\\Attendance.js": "16", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Attendance\\AttendanceDetails.js": "17", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Subscriptions\\AddSubscription.js": "18", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Subscriptions\\EditSubscription.js": "19", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Students\\StudentDetails.js": "20", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Reports\\Reports.js": "21", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Attendance\\AddAttendance.js": "22", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Settings\\Settings.js": "23", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Profile\\Profile.js": "24", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Layout\\Header.js": "25", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Layout\\Sidebar.js": "26", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Auth\\ChangePassword.js": "27", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Auth\\LogoutDialog.js": "28", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\services\\studentService.js": "29", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\services\\api.js": "30"}, {"size": 2749, "mtime": 1751039348760, "results": "31", "hashOfConfig": "32"}, {"size": 4815, "mtime": 1751039390342, "results": "33", "hashOfConfig": "32"}, {"size": 8472, "mtime": 1751044789721, "results": "34", "hashOfConfig": "32"}, {"size": 637, "mtime": 1751040339945, "results": "35", "hashOfConfig": "32"}, {"size": 578, "mtime": 1751039444680, "results": "36", "hashOfConfig": "32"}, {"size": 1411, "mtime": 1751039471900, "results": "37", "hashOfConfig": "32"}, {"size": 3296, "mtime": 1751039494352, "results": "38", "hashOfConfig": "32"}, {"size": 12387, "mtime": 1751043223123, "results": "39", "hashOfConfig": "32"}, {"size": 20749, "mtime": 1751043403241, "results": "40", "hashOfConfig": "32"}, {"size": 22788, "mtime": 1751043637771, "results": "41", "hashOfConfig": "32"}, {"size": 479, "mtime": 1751039716566, "results": "42", "hashOfConfig": "32"}, {"size": 5913, "mtime": 1751039657938, "results": "43", "hashOfConfig": "32"}, {"size": 9621, "mtime": 1751045135050, "results": "44", "hashOfConfig": "32"}, {"size": 487, "mtime": 1751039728071, "results": "45", "hashOfConfig": "32"}, {"size": 479, "mtime": 1751039783554, "results": "46", "hashOfConfig": "32"}, {"size": 457, "mtime": 1751039755068, "results": "47", "hashOfConfig": "32"}, {"size": 475, "mtime": 1751039763687, "results": "48", "hashOfConfig": "32"}, {"size": 485, "mtime": 1751039737425, "results": "49", "hashOfConfig": "32"}, {"size": 477, "mtime": 1751039746784, "results": "50", "hashOfConfig": "32"}, {"size": 20386, "mtime": 1751043508094, "results": "51", "hashOfConfig": "32"}, {"size": 437, "mtime": 1751039793610, "results": "52", "hashOfConfig": "32"}, {"size": 473, "mtime": 1751039773346, "results": "53", "hashOfConfig": "32"}, {"size": 443, "mtime": 1751039802405, "results": "54", "hashOfConfig": "32"}, {"size": 6019, "mtime": 1751041215403, "results": "55", "hashOfConfig": "32"}, {"size": 4801, "mtime": 1751041321352, "results": "56", "hashOfConfig": "32"}, {"size": 5090, "mtime": 1751039549121, "results": "57", "hashOfConfig": "32"}, {"size": 8121, "mtime": 1751040382686, "results": "58", "hashOfConfig": "32"}, {"size": 2892, "mtime": 1751041238913, "results": "59", "hashOfConfig": "32"}, {"size": 2656, "mtime": 1751043298816, "results": "60", "hashOfConfig": "32"}, {"size": 1097, "mtime": 1751043808848, "results": "61", "hashOfConfig": "32"}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1wils2a", {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\contexts\\AuthContext.js", [], ["152"], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Auth\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Common\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Auth\\AdminRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Layout\\Layout.js", ["153", "154", "155", "156", "157"], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Students\\Students.js", ["158"], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Students\\AddStudent.js", ["159", "160"], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Students\\EditStudent.js", ["161"], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Subscriptions\\Subscriptions.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Dashboard\\Dashboard.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Auth\\Login.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Subscriptions\\SubscriptionDetails.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Attendance\\EditAttendance.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Attendance\\Attendance.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Attendance\\AttendanceDetails.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Subscriptions\\AddSubscription.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Subscriptions\\EditSubscription.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Students\\StudentDetails.js", ["162", "163"], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Reports\\Reports.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Attendance\\AddAttendance.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Settings\\Settings.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Profile\\Profile.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Layout\\Header.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Layout\\Sidebar.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Auth\\ChangePassword.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Auth\\LogoutDialog.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\services\\studentService.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\services\\api.js", [], [], {"ruleId": "164", "severity": 1, "message": "165", "line": 166, "column": 6, "nodeType": "166", "endLine": 166, "endColumn": 8, "suggestions": "167", "suppressions": "168"}, {"ruleId": "169", "severity": 1, "message": "170", "line": 6, "column": 3, "nodeType": "171", "messageId": "172", "endLine": 6, "endColumn": 10}, {"ruleId": "169", "severity": 1, "message": "173", "line": 7, "column": 3, "nodeType": "171", "messageId": "172", "endLine": 7, "endColumn": 13}, {"ruleId": "169", "severity": 1, "message": "174", "line": 8, "column": 3, "nodeType": "171", "messageId": "172", "endLine": 8, "endColumn": 13}, {"ruleId": "169", "severity": 1, "message": "175", "line": 13, "column": 11, "nodeType": "171", "messageId": "172", "endLine": 13, "endColumn": 19}, {"ruleId": "169", "severity": 1, "message": "176", "line": 14, "column": 18, "nodeType": "171", "messageId": "172", "endLine": 14, "endColumn": 33}, {"ruleId": "164", "severity": 1, "message": "177", "line": 62, "column": 6, "nodeType": "166", "endLine": 62, "endColumn": 50, "suggestions": "178"}, {"ruleId": "169", "severity": 1, "message": "179", "line": 15, "column": 3, "nodeType": "171", "messageId": "172", "endLine": 15, "endColumn": 10}, {"ruleId": "169", "severity": 1, "message": "180", "line": 28, "column": 20, "nodeType": "171", "messageId": "172", "endLine": 28, "endColumn": 32}, {"ruleId": "164", "severity": 1, "message": "181", "line": 102, "column": 6, "nodeType": "166", "endLine": 102, "endColumn": 10, "suggestions": "182"}, {"ruleId": "169", "severity": 1, "message": "183", "line": 35, "column": 14, "nodeType": "171", "messageId": "172", "endLine": 35, "endColumn": 25}, {"ruleId": "164", "severity": 1, "message": "181", "line": 54, "column": 6, "nodeType": "166", "endLine": 54, "endColumn": 10, "suggestions": "184"}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'state.token' and 'state.user'. Either include them or remove the dependency array.", "ArrayExpression", ["185"], ["186"], "no-unused-vars", "'Toolbar' is defined but never used.", "Identifier", "unusedVar", "'Typography' is defined but never used.", "'IconButton' is defined but never used.", "'MenuIcon' is defined but never used.", "'ChevronLeftIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchStudents'. Either include it or remove the dependency array.", ["187"], "'Divider' is defined but never used.", "'CalendarIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchStudent'. Either include it or remove the dependency array.", ["188"], "'MessageIcon' is defined but never used.", ["189"], {"desc": "190", "fix": "191"}, {"kind": "192", "justification": "193"}, {"desc": "194", "fix": "195"}, {"desc": "196", "fix": "197"}, {"desc": "196", "fix": "198"}, "Update the dependencies array to be: [state.token, state.user]", {"range": "199", "text": "200"}, "directive", "", "Update the dependencies array to be: [page, searchTerm, beltFilter, statusFilter, fetchStudents]", {"range": "201", "text": "202"}, "Update the dependencies array to be: [fetchStudent, id]", {"range": "203", "text": "204"}, {"range": "205", "text": "204"}, [4266, 4268], "[state.token, state.user]", [1695, 1739], "[page, searchTerm, beltFilter, statusFilter, fetchStudents]", [2398, 2402], "[fetchStudent, id]", [1357, 1361]]