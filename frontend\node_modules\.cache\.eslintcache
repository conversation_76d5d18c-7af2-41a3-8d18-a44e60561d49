[{"C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\contexts\\AuthContext.js": "3", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Auth\\ProtectedRoute.js": "4", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Common\\LoadingSpinner.js": "5", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Auth\\AdminRoute.js": "6", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Layout\\Layout.js": "7", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Students\\Students.js": "8", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Students\\AddStudent.js": "9", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Students\\EditStudent.js": "10", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Subscriptions\\Subscriptions.js": "11", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Dashboard\\Dashboard.js": "12", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Auth\\Login.js": "13", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Subscriptions\\SubscriptionDetails.js": "14", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Attendance\\EditAttendance.js": "15", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Attendance\\Attendance.js": "16", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Attendance\\AttendanceDetails.js": "17", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Subscriptions\\AddSubscription.js": "18", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Subscriptions\\EditSubscription.js": "19", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Students\\StudentDetails.js": "20", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Reports\\Reports.js": "21", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Attendance\\AddAttendance.js": "22", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Settings\\Settings.js": "23", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Profile\\Profile.js": "24", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Layout\\Header.js": "25", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Layout\\Sidebar.js": "26", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Auth\\ChangePassword.js": "27", "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Auth\\LogoutDialog.js": "28"}, {"size": 2749, "mtime": 1751039348760, "results": "29", "hashOfConfig": "30"}, {"size": 4815, "mtime": 1751039390342, "results": "31", "hashOfConfig": "30"}, {"size": 7290, "mtime": 1751040162100, "results": "32", "hashOfConfig": "30"}, {"size": 637, "mtime": 1751040339945, "results": "33", "hashOfConfig": "30"}, {"size": 578, "mtime": 1751039444680, "results": "34", "hashOfConfig": "30"}, {"size": 1411, "mtime": 1751039471900, "results": "35", "hashOfConfig": "30"}, {"size": 3296, "mtime": 1751039494352, "results": "36", "hashOfConfig": "30"}, {"size": 453, "mtime": 1751039669680, "results": "37", "hashOfConfig": "30"}, {"size": 467, "mtime": 1751039691878, "results": "38", "hashOfConfig": "30"}, {"size": 485, "mtime": 1751039706720, "results": "39", "hashOfConfig": "30"}, {"size": 479, "mtime": 1751039716566, "results": "40", "hashOfConfig": "30"}, {"size": 5913, "mtime": 1751039657938, "results": "41", "hashOfConfig": "30"}, {"size": 8984, "mtime": 1751040060092, "results": "42", "hashOfConfig": "30"}, {"size": 487, "mtime": 1751039728071, "results": "43", "hashOfConfig": "30"}, {"size": 479, "mtime": 1751039783554, "results": "44", "hashOfConfig": "30"}, {"size": 457, "mtime": 1751039755068, "results": "45", "hashOfConfig": "30"}, {"size": 475, "mtime": 1751039763687, "results": "46", "hashOfConfig": "30"}, {"size": 485, "mtime": 1751039737425, "results": "47", "hashOfConfig": "30"}, {"size": 477, "mtime": 1751039746784, "results": "48", "hashOfConfig": "30"}, {"size": 469, "mtime": 1751039678591, "results": "49", "hashOfConfig": "30"}, {"size": 437, "mtime": 1751039793610, "results": "50", "hashOfConfig": "30"}, {"size": 473, "mtime": 1751039773346, "results": "51", "hashOfConfig": "30"}, {"size": 443, "mtime": 1751039802405, "results": "52", "hashOfConfig": "30"}, {"size": 6019, "mtime": 1751041215403, "results": "53", "hashOfConfig": "30"}, {"size": 4801, "mtime": 1751041321352, "results": "54", "hashOfConfig": "30"}, {"size": 5090, "mtime": 1751039549121, "results": "55", "hashOfConfig": "30"}, {"size": 8121, "mtime": 1751040382686, "results": "56", "hashOfConfig": "30"}, {"size": 2892, "mtime": 1751041238913, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1wils2a", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Auth\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Common\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Auth\\AdminRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Layout\\Layout.js", ["142", "143", "144", "145", "146"], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Students\\Students.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Students\\AddStudent.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Students\\EditStudent.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Subscriptions\\Subscriptions.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Dashboard\\Dashboard.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Auth\\Login.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Subscriptions\\SubscriptionDetails.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Attendance\\EditAttendance.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Attendance\\Attendance.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Attendance\\AttendanceDetails.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Subscriptions\\AddSubscription.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Subscriptions\\EditSubscription.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Students\\StudentDetails.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Reports\\Reports.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Attendance\\AddAttendance.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Settings\\Settings.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\pages\\Profile\\Profile.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Layout\\Header.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Layout\\Sidebar.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Auth\\ChangePassword.js", [], [], "C:\\Users\\<USER>\\OneDrive - MOE Student S4M\\Desktop\\CRM Project\\frontend\\src\\components\\Auth\\LogoutDialog.js", [], [], {"ruleId": "147", "severity": 1, "message": "148", "line": 6, "column": 3, "nodeType": "149", "messageId": "150", "endLine": 6, "endColumn": 10}, {"ruleId": "147", "severity": 1, "message": "151", "line": 7, "column": 3, "nodeType": "149", "messageId": "150", "endLine": 7, "endColumn": 13}, {"ruleId": "147", "severity": 1, "message": "152", "line": 8, "column": 3, "nodeType": "149", "messageId": "150", "endLine": 8, "endColumn": 13}, {"ruleId": "147", "severity": 1, "message": "153", "line": 13, "column": 11, "nodeType": "149", "messageId": "150", "endLine": 13, "endColumn": 19}, {"ruleId": "147", "severity": 1, "message": "154", "line": 14, "column": 18, "nodeType": "149", "messageId": "150", "endLine": 14, "endColumn": 33}, "no-unused-vars", "'Toolbar' is defined but never used.", "Identifier", "unusedVar", "'Typography' is defined but never used.", "'IconButton' is defined but never used.", "'MenuIcon' is defined but never used.", "'ChevronLeftIcon' is defined but never used."]