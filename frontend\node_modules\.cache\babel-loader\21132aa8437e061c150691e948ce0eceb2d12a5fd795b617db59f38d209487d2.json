{"ast": null, "code": "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactIs = require('react-is');\nvar assign = require('object-assign');\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\nvar has = require('./lib/has');\nvar checkPropTypes = require('./checkPropTypes');\nvar printWarning = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  printWarning = function (text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  };\n}\nfunction emptyFunctionThatReturnsNull() {\n  return null;\n}\nmodule.exports = function (isValidElement, throwOnDirectAccess) {\n  /* global Symbol */\n  var ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\n  var FAUX_ITERATOR_SYMBOL = '@@iterator'; // Before Symbol spec.\n\n  /**\n   * Returns the iterator method function contained on the iterable object.\n   *\n   * Be sure to invoke the function with the iterable as context:\n   *\n   *     var iteratorFn = getIteratorFn(myIterable);\n   *     if (iteratorFn) {\n   *       var iterator = iteratorFn.call(myIterable);\n   *       ...\n   *     }\n   *\n   * @param {?object} maybeIterable\n   * @return {?function}\n   */\n  function getIteratorFn(maybeIterable) {\n    var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);\n    if (typeof iteratorFn === 'function') {\n      return iteratorFn;\n    }\n  }\n\n  /**\n   * Collection of methods that allow declaration and validation of props that are\n   * supplied to React components. Example usage:\n   *\n   *   var Props = require('ReactPropTypes');\n   *   var MyArticle = React.createClass({\n   *     propTypes: {\n   *       // An optional string prop named \"description\".\n   *       description: Props.string,\n   *\n   *       // A required enum prop named \"category\".\n   *       category: Props.oneOf(['News','Photos']).isRequired,\n   *\n   *       // A prop named \"dialog\" that requires an instance of Dialog.\n   *       dialog: Props.instanceOf(Dialog).isRequired\n   *     },\n   *     render: function() { ... }\n   *   });\n   *\n   * A more formal specification of how these methods are used:\n   *\n   *   type := array|bool|func|object|number|string|oneOf([...])|instanceOf(...)\n   *   decl := ReactPropTypes.{type}(.isRequired)?\n   *\n   * Each and every declaration produces a function with the same signature. This\n   * allows the creation of custom validation functions. For example:\n   *\n   *  var MyLink = React.createClass({\n   *    propTypes: {\n   *      // An optional string or URI prop named \"href\".\n   *      href: function(props, propName, componentName) {\n   *        var propValue = props[propName];\n   *        if (propValue != null && typeof propValue !== 'string' &&\n   *            !(propValue instanceof URI)) {\n   *          return new Error(\n   *            'Expected a string or an URI for ' + propName + ' in ' +\n   *            componentName\n   *          );\n   *        }\n   *      }\n   *    },\n   *    render: function() {...}\n   *  });\n   *\n   * @internal\n   */\n\n  var ANONYMOUS = '<<anonymous>>';\n\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithThrowingShims.js`.\n  var ReactPropTypes = {\n    array: createPrimitiveTypeChecker('array'),\n    bigint: createPrimitiveTypeChecker('bigint'),\n    bool: createPrimitiveTypeChecker('boolean'),\n    func: createPrimitiveTypeChecker('function'),\n    number: createPrimitiveTypeChecker('number'),\n    object: createPrimitiveTypeChecker('object'),\n    string: createPrimitiveTypeChecker('string'),\n    symbol: createPrimitiveTypeChecker('symbol'),\n    any: createAnyTypeChecker(),\n    arrayOf: createArrayOfTypeChecker,\n    element: createElementTypeChecker(),\n    elementType: createElementTypeTypeChecker(),\n    instanceOf: createInstanceTypeChecker,\n    node: createNodeChecker(),\n    objectOf: createObjectOfTypeChecker,\n    oneOf: createEnumTypeChecker,\n    oneOfType: createUnionTypeChecker,\n    shape: createShapeTypeChecker,\n    exact: createStrictShapeTypeChecker\n  };\n\n  /**\n   * inlined Object.is polyfill to avoid requiring consumers ship their own\n   * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n   */\n  /*eslint-disable no-self-compare*/\n  function is(x, y) {\n    // SameValue algorithm\n    if (x === y) {\n      // Steps 1-5, 7-10\n      // Steps 6.b-6.e: +0 != -0\n      return x !== 0 || 1 / x === 1 / y;\n    } else {\n      // Step 6.a: NaN == NaN\n      return x !== x && y !== y;\n    }\n  }\n  /*eslint-enable no-self-compare*/\n\n  /**\n   * We use an Error-like object for backward compatibility as people may call\n   * PropTypes directly and inspect their output. However, we don't use real\n   * Errors anymore. We don't inspect their stack anyway, and creating them\n   * is prohibitively expensive if they are created too often, such as what\n   * happens in oneOfType() for any type before the one that matched.\n   */\n  function PropTypeError(message, data) {\n    this.message = message;\n    this.data = data && typeof data === 'object' ? data : {};\n    this.stack = '';\n  }\n  // Make `instanceof Error` still work for returned errors.\n  PropTypeError.prototype = Error.prototype;\n  function createChainableTypeChecker(validate) {\n    if (process.env.NODE_ENV !== 'production') {\n      var manualPropTypeCallCache = {};\n      var manualPropTypeWarningCount = 0;\n    }\n    function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {\n      componentName = componentName || ANONYMOUS;\n      propFullName = propFullName || propName;\n      if (secret !== ReactPropTypesSecret) {\n        if (throwOnDirectAccess) {\n          // New behavior only for users of `prop-types` package\n          var err = new Error('Calling PropTypes validators directly is not supported by the `prop-types` package. ' + 'Use `PropTypes.checkPropTypes()` to call them. ' + 'Read more at http://fb.me/use-check-prop-types');\n          err.name = 'Invariant Violation';\n          throw err;\n        } else if (process.env.NODE_ENV !== 'production' && typeof console !== 'undefined') {\n          // Old behavior for people using React.PropTypes\n          var cacheKey = componentName + ':' + propName;\n          if (!manualPropTypeCallCache[cacheKey] &&\n          // Avoid spamming the console because they are often not actionable except for lib authors\n          manualPropTypeWarningCount < 3) {\n            printWarning('You are manually calling a React.PropTypes validation ' + 'function for the `' + propFullName + '` prop on `' + componentName + '`. This is deprecated ' + 'and will throw in the standalone `prop-types` package. ' + 'You may be seeing this warning due to a third-party PropTypes ' + 'library. See https://fb.me/react-warning-dont-call-proptypes ' + 'for details.');\n            manualPropTypeCallCache[cacheKey] = true;\n            manualPropTypeWarningCount++;\n          }\n        }\n      }\n      if (props[propName] == null) {\n        if (isRequired) {\n          if (props[propName] === null) {\n            return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required ' + ('in `' + componentName + '`, but its value is `null`.'));\n          }\n          return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required in ' + ('`' + componentName + '`, but its value is `undefined`.'));\n        }\n        return null;\n      } else {\n        return validate(props, propName, componentName, location, propFullName);\n      }\n    }\n    var chainedCheckType = checkType.bind(null, false);\n    chainedCheckType.isRequired = checkType.bind(null, true);\n    return chainedCheckType;\n  }\n  function createPrimitiveTypeChecker(expectedType) {\n    function validate(props, propName, componentName, location, propFullName, secret) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== expectedType) {\n        // `propValue` being instance of, say, date/regexp, pass the 'object'\n        // check, but we can offer a more precise error message here rather than\n        // 'of type `object`'.\n        var preciseType = getPreciseType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + preciseType + '` supplied to `' + componentName + '`, expected ') + ('`' + expectedType + '`.'), {\n          expectedType: expectedType\n        });\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n  function createAnyTypeChecker() {\n    return createChainableTypeChecker(emptyFunctionThatReturnsNull);\n  }\n  function createArrayOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside arrayOf.');\n      }\n      var propValue = props[propName];\n      if (!Array.isArray(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an array.'));\n      }\n      for (var i = 0; i < propValue.length; i++) {\n        var error = typeChecker(propValue, i, componentName, location, propFullName + '[' + i + ']', ReactPropTypesSecret);\n        if (error instanceof Error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n  function createElementTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!isValidElement(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n  function createElementTypeTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!ReactIs.isValidElementType(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement type.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n  function createInstanceTypeChecker(expectedClass) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!(props[propName] instanceof expectedClass)) {\n        var expectedClassName = expectedClass.name || ANONYMOUS;\n        var actualClassName = getClassName(props[propName]);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + actualClassName + '` supplied to `' + componentName + '`, expected ') + ('instance of `' + expectedClassName + '`.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n  function createEnumTypeChecker(expectedValues) {\n    if (!Array.isArray(expectedValues)) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (arguments.length > 1) {\n          printWarning('Invalid arguments supplied to oneOf, expected an array, got ' + arguments.length + ' arguments. ' + 'A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).');\n        } else {\n          printWarning('Invalid argument supplied to oneOf, expected an array.');\n        }\n      }\n      return emptyFunctionThatReturnsNull;\n    }\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      for (var i = 0; i < expectedValues.length; i++) {\n        if (is(propValue, expectedValues[i])) {\n          return null;\n        }\n      }\n      var valuesString = JSON.stringify(expectedValues, function replacer(key, value) {\n        var type = getPreciseType(value);\n        if (type === 'symbol') {\n          return String(value);\n        }\n        return value;\n      });\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of value `' + String(propValue) + '` ' + ('supplied to `' + componentName + '`, expected one of ' + valuesString + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n  function createObjectOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside objectOf.');\n      }\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an object.'));\n      }\n      for (var key in propValue) {\n        if (has(propValue, key)) {\n          var error = typeChecker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n          if (error instanceof Error) {\n            return error;\n          }\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n  function createUnionTypeChecker(arrayOfTypeCheckers) {\n    if (!Array.isArray(arrayOfTypeCheckers)) {\n      process.env.NODE_ENV !== 'production' ? printWarning('Invalid argument supplied to oneOfType, expected an instance of array.') : void 0;\n      return emptyFunctionThatReturnsNull;\n    }\n    for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n      var checker = arrayOfTypeCheckers[i];\n      if (typeof checker !== 'function') {\n        printWarning('Invalid argument supplied to oneOfType. Expected an array of check functions, but ' + 'received ' + getPostfixForTypeWarning(checker) + ' at index ' + i + '.');\n        return emptyFunctionThatReturnsNull;\n      }\n    }\n    function validate(props, propName, componentName, location, propFullName) {\n      var expectedTypes = [];\n      for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n        var checker = arrayOfTypeCheckers[i];\n        var checkerResult = checker(props, propName, componentName, location, propFullName, ReactPropTypesSecret);\n        if (checkerResult == null) {\n          return null;\n        }\n        if (checkerResult.data && has(checkerResult.data, 'expectedType')) {\n          expectedTypes.push(checkerResult.data.expectedType);\n        }\n      }\n      var expectedTypesMessage = expectedTypes.length > 0 ? ', expected one of type [' + expectedTypes.join(', ') + ']' : '';\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`' + expectedTypesMessage + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n  function createNodeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!isNode(props[propName])) {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`, expected a ReactNode.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n  function invalidValidatorError(componentName, location, propFullName, key, type) {\n    return new PropTypeError((componentName || 'React class') + ': ' + location + ' type `' + propFullName + '.' + key + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + type + '`.');\n  }\n  function createShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      for (var key in shapeTypes) {\n        var checker = shapeTypes[key];\n        if (typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n  function createStrictShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      // We need to check all keys in case some are required but missing from props.\n      var allKeys = assign({}, props[propName], shapeTypes);\n      for (var key in allKeys) {\n        var checker = shapeTypes[key];\n        if (has(shapeTypes, key) && typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        if (!checker) {\n          return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` key `' + key + '` supplied to `' + componentName + '`.' + '\\nBad object: ' + JSON.stringify(props[propName], null, '  ') + '\\nValid keys: ' + JSON.stringify(Object.keys(shapeTypes), null, '  '));\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n  function isNode(propValue) {\n    switch (typeof propValue) {\n      case 'number':\n      case 'string':\n      case 'undefined':\n        return true;\n      case 'boolean':\n        return !propValue;\n      case 'object':\n        if (Array.isArray(propValue)) {\n          return propValue.every(isNode);\n        }\n        if (propValue === null || isValidElement(propValue)) {\n          return true;\n        }\n        var iteratorFn = getIteratorFn(propValue);\n        if (iteratorFn) {\n          var iterator = iteratorFn.call(propValue);\n          var step;\n          if (iteratorFn !== propValue.entries) {\n            while (!(step = iterator.next()).done) {\n              if (!isNode(step.value)) {\n                return false;\n              }\n            }\n          } else {\n            // Iterator will provide entry [k,v] tuples rather than values.\n            while (!(step = iterator.next()).done) {\n              var entry = step.value;\n              if (entry) {\n                if (!isNode(entry[1])) {\n                  return false;\n                }\n              }\n            }\n          }\n        } else {\n          return false;\n        }\n        return true;\n      default:\n        return false;\n    }\n  }\n  function isSymbol(propType, propValue) {\n    // Native Symbol.\n    if (propType === 'symbol') {\n      return true;\n    }\n\n    // falsy value can't be a Symbol\n    if (!propValue) {\n      return false;\n    }\n\n    // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n    if (propValue['@@toStringTag'] === 'Symbol') {\n      return true;\n    }\n\n    // Fallback for non-spec compliant Symbols which are polyfilled.\n    if (typeof Symbol === 'function' && propValue instanceof Symbol) {\n      return true;\n    }\n    return false;\n  }\n\n  // Equivalent of `typeof` but with special handling for array and regexp.\n  function getPropType(propValue) {\n    var propType = typeof propValue;\n    if (Array.isArray(propValue)) {\n      return 'array';\n    }\n    if (propValue instanceof RegExp) {\n      // Old webkits (at least until Android 4.0) return 'function' rather than\n      // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n      // passes PropTypes.object.\n      return 'object';\n    }\n    if (isSymbol(propType, propValue)) {\n      return 'symbol';\n    }\n    return propType;\n  }\n\n  // This handles more types than `getPropType`. Only used for error messages.\n  // See `createPrimitiveTypeChecker`.\n  function getPreciseType(propValue) {\n    if (typeof propValue === 'undefined' || propValue === null) {\n      return '' + propValue;\n    }\n    var propType = getPropType(propValue);\n    if (propType === 'object') {\n      if (propValue instanceof Date) {\n        return 'date';\n      } else if (propValue instanceof RegExp) {\n        return 'regexp';\n      }\n    }\n    return propType;\n  }\n\n  // Returns a string that is postfixed to a warning about an invalid type.\n  // For example, \"undefined\" or \"of type array\"\n  function getPostfixForTypeWarning(value) {\n    var type = getPreciseType(value);\n    switch (type) {\n      case 'array':\n      case 'object':\n        return 'an ' + type;\n      case 'boolean':\n      case 'date':\n      case 'regexp':\n        return 'a ' + type;\n      default:\n        return type;\n    }\n  }\n\n  // Returns class name of the object, if any.\n  function getClassName(propValue) {\n    if (!propValue.constructor || !propValue.constructor.name) {\n      return ANONYMOUS;\n    }\n    return propValue.constructor.name;\n  }\n  ReactPropTypes.checkPropTypes = checkPropTypes;\n  ReactPropTypes.resetWarningCache = checkPropTypes.resetWarningCache;\n  ReactPropTypes.PropTypes = ReactPropTypes;\n  return ReactPropTypes;\n};", "map": {"version": 3, "names": ["ReactIs", "require", "assign", "ReactPropTypesSecret", "has", "checkPropTypes", "printWarning", "process", "env", "NODE_ENV", "text", "message", "console", "error", "Error", "x", "emptyFunctionThatReturnsNull", "module", "exports", "isValidElement", "throwOnDirectAccess", "ITERATOR_SYMBOL", "Symbol", "iterator", "FAUX_ITERATOR_SYMBOL", "getIteratorFn", "maybeIterable", "iteratorFn", "ANONYMOUS", "ReactPropTypes", "array", "createPrimitiveTypeChecker", "bigint", "bool", "func", "number", "object", "string", "symbol", "any", "createAnyTypeChecker", "arrayOf", "createArrayOfTypeChecker", "element", "createElementTypeChecker", "elementType", "createElementTypeTypeChecker", "instanceOf", "createInstanceTypeChecker", "node", "createNodeChecker", "objectOf", "createObjectOfTypeChecker", "oneOf", "createEnumTypeChecker", "oneOfType", "createUnionTypeChecker", "shape", "createShapeTypeChecker", "exact", "createStrictShapeTypeChecker", "is", "y", "PropTypeError", "data", "stack", "prototype", "createChainableTypeChecker", "validate", "manualPropTypeCallCache", "manualPropTypeWarningCount", "checkType", "isRequired", "props", "propName", "componentName", "location", "prop<PERSON><PERSON><PERSON><PERSON>", "secret", "err", "name", "cache<PERSON>ey", "chainedCheckType", "bind", "expectedType", "propValue", "propType", "getPropType", "preciseType", "getPreciseType", "typeC<PERSON>cker", "Array", "isArray", "i", "length", "isValidElementType", "expectedClass", "expectedClassName", "actualClassName", "getClassName", "expectedV<PERSON>ues", "arguments", "valuesString", "JSON", "stringify", "replacer", "key", "value", "type", "String", "arrayOfTypeCheckers", "checker", "getPostfixForTypeWarning", "expectedTypes", "checkerResult", "push", "expectedTypesMessage", "join", "isNode", "invalidValidatorError", "shapeTypes", "allKeys", "Object", "keys", "every", "call", "step", "entries", "next", "done", "entry", "isSymbol", "RegExp", "Date", "constructor", "resetWarningCache", "PropTypes"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/prop-types/factoryWithTypeCheckers.js"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactIs = require('react-is');\nvar assign = require('object-assign');\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\nvar has = require('./lib/has');\nvar checkPropTypes = require('./checkPropTypes');\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  };\n}\n\nfunction emptyFunctionThatReturnsNull() {\n  return null;\n}\n\nmodule.exports = function(isValidElement, throwOnDirectAccess) {\n  /* global Symbol */\n  var ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\n  var FAUX_ITERATOR_SYMBOL = '@@iterator'; // Before Symbol spec.\n\n  /**\n   * Returns the iterator method function contained on the iterable object.\n   *\n   * Be sure to invoke the function with the iterable as context:\n   *\n   *     var iteratorFn = getIteratorFn(myIterable);\n   *     if (iteratorFn) {\n   *       var iterator = iteratorFn.call(myIterable);\n   *       ...\n   *     }\n   *\n   * @param {?object} maybeIterable\n   * @return {?function}\n   */\n  function getIteratorFn(maybeIterable) {\n    var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);\n    if (typeof iteratorFn === 'function') {\n      return iteratorFn;\n    }\n  }\n\n  /**\n   * Collection of methods that allow declaration and validation of props that are\n   * supplied to React components. Example usage:\n   *\n   *   var Props = require('ReactPropTypes');\n   *   var MyArticle = React.createClass({\n   *     propTypes: {\n   *       // An optional string prop named \"description\".\n   *       description: Props.string,\n   *\n   *       // A required enum prop named \"category\".\n   *       category: Props.oneOf(['News','Photos']).isRequired,\n   *\n   *       // A prop named \"dialog\" that requires an instance of Dialog.\n   *       dialog: Props.instanceOf(Dialog).isRequired\n   *     },\n   *     render: function() { ... }\n   *   });\n   *\n   * A more formal specification of how these methods are used:\n   *\n   *   type := array|bool|func|object|number|string|oneOf([...])|instanceOf(...)\n   *   decl := ReactPropTypes.{type}(.isRequired)?\n   *\n   * Each and every declaration produces a function with the same signature. This\n   * allows the creation of custom validation functions. For example:\n   *\n   *  var MyLink = React.createClass({\n   *    propTypes: {\n   *      // An optional string or URI prop named \"href\".\n   *      href: function(props, propName, componentName) {\n   *        var propValue = props[propName];\n   *        if (propValue != null && typeof propValue !== 'string' &&\n   *            !(propValue instanceof URI)) {\n   *          return new Error(\n   *            'Expected a string or an URI for ' + propName + ' in ' +\n   *            componentName\n   *          );\n   *        }\n   *      }\n   *    },\n   *    render: function() {...}\n   *  });\n   *\n   * @internal\n   */\n\n  var ANONYMOUS = '<<anonymous>>';\n\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithThrowingShims.js`.\n  var ReactPropTypes = {\n    array: createPrimitiveTypeChecker('array'),\n    bigint: createPrimitiveTypeChecker('bigint'),\n    bool: createPrimitiveTypeChecker('boolean'),\n    func: createPrimitiveTypeChecker('function'),\n    number: createPrimitiveTypeChecker('number'),\n    object: createPrimitiveTypeChecker('object'),\n    string: createPrimitiveTypeChecker('string'),\n    symbol: createPrimitiveTypeChecker('symbol'),\n\n    any: createAnyTypeChecker(),\n    arrayOf: createArrayOfTypeChecker,\n    element: createElementTypeChecker(),\n    elementType: createElementTypeTypeChecker(),\n    instanceOf: createInstanceTypeChecker,\n    node: createNodeChecker(),\n    objectOf: createObjectOfTypeChecker,\n    oneOf: createEnumTypeChecker,\n    oneOfType: createUnionTypeChecker,\n    shape: createShapeTypeChecker,\n    exact: createStrictShapeTypeChecker,\n  };\n\n  /**\n   * inlined Object.is polyfill to avoid requiring consumers ship their own\n   * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n   */\n  /*eslint-disable no-self-compare*/\n  function is(x, y) {\n    // SameValue algorithm\n    if (x === y) {\n      // Steps 1-5, 7-10\n      // Steps 6.b-6.e: +0 != -0\n      return x !== 0 || 1 / x === 1 / y;\n    } else {\n      // Step 6.a: NaN == NaN\n      return x !== x && y !== y;\n    }\n  }\n  /*eslint-enable no-self-compare*/\n\n  /**\n   * We use an Error-like object for backward compatibility as people may call\n   * PropTypes directly and inspect their output. However, we don't use real\n   * Errors anymore. We don't inspect their stack anyway, and creating them\n   * is prohibitively expensive if they are created too often, such as what\n   * happens in oneOfType() for any type before the one that matched.\n   */\n  function PropTypeError(message, data) {\n    this.message = message;\n    this.data = data && typeof data === 'object' ? data: {};\n    this.stack = '';\n  }\n  // Make `instanceof Error` still work for returned errors.\n  PropTypeError.prototype = Error.prototype;\n\n  function createChainableTypeChecker(validate) {\n    if (process.env.NODE_ENV !== 'production') {\n      var manualPropTypeCallCache = {};\n      var manualPropTypeWarningCount = 0;\n    }\n    function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {\n      componentName = componentName || ANONYMOUS;\n      propFullName = propFullName || propName;\n\n      if (secret !== ReactPropTypesSecret) {\n        if (throwOnDirectAccess) {\n          // New behavior only for users of `prop-types` package\n          var err = new Error(\n            'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n            'Use `PropTypes.checkPropTypes()` to call them. ' +\n            'Read more at http://fb.me/use-check-prop-types'\n          );\n          err.name = 'Invariant Violation';\n          throw err;\n        } else if (process.env.NODE_ENV !== 'production' && typeof console !== 'undefined') {\n          // Old behavior for people using React.PropTypes\n          var cacheKey = componentName + ':' + propName;\n          if (\n            !manualPropTypeCallCache[cacheKey] &&\n            // Avoid spamming the console because they are often not actionable except for lib authors\n            manualPropTypeWarningCount < 3\n          ) {\n            printWarning(\n              'You are manually calling a React.PropTypes validation ' +\n              'function for the `' + propFullName + '` prop on `' + componentName + '`. This is deprecated ' +\n              'and will throw in the standalone `prop-types` package. ' +\n              'You may be seeing this warning due to a third-party PropTypes ' +\n              'library. See https://fb.me/react-warning-dont-call-proptypes ' + 'for details.'\n            );\n            manualPropTypeCallCache[cacheKey] = true;\n            manualPropTypeWarningCount++;\n          }\n        }\n      }\n      if (props[propName] == null) {\n        if (isRequired) {\n          if (props[propName] === null) {\n            return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required ' + ('in `' + componentName + '`, but its value is `null`.'));\n          }\n          return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required in ' + ('`' + componentName + '`, but its value is `undefined`.'));\n        }\n        return null;\n      } else {\n        return validate(props, propName, componentName, location, propFullName);\n      }\n    }\n\n    var chainedCheckType = checkType.bind(null, false);\n    chainedCheckType.isRequired = checkType.bind(null, true);\n\n    return chainedCheckType;\n  }\n\n  function createPrimitiveTypeChecker(expectedType) {\n    function validate(props, propName, componentName, location, propFullName, secret) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== expectedType) {\n        // `propValue` being instance of, say, date/regexp, pass the 'object'\n        // check, but we can offer a more precise error message here rather than\n        // 'of type `object`'.\n        var preciseType = getPreciseType(propValue);\n\n        return new PropTypeError(\n          'Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + preciseType + '` supplied to `' + componentName + '`, expected ') + ('`' + expectedType + '`.'),\n          {expectedType: expectedType}\n        );\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createAnyTypeChecker() {\n    return createChainableTypeChecker(emptyFunctionThatReturnsNull);\n  }\n\n  function createArrayOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside arrayOf.');\n      }\n      var propValue = props[propName];\n      if (!Array.isArray(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an array.'));\n      }\n      for (var i = 0; i < propValue.length; i++) {\n        var error = typeChecker(propValue, i, componentName, location, propFullName + '[' + i + ']', ReactPropTypesSecret);\n        if (error instanceof Error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!isValidElement(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!ReactIs.isValidElementType(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement type.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createInstanceTypeChecker(expectedClass) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!(props[propName] instanceof expectedClass)) {\n        var expectedClassName = expectedClass.name || ANONYMOUS;\n        var actualClassName = getClassName(props[propName]);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + actualClassName + '` supplied to `' + componentName + '`, expected ') + ('instance of `' + expectedClassName + '`.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createEnumTypeChecker(expectedValues) {\n    if (!Array.isArray(expectedValues)) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (arguments.length > 1) {\n          printWarning(\n            'Invalid arguments supplied to oneOf, expected an array, got ' + arguments.length + ' arguments. ' +\n            'A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).'\n          );\n        } else {\n          printWarning('Invalid argument supplied to oneOf, expected an array.');\n        }\n      }\n      return emptyFunctionThatReturnsNull;\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      for (var i = 0; i < expectedValues.length; i++) {\n        if (is(propValue, expectedValues[i])) {\n          return null;\n        }\n      }\n\n      var valuesString = JSON.stringify(expectedValues, function replacer(key, value) {\n        var type = getPreciseType(value);\n        if (type === 'symbol') {\n          return String(value);\n        }\n        return value;\n      });\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of value `' + String(propValue) + '` ' + ('supplied to `' + componentName + '`, expected one of ' + valuesString + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createObjectOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside objectOf.');\n      }\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an object.'));\n      }\n      for (var key in propValue) {\n        if (has(propValue, key)) {\n          var error = typeChecker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n          if (error instanceof Error) {\n            return error;\n          }\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createUnionTypeChecker(arrayOfTypeCheckers) {\n    if (!Array.isArray(arrayOfTypeCheckers)) {\n      process.env.NODE_ENV !== 'production' ? printWarning('Invalid argument supplied to oneOfType, expected an instance of array.') : void 0;\n      return emptyFunctionThatReturnsNull;\n    }\n\n    for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n      var checker = arrayOfTypeCheckers[i];\n      if (typeof checker !== 'function') {\n        printWarning(\n          'Invalid argument supplied to oneOfType. Expected an array of check functions, but ' +\n          'received ' + getPostfixForTypeWarning(checker) + ' at index ' + i + '.'\n        );\n        return emptyFunctionThatReturnsNull;\n      }\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var expectedTypes = [];\n      for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n        var checker = arrayOfTypeCheckers[i];\n        var checkerResult = checker(props, propName, componentName, location, propFullName, ReactPropTypesSecret);\n        if (checkerResult == null) {\n          return null;\n        }\n        if (checkerResult.data && has(checkerResult.data, 'expectedType')) {\n          expectedTypes.push(checkerResult.data.expectedType);\n        }\n      }\n      var expectedTypesMessage = (expectedTypes.length > 0) ? ', expected one of type [' + expectedTypes.join(', ') + ']': '';\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`' + expectedTypesMessage + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createNodeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!isNode(props[propName])) {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`, expected a ReactNode.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function invalidValidatorError(componentName, location, propFullName, key, type) {\n    return new PropTypeError(\n      (componentName || 'React class') + ': ' + location + ' type `' + propFullName + '.' + key + '` is invalid; ' +\n      'it must be a function, usually from the `prop-types` package, but received `' + type + '`.'\n    );\n  }\n\n  function createShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      for (var key in shapeTypes) {\n        var checker = shapeTypes[key];\n        if (typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createStrictShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      // We need to check all keys in case some are required but missing from props.\n      var allKeys = assign({}, props[propName], shapeTypes);\n      for (var key in allKeys) {\n        var checker = shapeTypes[key];\n        if (has(shapeTypes, key) && typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        if (!checker) {\n          return new PropTypeError(\n            'Invalid ' + location + ' `' + propFullName + '` key `' + key + '` supplied to `' + componentName + '`.' +\n            '\\nBad object: ' + JSON.stringify(props[propName], null, '  ') +\n            '\\nValid keys: ' + JSON.stringify(Object.keys(shapeTypes), null, '  ')\n          );\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function isNode(propValue) {\n    switch (typeof propValue) {\n      case 'number':\n      case 'string':\n      case 'undefined':\n        return true;\n      case 'boolean':\n        return !propValue;\n      case 'object':\n        if (Array.isArray(propValue)) {\n          return propValue.every(isNode);\n        }\n        if (propValue === null || isValidElement(propValue)) {\n          return true;\n        }\n\n        var iteratorFn = getIteratorFn(propValue);\n        if (iteratorFn) {\n          var iterator = iteratorFn.call(propValue);\n          var step;\n          if (iteratorFn !== propValue.entries) {\n            while (!(step = iterator.next()).done) {\n              if (!isNode(step.value)) {\n                return false;\n              }\n            }\n          } else {\n            // Iterator will provide entry [k,v] tuples rather than values.\n            while (!(step = iterator.next()).done) {\n              var entry = step.value;\n              if (entry) {\n                if (!isNode(entry[1])) {\n                  return false;\n                }\n              }\n            }\n          }\n        } else {\n          return false;\n        }\n\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  function isSymbol(propType, propValue) {\n    // Native Symbol.\n    if (propType === 'symbol') {\n      return true;\n    }\n\n    // falsy value can't be a Symbol\n    if (!propValue) {\n      return false;\n    }\n\n    // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n    if (propValue['@@toStringTag'] === 'Symbol') {\n      return true;\n    }\n\n    // Fallback for non-spec compliant Symbols which are polyfilled.\n    if (typeof Symbol === 'function' && propValue instanceof Symbol) {\n      return true;\n    }\n\n    return false;\n  }\n\n  // Equivalent of `typeof` but with special handling for array and regexp.\n  function getPropType(propValue) {\n    var propType = typeof propValue;\n    if (Array.isArray(propValue)) {\n      return 'array';\n    }\n    if (propValue instanceof RegExp) {\n      // Old webkits (at least until Android 4.0) return 'function' rather than\n      // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n      // passes PropTypes.object.\n      return 'object';\n    }\n    if (isSymbol(propType, propValue)) {\n      return 'symbol';\n    }\n    return propType;\n  }\n\n  // This handles more types than `getPropType`. Only used for error messages.\n  // See `createPrimitiveTypeChecker`.\n  function getPreciseType(propValue) {\n    if (typeof propValue === 'undefined' || propValue === null) {\n      return '' + propValue;\n    }\n    var propType = getPropType(propValue);\n    if (propType === 'object') {\n      if (propValue instanceof Date) {\n        return 'date';\n      } else if (propValue instanceof RegExp) {\n        return 'regexp';\n      }\n    }\n    return propType;\n  }\n\n  // Returns a string that is postfixed to a warning about an invalid type.\n  // For example, \"undefined\" or \"of type array\"\n  function getPostfixForTypeWarning(value) {\n    var type = getPreciseType(value);\n    switch (type) {\n      case 'array':\n      case 'object':\n        return 'an ' + type;\n      case 'boolean':\n      case 'date':\n      case 'regexp':\n        return 'a ' + type;\n      default:\n        return type;\n    }\n  }\n\n  // Returns class name of the object, if any.\n  function getClassName(propValue) {\n    if (!propValue.constructor || !propValue.constructor.name) {\n      return ANONYMOUS;\n    }\n    return propValue.constructor.name;\n  }\n\n  ReactPropTypes.checkPropTypes = checkPropTypes;\n  ReactPropTypes.resetWarningCache = checkPropTypes.resetWarningCache;\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,IAAIA,OAAO,GAAGC,OAAO,CAAC,UAAU,CAAC;AACjC,IAAIC,MAAM,GAAGD,OAAO,CAAC,eAAe,CAAC;AAErC,IAAIE,oBAAoB,GAAGF,OAAO,CAAC,4BAA4B,CAAC;AAChE,IAAIG,GAAG,GAAGH,OAAO,CAAC,WAAW,CAAC;AAC9B,IAAII,cAAc,GAAGJ,OAAO,CAAC,kBAAkB,CAAC;AAEhD,IAAIK,YAAY,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;AAEhC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,YAAY,GAAG,SAAAA,CAASI,IAAI,EAAE;IAC5B,IAAIC,OAAO,GAAG,WAAW,GAAGD,IAAI;IAChC,IAAI,OAAOE,OAAO,KAAK,WAAW,EAAE;MAClCA,OAAO,CAACC,KAAK,CAACF,OAAO,CAAC;IACxB;IACA,IAAI;MACF;MACA;MACA;MACA,MAAM,IAAIG,KAAK,CAACH,OAAO,CAAC;IAC1B,CAAC,CAAC,OAAOI,CAAC,EAAE,CAAC;EACf,CAAC;AACH;AAEA,SAASC,4BAA4BA,CAAA,EAAG;EACtC,OAAO,IAAI;AACb;AAEAC,MAAM,CAACC,OAAO,GAAG,UAASC,cAAc,EAAEC,mBAAmB,EAAE;EAC7D;EACA,IAAIC,eAAe,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;EACrE,IAAIC,oBAAoB,GAAG,YAAY,CAAC,CAAC;;EAEzC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,aAAaA,CAACC,aAAa,EAAE;IACpC,IAAIC,UAAU,GAAGD,aAAa,KAAKL,eAAe,IAAIK,aAAa,CAACL,eAAe,CAAC,IAAIK,aAAa,CAACF,oBAAoB,CAAC,CAAC;IAC5H,IAAI,OAAOG,UAAU,KAAK,UAAU,EAAE;MACpC,OAAOA,UAAU;IACnB;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE,IAAIC,SAAS,GAAG,eAAe;;EAE/B;EACA;EACA,IAAIC,cAAc,GAAG;IACnBC,KAAK,EAAEC,0BAA0B,CAAC,OAAO,CAAC;IAC1CC,MAAM,EAAED,0BAA0B,CAAC,QAAQ,CAAC;IAC5CE,IAAI,EAAEF,0BAA0B,CAAC,SAAS,CAAC;IAC3CG,IAAI,EAAEH,0BAA0B,CAAC,UAAU,CAAC;IAC5CI,MAAM,EAAEJ,0BAA0B,CAAC,QAAQ,CAAC;IAC5CK,MAAM,EAAEL,0BAA0B,CAAC,QAAQ,CAAC;IAC5CM,MAAM,EAAEN,0BAA0B,CAAC,QAAQ,CAAC;IAC5CO,MAAM,EAAEP,0BAA0B,CAAC,QAAQ,CAAC;IAE5CQ,GAAG,EAAEC,oBAAoB,CAAC,CAAC;IAC3BC,OAAO,EAAEC,wBAAwB;IACjCC,OAAO,EAAEC,wBAAwB,CAAC,CAAC;IACnCC,WAAW,EAAEC,4BAA4B,CAAC,CAAC;IAC3CC,UAAU,EAAEC,yBAAyB;IACrCC,IAAI,EAAEC,iBAAiB,CAAC,CAAC;IACzBC,QAAQ,EAAEC,yBAAyB;IACnCC,KAAK,EAAEC,qBAAqB;IAC5BC,SAAS,EAAEC,sBAAsB;IACjCC,KAAK,EAAEC,sBAAsB;IAC7BC,KAAK,EAAEC;EACT,CAAC;;EAED;AACF;AACA;AACA;EACE;EACA,SAASC,EAAEA,CAAC9C,CAAC,EAAE+C,CAAC,EAAE;IAChB;IACA,IAAI/C,CAAC,KAAK+C,CAAC,EAAE;MACX;MACA;MACA,OAAO/C,CAAC,KAAK,CAAC,IAAI,CAAC,GAAGA,CAAC,KAAK,CAAC,GAAG+C,CAAC;IACnC,CAAC,MAAM;MACL;MACA,OAAO/C,CAAC,KAAKA,CAAC,IAAI+C,CAAC,KAAKA,CAAC;IAC3B;EACF;EACA;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,aAAaA,CAACpD,OAAO,EAAEqD,IAAI,EAAE;IACpC,IAAI,CAACrD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACqD,IAAI,GAAGA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAE,CAAC,CAAC;IACvD,IAAI,CAACC,KAAK,GAAG,EAAE;EACjB;EACA;EACAF,aAAa,CAACG,SAAS,GAAGpD,KAAK,CAACoD,SAAS;EAEzC,SAASC,0BAA0BA,CAACC,QAAQ,EAAE;IAC5C,IAAI7D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI4D,uBAAuB,GAAG,CAAC,CAAC;MAChC,IAAIC,0BAA0B,GAAG,CAAC;IACpC;IACA,SAASC,SAASA,CAACC,UAAU,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,MAAM,EAAE;MAC7FH,aAAa,GAAGA,aAAa,IAAI/C,SAAS;MAC1CiD,YAAY,GAAGA,YAAY,IAAIH,QAAQ;MAEvC,IAAII,MAAM,KAAK3E,oBAAoB,EAAE;QACnC,IAAIiB,mBAAmB,EAAE;UACvB;UACA,IAAI2D,GAAG,GAAG,IAAIjE,KAAK,CACjB,sFAAsF,GACtF,iDAAiD,GACjD,gDACF,CAAC;UACDiE,GAAG,CAACC,IAAI,GAAG,qBAAqB;UAChC,MAAMD,GAAG;QACX,CAAC,MAAM,IAAIxE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,OAAOG,OAAO,KAAK,WAAW,EAAE;UAClF;UACA,IAAIqE,QAAQ,GAAGN,aAAa,GAAG,GAAG,GAAGD,QAAQ;UAC7C,IACE,CAACL,uBAAuB,CAACY,QAAQ,CAAC;UAClC;UACAX,0BAA0B,GAAG,CAAC,EAC9B;YACAhE,YAAY,CACV,wDAAwD,GACxD,oBAAoB,GAAGuE,YAAY,GAAG,aAAa,GAAGF,aAAa,GAAG,wBAAwB,GAC9F,yDAAyD,GACzD,gEAAgE,GAChE,+DAA+D,GAAG,cACpE,CAAC;YACDN,uBAAuB,CAACY,QAAQ,CAAC,GAAG,IAAI;YACxCX,0BAA0B,EAAE;UAC9B;QACF;MACF;MACA,IAAIG,KAAK,CAACC,QAAQ,CAAC,IAAI,IAAI,EAAE;QAC3B,IAAIF,UAAU,EAAE;UACd,IAAIC,KAAK,CAACC,QAAQ,CAAC,KAAK,IAAI,EAAE;YAC5B,OAAO,IAAIX,aAAa,CAAC,MAAM,GAAGa,QAAQ,GAAG,IAAI,GAAGC,YAAY,GAAG,0BAA0B,IAAI,MAAM,GAAGF,aAAa,GAAG,6BAA6B,CAAC,CAAC;UAC3J;UACA,OAAO,IAAIZ,aAAa,CAAC,MAAM,GAAGa,QAAQ,GAAG,IAAI,GAAGC,YAAY,GAAG,6BAA6B,IAAI,GAAG,GAAGF,aAAa,GAAG,kCAAkC,CAAC,CAAC;QAChK;QACA,OAAO,IAAI;MACb,CAAC,MAAM;QACL,OAAOP,QAAQ,CAACK,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,CAAC;MACzE;IACF;IAEA,IAAIK,gBAAgB,GAAGX,SAAS,CAACY,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC;IAClDD,gBAAgB,CAACV,UAAU,GAAGD,SAAS,CAACY,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;IAExD,OAAOD,gBAAgB;EACzB;EAEA,SAASnD,0BAA0BA,CAACqD,YAAY,EAAE;IAChD,SAAShB,QAAQA,CAACK,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,MAAM,EAAE;MAChF,IAAIO,SAAS,GAAGZ,KAAK,CAACC,QAAQ,CAAC;MAC/B,IAAIY,QAAQ,GAAGC,WAAW,CAACF,SAAS,CAAC;MACrC,IAAIC,QAAQ,KAAKF,YAAY,EAAE;QAC7B;QACA;QACA;QACA,IAAII,WAAW,GAAGC,cAAc,CAACJ,SAAS,CAAC;QAE3C,OAAO,IAAItB,aAAa,CACtB,UAAU,GAAGa,QAAQ,GAAG,IAAI,GAAGC,YAAY,GAAG,YAAY,IAAI,GAAG,GAAGW,WAAW,GAAG,iBAAiB,GAAGb,aAAa,GAAG,cAAc,CAAC,IAAI,GAAG,GAAGS,YAAY,GAAG,IAAI,CAAC,EACnK;UAACA,YAAY,EAAEA;QAAY,CAC7B,CAAC;MACH;MACA,OAAO,IAAI;IACb;IACA,OAAOjB,0BAA0B,CAACC,QAAQ,CAAC;EAC7C;EAEA,SAAS5B,oBAAoBA,CAAA,EAAG;IAC9B,OAAO2B,0BAA0B,CAACnD,4BAA4B,CAAC;EACjE;EAEA,SAAS0B,wBAAwBA,CAACgD,WAAW,EAAE;IAC7C,SAAStB,QAAQA,CAACK,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAE;MACxE,IAAI,OAAOa,WAAW,KAAK,UAAU,EAAE;QACrC,OAAO,IAAI3B,aAAa,CAAC,YAAY,GAAGc,YAAY,GAAG,kBAAkB,GAAGF,aAAa,GAAG,iDAAiD,CAAC;MAChJ;MACA,IAAIU,SAAS,GAAGZ,KAAK,CAACC,QAAQ,CAAC;MAC/B,IAAI,CAACiB,KAAK,CAACC,OAAO,CAACP,SAAS,CAAC,EAAE;QAC7B,IAAIC,QAAQ,GAAGC,WAAW,CAACF,SAAS,CAAC;QACrC,OAAO,IAAItB,aAAa,CAAC,UAAU,GAAGa,QAAQ,GAAG,IAAI,GAAGC,YAAY,GAAG,YAAY,IAAI,GAAG,GAAGS,QAAQ,GAAG,iBAAiB,GAAGX,aAAa,GAAG,uBAAuB,CAAC,CAAC;MACvK;MACA,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,SAAS,CAACS,MAAM,EAAED,CAAC,EAAE,EAAE;QACzC,IAAIhF,KAAK,GAAG6E,WAAW,CAACL,SAAS,EAAEQ,CAAC,EAAElB,aAAa,EAAEC,QAAQ,EAAEC,YAAY,GAAG,GAAG,GAAGgB,CAAC,GAAG,GAAG,EAAE1F,oBAAoB,CAAC;QAClH,IAAIU,KAAK,YAAYC,KAAK,EAAE;UAC1B,OAAOD,KAAK;QACd;MACF;MACA,OAAO,IAAI;IACb;IACA,OAAOsD,0BAA0B,CAACC,QAAQ,CAAC;EAC7C;EAEA,SAASxB,wBAAwBA,CAAA,EAAG;IAClC,SAASwB,QAAQA,CAACK,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAE;MACxE,IAAIQ,SAAS,GAAGZ,KAAK,CAACC,QAAQ,CAAC;MAC/B,IAAI,CAACvD,cAAc,CAACkE,SAAS,CAAC,EAAE;QAC9B,IAAIC,QAAQ,GAAGC,WAAW,CAACF,SAAS,CAAC;QACrC,OAAO,IAAItB,aAAa,CAAC,UAAU,GAAGa,QAAQ,GAAG,IAAI,GAAGC,YAAY,GAAG,YAAY,IAAI,GAAG,GAAGS,QAAQ,GAAG,iBAAiB,GAAGX,aAAa,GAAG,oCAAoC,CAAC,CAAC;MACpL;MACA,OAAO,IAAI;IACb;IACA,OAAOR,0BAA0B,CAACC,QAAQ,CAAC;EAC7C;EAEA,SAAStB,4BAA4BA,CAAA,EAAG;IACtC,SAASsB,QAAQA,CAACK,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAE;MACxE,IAAIQ,SAAS,GAAGZ,KAAK,CAACC,QAAQ,CAAC;MAC/B,IAAI,CAAC1E,OAAO,CAAC+F,kBAAkB,CAACV,SAAS,CAAC,EAAE;QAC1C,IAAIC,QAAQ,GAAGC,WAAW,CAACF,SAAS,CAAC;QACrC,OAAO,IAAItB,aAAa,CAAC,UAAU,GAAGa,QAAQ,GAAG,IAAI,GAAGC,YAAY,GAAG,YAAY,IAAI,GAAG,GAAGS,QAAQ,GAAG,iBAAiB,GAAGX,aAAa,GAAG,yCAAyC,CAAC,CAAC;MACzL;MACA,OAAO,IAAI;IACb;IACA,OAAOR,0BAA0B,CAACC,QAAQ,CAAC;EAC7C;EAEA,SAASpB,yBAAyBA,CAACgD,aAAa,EAAE;IAChD,SAAS5B,QAAQA,CAACK,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAE;MACxE,IAAI,EAAEJ,KAAK,CAACC,QAAQ,CAAC,YAAYsB,aAAa,CAAC,EAAE;QAC/C,IAAIC,iBAAiB,GAAGD,aAAa,CAAChB,IAAI,IAAIpD,SAAS;QACvD,IAAIsE,eAAe,GAAGC,YAAY,CAAC1B,KAAK,CAACC,QAAQ,CAAC,CAAC;QACnD,OAAO,IAAIX,aAAa,CAAC,UAAU,GAAGa,QAAQ,GAAG,IAAI,GAAGC,YAAY,GAAG,YAAY,IAAI,GAAG,GAAGqB,eAAe,GAAG,iBAAiB,GAAGvB,aAAa,GAAG,cAAc,CAAC,IAAI,eAAe,GAAGsB,iBAAiB,GAAG,IAAI,CAAC,CAAC;MACpN;MACA,OAAO,IAAI;IACb;IACA,OAAO9B,0BAA0B,CAACC,QAAQ,CAAC;EAC7C;EAEA,SAASd,qBAAqBA,CAAC8C,cAAc,EAAE;IAC7C,IAAI,CAACT,KAAK,CAACC,OAAO,CAACQ,cAAc,CAAC,EAAE;MAClC,IAAI7F,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAI4F,SAAS,CAACP,MAAM,GAAG,CAAC,EAAE;UACxBxF,YAAY,CACV,8DAA8D,GAAG+F,SAAS,CAACP,MAAM,GAAG,cAAc,GAClG,0EACF,CAAC;QACH,CAAC,MAAM;UACLxF,YAAY,CAAC,wDAAwD,CAAC;QACxE;MACF;MACA,OAAOU,4BAA4B;IACrC;IAEA,SAASoD,QAAQA,CAACK,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAE;MACxE,IAAIQ,SAAS,GAAGZ,KAAK,CAACC,QAAQ,CAAC;MAC/B,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,cAAc,CAACN,MAAM,EAAED,CAAC,EAAE,EAAE;QAC9C,IAAIhC,EAAE,CAACwB,SAAS,EAAEe,cAAc,CAACP,CAAC,CAAC,CAAC,EAAE;UACpC,OAAO,IAAI;QACb;MACF;MAEA,IAAIS,YAAY,GAAGC,IAAI,CAACC,SAAS,CAACJ,cAAc,EAAE,SAASK,QAAQA,CAACC,GAAG,EAAEC,KAAK,EAAE;QAC9E,IAAIC,IAAI,GAAGnB,cAAc,CAACkB,KAAK,CAAC;QAChC,IAAIC,IAAI,KAAK,QAAQ,EAAE;UACrB,OAAOC,MAAM,CAACF,KAAK,CAAC;QACtB;QACA,OAAOA,KAAK;MACd,CAAC,CAAC;MACF,OAAO,IAAI5C,aAAa,CAAC,UAAU,GAAGa,QAAQ,GAAG,IAAI,GAAGC,YAAY,GAAG,cAAc,GAAGgC,MAAM,CAACxB,SAAS,CAAC,GAAG,IAAI,IAAI,eAAe,GAAGV,aAAa,GAAG,qBAAqB,GAAG2B,YAAY,GAAG,GAAG,CAAC,CAAC;IACpM;IACA,OAAOnC,0BAA0B,CAACC,QAAQ,CAAC;EAC7C;EAEA,SAAShB,yBAAyBA,CAACsC,WAAW,EAAE;IAC9C,SAAStB,QAAQA,CAACK,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAE;MACxE,IAAI,OAAOa,WAAW,KAAK,UAAU,EAAE;QACrC,OAAO,IAAI3B,aAAa,CAAC,YAAY,GAAGc,YAAY,GAAG,kBAAkB,GAAGF,aAAa,GAAG,kDAAkD,CAAC;MACjJ;MACA,IAAIU,SAAS,GAAGZ,KAAK,CAACC,QAAQ,CAAC;MAC/B,IAAIY,QAAQ,GAAGC,WAAW,CAACF,SAAS,CAAC;MACrC,IAAIC,QAAQ,KAAK,QAAQ,EAAE;QACzB,OAAO,IAAIvB,aAAa,CAAC,UAAU,GAAGa,QAAQ,GAAG,IAAI,GAAGC,YAAY,GAAG,YAAY,IAAI,GAAG,GAAGS,QAAQ,GAAG,iBAAiB,GAAGX,aAAa,GAAG,wBAAwB,CAAC,CAAC;MACxK;MACA,KAAK,IAAI+B,GAAG,IAAIrB,SAAS,EAAE;QACzB,IAAIjF,GAAG,CAACiF,SAAS,EAAEqB,GAAG,CAAC,EAAE;UACvB,IAAI7F,KAAK,GAAG6E,WAAW,CAACL,SAAS,EAAEqB,GAAG,EAAE/B,aAAa,EAAEC,QAAQ,EAAEC,YAAY,GAAG,GAAG,GAAG6B,GAAG,EAAEvG,oBAAoB,CAAC;UAChH,IAAIU,KAAK,YAAYC,KAAK,EAAE;YAC1B,OAAOD,KAAK;UACd;QACF;MACF;MACA,OAAO,IAAI;IACb;IACA,OAAOsD,0BAA0B,CAACC,QAAQ,CAAC;EAC7C;EAEA,SAASZ,sBAAsBA,CAACsD,mBAAmB,EAAE;IACnD,IAAI,CAACnB,KAAK,CAACC,OAAO,CAACkB,mBAAmB,CAAC,EAAE;MACvCvG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGH,YAAY,CAAC,wEAAwE,CAAC,GAAG,KAAK,CAAC;MACvI,OAAOU,4BAA4B;IACrC;IAEA,KAAK,IAAI6E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,mBAAmB,CAAChB,MAAM,EAAED,CAAC,EAAE,EAAE;MACnD,IAAIkB,OAAO,GAAGD,mBAAmB,CAACjB,CAAC,CAAC;MACpC,IAAI,OAAOkB,OAAO,KAAK,UAAU,EAAE;QACjCzG,YAAY,CACV,oFAAoF,GACpF,WAAW,GAAG0G,wBAAwB,CAACD,OAAO,CAAC,GAAG,YAAY,GAAGlB,CAAC,GAAG,GACvE,CAAC;QACD,OAAO7E,4BAA4B;MACrC;IACF;IAEA,SAASoD,QAAQA,CAACK,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAE;MACxE,IAAIoC,aAAa,GAAG,EAAE;MACtB,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,mBAAmB,CAAChB,MAAM,EAAED,CAAC,EAAE,EAAE;QACnD,IAAIkB,OAAO,GAAGD,mBAAmB,CAACjB,CAAC,CAAC;QACpC,IAAIqB,aAAa,GAAGH,OAAO,CAACtC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAE1E,oBAAoB,CAAC;QACzG,IAAI+G,aAAa,IAAI,IAAI,EAAE;UACzB,OAAO,IAAI;QACb;QACA,IAAIA,aAAa,CAAClD,IAAI,IAAI5D,GAAG,CAAC8G,aAAa,CAAClD,IAAI,EAAE,cAAc,CAAC,EAAE;UACjEiD,aAAa,CAACE,IAAI,CAACD,aAAa,CAAClD,IAAI,CAACoB,YAAY,CAAC;QACrD;MACF;MACA,IAAIgC,oBAAoB,GAAIH,aAAa,CAACnB,MAAM,GAAG,CAAC,GAAI,0BAA0B,GAAGmB,aAAa,CAACI,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,GAAE,EAAE;MACvH,OAAO,IAAItD,aAAa,CAAC,UAAU,GAAGa,QAAQ,GAAG,IAAI,GAAGC,YAAY,GAAG,gBAAgB,IAAI,GAAG,GAAGF,aAAa,GAAG,GAAG,GAAGyC,oBAAoB,GAAG,GAAG,CAAC,CAAC;IACrJ;IACA,OAAOjD,0BAA0B,CAACC,QAAQ,CAAC;EAC7C;EAEA,SAASlB,iBAAiBA,CAAA,EAAG;IAC3B,SAASkB,QAAQA,CAACK,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAE;MACxE,IAAI,CAACyC,MAAM,CAAC7C,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAE;QAC5B,OAAO,IAAIX,aAAa,CAAC,UAAU,GAAGa,QAAQ,GAAG,IAAI,GAAGC,YAAY,GAAG,gBAAgB,IAAI,GAAG,GAAGF,aAAa,GAAG,0BAA0B,CAAC,CAAC;MAC/I;MACA,OAAO,IAAI;IACb;IACA,OAAOR,0BAA0B,CAACC,QAAQ,CAAC;EAC7C;EAEA,SAASmD,qBAAqBA,CAAC5C,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAE6B,GAAG,EAAEE,IAAI,EAAE;IAC/E,OAAO,IAAI7C,aAAa,CACtB,CAACY,aAAa,IAAI,aAAa,IAAI,IAAI,GAAGC,QAAQ,GAAG,SAAS,GAAGC,YAAY,GAAG,GAAG,GAAG6B,GAAG,GAAG,gBAAgB,GAC5G,8EAA8E,GAAGE,IAAI,GAAG,IAC1F,CAAC;EACH;EAEA,SAASlD,sBAAsBA,CAAC8D,UAAU,EAAE;IAC1C,SAASpD,QAAQA,CAACK,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAE;MACxE,IAAIQ,SAAS,GAAGZ,KAAK,CAACC,QAAQ,CAAC;MAC/B,IAAIY,QAAQ,GAAGC,WAAW,CAACF,SAAS,CAAC;MACrC,IAAIC,QAAQ,KAAK,QAAQ,EAAE;QACzB,OAAO,IAAIvB,aAAa,CAAC,UAAU,GAAGa,QAAQ,GAAG,IAAI,GAAGC,YAAY,GAAG,aAAa,GAAGS,QAAQ,GAAG,IAAI,IAAI,eAAe,GAAGX,aAAa,GAAG,uBAAuB,CAAC,CAAC;MACvK;MACA,KAAK,IAAI+B,GAAG,IAAIc,UAAU,EAAE;QAC1B,IAAIT,OAAO,GAAGS,UAAU,CAACd,GAAG,CAAC;QAC7B,IAAI,OAAOK,OAAO,KAAK,UAAU,EAAE;UACjC,OAAOQ,qBAAqB,CAAC5C,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAE6B,GAAG,EAAEjB,cAAc,CAACsB,OAAO,CAAC,CAAC;QACnG;QACA,IAAIlG,KAAK,GAAGkG,OAAO,CAAC1B,SAAS,EAAEqB,GAAG,EAAE/B,aAAa,EAAEC,QAAQ,EAAEC,YAAY,GAAG,GAAG,GAAG6B,GAAG,EAAEvG,oBAAoB,CAAC;QAC5G,IAAIU,KAAK,EAAE;UACT,OAAOA,KAAK;QACd;MACF;MACA,OAAO,IAAI;IACb;IACA,OAAOsD,0BAA0B,CAACC,QAAQ,CAAC;EAC7C;EAEA,SAASR,4BAA4BA,CAAC4D,UAAU,EAAE;IAChD,SAASpD,QAAQA,CAACK,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAE;MACxE,IAAIQ,SAAS,GAAGZ,KAAK,CAACC,QAAQ,CAAC;MAC/B,IAAIY,QAAQ,GAAGC,WAAW,CAACF,SAAS,CAAC;MACrC,IAAIC,QAAQ,KAAK,QAAQ,EAAE;QACzB,OAAO,IAAIvB,aAAa,CAAC,UAAU,GAAGa,QAAQ,GAAG,IAAI,GAAGC,YAAY,GAAG,aAAa,GAAGS,QAAQ,GAAG,IAAI,IAAI,eAAe,GAAGX,aAAa,GAAG,uBAAuB,CAAC,CAAC;MACvK;MACA;MACA,IAAI8C,OAAO,GAAGvH,MAAM,CAAC,CAAC,CAAC,EAAEuE,KAAK,CAACC,QAAQ,CAAC,EAAE8C,UAAU,CAAC;MACrD,KAAK,IAAId,GAAG,IAAIe,OAAO,EAAE;QACvB,IAAIV,OAAO,GAAGS,UAAU,CAACd,GAAG,CAAC;QAC7B,IAAItG,GAAG,CAACoH,UAAU,EAAEd,GAAG,CAAC,IAAI,OAAOK,OAAO,KAAK,UAAU,EAAE;UACzD,OAAOQ,qBAAqB,CAAC5C,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAE6B,GAAG,EAAEjB,cAAc,CAACsB,OAAO,CAAC,CAAC;QACnG;QACA,IAAI,CAACA,OAAO,EAAE;UACZ,OAAO,IAAIhD,aAAa,CACtB,UAAU,GAAGa,QAAQ,GAAG,IAAI,GAAGC,YAAY,GAAG,SAAS,GAAG6B,GAAG,GAAG,iBAAiB,GAAG/B,aAAa,GAAG,IAAI,GACxG,gBAAgB,GAAG4B,IAAI,CAACC,SAAS,CAAC/B,KAAK,CAACC,QAAQ,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,GAC9D,gBAAgB,GAAG6B,IAAI,CAACC,SAAS,CAACkB,MAAM,CAACC,IAAI,CAACH,UAAU,CAAC,EAAE,IAAI,EAAE,IAAI,CACvE,CAAC;QACH;QACA,IAAI3G,KAAK,GAAGkG,OAAO,CAAC1B,SAAS,EAAEqB,GAAG,EAAE/B,aAAa,EAAEC,QAAQ,EAAEC,YAAY,GAAG,GAAG,GAAG6B,GAAG,EAAEvG,oBAAoB,CAAC;QAC5G,IAAIU,KAAK,EAAE;UACT,OAAOA,KAAK;QACd;MACF;MACA,OAAO,IAAI;IACb;IAEA,OAAOsD,0BAA0B,CAACC,QAAQ,CAAC;EAC7C;EAEA,SAASkD,MAAMA,CAACjC,SAAS,EAAE;IACzB,QAAQ,OAAOA,SAAS;MACtB,KAAK,QAAQ;MACb,KAAK,QAAQ;MACb,KAAK,WAAW;QACd,OAAO,IAAI;MACb,KAAK,SAAS;QACZ,OAAO,CAACA,SAAS;MACnB,KAAK,QAAQ;QACX,IAAIM,KAAK,CAACC,OAAO,CAACP,SAAS,CAAC,EAAE;UAC5B,OAAOA,SAAS,CAACuC,KAAK,CAACN,MAAM,CAAC;QAChC;QACA,IAAIjC,SAAS,KAAK,IAAI,IAAIlE,cAAc,CAACkE,SAAS,CAAC,EAAE;UACnD,OAAO,IAAI;QACb;QAEA,IAAI1D,UAAU,GAAGF,aAAa,CAAC4D,SAAS,CAAC;QACzC,IAAI1D,UAAU,EAAE;UACd,IAAIJ,QAAQ,GAAGI,UAAU,CAACkG,IAAI,CAACxC,SAAS,CAAC;UACzC,IAAIyC,IAAI;UACR,IAAInG,UAAU,KAAK0D,SAAS,CAAC0C,OAAO,EAAE;YACpC,OAAO,CAAC,CAACD,IAAI,GAAGvG,QAAQ,CAACyG,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAE;cACrC,IAAI,CAACX,MAAM,CAACQ,IAAI,CAACnB,KAAK,CAAC,EAAE;gBACvB,OAAO,KAAK;cACd;YACF;UACF,CAAC,MAAM;YACL;YACA,OAAO,CAAC,CAACmB,IAAI,GAAGvG,QAAQ,CAACyG,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAE;cACrC,IAAIC,KAAK,GAAGJ,IAAI,CAACnB,KAAK;cACtB,IAAIuB,KAAK,EAAE;gBACT,IAAI,CAACZ,MAAM,CAACY,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;kBACrB,OAAO,KAAK;gBACd;cACF;YACF;UACF;QACF,CAAC,MAAM;UACL,OAAO,KAAK;QACd;QAEA,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEA,SAASC,QAAQA,CAAC7C,QAAQ,EAAED,SAAS,EAAE;IACrC;IACA,IAAIC,QAAQ,KAAK,QAAQ,EAAE;MACzB,OAAO,IAAI;IACb;;IAEA;IACA,IAAI,CAACD,SAAS,EAAE;MACd,OAAO,KAAK;IACd;;IAEA;IACA,IAAIA,SAAS,CAAC,eAAe,CAAC,KAAK,QAAQ,EAAE;MAC3C,OAAO,IAAI;IACb;;IAEA;IACA,IAAI,OAAO/D,MAAM,KAAK,UAAU,IAAI+D,SAAS,YAAY/D,MAAM,EAAE;MAC/D,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;;EAEA;EACA,SAASiE,WAAWA,CAACF,SAAS,EAAE;IAC9B,IAAIC,QAAQ,GAAG,OAAOD,SAAS;IAC/B,IAAIM,KAAK,CAACC,OAAO,CAACP,SAAS,CAAC,EAAE;MAC5B,OAAO,OAAO;IAChB;IACA,IAAIA,SAAS,YAAY+C,MAAM,EAAE;MAC/B;MACA;MACA;MACA,OAAO,QAAQ;IACjB;IACA,IAAID,QAAQ,CAAC7C,QAAQ,EAAED,SAAS,CAAC,EAAE;MACjC,OAAO,QAAQ;IACjB;IACA,OAAOC,QAAQ;EACjB;;EAEA;EACA;EACA,SAASG,cAAcA,CAACJ,SAAS,EAAE;IACjC,IAAI,OAAOA,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAK,IAAI,EAAE;MAC1D,OAAO,EAAE,GAAGA,SAAS;IACvB;IACA,IAAIC,QAAQ,GAAGC,WAAW,CAACF,SAAS,CAAC;IACrC,IAAIC,QAAQ,KAAK,QAAQ,EAAE;MACzB,IAAID,SAAS,YAAYgD,IAAI,EAAE;QAC7B,OAAO,MAAM;MACf,CAAC,MAAM,IAAIhD,SAAS,YAAY+C,MAAM,EAAE;QACtC,OAAO,QAAQ;MACjB;IACF;IACA,OAAO9C,QAAQ;EACjB;;EAEA;EACA;EACA,SAAS0B,wBAAwBA,CAACL,KAAK,EAAE;IACvC,IAAIC,IAAI,GAAGnB,cAAc,CAACkB,KAAK,CAAC;IAChC,QAAQC,IAAI;MACV,KAAK,OAAO;MACZ,KAAK,QAAQ;QACX,OAAO,KAAK,GAAGA,IAAI;MACrB,KAAK,SAAS;MACd,KAAK,MAAM;MACX,KAAK,QAAQ;QACX,OAAO,IAAI,GAAGA,IAAI;MACpB;QACE,OAAOA,IAAI;IACf;EACF;;EAEA;EACA,SAAST,YAAYA,CAACd,SAAS,EAAE;IAC/B,IAAI,CAACA,SAAS,CAACiD,WAAW,IAAI,CAACjD,SAAS,CAACiD,WAAW,CAACtD,IAAI,EAAE;MACzD,OAAOpD,SAAS;IAClB;IACA,OAAOyD,SAAS,CAACiD,WAAW,CAACtD,IAAI;EACnC;EAEAnD,cAAc,CAACxB,cAAc,GAAGA,cAAc;EAC9CwB,cAAc,CAAC0G,iBAAiB,GAAGlI,cAAc,CAACkI,iBAAiB;EACnE1G,cAAc,CAAC2G,SAAS,GAAG3G,cAAc;EAEzC,OAAOA,cAAc;AACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}