{"ast": null, "code": "import isSameUTCWeek from \"../../../../_lib/isSameUTCWeek/index.js\";\nvar accusativeWeekdays = ['жексенбіде', 'дүйсенбіде', 'сейсенбіде', 'сәрсенбіде', 'бейсенбіде', 'жұмада', 'сенбіде'];\nfunction _lastWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  return \"'өткен \" + weekday + \" сағат' p'-де'\";\n}\nfunction thisWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  return \"'\" + weekday + \" сағат' p'-де'\";\n}\nfunction _nextWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  return \"'келесі \" + weekday + \" сағат' p'-де'\";\n}\nvar formatRelativeLocale = {\n  lastWeek: function lastWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _lastWeek(day);\n    }\n  },\n  yesterday: \"'кеше сағат' p'-де'\",\n  today: \"'бүгін сағат' p'-де'\",\n  tomorrow: \"'ертең сағат' p'-де'\",\n  nextWeek: function nextWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _nextWeek(day);\n    }\n  },\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n  return format;\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["isSameUTCWeek", "accusativeWeekdays", "_lastWeek", "day", "weekday", "thisWeek", "_nextWeek", "formatRelativeLocale", "lastWeek", "date", "baseDate", "options", "getUTCDay", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "format"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/kk/_lib/formatRelative/index.js"], "sourcesContent": ["import isSameUTCWeek from \"../../../../_lib/isSameUTCWeek/index.js\";\nvar accusativeWeekdays = ['жексенбіде', 'дүйсенбіде', 'сейсенбіде', 'сәрсенбіде', 'бейсенбіде', 'жұмада', 'сенбіде'];\nfunction _lastWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  return \"'өткен \" + weekday + \" сағат' p'-де'\";\n}\nfunction thisWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  return \"'\" + weekday + \" сағат' p'-де'\";\n}\nfunction _nextWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  return \"'келесі \" + weekday + \" сағат' p'-де'\";\n}\nvar formatRelativeLocale = {\n  lastWeek: function lastWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _lastWeek(day);\n    }\n  },\n  yesterday: \"'кеше сағат' p'-де'\",\n  today: \"'бүгін сағат' p'-де'\",\n  tomorrow: \"'ертең сағат' p'-де'\",\n  nextWeek: function nextWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _nextWeek(day);\n    }\n  },\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n  return format;\n};\nexport default formatRelative;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,yCAAyC;AACnE,IAAIC,kBAAkB,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,CAAC;AACpH,SAASC,SAASA,CAACC,GAAG,EAAE;EACtB,IAAIC,OAAO,GAAGH,kBAAkB,CAACE,GAAG,CAAC;EACrC,OAAO,SAAS,GAAGC,OAAO,GAAG,gBAAgB;AAC/C;AACA,SAASC,QAAQA,CAACF,GAAG,EAAE;EACrB,IAAIC,OAAO,GAAGH,kBAAkB,CAACE,GAAG,CAAC;EACrC,OAAO,GAAG,GAAGC,OAAO,GAAG,gBAAgB;AACzC;AACA,SAASE,SAASA,CAACH,GAAG,EAAE;EACtB,IAAIC,OAAO,GAAGH,kBAAkB,CAACE,GAAG,CAAC;EACrC,OAAO,UAAU,GAAGC,OAAO,GAAG,gBAAgB;AAChD;AACA,IAAIG,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,SAASA,QAAQA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACnD,IAAIR,GAAG,GAAGM,IAAI,CAACG,SAAS,CAAC,CAAC;IAC1B,IAAIZ,aAAa,CAACS,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAE;MAC1C,OAAON,QAAQ,CAACF,GAAG,CAAC;IACtB,CAAC,MAAM;MACL,OAAOD,SAAS,CAACC,GAAG,CAAC;IACvB;EACF,CAAC;EACDU,SAAS,EAAE,qBAAqB;EAChCC,KAAK,EAAE,sBAAsB;EAC7BC,QAAQ,EAAE,sBAAsB;EAChCC,QAAQ,EAAE,SAASA,QAAQA,CAACP,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACnD,IAAIR,GAAG,GAAGM,IAAI,CAACG,SAAS,CAAC,CAAC;IAC1B,IAAIZ,aAAa,CAACS,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAE;MAC1C,OAAON,QAAQ,CAACF,GAAG,CAAC;IACtB,CAAC,MAAM;MACL,OAAOG,SAAS,CAACH,GAAG,CAAC;IACvB;EACF,CAAC;EACDc,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEV,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC3E,IAAIS,MAAM,GAAGb,oBAAoB,CAACY,KAAK,CAAC;EACxC,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACX,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EACxC;EACA,OAAOS,MAAM;AACf,CAAC;AACD,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}