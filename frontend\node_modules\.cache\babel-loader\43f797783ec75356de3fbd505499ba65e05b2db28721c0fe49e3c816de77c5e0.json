{"ast": null, "code": "export { default as appendOwnerState } from '@mui/utils/appendOwnerState';", "map": {"version": 3, "names": ["default", "appendOwnerState"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/base/utils/appendOwnerState.js"], "sourcesContent": ["export { default as appendOwnerState } from '@mui/utils/appendOwnerState';"], "mappings": "AAAA,SAASA,OAAO,IAAIC,gBAAgB,QAAQ,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}