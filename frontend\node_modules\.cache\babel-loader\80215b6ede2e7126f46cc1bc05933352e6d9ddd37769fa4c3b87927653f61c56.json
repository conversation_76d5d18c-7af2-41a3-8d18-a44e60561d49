{"ast": null, "code": "import isSameUTCWeek from \"../../../../_lib/isSameUTCWeek/index.js\";\nfunction checkWeek(date, baseDate, options) {\n  var baseFormat = 'eeee p';\n  if (isSameUTCWeek(date, baseDate, options)) {\n    return baseFormat; // in same week\n  } else if (date.getTime() > baseDate.getTime()) {\n    return \"'下个'\" + baseFormat; // in next week\n  }\n  return \"'上个'\" + baseFormat; // in last week\n}\nvar formatRelativeLocale = {\n  lastWeek: checkWeek,\n  // days before yesterday, maybe in this week or last week\n  yesterday: \"'昨天' p\",\n  today: \"'今天' p\",\n  tomorrow: \"'明天' p\",\n  nextWeek: checkWeek,\n  // days after tomorrow, maybe in this week or next week\n  other: 'PP p'\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n  return format;\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["isSameUTCWeek", "checkWeek", "date", "baseDate", "options", "baseFormat", "getTime", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "format"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/zh-CN/_lib/formatRelative/index.js"], "sourcesContent": ["import isSameUTCWeek from \"../../../../_lib/isSameUTCWeek/index.js\";\nfunction checkWeek(date, baseDate, options) {\n  var baseFormat = 'eeee p';\n  if (isSameUTCWeek(date, baseDate, options)) {\n    return baseFormat; // in same week\n  } else if (date.getTime() > baseDate.getTime()) {\n    return \"'下个'\" + baseFormat; // in next week\n  }\n\n  return \"'上个'\" + baseFormat; // in last week\n}\n\nvar formatRelativeLocale = {\n  lastWeek: checkWeek,\n  // days before yesterday, maybe in this week or last week\n  yesterday: \"'昨天' p\",\n  today: \"'今天' p\",\n  tomorrow: \"'明天' p\",\n  nextWeek: checkWeek,\n  // days after tomorrow, maybe in this week or next week\n  other: 'PP p'\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n  return format;\n};\nexport default formatRelative;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,yCAAyC;AACnE,SAASC,SAASA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC1C,IAAIC,UAAU,GAAG,QAAQ;EACzB,IAAIL,aAAa,CAACE,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAE;IAC1C,OAAOC,UAAU,CAAC,CAAC;EACrB,CAAC,MAAM,IAAIH,IAAI,CAACI,OAAO,CAAC,CAAC,GAAGH,QAAQ,CAACG,OAAO,CAAC,CAAC,EAAE;IAC9C,OAAO,MAAM,GAAGD,UAAU,CAAC,CAAC;EAC9B;EAEA,OAAO,MAAM,GAAGA,UAAU,CAAC,CAAC;AAC9B;AAEA,IAAIE,oBAAoB,GAAG;EACzBC,QAAQ,EAAEP,SAAS;EACnB;EACAQ,SAAS,EAAE,QAAQ;EACnBC,KAAK,EAAE,QAAQ;EACfC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAEX,SAAS;EACnB;EACAY,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEb,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC3E,IAAIY,MAAM,GAAGT,oBAAoB,CAACQ,KAAK,CAAC;EACxC,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACd,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EACxC;EACA,OAAOY,MAAM;AACf,CAAC;AACD,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}