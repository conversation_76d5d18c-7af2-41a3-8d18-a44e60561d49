const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Student = require('../models/Student');
const { protect, adminOnly, adminOrCoach } = require('../middleware/auth');

const router = express.Router();

// Apply protection to all routes
router.use(protect);

// @desc    Get all students
// @route   GET /api/students
// @access  Private (Admin/Coach)
router.get('/', [
  adminOrCoach,
  query('page').optional().isInt({ min: 1 }).withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('حد النتائج يجب أن يكون بين 1 و 100'),
  query('search').optional().isLength({ min: 1 }).withMessage('نص البحث يجب أن يكون حرف واحد على الأقل'),
  query('beltLevel').optional().isIn([
    'white', 'yellow', 'orange', 'green', 'blue', 'brown', 'red',
    'black-1st', 'black-2nd', 'black-3rd', 'black-4th', 'black-5th',
    'black-6th', 'black-7th', 'black-8th', 'black-9th'
  ]).withMessage('مستوى الحزام غير صحيح'),
  query('isActive').optional().isBoolean().withMessage('حالة النشاط يجب أن تكون صحيح أو خطأ')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const { search, beltLevel, isActive } = req.query;

    // Build query
    let query = {};

    if (search) {
      query.$or = [
        { fullName: { $regex: search, $options: 'i' } },
        { studentId: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } },
        { 'emergencyContact.name': { $regex: search, $options: 'i' } }
      ];
    }

    if (beltLevel) {
      query.beltLevel = beltLevel;
    }

    if (isActive !== undefined) {
      query.isActive = isActive === 'true';
    }

    // Get students with pagination
    const students = await Student.find(query)
      .populate('createdBy', 'fullName')
      .populate('updatedBy', 'fullName')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // Get total count for pagination
    const total = await Student.countDocuments(query);

    res.status(200).json({
      success: true,
      count: students.length,
      total,
      pagination: {
        page,
        limit,
        pages: Math.ceil(total / limit)
      },
      data: students
    });

  } catch (error) {
    console.error('Get students error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Get single student
// @route   GET /api/students/:id
// @access  Private (Admin/Coach)
router.get('/:id', adminOrCoach, async (req, res) => {
  try {
    const student = await Student.findById(req.params.id)
      .populate('createdBy', 'fullName')
      .populate('updatedBy', 'fullName');

    if (!student) {
      return res.status(404).json({
        success: false,
        message: 'الطالب غير موجود'
      });
    }

    res.status(200).json({
      success: true,
      data: student
    });

  } catch (error) {
    console.error('Get student error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Create new student
// @route   POST /api/students
// @access  Private (Admin only)
router.post('/', [
  adminOnly,
  body('fullName')
    .notEmpty()
    .withMessage('الاسم الكامل مطلوب')
    .isLength({ max: 100 })
    .withMessage('الاسم الكامل يجب أن يكون أقل من 100 حرف'),
  body('dateOfBirth')
    .isISO8601()
    .withMessage('تاريخ الميلاد يجب أن يكون تاريخ صحيح'),
  body('gender')
    .isIn(['male', 'female'])
    .withMessage('الجنس يجب أن يكون ذكر أو أنثى'),
  body('phone')
    .matches(/^[0-9+\-\s()]+$/)
    .withMessage('يرجى إدخال رقم هاتف صحيح'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('يرجى إدخال بريد إلكتروني صحيح'),
  body('emergencyContact.name')
    .notEmpty()
    .withMessage('اسم جهة الاتصال في حالات الطوارئ مطلوب'),
  body('emergencyContact.phone')
    .matches(/^[0-9+\-\s()]+$/)
    .withMessage('رقم هاتف جهة الاتصال في حالات الطوارئ يجب أن يكون صحيح'),
  body('emergencyContact.relationship')
    .notEmpty()
    .withMessage('صلة القرابة مطلوبة'),
  body('beltLevel')
    .optional()
    .isIn([
      'white', 'yellow', 'orange', 'green', 'blue', 'brown', 'red',
      'black-1st', 'black-2nd', 'black-3rd', 'black-4th', 'black-5th',
      'black-6th', 'black-7th', 'black-8th', 'black-9th'
    ])
    .withMessage('مستوى الحزام غير صحيح')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    // Check if phone number already exists
    const existingStudent = await Student.findOne({ phone: req.body.phone });
    if (existingStudent) {
      return res.status(400).json({
        success: false,
        message: 'رقم الهاتف مستخدم بالفعل'
      });
    }

    // Create student
    const student = await Student.create({
      ...req.body,
      createdBy: req.user._id
    });

    res.status(201).json({
      success: true,
      message: 'تم إنشاء الطالب بنجاح',
      data: student
    });

  } catch (error) {
    console.error('Create student error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Update student
// @route   PUT /api/students/:id
// @access  Private (Admin only)
router.put('/:id', [
  adminOnly,
  body('fullName')
    .optional()
    .isLength({ max: 100 })
    .withMessage('الاسم الكامل يجب أن يكون أقل من 100 حرف'),
  body('dateOfBirth')
    .optional()
    .isISO8601()
    .withMessage('تاريخ الميلاد يجب أن يكون تاريخ صحيح'),
  body('gender')
    .optional()
    .isIn(['male', 'female'])
    .withMessage('الجنس يجب أن يكون ذكر أو أنثى'),
  body('phone')
    .optional()
    .matches(/^[0-9+\-\s()]+$/)
    .withMessage('يرجى إدخال رقم هاتف صحيح'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('يرجى إدخال بريد إلكتروني صحيح'),
  body('beltLevel')
    .optional()
    .isIn([
      'white', 'yellow', 'orange', 'green', 'blue', 'brown', 'red',
      'black-1st', 'black-2nd', 'black-3rd', 'black-4th', 'black-5th',
      'black-6th', 'black-7th', 'black-8th', 'black-9th'
    ])
    .withMessage('مستوى الحزام غير صحيح')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    // Check if phone number already exists (excluding current student)
    if (req.body.phone) {
      const existingStudent = await Student.findOne({
        phone: req.body.phone,
        _id: { $ne: req.params.id }
      });
      if (existingStudent) {
        return res.status(400).json({
          success: false,
          message: 'رقم الهاتف مستخدم بالفعل'
        });
      }
    }

    const student = await Student.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedBy: req.user._id },
      { new: true, runValidators: true }
    );

    if (!student) {
      return res.status(404).json({
        success: false,
        message: 'الطالب غير موجود'
      });
    }

    res.status(200).json({
      success: true,
      message: 'تم تحديث بيانات الطالب بنجاح',
      data: student
    });

  } catch (error) {
    console.error('Update student error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Delete student
// @route   DELETE /api/students/:id
// @access  Private (Admin only)
router.delete('/:id', adminOnly, async (req, res) => {
  try {
    const student = await Student.findById(req.params.id);

    if (!student) {
      return res.status(404).json({
        success: false,
        message: 'الطالب غير موجود'
      });
    }

    // Soft delete - deactivate instead of removing
    await student.deactivate(req.user._id);

    res.status(200).json({
      success: true,
      message: 'تم حذف الطالب بنجاح'
    });

  } catch (error) {
    console.error('Delete student error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Get students by belt level
// @route   GET /api/students/belt/:level
// @access  Private (Admin/Coach)
router.get('/belt/:level', adminOrCoach, async (req, res) => {
  try {
    const { level } = req.params;

    const validBeltLevels = [
      'white', 'yellow', 'orange', 'green', 'blue', 'brown', 'red',
      'black-1st', 'black-2nd', 'black-3rd', 'black-4th', 'black-5th',
      'black-6th', 'black-7th', 'black-8th', 'black-9th'
    ];

    if (!validBeltLevels.includes(level)) {
      return res.status(400).json({
        success: false,
        message: 'مستوى الحزام غير صحيح'
      });
    }

    const students = await Student.getByBeltLevel(level);

    res.status(200).json({
      success: true,
      count: students.length,
      data: students
    });

  } catch (error) {
    console.error('Get students by belt error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

// @desc    Get active students
// @route   GET /api/students/active/list
// @access  Private (Admin/Coach)
router.get('/active/list', adminOrCoach, async (req, res) => {
  try {
    const students = await Student.getActiveStudents();

    res.status(200).json({
      success: true,
      count: students.length,
      data: students
    });

  } catch (error) {
    console.error('Get active students error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي'
    });
  }
});

module.exports = router;
