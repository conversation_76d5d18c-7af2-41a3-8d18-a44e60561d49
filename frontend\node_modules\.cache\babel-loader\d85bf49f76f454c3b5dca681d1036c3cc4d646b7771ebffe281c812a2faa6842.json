{"ast": null, "code": "export { default as extractEventHandlers } from '@mui/utils/extractEventHandlers';", "map": {"version": 3, "names": ["default", "extractEventHandlers"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/base/utils/extractEventHandlers.js"], "sourcesContent": ["export { default as extractEventHandlers } from '@mui/utils/extractEventHandlers';"], "mappings": "AAAA,SAASA,OAAO,IAAIC,oBAAoB,QAAQ,iCAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}