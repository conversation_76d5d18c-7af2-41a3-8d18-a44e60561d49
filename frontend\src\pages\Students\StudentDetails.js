import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  IconButton,
  Chip,
  Avatar,
  Divider,
  Alert,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  WhatsApp as WhatsAppIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Home as HomeIcon,
  Person as PersonIcon,
  ContactEmergency as ContactEmergencyIcon,
  SportsKabaddi as SportsIcon,
  CalendarToday as CalendarIcon,
  Cake as CakeIcon,
  School as SchoolIcon,
  Message as MessageIcon
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import LoadingSpinner from '../../components/Common/LoadingSpinner';
import { studentService } from '../../services/studentService';

const StudentDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [student, setStudent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [whatsappDialog, setWhatsappDialog] = useState(false);
  const [whatsappMessage, setWhatsappMessage] = useState('');

  useEffect(() => {
    fetchStudent();
  }, [id]);

  const fetchStudent = async () => {
    try {
      setLoading(true);
      const response = await studentService.getStudent(id);
      setStudent(response.data);
      setError('');
    } catch (err) {
      setError('فشل في تحميل بيانات الطالب');
      console.error('Error fetching student:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleWhatsApp = (phone, message = '') => {
    const cleanPhone = phone.replace(/[^\d+]/g, '');
    const encodedMessage = encodeURIComponent(message);
    window.open(`https://wa.me/${cleanPhone}${message ? `?text=${encodedMessage}` : ''}`, '_blank');
  };

  const handleDeleteStudent = async () => {
    if (window.confirm('هل أنت متأكد من حذف هذا الطالب؟ هذا الإجراء لا يمكن التراجع عنه.')) {
      try {
        await studentService.deleteStudent(id);
        navigate('/students');
      } catch (err) {
        setError('فشل في حذف الطالب');
      }
    }
  };

  const handleWhatsappWithMessage = () => {
    if (whatsappMessage.trim()) {
      handleWhatsApp(student.phone, whatsappMessage);
      setWhatsappDialog(false);
      setWhatsappMessage('');
    }
  };

  if (loading) {
    return <LoadingSpinner message="جاري تحميل بيانات الطالب..." />;
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/students')}
          sx={{ mt: 2 }}
        >
          العودة إلى قائمة الطلاب
        </Button>
      </Box>
    );
  }

  if (!student) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="warning">الطالب غير موجود</Alert>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/students')}
          sx={{ mt: 2 }}
        >
          العودة إلى قائمة الطلاب
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton
            onClick={() => navigate('/students')}
            sx={{ mr: 2 }}
          >
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
            تفاصيل الطالب
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="إرسال رسالة واتساب">
            <IconButton
              onClick={() => setWhatsappDialog(true)}
              sx={{ color: '#25D366' }}
            >
              <WhatsAppIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="تعديل">
            <IconButton
              onClick={() => navigate(`/students/${id}/edit`)}
              sx={{ color: 'warning.main' }}
            >
              <EditIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="حذف">
            <IconButton
              onClick={handleDeleteStudent}
              sx={{ color: 'error.main' }}
            >
              <DeleteIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Student Profile Card */}
        <Grid item xs={12} md={4}>
          <Card sx={{ textAlign: 'center' }}>
            <CardContent>
              <Avatar
                sx={{
                  width: 120,
                  height: 120,
                  mx: 'auto',
                  mb: 2,
                  bgcolor: 'primary.main',
                  fontSize: '3rem'
                }}
              >
                {student.fullName.charAt(0)}
              </Avatar>

              <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 1 }}>
                {student.fullName}
              </Typography>

              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                رقم الطالب: {student.studentId}
              </Typography>

              <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mb: 2 }}>
                <Chip
                  label={student.beltLevelArabic}
                  color="primary"
                  variant="outlined"
                />
                <Chip
                  label={student.isActive ? 'نشط' : 'غير نشط'}
                  color={student.isActive ? 'success' : 'error'}
                />
              </Box>

              <Divider sx={{ my: 2 }} />

              <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1 }}>
                <Tooltip title="اتصال">
                  <IconButton
                    href={`tel:${student.phone}`}
                    sx={{ color: 'primary.main' }}
                  >
                    <PhoneIcon />
                  </IconButton>
                </Tooltip>

                {student.email && (
                  <Tooltip title="إرسال بريد إلكتروني">
                    <IconButton
                      href={`mailto:${student.email}`}
                      sx={{ color: 'primary.main' }}
                    >
                      <EmailIcon />
                    </IconButton>
                  </Tooltip>
                )}

                <Tooltip title="واتساب">
                  <IconButton
                    onClick={() => handleWhatsApp(student.phone)}
                    sx={{ color: '#25D366' }}
                  >
                    <WhatsAppIcon />
                  </IconButton>
                </Tooltip>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Student Information */}
        <Grid item xs={12} md={8}>
          <Grid container spacing={3}>
            {/* Personal Information */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      المعلومات الشخصية
                    </Typography>
                  </Box>

                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <CakeIcon sx={{ mr: 1, color: 'text.secondary' }} />
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            تاريخ الميلاد
                          </Typography>
                          <Typography variant="body1">
                            {format(new Date(student.dateOfBirth), 'dd MMMM yyyy', { locale: ar })}
                          </Typography>
                        </Box>
                      </Box>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <CalendarIcon sx={{ mr: 1, color: 'text.secondary' }} />
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            العمر
                          </Typography>
                          <Typography variant="body1">
                            {student.age} سنة
                          </Typography>
                        </Box>
                      </Box>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <PersonIcon sx={{ mr: 1, color: 'text.secondary' }} />
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            الجنس
                          </Typography>
                          <Typography variant="body1">
                            {student.genderArabic}
                          </Typography>
                        </Box>
                      </Box>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <SchoolIcon sx={{ mr: 1, color: 'text.secondary' }} />
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            تاريخ الانضمام
                          </Typography>
                          <Typography variant="body1">
                            {format(new Date(student.joinDate), 'dd MMMM yyyy', { locale: ar })}
                          </Typography>
                        </Box>
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Contact Information */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <PhoneIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      معلومات الاتصال
                    </Typography>
                  </Box>

                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <PhoneIcon sx={{ mr: 1, color: 'text.secondary' }} />
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            رقم الهاتف
                          </Typography>
                          <Typography variant="body1">
                            {student.phone}
                          </Typography>
                        </Box>
                      </Box>
                    </Grid>

                    {student.email && (
                      <Grid item xs={12} sm={6}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              البريد الإلكتروني
                            </Typography>
                            <Typography variant="body1">
                              {student.email}
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                    )}
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Address Information */}
            {(student.address?.street || student.address?.city || student.address?.postalCode) && (
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                      <HomeIcon sx={{ mr: 1, color: 'primary.main' }} />
                      <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                        العنوان
                      </Typography>
                    </Box>

                    <Grid container spacing={2}>
                      {student.address?.street && (
                        <Grid item xs={12}>
                          <Typography variant="body2" color="text.secondary">
                            الشارع
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 2 }}>
                            {student.address.street}
                          </Typography>
                        </Grid>
                      )}

                      {student.address?.city && (
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2" color="text.secondary">
                            المدينة
                          </Typography>
                          <Typography variant="body1">
                            {student.address.city}
                          </Typography>
                        </Grid>
                      )}

                      {student.address?.postalCode && (
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2" color="text.secondary">
                            الرمز البريدي
                          </Typography>
                          <Typography variant="body1">
                            {student.address.postalCode}
                          </Typography>
                        </Grid>
                      )}
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>
            )}

            {/* Emergency Contact */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <ContactEmergencyIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      جهة الاتصال في حالات الطوارئ
                    </Typography>
                  </Box>

                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={4}>
                      <Typography variant="body2" color="text.secondary">
                        الاسم
                      </Typography>
                      <Typography variant="body1" sx={{ mb: 2 }}>
                        {student.emergencyContact?.name}
                      </Typography>
                    </Grid>

                    <Grid item xs={12} sm={4}>
                      <Typography variant="body2" color="text.secondary">
                        صلة القرابة
                      </Typography>
                      <Typography variant="body1" sx={{ mb: 2 }}>
                        {student.emergencyContact?.relationship}
                      </Typography>
                    </Grid>

                    <Grid item xs={12} sm={4}>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            رقم الهاتف
                          </Typography>
                          <Typography variant="body1">
                            {student.emergencyContact?.phone}
                          </Typography>
                        </Box>
                        <Box>
                          <Tooltip title="اتصال">
                            <IconButton
                              href={`tel:${student.emergencyContact?.phone}`}
                              size="small"
                              sx={{ color: 'primary.main' }}
                            >
                              <PhoneIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="واتساب">
                            <IconButton
                              onClick={() => handleWhatsApp(student.emergencyContact?.phone)}
                              size="small"
                              sx={{ color: '#25D366' }}
                            >
                              <WhatsAppIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Additional Information */}
            {(student.medicalConditions || student.notes) && (
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                      <SportsIcon sx={{ mr: 1, color: 'primary.main' }} />
                      <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                        معلومات إضافية
                      </Typography>
                    </Box>

                    {student.medicalConditions && (
                      <Box sx={{ mb: 3 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                          الحالات الطبية
                        </Typography>
                        <Typography variant="body1">
                          {student.medicalConditions}
                        </Typography>
                      </Box>
                    )}

                    {student.notes && (
                      <Box>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                          ملاحظات
                        </Typography>
                        <Typography variant="body1">
                          {student.notes}
                        </Typography>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            )}
          </Grid>
        </Grid>
      </Grid>

      {/* WhatsApp Message Dialog */}
      <Dialog
        open={whatsappDialog}
        onClose={() => setWhatsappDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          إرسال رسالة واتساب
        </DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            multiline
            rows={4}
            label="الرسالة"
            value={whatsappMessage}
            onChange={(e) => setWhatsappMessage(e.target.value)}
            placeholder="اكتب رسالتك هنا..."
            sx={{ mt: 1 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setWhatsappDialog(false)}>
            إلغاء
          </Button>
          <Button
            onClick={handleWhatsappWithMessage}
            variant="contained"
            startIcon={<WhatsAppIcon />}
            disabled={!whatsappMessage.trim()}
          >
            إرسال
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StudentDetails;
