{"ast": null, "code": "import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport startOfISOWeekYear from \"../startOfISOWeekYear/index.js\";\nimport differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Set the ISO week-numbering year to the given date.\n *\n * @description\n * Set the ISO week-numbering year to the given date,\n * saving the week number and the weekday number.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} isoWeekYear - the ISO week-numbering year of the new date\n * @returns {Date} the new date with the ISO week-numbering year set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set ISO week-numbering year 2007 to 29 December 2008:\n * const result = setISOWeekYear(new Date(2008, 11, 29), 2007)\n * //=> Mon Jan 01 2007 00:00:00\n */\nexport default function setISOWeekYear(dirtyDate, dirtyISOWeekYear) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var isoWeekYear = toInteger(dirtyISOWeekYear);\n  var diff = differenceInCalendarDays(date, startOfISOWeekYear(date));\n  var fourthOfJanuary = new Date(0);\n  fourthOfJanuary.setFullYear(isoWeekYear, 0, 4);\n  fourthOfJanuary.setHours(0, 0, 0, 0);\n  date = startOfISOWeekYear(fourthOfJanuary);\n  date.setDate(date.getDate() + diff);\n  return date;\n}", "map": {"version": 3, "names": ["toInteger", "toDate", "startOfISOWeekYear", "differenceInCalendarDays", "requiredArgs", "setISOWeekYear", "dirtyDate", "dirtyISOWeekYear", "arguments", "date", "isoWeekYear", "diff", "fourthOfJanuary", "Date", "setFullYear", "setHours", "setDate", "getDate"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/setISOWeekYear/index.js"], "sourcesContent": ["import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport startOfISOWeekYear from \"../startOfISOWeekYear/index.js\";\nimport differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Set the ISO week-numbering year to the given date.\n *\n * @description\n * Set the ISO week-numbering year to the given date,\n * saving the week number and the weekday number.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} isoWeekYear - the ISO week-numbering year of the new date\n * @returns {Date} the new date with the ISO week-numbering year set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set ISO week-numbering year 2007 to 29 December 2008:\n * const result = setISOWeekYear(new Date(2008, 11, 29), 2007)\n * //=> Mon Jan 01 2007 00:00:00\n */\nexport default function setISOWeekYear(dirtyDate, dirtyISOWeekYear) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var isoWeekYear = toInteger(dirtyISOWeekYear);\n  var diff = differenceInCalendarDays(date, startOfISOWeekYear(date));\n  var fourthOfJanuary = new Date(0);\n  fourthOfJanuary.setFullYear(isoWeekYear, 0, 4);\n  fourthOfJanuary.setHours(0, 0, 0, 0);\n  date = startOfISOWeekYear(fourthOfJanuary);\n  date.setDate(date.getDate() + diff);\n  return date;\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,4BAA4B;AAClD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,kBAAkB,MAAM,gCAAgC;AAC/D,OAAOC,wBAAwB,MAAM,sCAAsC;AAC3E,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,cAAcA,CAACC,SAAS,EAAEC,gBAAgB,EAAE;EAClEH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGR,MAAM,CAACK,SAAS,CAAC;EAC5B,IAAII,WAAW,GAAGV,SAAS,CAACO,gBAAgB,CAAC;EAC7C,IAAII,IAAI,GAAGR,wBAAwB,CAACM,IAAI,EAAEP,kBAAkB,CAACO,IAAI,CAAC,CAAC;EACnE,IAAIG,eAAe,GAAG,IAAIC,IAAI,CAAC,CAAC,CAAC;EACjCD,eAAe,CAACE,WAAW,CAACJ,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9CE,eAAe,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACpCN,IAAI,GAAGP,kBAAkB,CAACU,eAAe,CAAC;EAC1CH,IAAI,CAACO,OAAO,CAACP,IAAI,CAACQ,OAAO,CAAC,CAAC,GAAGN,IAAI,CAAC;EACnC,OAAOF,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}