{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { useIsLandscape } from '../useIsLandscape';\n\n/**\n * Props used to create the layout of the views.\n * Those props are exposed on all the pickers.\n */\n\n/**\n * Prepare the props for the view layout (managed by `PickersLayout`)\n */\nexport const usePickerLayoutProps = ({\n  props,\n  propsFromPickerValue,\n  propsFromPickerViews,\n  wrapperVariant\n}) => {\n  const {\n    orientation\n  } = props;\n  const isLandscape = useIsLandscape(propsFromPickerViews.views, orientation);\n  const layoutProps = _extends({}, propsFromPickerViews, propsFromPickerValue, {\n    isLandscape,\n    wrapperVariant,\n    disabled: props.disabled,\n    readOnly: props.readOnly\n  });\n  return {\n    layoutProps\n  };\n};", "map": {"version": 3, "names": ["_extends", "useIsLandscape", "usePickerLayoutProps", "props", "propsFromPickerValue", "propsFromPickerViews", "wrapperVariant", "orientation", "isLandscape", "views", "layoutProps", "disabled", "readOnly"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/@mui/x-date-pickers/internals/hooks/usePicker/usePickerLayoutProps.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { useIsLandscape } from '../useIsLandscape';\n\n/**\n * Props used to create the layout of the views.\n * Those props are exposed on all the pickers.\n */\n\n/**\n * Prepare the props for the view layout (managed by `PickersLayout`)\n */\nexport const usePickerLayoutProps = ({\n  props,\n  propsFromPickerValue,\n  propsFromPickerViews,\n  wrapperVariant\n}) => {\n  const {\n    orientation\n  } = props;\n  const isLandscape = useIsLandscape(propsFromPickerViews.views, orientation);\n  const layoutProps = _extends({}, propsFromPickerViews, propsFromPickerValue, {\n    isLandscape,\n    wrapperVariant,\n    disabled: props.disabled,\n    readOnly: props.readOnly\n  });\n  return {\n    layoutProps\n  };\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,cAAc,QAAQ,mBAAmB;;AAElD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO,MAAMC,oBAAoB,GAAGA,CAAC;EACnCC,KAAK;EACLC,oBAAoB;EACpBC,oBAAoB;EACpBC;AACF,CAAC,KAAK;EACJ,MAAM;IACJC;EACF,CAAC,GAAGJ,KAAK;EACT,MAAMK,WAAW,GAAGP,cAAc,CAACI,oBAAoB,CAACI,KAAK,EAAEF,WAAW,CAAC;EAC3E,MAAMG,WAAW,GAAGV,QAAQ,CAAC,CAAC,CAAC,EAAEK,oBAAoB,EAAED,oBAAoB,EAAE;IAC3EI,WAAW;IACXF,cAAc;IACdK,QAAQ,EAAER,KAAK,CAACQ,QAAQ;IACxBC,QAAQ,EAAET,KAAK,CAACS;EAClB,CAAC,CAAC;EACF,OAAO;IACLF;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}