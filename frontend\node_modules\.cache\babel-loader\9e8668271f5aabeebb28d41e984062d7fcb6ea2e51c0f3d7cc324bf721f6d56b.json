{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: function lastWeek(date) {\n    switch (date.getUTCDay()) {\n      case 6:\n        //Σάββατο\n        return \"'το προηγούμενο' eeee 'στις' p\";\n      default:\n        return \"'την προηγούμενη' eeee 'στις' p\";\n    }\n  },\n  yesterday: \"'χθες στις' p\",\n  today: \"'σήμερα στις' p\",\n  tomorrow: \"'αύριο στις' p\",\n  nextWeek: \"eeee 'στις' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') return format(date);\n  return format;\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "date", "getUTCDay", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "format"], "sources": ["C:/Users/<USER>/OneDrive - MOE Student S4M/Desktop/CRM Project/frontend/node_modules/date-fns/esm/locale/el/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: function lastWeek(date) {\n    switch (date.getUTCDay()) {\n      case 6:\n        //Σάββατο\n        return \"'το προηγούμενο' eeee 'στις' p\";\n      default:\n        return \"'την προηγούμενη' eeee 'στις' p\";\n    }\n  },\n  yesterday: \"'χθες στις' p\",\n  today: \"'σήμερα στις' p\",\n  tomorrow: \"'αύριο στις' p\",\n  nextWeek: \"eeee 'στις' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') return format(date);\n  return format;\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,SAASA,QAAQA,CAACC,IAAI,EAAE;IAChC,QAAQA,IAAI,CAACC,SAAS,CAAC,CAAC;MACtB,KAAK,CAAC;QACJ;QACA,OAAO,gCAAgC;MACzC;QACE,OAAO,iCAAiC;IAC5C;EACF,CAAC;EACDC,SAAS,EAAE,eAAe;EAC1BC,KAAK,EAAE,iBAAiB;EACxBC,QAAQ,EAAE,gBAAgB;EAC1BC,QAAQ,EAAE,eAAe;EACzBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAER,IAAI,EAAE;EACxD,IAAIS,MAAM,GAAGX,oBAAoB,CAACU,KAAK,CAAC;EACxC,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE,OAAOA,MAAM,CAACT,IAAI,CAAC;EACrD,OAAOS,MAAM;AACf,CAAC;AACD,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}